{"version": 3, "sources": ["../../../src/build/adapter/build-complete.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs/promises'\nimport { promisify } from 'util'\nimport { pathToFileURL } from 'url'\nimport * as Log from '../output/log'\nimport globOriginal from 'next/dist/compiled/glob'\nimport { interopDefault } from '../../lib/interop-default'\nimport type { AdapterOutputs, NextAdapter } from '../../server/config-shared'\nimport type {\n  FunctionsConfigManifest,\n  PrerenderManifest,\n  RoutesManifest,\n} from '..'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../webpack/plugins/middleware-plugin'\nimport { isMiddlewareFilename } from '../utils'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { AdapterOutputType } from '../../shared/lib/constants'\nimport { RenderingMode } from '../rendering-mode'\nimport { isDynamicRoute } from '../../shared/lib/router/utils'\n\nconst glob = promisify(globOriginal)\n\nexport async function handleBuildComplete({\n  // dir,\n  distDir,\n  tracingRoot,\n  adapterPath,\n  pageKeys,\n  appPageKeys,\n  hasNodeMiddleware,\n  hasInstrumentationHook,\n  requiredServerFiles,\n  routesManifest,\n  prerenderManifest,\n  middlewareManifest,\n  functionsConfigManifest,\n  hasStatic404,\n}: {\n  dir: string\n  distDir: string\n  adapterPath: string\n  tracingRoot: string\n  hasNodeMiddleware: boolean\n  pageKeys: readonly string[]\n  hasInstrumentationHook: boolean\n  appPageKeys?: readonly string[] | undefined\n  requiredServerFiles: string[]\n  routesManifest: RoutesManifest\n  prerenderManifest: PrerenderManifest\n  middlewareManifest: MiddlewareManifest\n  functionsConfigManifest: FunctionsConfigManifest\n  hasStatic404: boolean\n}) {\n  const adapterMod = interopDefault(\n    await import(pathToFileURL(require.resolve(adapterPath)).href)\n  ) as NextAdapter\n\n  if (typeof adapterMod.onBuildComplete === 'function') {\n    Log.info(`Running onBuildComplete from ${adapterMod.name}`)\n\n    try {\n      const outputs: AdapterOutputs = []\n\n      const staticFiles = await glob('**/*', {\n        cwd: path.join(distDir, 'static'),\n      })\n\n      for (const file of staticFiles) {\n        const pathname = path.posix.join('/_next/static', file)\n        const filePath = path.join(distDir, 'static', file)\n        outputs.push({\n          type: AdapterOutputType.STATIC_FILE,\n          id: path.join('static', file),\n          pathname,\n          filePath,\n        })\n      }\n\n      const sharedNodeAssets: Record<string, string> = {}\n\n      for (const file of requiredServerFiles) {\n        // add to shared node assets\n        const filePath = path.join(distDir, file)\n        const fileOutputPath = path.relative(tracingRoot, filePath)\n        sharedNodeAssets[fileOutputPath] = filePath\n      }\n\n      if (hasInstrumentationHook) {\n        const assets = await handleTraceFiles(\n          path.join(distDir, 'server', 'instrumentation.js.nft.json')\n        )\n        const fileOutputPath = path.relative(\n          tracingRoot,\n          path.join(distDir, 'server', 'instrumentation.js')\n        )\n        sharedNodeAssets[fileOutputPath] = path.join(\n          distDir,\n          'server',\n          'instrumentation.js'\n        )\n        Object.assign(sharedNodeAssets, assets)\n      }\n\n      async function handleTraceFiles(\n        traceFilePath: string\n      ): Promise<Record<string, string>> {\n        const assets: Record<string, string> = Object.assign(\n          {},\n          sharedNodeAssets\n        )\n        const traceData = JSON.parse(\n          await fs.readFile(traceFilePath, 'utf8')\n        ) as {\n          files: string[]\n        }\n        const traceFileDir = path.dirname(traceFilePath)\n\n        for (const relativeFile of traceData.files) {\n          const tracedFilePath = path.join(traceFileDir, relativeFile)\n          const fileOutputPath = path.relative(tracingRoot, tracedFilePath)\n          assets[fileOutputPath] = tracedFilePath\n        }\n        return assets\n      }\n\n      async function handleEdgeFunction(\n        page: EdgeFunctionDefinition,\n        isMiddleware: boolean = false\n      ) {\n        let type = AdapterOutputType.PAGES\n        const isAppPrefix = page.page.startsWith('app/')\n        const isAppPage = isAppPrefix && page.page.endsWith('/page')\n        const isAppRoute = isAppPrefix && page.page.endsWith('/route')\n\n        if (isMiddleware) {\n          type = AdapterOutputType.MIDDLEWARE\n        } else if (isAppPage) {\n          type = AdapterOutputType.APP_PAGE\n        } else if (isAppRoute) {\n          type = AdapterOutputType.APP_ROUTE\n        } else if (page.page.startsWith('/api')) {\n          type = AdapterOutputType.PAGES_API\n        }\n\n        const output: AdapterOutputs[0] = {\n          id: page.name,\n          runtime: 'edge',\n          pathname: isAppPrefix ? normalizeAppPath(page.name) : page.name,\n          filePath: path.join(\n            distDir,\n            'server',\n            page.files.find(\n              (item) =>\n                item.startsWith('server/app') || item.startsWith('server/pages')\n            ) || ''\n          ),\n          assets: {},\n          type,\n          config:\n            type === AdapterOutputType.MIDDLEWARE\n              ? {\n                  matchers: page.matchers,\n                }\n              : {},\n        }\n\n        function handleFile(file: string) {\n          const originalPath = path.join(distDir, file)\n          const fileOutputPath = path.join(\n            path.relative(tracingRoot, distDir),\n            file\n          )\n          if (!output.assets) {\n            output.assets = {}\n          }\n          output.assets[fileOutputPath] = originalPath\n        }\n        for (const file of page.files) {\n          handleFile(file)\n        }\n        for (const item of [...(page.wasm || []), ...(page.assets || [])]) {\n          handleFile(item.filePath)\n        }\n        outputs.push(output)\n      }\n\n      const edgeFunctionHandlers: Promise<any>[] = []\n\n      for (const middleware of Object.values(middlewareManifest.middleware)) {\n        if (isMiddlewareFilename(middleware.name)) {\n          edgeFunctionHandlers.push(handleEdgeFunction(middleware, true))\n        }\n      }\n\n      for (const page of Object.values(middlewareManifest.functions)) {\n        edgeFunctionHandlers.push(handleEdgeFunction(page))\n      }\n      const pagesDistDir = path.join(distDir, 'server', 'pages')\n      const pageOutputMap: Record<string, AdapterOutputs[0]> = {}\n\n      for (const page of pageKeys) {\n        if (middlewareManifest.functions.hasOwnProperty(page)) {\n          continue\n        }\n        const route = normalizePagePath(page)\n\n        const pageFile = path.join(\n          pagesDistDir,\n          `${normalizePagePath(page)}.js`\n        )\n        const pageTraceFile = `${pageFile}.nft.json`\n        const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n          if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n            Log.warn(`Failed to locate traced assets for ${pageFile}`, err)\n          }\n          return {} as Record<string, string>\n        })\n        const functionConfig = functionsConfigManifest.functions[route] || {}\n\n        const output: AdapterOutputs[0] = {\n          id: route,\n          type: page.startsWith('/api')\n            ? AdapterOutputType.PAGES_API\n            : AdapterOutputType.PAGES,\n          filePath: pageTraceFile.replace(/\\.nft\\.json$/, ''),\n          pathname: route,\n          assets,\n          runtime: 'nodejs',\n          config: {\n            maxDuration: functionConfig.maxDuration,\n            preferredRegion: functionConfig.regions,\n          },\n        }\n        pageOutputMap[page] = output\n        outputs.push(output)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareFile = path.join(distDir, 'server', 'middleware.js')\n        const middlewareTrace = `${middlewareFile}.nft.json`\n        const assets = await handleTraceFiles(middlewareTrace)\n        const functionConfig =\n          functionsConfigManifest.functions['/_middleware'] || {}\n\n        outputs.push({\n          pathname: '/_middleware',\n          id: '/_middleware',\n          assets,\n          type: AdapterOutputType.MIDDLEWARE,\n          runtime: 'nodejs',\n          filePath: middlewareFile,\n          config: {\n            matchers: functionConfig.matchers,\n          },\n        })\n      }\n      const appOutputMap: Record<string, AdapterOutputs[0]> = {}\n      const appDistDir = path.join(distDir, 'server', 'app')\n\n      if (appPageKeys) {\n        for (const page of appPageKeys) {\n          if (middlewareManifest.functions.hasOwnProperty(page)) {\n            continue\n          }\n          const normalizedPage = normalizeAppPath(page)\n          const pageFile = path.join(appDistDir, `${page}.js`)\n          const pageTraceFile = `${pageFile}.nft.json`\n          const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n            Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n            return {} as Record<string, string>\n          })\n          const functionConfig =\n            functionsConfigManifest.functions[normalizedPage] || {}\n\n          const output: AdapterOutputs[0] = {\n            pathname: normalizedPage,\n            id: normalizedPage,\n            assets,\n            type: page.endsWith('/route')\n              ? AdapterOutputType.APP_ROUTE\n              : AdapterOutputType.APP_PAGE,\n            runtime: 'nodejs',\n            filePath: pageFile,\n            config: {\n              maxDuration: functionConfig.maxDuration,\n              preferredRegion: functionConfig.regions,\n            },\n          }\n          appOutputMap[normalizedPage] = output\n          outputs.push(output)\n        }\n      }\n\n      const getParentOutput = (\n        srcRoute: string,\n        childRoute: string,\n        allowMissing?: boolean\n      ) => {\n        const parentOutput = pageOutputMap[srcRoute] || appOutputMap[srcRoute]\n\n        if (!parentOutput && !allowMissing) {\n          console.error({\n            appOutputs: Object.keys(appOutputMap),\n            pageOutputs: Object.keys(pageOutputMap),\n          })\n          throw new Error(\n            `Invariant: failed to find source route ${srcRoute} for prerender ${childRoute}`\n          )\n        }\n        return parentOutput\n      }\n\n      const {\n        prefetchSegmentDirSuffix,\n        prefetchSegmentSuffix,\n        varyHeader,\n        didPostponeHeader,\n        contentTypeHeader,\n      } = routesManifest.rsc\n\n      const handleAppMeta = async (\n        route: string,\n        initialOutput: AdapterOutputs[0]\n      ) => {\n        const meta: {\n          segmentPaths?: string[]\n          postponed?: string\n        } = JSON.parse(\n          await fs\n            .readFile(path.join(appDistDir, `${route}.meta`), 'utf8')\n            .catch(() => '{}')\n        )\n\n        if (meta.postponed && initialOutput.config) {\n          initialOutput.config.postponed = meta.postponed\n        }\n\n        if (meta?.segmentPaths) {\n          const segmentsDir = path.join(\n            appDistDir,\n            `${route}${prefetchSegmentDirSuffix}`\n          )\n\n          for (const segmentPath of meta.segmentPaths) {\n            const outputSegmentPath =\n              path.join(\n                appDistDir,\n                route + prefetchSegmentDirSuffix,\n                segmentPath\n              ) + prefetchSegmentSuffix\n\n            const fallbackPathname = path.join(\n              segmentsDir,\n              segmentPath + prefetchSegmentSuffix\n            )\n\n            outputs.push({\n              id: outputSegmentPath,\n              pathname: outputSegmentPath,\n              type: AdapterOutputType.PRERENDER,\n              parentOutputId: initialOutput.parentOutputId,\n\n              config: {\n                ...initialOutput.config,\n              },\n\n              fallback: {\n                filePath: fallbackPathname,\n                initialExpiration: initialOutput.fallback?.initialExpiration,\n                initialRevalidate: initialOutput.fallback?.initialRevalidate,\n\n                initialHeaders: {\n                  ...initialOutput.fallback?.initialHeaders,\n                  vary: varyHeader,\n                  'content-type': contentTypeHeader,\n                  [didPostponeHeader]: '2',\n                },\n              },\n            })\n          }\n        }\n      }\n\n      for (const route in prerenderManifest.routes) {\n        const {\n          initialExpireSeconds: initialExpiration,\n          initialRevalidateSeconds: initialRevalidate,\n          initialHeaders,\n          initialStatus,\n          prefetchDataRoute,\n          dataRoute,\n          renderingMode,\n          allowHeader,\n          experimentalBypassFor,\n        } = prerenderManifest.routes[route]\n\n        const srcRoute = prerenderManifest.routes[route].srcRoute || route\n        const isAppPage =\n          Boolean(appOutputMap[srcRoute]) || srcRoute === '/_not-found'\n\n        const isNotFoundTrue = prerenderManifest.notFoundRoutes.includes(route)\n\n        let allowQuery: string[] | undefined\n        const routeKeys = routesManifest.dynamicRoutes.find(\n          (item) => item.page === srcRoute\n        )?.routeKeys\n\n        if (!isDynamicRoute(srcRoute)) {\n          // for non-dynamic routes we use an empty array since\n          // no query values bust the cache for non-dynamic prerenders\n          // prerendered paths also do not pass allowQuery as they match\n          // during handle: 'filesystem' so should not cache differently\n          // by query values\n          allowQuery = []\n        } else if (routeKeys) {\n          // if we have routeKeys in the routes-manifest we use those\n          // for allowQuery for dynamic routes\n          allowQuery = Object.values(routeKeys)\n        }\n\n        let filePath = path.join(\n          isAppPage ? appDistDir : pagesDistDir,\n          `${route}.${isAppPage && !dataRoute ? 'body' : 'html'}`\n        )\n\n        // we use the static 404 for notFound: true if available\n        // if not we do a blocking invoke on first request\n        if (isNotFoundTrue && hasStatic404) {\n          filePath = path.join(pagesDistDir, '404.html')\n        }\n\n        const initialOutput: AdapterOutputs[0] = {\n          id: route,\n          type: AdapterOutputType.PRERENDER,\n          pathname: route,\n          parentOutputId:\n            srcRoute === '/_not-found'\n              ? srcRoute\n              : getParentOutput(srcRoute, route).id,\n          fallback:\n            !isNotFoundTrue || (isNotFoundTrue && hasStatic404)\n              ? {\n                  filePath,\n                  initialStatus,\n                  initialHeaders: {\n                    ...initialHeaders,\n                    vary: varyHeader,\n                    'content-type': contentTypeHeader,\n                  },\n                  initialExpiration,\n                  initialRevalidate: initialRevalidate || 1,\n                }\n              : undefined,\n          config: {\n            allowQuery,\n            allowHeader,\n            renderingMode,\n            bypassFor: experimentalBypassFor,\n            bypassToken: prerenderManifest.preview.previewModeId,\n          },\n        }\n        outputs.push(initialOutput)\n\n        if (dataRoute) {\n          let dataFilePath = path.join(pagesDistDir, `${route}.json`)\n\n          if (isAppPage) {\n            // When experimental PPR is enabled, we expect that the data\n            // that should be served as a part of the prerender should\n            // be from the prefetch data route. If this isn't enabled\n            // for ppr, the only way to get the data is from the data\n            // route.\n            dataFilePath = path.join(\n              appDistDir,\n              prefetchDataRoute &&\n                renderingMode === RenderingMode.PARTIALLY_STATIC\n                ? prefetchDataRoute\n                : dataRoute\n            )\n          }\n\n          outputs.push({\n            ...initialOutput,\n            id: dataRoute,\n            pathname: dataRoute,\n            fallback: isNotFoundTrue\n              ? undefined\n              : {\n                  ...initialOutput.fallback,\n                  filePath: dataFilePath,\n                },\n          })\n        }\n\n        if (isAppPage) {\n          await handleAppMeta(route, initialOutput)\n        }\n      }\n\n      for (const dynamicRoute in prerenderManifest.dynamicRoutes) {\n        const {\n          fallback,\n          fallbackExpire,\n          fallbackRevalidate,\n          fallbackHeaders,\n          fallbackStatus,\n          allowHeader,\n          dataRoute,\n          renderingMode,\n          experimentalBypassFor,\n        } = prerenderManifest.dynamicRoutes[dynamicRoute]\n\n        const isAppPage = Boolean(appOutputMap[dynamicRoute])\n\n        const allowQuery = Object.values(\n          routesManifest.dynamicRoutes.find(\n            (item) => item.page === dynamicRoute\n          )?.routeKeys || {}\n        )\n\n        const initialOutput: AdapterOutputs[0] = {\n          id: dynamicRoute,\n          type: AdapterOutputType.PRERENDER,\n          pathname: dynamicRoute,\n          parentOutputId: getParentOutput(dynamicRoute, dynamicRoute).id,\n          config: {\n            allowQuery,\n            allowHeader,\n            renderingMode,\n            bypassFor: experimentalBypassFor,\n            bypassToken: prerenderManifest.preview.previewModeId,\n          },\n          fallback:\n            typeof fallback === 'string'\n              ? {\n                  filePath: path.join(\n                    isAppPage ? appDistDir : pagesDistDir,\n                    fallback\n                  ),\n                  initialStatus: fallbackStatus,\n                  initialHeaders: fallbackHeaders,\n                  initialExpiration: fallbackExpire,\n                  initialRevalidate: fallbackRevalidate || 1,\n                }\n              : undefined,\n        }\n        outputs.push(initialOutput)\n\n        if (isAppPage) {\n          await handleAppMeta(dynamicRoute, initialOutput)\n        }\n\n        if (dataRoute) {\n          outputs.push({\n            ...initialOutput,\n            id: dataRoute,\n            pathname: dataRoute,\n            fallback: undefined,\n          })\n        }\n      }\n\n      await adapterMod.onBuildComplete({\n        routes: {\n          dynamicRoutes: routesManifest.dynamicRoutes,\n          rewrites: routesManifest.rewrites,\n          redirects: routesManifest.redirects,\n          headers: routesManifest.headers,\n        },\n        outputs,\n      })\n    } catch (err) {\n      Log.error(`Failed to run onBuildComplete from ${adapterMod.name}`)\n      throw err\n    }\n  }\n}\n"], "names": ["path", "fs", "promisify", "pathToFileURL", "Log", "globOriginal", "interopDefault", "isMiddlewareFilename", "normalizePagePath", "normalizeAppPath", "AdapterOutputType", "RenderingMode", "isDynamicRoute", "glob", "handleBuildComplete", "distDir", "tracingRoot", "adapterPath", "pageKeys", "appPageKeys", "hasNodeMiddleware", "hasInstrumentationHook", "requiredServerFiles", "routesManifest", "prerenderManifest", "middlewareManifest", "functionsConfigManifest", "hasStatic404", "adapterMod", "require", "resolve", "href", "onBuildComplete", "info", "name", "outputs", "staticFiles", "cwd", "join", "file", "pathname", "posix", "filePath", "push", "type", "STATIC_FILE", "id", "sharedNodeAssets", "fileOutputPath", "relative", "assets", "handleTraceFiles", "Object", "assign", "traceFilePath", "traceData", "JSON", "parse", "readFile", "traceFileDir", "dirname", "relativeFile", "files", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleEdgeFunction", "page", "isMiddleware", "PAGES", "isAppPrefix", "startsWith", "isAppPage", "endsWith", "isAppRoute", "MIDDLEWARE", "APP_PAGE", "APP_ROUTE", "PAGES_API", "output", "runtime", "find", "item", "config", "matchers", "handleFile", "originalPath", "wasm", "edgeFunctionHandlers", "middleware", "values", "functions", "pagesDistDir", "pageOutputMap", "hasOwnProperty", "route", "pageFile", "pageTraceFile", "catch", "err", "code", "warn", "functionConfig", "replace", "maxDuration", "preferredRegion", "regions", "middlewareFile", "middlewareTrace", "appOutputMap", "appDistDir", "normalizedPage", "getParentOutput", "srcRoute", "childRoute", "allowMissing", "parentOutput", "console", "error", "appOutputs", "keys", "pageOutputs", "Error", "prefetchSegmentDirSuffix", "prefetchSegmentSuffix", "<PERSON><PERSON><PERSON><PERSON>", "didPostponeHeader", "contentTypeHeader", "rsc", "handleAppMeta", "initialOutput", "meta", "postponed", "segmentPaths", "segmentsDir", "segmentPath", "outputSegmentPath", "fallbackPathname", "PRERENDER", "parentOutputId", "fallback", "initialExpiration", "initialRevalidate", "initialHeaders", "vary", "routes", "initialExpireSeconds", "initialRevalidateSeconds", "initialStatus", "prefetchDataRoute", "dataRoute", "renderingMode", "allow<PERSON>eader", "experimentalBypassFor", "Boolean", "isNotFoundTrue", "notFoundRoutes", "includes", "<PERSON><PERSON><PERSON><PERSON>", "routeKeys", "dynamicRoutes", "undefined", "bypassFor", "bypassToken", "preview", "previewModeId", "dataFilePath", "PARTIALLY_STATIC", "dynamicRoute", "fallbackExpire", "fallbackRevalidate", "fallbackHeaders", "fallback<PERSON><PERSON><PERSON>", "rewrites", "redirects", "headers"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,SAASC,SAAS,QAAQ,OAAM;AAChC,SAASC,aAAa,QAAQ,MAAK;AACnC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,kBAAkB,0BAAyB;AAClD,SAASC,cAAc,QAAQ,4BAA2B;AAW1D,SAASC,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,iBAAiB,QAAQ,6BAA4B;AAC9D,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,cAAc,QAAQ,gCAA+B;AAE9D,MAAMC,OAAOX,UAAUG;AAEvB,OAAO,eAAeS,oBAAoB,EACxC,OAAO;AACPC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,WAAW,EACXC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,cAAc,EACdC,iBAAiB,EACjBC,kBAAkB,EAClBC,uBAAuB,EACvBC,YAAY,EAgBb;IACC,MAAMC,aAAatB,eACjB,MAAM,MAAM,CAACH,cAAc0B,QAAQC,OAAO,CAACb,cAAcc,IAAI;IAG/D,IAAI,OAAOH,WAAWI,eAAe,KAAK,YAAY;QACpD5B,IAAI6B,IAAI,CAAC,CAAC,6BAA6B,EAAEL,WAAWM,IAAI,EAAE;QAE1D,IAAI;YACF,MAAMC,UAA0B,EAAE;YAElC,MAAMC,cAAc,MAAMvB,KAAK,QAAQ;gBACrCwB,KAAKrC,KAAKsC,IAAI,CAACvB,SAAS;YAC1B;YAEA,KAAK,MAAMwB,QAAQH,YAAa;gBAC9B,MAAMI,WAAWxC,KAAKyC,KAAK,CAACH,IAAI,CAAC,iBAAiBC;gBAClD,MAAMG,WAAW1C,KAAKsC,IAAI,CAACvB,SAAS,UAAUwB;gBAC9CJ,QAAQQ,IAAI,CAAC;oBACXC,MAAMlC,kBAAkBmC,WAAW;oBACnCC,IAAI9C,KAAKsC,IAAI,CAAC,UAAUC;oBACxBC;oBACAE;gBACF;YACF;YAEA,MAAMK,mBAA2C,CAAC;YAElD,KAAK,MAAMR,QAAQjB,oBAAqB;gBACtC,4BAA4B;gBAC5B,MAAMoB,WAAW1C,KAAKsC,IAAI,CAACvB,SAASwB;gBACpC,MAAMS,iBAAiBhD,KAAKiD,QAAQ,CAACjC,aAAa0B;gBAClDK,gBAAgB,CAACC,eAAe,GAAGN;YACrC;YAEA,IAAIrB,wBAAwB;gBAC1B,MAAM6B,SAAS,MAAMC,iBACnBnD,KAAKsC,IAAI,CAACvB,SAAS,UAAU;gBAE/B,MAAMiC,iBAAiBhD,KAAKiD,QAAQ,CAClCjC,aACAhB,KAAKsC,IAAI,CAACvB,SAAS,UAAU;gBAE/BgC,gBAAgB,CAACC,eAAe,GAAGhD,KAAKsC,IAAI,CAC1CvB,SACA,UACA;gBAEFqC,OAAOC,MAAM,CAACN,kBAAkBG;YAClC;YAEA,eAAeC,iBACbG,aAAqB;gBAErB,MAAMJ,SAAiCE,OAAOC,MAAM,CAClD,CAAC,GACDN;gBAEF,MAAMQ,YAAYC,KAAKC,KAAK,CAC1B,MAAMxD,GAAGyD,QAAQ,CAACJ,eAAe;gBAInC,MAAMK,eAAe3D,KAAK4D,OAAO,CAACN;gBAElC,KAAK,MAAMO,gBAAgBN,UAAUO,KAAK,CAAE;oBAC1C,MAAMC,iBAAiB/D,KAAKsC,IAAI,CAACqB,cAAcE;oBAC/C,MAAMb,iBAAiBhD,KAAKiD,QAAQ,CAACjC,aAAa+C;oBAClDb,MAAM,CAACF,eAAe,GAAGe;gBAC3B;gBACA,OAAOb;YACT;YAEA,eAAec,mBACbC,IAA4B,EAC5BC,eAAwB,KAAK;gBAE7B,IAAItB,OAAOlC,kBAAkByD,KAAK;gBAClC,MAAMC,cAAcH,KAAKA,IAAI,CAACI,UAAU,CAAC;gBACzC,MAAMC,YAAYF,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBACpD,MAAMC,aAAaJ,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBAErD,IAAIL,cAAc;oBAChBtB,OAAOlC,kBAAkB+D,UAAU;gBACrC,OAAO,IAAIH,WAAW;oBACpB1B,OAAOlC,kBAAkBgE,QAAQ;gBACnC,OAAO,IAAIF,YAAY;oBACrB5B,OAAOlC,kBAAkBiE,SAAS;gBACpC,OAAO,IAAIV,KAAKA,IAAI,CAACI,UAAU,CAAC,SAAS;oBACvCzB,OAAOlC,kBAAkBkE,SAAS;gBACpC;gBAEA,MAAMC,SAA4B;oBAChC/B,IAAImB,KAAK/B,IAAI;oBACb4C,SAAS;oBACTtC,UAAU4B,cAAc3D,iBAAiBwD,KAAK/B,IAAI,IAAI+B,KAAK/B,IAAI;oBAC/DQ,UAAU1C,KAAKsC,IAAI,CACjBvB,SACA,UACAkD,KAAKH,KAAK,CAACiB,IAAI,CACb,CAACC,OACCA,KAAKX,UAAU,CAAC,iBAAiBW,KAAKX,UAAU,CAAC,oBAChD;oBAEPnB,QAAQ,CAAC;oBACTN;oBACAqC,QACErC,SAASlC,kBAAkB+D,UAAU,GACjC;wBACES,UAAUjB,KAAKiB,QAAQ;oBACzB,IACA,CAAC;gBACT;gBAEA,SAASC,WAAW5C,IAAY;oBAC9B,MAAM6C,eAAepF,KAAKsC,IAAI,CAACvB,SAASwB;oBACxC,MAAMS,iBAAiBhD,KAAKsC,IAAI,CAC9BtC,KAAKiD,QAAQ,CAACjC,aAAaD,UAC3BwB;oBAEF,IAAI,CAACsC,OAAO3B,MAAM,EAAE;wBAClB2B,OAAO3B,MAAM,GAAG,CAAC;oBACnB;oBACA2B,OAAO3B,MAAM,CAACF,eAAe,GAAGoC;gBAClC;gBACA,KAAK,MAAM7C,QAAQ0B,KAAKH,KAAK,CAAE;oBAC7BqB,WAAW5C;gBACb;gBACA,KAAK,MAAMyC,QAAQ;uBAAKf,KAAKoB,IAAI,IAAI,EAAE;uBAAOpB,KAAKf,MAAM,IAAI,EAAE;iBAAE,CAAE;oBACjEiC,WAAWH,KAAKtC,QAAQ;gBAC1B;gBACAP,QAAQQ,IAAI,CAACkC;YACf;YAEA,MAAMS,uBAAuC,EAAE;YAE/C,KAAK,MAAMC,cAAcnC,OAAOoC,MAAM,CAAC/D,mBAAmB8D,UAAU,EAAG;gBACrE,IAAIhF,qBAAqBgF,WAAWrD,IAAI,GAAG;oBACzCoD,qBAAqB3C,IAAI,CAACqB,mBAAmBuB,YAAY;gBAC3D;YACF;YAEA,KAAK,MAAMtB,QAAQb,OAAOoC,MAAM,CAAC/D,mBAAmBgE,SAAS,EAAG;gBAC9DH,qBAAqB3C,IAAI,CAACqB,mBAAmBC;YAC/C;YACA,MAAMyB,eAAe1F,KAAKsC,IAAI,CAACvB,SAAS,UAAU;YAClD,MAAM4E,gBAAmD,CAAC;YAE1D,KAAK,MAAM1B,QAAQ/C,SAAU;gBAC3B,IAAIO,mBAAmBgE,SAAS,CAACG,cAAc,CAAC3B,OAAO;oBACrD;gBACF;gBACA,MAAM4B,QAAQrF,kBAAkByD;gBAEhC,MAAM6B,WAAW9F,KAAKsC,IAAI,CACxBoD,cACA,GAAGlF,kBAAkByD,MAAM,GAAG,CAAC;gBAEjC,MAAM8B,gBAAgB,GAAGD,SAAS,SAAS,CAAC;gBAC5C,MAAM5C,SAAS,MAAMC,iBAAiB4C,eAAeC,KAAK,CAAC,CAACC;oBAC1D,IAAIA,IAAIC,IAAI,KAAK,YAAajC,SAAS,UAAUA,SAAS,QAAS;wBACjE7D,IAAI+F,IAAI,CAAC,CAAC,mCAAmC,EAAEL,UAAU,EAAEG;oBAC7D;oBACA,OAAO,CAAC;gBACV;gBACA,MAAMG,iBAAiB1E,wBAAwB+D,SAAS,CAACI,MAAM,IAAI,CAAC;gBAEpE,MAAMhB,SAA4B;oBAChC/B,IAAI+C;oBACJjD,MAAMqB,KAAKI,UAAU,CAAC,UAClB3D,kBAAkBkE,SAAS,GAC3BlE,kBAAkByD,KAAK;oBAC3BzB,UAAUqD,cAAcM,OAAO,CAAC,gBAAgB;oBAChD7D,UAAUqD;oBACV3C;oBACA4B,SAAS;oBACTG,QAAQ;wBACNqB,aAAaF,eAAeE,WAAW;wBACvCC,iBAAiBH,eAAeI,OAAO;oBACzC;gBACF;gBACAb,aAAa,CAAC1B,KAAK,GAAGY;gBACtB1C,QAAQQ,IAAI,CAACkC;YACf;YAEA,IAAIzD,mBAAmB;gBACrB,MAAMqF,iBAAiBzG,KAAKsC,IAAI,CAACvB,SAAS,UAAU;gBACpD,MAAM2F,kBAAkB,GAAGD,eAAe,SAAS,CAAC;gBACpD,MAAMvD,SAAS,MAAMC,iBAAiBuD;gBACtC,MAAMN,iBACJ1E,wBAAwB+D,SAAS,CAAC,eAAe,IAAI,CAAC;gBAExDtD,QAAQQ,IAAI,CAAC;oBACXH,UAAU;oBACVM,IAAI;oBACJI;oBACAN,MAAMlC,kBAAkB+D,UAAU;oBAClCK,SAAS;oBACTpC,UAAU+D;oBACVxB,QAAQ;wBACNC,UAAUkB,eAAelB,QAAQ;oBACnC;gBACF;YACF;YACA,MAAMyB,eAAkD,CAAC;YACzD,MAAMC,aAAa5G,KAAKsC,IAAI,CAACvB,SAAS,UAAU;YAEhD,IAAII,aAAa;gBACf,KAAK,MAAM8C,QAAQ9C,YAAa;oBAC9B,IAAIM,mBAAmBgE,SAAS,CAACG,cAAc,CAAC3B,OAAO;wBACrD;oBACF;oBACA,MAAM4C,iBAAiBpG,iBAAiBwD;oBACxC,MAAM6B,WAAW9F,KAAKsC,IAAI,CAACsE,YAAY,GAAG3C,KAAK,GAAG,CAAC;oBACnD,MAAM8B,gBAAgB,GAAGD,SAAS,SAAS,CAAC;oBAC5C,MAAM5C,SAAS,MAAMC,iBAAiB4C,eAAeC,KAAK,CAAC,CAACC;wBAC1D7F,IAAI+F,IAAI,CAAC,CAAC,gCAAgC,EAAEL,UAAU,EAAEG;wBACxD,OAAO,CAAC;oBACV;oBACA,MAAMG,iBACJ1E,wBAAwB+D,SAAS,CAACoB,eAAe,IAAI,CAAC;oBAExD,MAAMhC,SAA4B;wBAChCrC,UAAUqE;wBACV/D,IAAI+D;wBACJ3D;wBACAN,MAAMqB,KAAKM,QAAQ,CAAC,YAChB7D,kBAAkBiE,SAAS,GAC3BjE,kBAAkBgE,QAAQ;wBAC9BI,SAAS;wBACTpC,UAAUoD;wBACVb,QAAQ;4BACNqB,aAAaF,eAAeE,WAAW;4BACvCC,iBAAiBH,eAAeI,OAAO;wBACzC;oBACF;oBACAG,YAAY,CAACE,eAAe,GAAGhC;oBAC/B1C,QAAQQ,IAAI,CAACkC;gBACf;YACF;YAEA,MAAMiC,kBAAkB,CACtBC,UACAC,YACAC;gBAEA,MAAMC,eAAevB,aAAa,CAACoB,SAAS,IAAIJ,YAAY,CAACI,SAAS;gBAEtE,IAAI,CAACG,gBAAgB,CAACD,cAAc;oBAClCE,QAAQC,KAAK,CAAC;wBACZC,YAAYjE,OAAOkE,IAAI,CAACX;wBACxBY,aAAanE,OAAOkE,IAAI,CAAC3B;oBAC3B;oBACA,MAAM,qBAEL,CAFK,IAAI6B,MACR,CAAC,uCAAuC,EAAET,SAAS,eAAe,EAAEC,YAAY,GAD5E,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,OAAOE;YACT;YAEA,MAAM,EACJO,wBAAwB,EACxBC,qBAAqB,EACrBC,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EAClB,GAAGtG,eAAeuG,GAAG;YAEtB,MAAMC,gBAAgB,OACpBlC,OACAmC;gBAEA,MAAMC,OAGFzE,KAAKC,KAAK,CACZ,MAAMxD,GACHyD,QAAQ,CAAC1D,KAAKsC,IAAI,CAACsE,YAAY,GAAGf,MAAM,KAAK,CAAC,GAAG,QACjDG,KAAK,CAAC,IAAM;gBAGjB,IAAIiC,KAAKC,SAAS,IAAIF,cAAc/C,MAAM,EAAE;oBAC1C+C,cAAc/C,MAAM,CAACiD,SAAS,GAAGD,KAAKC,SAAS;gBACjD;gBAEA,IAAID,wBAAAA,KAAME,YAAY,EAAE;oBACtB,MAAMC,cAAcpI,KAAKsC,IAAI,CAC3BsE,YACA,GAAGf,QAAQ4B,0BAA0B;oBAGvC,KAAK,MAAMY,eAAeJ,KAAKE,YAAY,CAAE;4BAyBpBH,yBACAA,0BAGdA;wBA5BT,MAAMM,oBACJtI,KAAKsC,IAAI,CACPsE,YACAf,QAAQ4B,0BACRY,eACEX;wBAEN,MAAMa,mBAAmBvI,KAAKsC,IAAI,CAChC8F,aACAC,cAAcX;wBAGhBvF,QAAQQ,IAAI,CAAC;4BACXG,IAAIwF;4BACJ9F,UAAU8F;4BACV1F,MAAMlC,kBAAkB8H,SAAS;4BACjCC,gBAAgBT,cAAcS,cAAc;4BAE5CxD,QAAQ;gCACN,GAAG+C,cAAc/C,MAAM;4BACzB;4BAEAyD,UAAU;gCACRhG,UAAU6F;gCACVI,iBAAiB,GAAEX,0BAAAA,cAAcU,QAAQ,qBAAtBV,wBAAwBW,iBAAiB;gCAC5DC,iBAAiB,GAAEZ,2BAAAA,cAAcU,QAAQ,qBAAtBV,yBAAwBY,iBAAiB;gCAE5DC,gBAAgB;wCACXb,2BAAAA,cAAcU,QAAQ,qBAAtBV,yBAAwBa,cAAc,AAAzC;oCACAC,MAAMnB;oCACN,gBAAgBE;oCAChB,CAACD,kBAAkB,EAAE;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,IAAK,MAAM/B,SAASrE,kBAAkBuH,MAAM,CAAE;oBAoB1BxH;gBAnBlB,MAAM,EACJyH,sBAAsBL,iBAAiB,EACvCM,0BAA0BL,iBAAiB,EAC3CC,cAAc,EACdK,aAAa,EACbC,iBAAiB,EACjBC,SAAS,EACTC,aAAa,EACbC,WAAW,EACXC,qBAAqB,EACtB,GAAG/H,kBAAkBuH,MAAM,CAAClD,MAAM;gBAEnC,MAAMkB,WAAWvF,kBAAkBuH,MAAM,CAAClD,MAAM,CAACkB,QAAQ,IAAIlB;gBAC7D,MAAMvB,YACJkF,QAAQ7C,YAAY,CAACI,SAAS,KAAKA,aAAa;gBAElD,MAAM0C,iBAAiBjI,kBAAkBkI,cAAc,CAACC,QAAQ,CAAC9D;gBAEjE,IAAI+D;gBACJ,MAAMC,aAAYtI,qCAAAA,eAAeuI,aAAa,CAAC/E,IAAI,CACjD,CAACC,OAASA,KAAKf,IAAI,KAAK8C,8BADRxF,mCAEfsI,SAAS;gBAEZ,IAAI,CAACjJ,eAAemG,WAAW;oBAC7B,qDAAqD;oBACrD,4DAA4D;oBAC5D,8DAA8D;oBAC9D,8DAA8D;oBAC9D,kBAAkB;oBAClB6C,aAAa,EAAE;gBACjB,OAAO,IAAIC,WAAW;oBACpB,2DAA2D;oBAC3D,oCAAoC;oBACpCD,aAAaxG,OAAOoC,MAAM,CAACqE;gBAC7B;gBAEA,IAAInH,WAAW1C,KAAKsC,IAAI,CACtBgC,YAAYsC,aAAalB,cACzB,GAAGG,MAAM,CAAC,EAAEvB,aAAa,CAAC8E,YAAY,SAAS,QAAQ;gBAGzD,wDAAwD;gBACxD,kDAAkD;gBAClD,IAAIK,kBAAkB9H,cAAc;oBAClCe,WAAW1C,KAAKsC,IAAI,CAACoD,cAAc;gBACrC;gBAEA,MAAMsC,gBAAmC;oBACvClF,IAAI+C;oBACJjD,MAAMlC,kBAAkB8H,SAAS;oBACjChG,UAAUqD;oBACV4C,gBACE1B,aAAa,gBACTA,WACAD,gBAAgBC,UAAUlB,OAAO/C,EAAE;oBACzC4F,UACE,CAACe,kBAAmBA,kBAAkB9H,eAClC;wBACEe;wBACAwG;wBACAL,gBAAgB;4BACd,GAAGA,cAAc;4BACjBC,MAAMnB;4BACN,gBAAgBE;wBAClB;wBACAc;wBACAC,mBAAmBA,qBAAqB;oBAC1C,IACAmB;oBACN9E,QAAQ;wBACN2E;wBACAN;wBACAD;wBACAW,WAAWT;wBACXU,aAAazI,kBAAkB0I,OAAO,CAACC,aAAa;oBACtD;gBACF;gBACAhI,QAAQQ,IAAI,CAACqF;gBAEb,IAAIoB,WAAW;oBACb,IAAIgB,eAAepK,KAAKsC,IAAI,CAACoD,cAAc,GAAGG,MAAM,KAAK,CAAC;oBAE1D,IAAIvB,WAAW;wBACb,4DAA4D;wBAC5D,0DAA0D;wBAC1D,yDAAyD;wBACzD,yDAAyD;wBACzD,SAAS;wBACT8F,eAAepK,KAAKsC,IAAI,CACtBsE,YACAuC,qBACEE,kBAAkB1I,cAAc0J,gBAAgB,GAC9ClB,oBACAC;oBAER;oBAEAjH,QAAQQ,IAAI,CAAC;wBACX,GAAGqF,aAAa;wBAChBlF,IAAIsG;wBACJ5G,UAAU4G;wBACVV,UAAUe,iBACNM,YACA;4BACE,GAAG/B,cAAcU,QAAQ;4BACzBhG,UAAU0H;wBACZ;oBACN;gBACF;gBAEA,IAAI9F,WAAW;oBACb,MAAMyD,cAAclC,OAAOmC;gBAC7B;YACF;YAEA,IAAK,MAAMsC,gBAAgB9I,kBAAkBsI,aAAa,CAAE;oBAgBxDvI;gBAfF,MAAM,EACJmH,QAAQ,EACR6B,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdpB,WAAW,EACXF,SAAS,EACTC,aAAa,EACbE,qBAAqB,EACtB,GAAG/H,kBAAkBsI,aAAa,CAACQ,aAAa;gBAEjD,MAAMhG,YAAYkF,QAAQ7C,YAAY,CAAC2D,aAAa;gBAEpD,MAAMV,aAAaxG,OAAOoC,MAAM,CAC9BjE,EAAAA,sCAAAA,eAAeuI,aAAa,CAAC/E,IAAI,CAC/B,CAACC,OAASA,KAAKf,IAAI,KAAKqG,kCAD1B/I,oCAEGsI,SAAS,KAAI,CAAC;gBAGnB,MAAM7B,gBAAmC;oBACvClF,IAAIwH;oBACJ1H,MAAMlC,kBAAkB8H,SAAS;oBACjChG,UAAU8H;oBACV7B,gBAAgB3B,gBAAgBwD,cAAcA,cAAcxH,EAAE;oBAC9DmC,QAAQ;wBACN2E;wBACAN;wBACAD;wBACAW,WAAWT;wBACXU,aAAazI,kBAAkB0I,OAAO,CAACC,aAAa;oBACtD;oBACAzB,UACE,OAAOA,aAAa,WAChB;wBACEhG,UAAU1C,KAAKsC,IAAI,CACjBgC,YAAYsC,aAAalB,cACzBgD;wBAEFQ,eAAewB;wBACf7B,gBAAgB4B;wBAChB9B,mBAAmB4B;wBACnB3B,mBAAmB4B,sBAAsB;oBAC3C,IACAT;gBACR;gBACA5H,QAAQQ,IAAI,CAACqF;gBAEb,IAAI1D,WAAW;oBACb,MAAMyD,cAAcuC,cAActC;gBACpC;gBAEA,IAAIoB,WAAW;oBACbjH,QAAQQ,IAAI,CAAC;wBACX,GAAGqF,aAAa;wBAChBlF,IAAIsG;wBACJ5G,UAAU4G;wBACVV,UAAUqB;oBACZ;gBACF;YACF;YAEA,MAAMnI,WAAWI,eAAe,CAAC;gBAC/B+G,QAAQ;oBACNe,eAAevI,eAAeuI,aAAa;oBAC3Ca,UAAUpJ,eAAeoJ,QAAQ;oBACjCC,WAAWrJ,eAAeqJ,SAAS;oBACnCC,SAAStJ,eAAesJ,OAAO;gBACjC;gBACA1I;YACF;QACF,EAAE,OAAO8D,KAAK;YACZ7F,IAAIgH,KAAK,CAAC,CAAC,mCAAmC,EAAExF,WAAWM,IAAI,EAAE;YACjE,MAAM+D;QACR;IACF;AACF", "ignoreList": [0]}