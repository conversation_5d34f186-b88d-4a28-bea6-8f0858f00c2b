{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/internal-error.ts"], "sourcesContent": ["import type { TurbopackInternalErrorOpts } from '../../../build/swc/generated-native'\nimport { eventErrorThrown } from '../../../telemetry/events'\nimport { traceGlobals } from '../../../trace/shared'\n\n/**\n * An error caused by a bug in Turbopack, and not the user's code (e.g. a Rust panic). These should\n * be written to a log file and details should not be shown to the user.\n *\n * These are constructed in Turbopack by calling `throwTurbopackInternalError`.\n */\nexport class TurbopackInternalError extends Error {\n  name = 'TurbopackInternalError'\n  location: string | undefined\n\n  // Manually set this as this isn't statically determinable\n  __NEXT_ERROR_CODE = 'TurbopackInternalError'\n\n  constructor({ message, anonymizedLocation }: TurbopackInternalErrorOpts) {\n    super(message)\n    this.location = anonymizedLocation\n  }\n}\n\n/**\n * A helper used by the napi Rust entrypoints to construct and throw a `TurbopackInternalError`.\n *\n * When called, this will emit a telemetry event.\n */\nexport function throwTurbopackInternalError(\n  conversionError: Error | null,\n  opts: TurbopackInternalErrorOpts\n): never {\n  if (conversionError != null) {\n    // Somehow napi failed to convert `opts` to a JS object??? Just give up and throw that instead.\n    throw new Error(\n      'NAPI type conversion error in throwTurbopackInternalError',\n      {\n        cause: conversionError,\n      }\n    )\n  }\n  const err = new TurbopackInternalError(opts)\n  const telemetry = traceGlobals.get('telemetry')\n  if (telemetry) {\n    telemetry.record(eventErrorThrown(err, opts.anonymizedLocation))\n  } else {\n    console.error('Expected `telemetry` to be set in globals')\n  }\n  throw err\n}\n"], "names": ["TurbopackInternalError", "throwTurbopackInternalError", "Error", "constructor", "message", "anonymizedLocation", "name", "__NEXT_ERROR_CODE", "location", "conversionError", "opts", "cause", "err", "telemetry", "traceGlobals", "get", "record", "eventErrorThrown", "console", "error"], "mappings": ";;;;;;;;;;;;;;;IAUaA,sBAAsB;eAAtBA;;IAkBGC,2BAA2B;eAA3BA;;;wBA3BiB;wBACJ;AAQtB,MAAMD,+BAA+BE;IAO1CC,YAAY,EAAEC,OAAO,EAAEC,kBAAkB,EAA8B,CAAE;QACvE,KAAK,CAACD,eAPRE,OAAO,0BAGP,0DAA0D;aAC1DC,oBAAoB;QAIlB,IAAI,CAACC,QAAQ,GAAGH;IAClB;AACF;AAOO,SAASJ,4BACdQ,eAA6B,EAC7BC,IAAgC;IAEhC,IAAID,mBAAmB,MAAM;QAC3B,+FAA+F;QAC/F,MAAM,qBAKL,CALK,IAAIP,MACR,6DACA;YACES,OAAOF;QACT,IAJI,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;IACA,MAAMG,MAAM,IAAIZ,uBAAuBU;IACvC,MAAMG,YAAYC,oBAAY,CAACC,GAAG,CAAC;IACnC,IAAIF,WAAW;QACbA,UAAUG,MAAM,CAACC,IAAAA,wBAAgB,EAACL,KAAKF,KAAKL,kBAAkB;IAChE,OAAO;QACLa,QAAQC,KAAK,CAAC;IAChB;IACA,MAAMP;AACR", "ignoreList": [0]}