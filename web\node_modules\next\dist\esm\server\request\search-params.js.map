{"version": 3, "sources": ["../../../src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n  type PrerenderStoreModernRuntime,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in cache contexts.'\n        )\n      case 'request':\n        return createRenderSearchParams(underlyingSearchParams, workStore)\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerSearchParamsForServerPage should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderSearchParams(\n          underlyingSearchParams,\n          workUnitStore\n        )\n      case 'request':\n        return createRenderSearchParams(underlyingSearchParams, workStore)\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        // We're prerendering in a mode that aborts (cacheComponents) and should stall\n        // the promise to ensure the RSC side is considered dynamic\n        return makeHangingPromise(\n          workUnitStore.renderSignal,\n          workStore.route,\n          '`searchParams`'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n        return Promise.resolve({})\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nfunction createStaticPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client':\n      // We are in a cacheComponents (PPR or otherwise) prerender\n      return makeHangingSearchParams(workStore, prerenderStore)\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // We are in a legacy static generation and need to interrupt the\n      // prerender when search params are accessed.\n      return makeErroringExoticSearchParams(workStore, prerenderStore)\n    default:\n      return prerenderStore satisfies never\n  }\n}\n\nfunction createRuntimePrerenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<SearchParams> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    process.env.__NEXT_CACHE_COMPONENTS\n      ? makeUntrackedSearchParams(underlyingSearchParams)\n      : makeUntrackedExoticSearchParams(underlyingSearchParams)\n  )\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      // Semantically we only need the dev tracking when running in `next dev`\n      // but since you would never use next dev with production NODE_ENV we use this\n      // as a proxy so we can statically exclude this code from production builds.\n      if (process.env.__NEXT_CACHE_COMPONENTS) {\n        return makeUntrackedSearchParamsWithDevWarnings(\n          underlyingSearchParams,\n          workStore\n        )\n      }\n\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      if (process.env.__NEXT_CACHE_COMPONENTS) {\n        return makeUntrackedSearchParams(underlyingSearchParams)\n      }\n\n      return makeUntrackedExoticSearchParams(underlyingSearchParams)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeHangingSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no cacheComponents)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no cacheComponents)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no cacheComponents)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no cacheComponents)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get: function get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, get)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has: function has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, has)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys: function ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore, ownKeys)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          if (workUnitStore) {\n            trackDynamicDataInDynamicRender(workUnitStore)\n          }\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        if (workUnitStore) {\n          trackDynamicDataInDynamicRender(workUnitStore)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = makeDevtoolsIOAwarePromise(underlyingSearchParams)\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticSearchParamsWithDevWarnings`, but\n// just logging the sync access without actually defining the search params on\n// the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n  const promise = makeDevtoolsIOAwarePromise(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["ReflectAdapter", "throwToInterruptStaticGeneration", "postponeWithTracking", "trackDynamicDataInDynamicRender", "annotateDynamicAccess", "trackSynchronousRequestDataAccessInDev", "delayUntilRuntimeStage", "workUnitAsyncStorage", "throwInvariantForMissingStore", "InvariantError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "wellKnownProperties", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "createSearchParamsFromClient", "underlyingSearchParams", "workStore", "workUnitStore", "getStore", "type", "createStaticPrerenderSearchParams", "createRenderSearchParams", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "createRuntimePrerenderSearchParams", "createPrerenderSearchParamsForClientPage", "forceStatic", "Promise", "resolve", "renderSignal", "route", "prerenderStore", "makeHangingSearchParams", "makeErroringExoticSearchParams", "process", "env", "__NEXT_CACHE_COMPONENTS", "makeUntrackedSearchParams", "makeUntrackedExoticSearchParams", "NODE_ENV", "makeUntrackedSearchParamsWithDevWarnings", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "expression", "set", "dynamicShouldError", "dynamicTracking", "has", "ownKeys", "makeErroringSearchParamsForUseCache", "keys", "for<PERSON>ach", "defineProperty", "value", "writable", "enumerable", "configurable", "store", "proxiedProperties", "Set", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "Reflect", "then", "push", "add", "newValue", "syncIODev", "delete", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "length", "prerenderPhase", "createSearchAccessError", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": "AAEA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,gCAAgC,EAChCC,oBAAoB,EACpBC,+BAA+B,EAC/BC,qBAAqB,EACrBC,sCAAsC,EACtCC,sBAAsB,QACjB,kCAAiC;AAExC,SACEC,oBAAoB,EAMpBC,6BAA6B,QACxB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SACEC,4BAA4B,EAC5BC,iCAAiC,EACjCC,mBAAmB,QACd,uCAAsC;AAC7C,SACEC,qDAAqD,EACrDC,oCAAoC,QAC/B,UAAS;AAgChB,OAAO,SAASC,6BACdC,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBd,qBAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCJ,WAAWC;YACtD,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIZ,eACR,8EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIA,eACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOgB,yBAAyBN,wBAAwBC;YAC1D;gBACEC;QACJ;IACF;IACAb;AACF;AAEA,6FAA6F;AAC7F,OAAO,MAAMkB,sCACXC,sCAAqC;AAEvC,OAAO,SAASA,sCACdR,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBd,qBAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCJ,WAAWC;YACtD,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIZ,eACR,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOmB,mCACLT,wBACAE;YAEJ,KAAK;gBACH,OAAOI,yBAAyBN,wBAAwBC;YAC1D;gBACEC;QACJ;IACF;IACAb;AACF;AAEA,OAAO,SAASqB,yCACdT,SAAoB;IAEpB,IAAIA,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMX,gBAAgBd,qBAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,8EAA8E;gBAC9E,2DAA2D;gBAC3D,OAAOZ,mBACLU,cAAcY,YAAY,EAC1Bb,UAAUc,KAAK,EACf;YAEJ,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIzB,eACR,0FADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIA,eACR,qFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOsB,QAAQC,OAAO,CAAC,CAAC;YAC1B;gBACEX;QACJ;IACF;IACAb;AACF;AAEA,SAASgB,kCACPJ,SAAoB,EACpBe,cAAoC;IAEpC,IAAIf,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,OAAQG,eAAeZ,IAAI;QACzB,KAAK;QACL,KAAK;YACH,2DAA2D;YAC3D,OAAOa,wBAAwBhB,WAAWe;QAC5C,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,6CAA6C;YAC7C,OAAOE,+BAA+BjB,WAAWe;QACnD;YACE,OAAOA;IACX;AACF;AAEA,SAASP,mCACPT,sBAAoC,EACpCE,aAA0C;IAE1C,OAAOf,uBACLe,eACAiB,QAAQC,GAAG,CAACC,uBAAuB,GAC/BC,0BAA0BtB,0BAC1BuB,gCAAgCvB;AAExC;AAEA,SAASM,yBACPN,sBAAoC,EACpCC,SAAoB;IAEpB,IAAIA,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,IAAIM,QAAQC,GAAG,CAACI,QAAQ,KAAK,eAAe;YAC1C,wEAAwE;YACxE,8EAA8E;YAC9E,4EAA4E;YAC5E,IAAIL,QAAQC,GAAG,CAACC,uBAAuB,EAAE;gBACvC,OAAOI,yCACLzB,wBACAC;YAEJ;YAEA,OAAOyB,wDACL1B,wBACAC;QAEJ,OAAO;YACL,IAAIkB,QAAQC,GAAG,CAACC,uBAAuB,EAAE;gBACvC,OAAOC,0BAA0BtB;YACnC;YAEA,OAAOuB,gCAAgCvB;QACzC;IACF;AACF;AAGA,MAAM2B,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASX,wBACPhB,SAAoB,EACpBe,cAAoC;IAEpC,MAAMc,qBAAqBH,mBAAmBI,GAAG,CAACf;IAClD,IAAIc,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUxC,mBACdwB,eAAeF,YAAY,EAC3Bb,UAAUc,KAAK,EACf;IAGF,MAAMkB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOvD,eAAekD,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;wBACFvD,sBAAsBuD,YAAYxB;wBAClC,OAAOnC,eAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMG,aACJ;wBACFvD,sBAAsBuD,YAAYxB;wBAClC,OAAOnC,eAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,OAAOxD,eAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;IACF;IAEAV,mBAAmBc,GAAG,CAACzB,gBAAgBiB;IACvC,OAAOA;AACT;AAEA,SAASf,+BACPjB,SAAoB,EACpBe,cAAwD;IAExD,MAAMc,qBAAqBH,mBAAmBI,GAAG,CAAC9B;IAClD,IAAI6B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM9B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAMgC,UAAUpB,QAAQC,OAAO,CAACb;IAEhC,MAAMiC,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOvD,eAAekD,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;wBACF,IAAIvC,UAAUyC,kBAAkB,EAAE;4BAChC7C,sDACEI,UAAUc,KAAK,EACfyB;wBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;4BAClD,qCAAqC;4BACrCrB,qBACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnB7D,iCACE0D,YACAvC,WACAe;wBAEJ;wBACA;oBACF;gBACA,KAAK;oBAAU;wBACb,MAAMwB,aACJ;wBACF,IAAIvC,UAAUyC,kBAAkB,EAAE;4BAChC7C,sDACEI,UAAUc,KAAK,EACfyB;wBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;4BAClD,qCAAqC;4BACrCrB,qBACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnB7D,iCACE0D,YACAvC,WACAe;wBAEJ;wBACA;oBACF;gBACA;oBAAS;wBACP,IAAI,OAAOoB,SAAS,YAAY,CAACxC,oBAAoBgD,GAAG,CAACR,OAAO;4BAC9D,MAAMI,aAAa9C,6BACjB,gBACA0C;4BAEF,IAAInC,UAAUyC,kBAAkB,EAAE;gCAChC7C,sDACEI,UAAUc,KAAK,EACfyB;4BAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;gCAClD,qCAAqC;gCACrCrB,qBACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;4BAElC,OAAO;gCACL,mBAAmB;gCACnB7D,iCACE0D,YACAvC,WACAe;4BAEJ;wBACF;wBACA,OAAOnC,eAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMI,aAAa7C,kCACjB,gBACAyC;gBAEF,IAAInC,UAAUyC,kBAAkB,EAAE;oBAChC7C,sDACEI,UAAUc,KAAK,EACfyB;gBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;oBAClD,qCAAqC;oBACrCrB,qBACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;gBAElC,OAAO;oBACL,mBAAmB;oBACnB7D,iCACE0D,YACAvC,WACAe;gBAEJ;gBACA,OAAO;YACT;YACA,OAAOnC,eAAe+D,GAAG,CAACT,QAAQC;QACpC;QACAS;YACE,MAAML,aACJ;YACF,IAAIvC,UAAUyC,kBAAkB,EAAE;gBAChC7C,sDACEI,UAAUc,KAAK,EACfyB;YAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;gBAClD,qCAAqC;gBACrCrB,qBACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;YAElC,OAAO;gBACL,mBAAmB;gBACnB7D,iCAAiC0D,YAAYvC,WAAWe;YAC1D;QACF;IACF;IAEAW,mBAAmBc,GAAG,CAACxC,WAAWgC;IAClC,OAAOA;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASa,oCACd7C,SAAoB;IAEpB,MAAM6B,qBAAqBD,8BAA8BE,GAAG,CAAC9B;IAC7D,IAAI6B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUpB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMoB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAK,SAASA,IAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACtC,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOvD,eAAekD,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACxC,oBAAoBgD,GAAG,CAACR,KAAI,GACjD;gBACAtC,qCAAqCG,WAAW8B;YAClD;YAEA,OAAOlD,eAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAO,KAAK,SAASA,IAAIT,MAAM,EAAEC,IAAI;YAC5B,8EAA8E;YAC9E,uFAAuF;YACvF,8FAA8F;YAC9F,kEAAkE;YAClE,IACE,OAAOA,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACxC,oBAAoBgD,GAAG,CAACR,KAAI,GACjD;gBACAtC,qCAAqCG,WAAW2C;YAClD;YAEA,OAAO/D,eAAe+D,GAAG,CAACT,QAAQC;QACpC;QACAS,SAAS,SAASA;YAChB/C,qCAAqCG,WAAW4C;QAClD;IACF;IAEAhB,8BAA8BY,GAAG,CAACxC,WAAWgC;IAC7C,OAAOA;AACT;AAEA,SAASV,gCACPvB,sBAAoC;IAEpC,MAAM8B,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUpB,QAAQC,OAAO,CAACb;IAChC2B,mBAAmBc,GAAG,CAACzC,wBAAwBgC;IAE/CM,OAAOS,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACZ;QAC3C,IAAI,CAACxC,oBAAoBgD,GAAG,CAACR,OAAO;YAClCE,OAAOW,cAAc,CAACjB,SAASI,MAAM;gBACnCL;oBACE,MAAM7B,gBAAgBd,qBAAqBe,QAAQ;oBACnD,IAAID,eAAe;wBACjBlB,gCAAgCkB;oBAClC;oBACA,OAAOF,sBAAsB,CAACoC,KAAK;gBACrC;gBACAK,KAAIS,KAAK;oBACPZ,OAAOW,cAAc,CAACjB,SAASI,MAAM;wBACnCc;wBACAC,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOrB;AACT;AAEA,SAASV,0BACPtB,sBAAoC;IAEpC,MAAM8B,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUpB,QAAQC,OAAO,CAACb;IAChC2B,mBAAmBc,GAAG,CAACzC,wBAAwBgC;IAE/C,OAAOA;AACT;AAEA,SAASN,wDACP1B,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAMxB,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMyB,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,IAAIC,qBAAqB;IACzB,MAAMC,oBAAoB,IAAIzB,MAAMlC,wBAAwB;QAC1D+B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYsB,oBAAoB;gBAClD,IAAIJ,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMF,aAAa9C,6BAA6B,gBAAgB0C;oBAChEvC,sDACEyD,MAAMvC,KAAK,EACXyB;gBAEJ;gBACA,MAAMtC,gBAAgBd,qBAAqBe,QAAQ;gBACnD,IAAID,eAAe;oBACjBlB,gCAAgCkB;gBAClC;YACF;YACA,OAAOrB,eAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIkB,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMF,aAAa7C,kCACjB,gBACAyC;oBAEFvC,sDACEyD,MAAMvC,KAAK,EACXyB;gBAEJ;YACF;YACA,OAAOoB,QAAQhB,GAAG,CAACT,QAAQC;QAC7B;QACAS,SAAQV,MAAM;YACZ,IAAImB,MAAMZ,kBAAkB,EAAE;gBAC5B,MAAMF,aACJ;gBACF3C,sDACEyD,MAAMvC,KAAK,EACXyB;YAEJ;YACA,OAAOoB,QAAQf,OAAO,CAACV;QACzB;IACF;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAMH,UAAUzC,2BAA2BS;IAC3CgC,QAAQ6B,IAAI,CAAC;QACXH,qBAAqB;IACvB;IAEApB,OAAOS,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACZ;QAC3C,IAAIxC,oBAAoBgD,GAAG,CAACR,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEqB,oBAAoBK,IAAI,CAAC1B;QAC3B,OAAO;YACLmB,kBAAkBQ,GAAG,CAAC3B;YACtBE,OAAOW,cAAc,CAACjB,SAASI,MAAM;gBACnCL;oBACE,OAAO4B,iBAAiB,CAACvB,KAAK;gBAChC;gBACAK,KAAIuB,QAAQ;oBACV1B,OAAOW,cAAc,CAACjB,SAASI,MAAM;wBACnCc,OAAOc;wBACPb,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,MAAMpB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUkB,MAAMZ,kBAAkB,EAAE;gBAC/C,MAAMF,aAAa;gBACnB3C,sDACEyD,MAAMvC,KAAK,EACXyB;YAEJ;YACA,IAAI,OAAOJ,SAAS,UAAU;gBAC5B,IACE,CAACxC,oBAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,aAAa9C,6BAA6B,gBAAgB0C;oBAChE6B,UAAUX,MAAMvC,KAAK,EAAEyB;gBACzB;YACF;YACA,OAAO3D,eAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI,EAAEc,KAAK,EAAEb,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BmB,kBAAkBW,MAAM,CAAC9B;YAC3B;YACA,OAAOwB,QAAQnB,GAAG,CAACN,QAAQC,MAAMc,OAAOb;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACxC,oBAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,aAAa7C,kCACjB,gBACAyC;oBAEF6B,UAAUX,MAAMvC,KAAK,EAAEyB;gBACzB;YACF;YACA,OAAOoB,QAAQhB,GAAG,CAACT,QAAQC;QAC7B;QACAS,SAAQV,MAAM;YACZ,MAAMK,aAAa;YACnByB,UAAUX,MAAMvC,KAAK,EAAEyB,YAAYiB;YACnC,OAAOG,QAAQf,OAAO,CAACV;QACzB;IACF;IAEAR,mBAAmBc,GAAG,CAACzC,wBAAwBiC;IAC/C,OAAOA;AACT;AAEA,4EAA4E;AAC5E,8EAA8E;AAC9E,eAAe;AACf,SAASR,yCACPzB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAMxB,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMyB,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAC7C,MAAMzB,UAAUzC,2BAA2BS;IAE3CsC,OAAOS,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACZ;QAC3C,IAAIxC,oBAAoBgD,GAAG,CAACR,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEqB,oBAAoBK,IAAI,CAAC1B;QAC3B,OAAO;YACLmB,kBAAkBQ,GAAG,CAAC3B;QACxB;IACF;IAEA,MAAMH,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,CAACxC,oBAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,aAAa9C,6BAA6B,gBAAgB0C;oBAChE+B,kBAAkBb,MAAMvC,KAAK,EAAEyB;gBACjC;YACF;YACA,OAAO3D,eAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI,EAAEc,KAAK,EAAEb,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BmB,kBAAkBW,MAAM,CAAC9B;YAC3B;YACA,OAAOwB,QAAQnB,GAAG,CAACN,QAAQC,MAAMc,OAAOb;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACxC,oBAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,aAAa7C,kCACjB,gBACAyC;oBAEF+B,kBAAkBb,MAAMvC,KAAK,EAAEyB;gBACjC;YACF;YACA,OAAOoB,QAAQhB,GAAG,CAACT,QAAQC;QAC7B;QACAS,SAAQV,MAAM;YACZ,MAAMK,aAAa;YACnB4B,6BAA6Bd,MAAMvC,KAAK,EAAEyB,YAAYiB;YACtD,OAAOG,QAAQf,OAAO,CAACV;QACzB;IACF;IAEAR,mBAAmBc,GAAG,CAACzC,wBAAwBiC;IAC/C,OAAOA;AACT;AAEA,SAASgC,UACPlD,KAAyB,EACzByB,UAAkB,EAClB6B,iBAAiC;IAEjC,gCAAgC;IAChC,IAAIA,qBAAqBA,kBAAkBC,MAAM,GAAG,GAAG;QACrDF,6BAA6BrD,OAAOyB,YAAY6B;IAClD,OAAO;QACLF,kBAAkBpD,OAAOyB;IAC3B;IAEA,MAAMtC,gBAAgBd,qBAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;gBACH,IAAIF,cAAcqE,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;oBACrErF,uCAAuCgB;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;AACF;AAEA,MAAMiE,oBAAoB1E,4CACxB+E;AAGF,MAAMJ,+BACJ3E,4CAA4CgF;AAE9C,SAASD,wBACPzD,KAAyB,EACzByB,UAAkB;IAElB,MAAMkC,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAElC,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASiC,iCACP1D,KAAyB,EACzByB,UAAkB,EAClB6B,iBAAgC;IAEhC,MAAMK,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAON,CAPM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAElC,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,gEAAgE,CAAC,GAClE,CAAC,iEAAiE,CAAC,GACnE,GAAGoC,4BAA4BP,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASO,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWP,MAAM;QACvB,KAAK;YACH,MAAM,qBAEL,CAFK,IAAIhF,eACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAEuF,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWP,MAAM,GAAG,GAAGS,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAED,UAAU,CAACE,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAED,UAAU,CAACA,WAAWP,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOQ;YACT;IACF;AACF", "ignoreList": [0]}