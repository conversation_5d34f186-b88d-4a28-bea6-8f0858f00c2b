{"version": 3, "sources": ["../../src/lib/find-root.ts"], "sourcesContent": ["import { dirname } from 'path'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\n\nexport function findRootLockFile(cwd: string) {\n  return findUp.sync(\n    [\n      'pnpm-lock.yaml',\n      'package-lock.json',\n      'yarn.lock',\n      'bun.lock',\n      'bun.lockb',\n    ],\n    {\n      cwd,\n    }\n  )\n}\n\nexport function findRootDir(cwd: string): string {\n  const lockFile = findRootLockFile(cwd)\n  if (!lockFile) return cwd\n\n  const lockFiles = [lockFile]\n  while (true) {\n    const lastLockFile = lockFiles[lockFiles.length - 1]\n    const currentDir = dirname(lastLockFile)\n    const parentDir = dirname(currentDir)\n\n    // dirname('/')==='/' so if we happen to reach the FS root (as might happen in a container we need to quit to avoid looping forever\n    if (parentDir === currentDir) break\n\n    const newLockFile = findRootLockFile(parentDir)\n\n    if (!newLockFile) break\n\n    lockFiles.push(newLockFile)\n  }\n\n  // Only warn if not in a build worker to avoid duplicate warnings\n  if (typeof process.send !== 'function' && lockFiles.length > 1) {\n    const additionalLockFiles = lockFiles\n      .slice(0, -1)\n      .map((str) => `\\n   * ${str}`)\n      .join('')\n\n    if (process.env.TURBOPACK) {\n      Log.warnOnce(\n        `Warning: Next.js inferred your workspace root, but it may not be correct.\\n` +\n          ` We detected multiple lockfiles and selected the directory of ${lockFiles[lockFiles.length - 1]} as the root directory.\\n` +\n          ` To silence this warning, set \\`turbopack.root\\` in your Next.js config, or consider ` +\n          `removing one of the lockfiles if it's not needed.\\n` +\n          `   See https://nextjs.org/docs/app/api-reference/config/next-config-js/turbopack#root-directory for more information.\\n` +\n          ` Detected additional lockfiles: ${additionalLockFiles}\\n`\n      )\n    } else {\n      Log.warnOnce(\n        `Warning: Next.js inferred your workspace root, but it may not be correct.\\n` +\n          ` We detected multiple lockfiles and selected the directory of ${lockFiles[lockFiles.length - 1]} as the root directory.\\n` +\n          ` To silence this warning, set \\`outputFileTracingRoot\\` in your Next.js config, or consider ` +\n          `removing one of the lockfiles if it's not needed.\\n` +\n          `   See https://nextjs.org/docs/app/api-reference/config/next-config-js/output#caveats for more information.\\n` +\n          ` Detected additional lockfiles: ${additionalLockFiles}\\n`\n      )\n    }\n  }\n\n  return dirname(lockFiles[lockFiles.length - 1])\n}\n"], "names": ["dirname", "findUp", "Log", "findRootLockFile", "cwd", "sync", "findRootDir", "lockFile", "lockFiles", "lastLockFile", "length", "currentDir", "parentDir", "newLockFile", "push", "process", "send", "additionalLockFiles", "slice", "map", "str", "join", "env", "TURBOPACK", "warnOnce"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAM;AAC9B,OAAOC,YAAY,6BAA4B;AAC/C,YAAYC,SAAS,sBAAqB;AAE1C,OAAO,SAASC,iBAAiBC,GAAW;IAC1C,OAAOH,OAAOI,IAAI,CAChB;QACE;QACA;QACA;QACA;QACA;KACD,EACD;QACED;IACF;AAEJ;AAEA,OAAO,SAASE,YAAYF,GAAW;IACrC,MAAMG,WAAWJ,iBAAiBC;IAClC,IAAI,CAACG,UAAU,OAAOH;IAEtB,MAAMI,YAAY;QAACD;KAAS;IAC5B,MAAO,KAAM;QACX,MAAME,eAAeD,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE;QACpD,MAAMC,aAAaX,QAAQS;QAC3B,MAAMG,YAAYZ,QAAQW;QAE1B,mIAAmI;QACnI,IAAIC,cAAcD,YAAY;QAE9B,MAAME,cAAcV,iBAAiBS;QAErC,IAAI,CAACC,aAAa;QAElBL,UAAUM,IAAI,CAACD;IACjB;IAEA,iEAAiE;IACjE,IAAI,OAAOE,QAAQC,IAAI,KAAK,cAAcR,UAAUE,MAAM,GAAG,GAAG;QAC9D,MAAMO,sBAAsBT,UACzBU,KAAK,CAAC,GAAG,CAAC,GACVC,GAAG,CAAC,CAACC,MAAQ,CAAC,OAAO,EAAEA,KAAK,EAC5BC,IAAI,CAAC;QAER,IAAIN,QAAQO,GAAG,CAACC,SAAS,EAAE;YACzBrB,IAAIsB,QAAQ,CACV,CAAC,2EAA2E,CAAC,GAC3E,CAAC,8DAA8D,EAAEhB,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE,CAAC,yBAAyB,CAAC,GAC3H,CAAC,qFAAqF,CAAC,GACvF,CAAC,mDAAmD,CAAC,GACrD,CAAC,uHAAuH,CAAC,GACzH,CAAC,gCAAgC,EAAEO,oBAAoB,EAAE,CAAC;QAEhE,OAAO;YACLf,IAAIsB,QAAQ,CACV,CAAC,2EAA2E,CAAC,GAC3E,CAAC,8DAA8D,EAAEhB,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE,CAAC,yBAAyB,CAAC,GAC3H,CAAC,4FAA4F,CAAC,GAC9F,CAAC,mDAAmD,CAAC,GACrD,CAAC,6GAA6G,CAAC,GAC/G,CAAC,gCAAgC,EAAEO,oBAAoB,EAAE,CAAC;QAEhE;IACF;IAEA,OAAOjB,QAAQQ,SAAS,CAACA,UAAUE,MAAM,GAAG,EAAE;AAChD", "ignoreList": [0]}