{"version": 3, "sources": ["../../../src/build/templates/app-route.ts"], "sourcesContent": ["import {\n  AppRouteRouteModule,\n  type AppRouteRouteHandlerContext,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { getTracer, type Span, SpanKind } from '../../server/lib/trace/tracer'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from '../../server/web/spec-extension/adapters/next-request'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { sendResponse } from '../../server/send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  toNodeOutgoingHttpHeaders,\n} from '../../server/web/utils'\nimport { getCacheControlHeader } from '../../server/lib/cache-control'\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from '../../lib/constants'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport {\n  CachedRouteKind,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    params,\n    nextConfig,\n    isDraftMode,\n    prerenderManifest,\n    routerServerContext,\n    isOnDemandRevalidate,\n    revalidateOnlyGenerated,\n    resolvedPathname,\n  } = prepareResult\n\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let isIsr = Boolean(\n    prerenderManifest.dynamicRoutes[normalizedSrcPage] ||\n      prerenderManifest.routes[resolvedPathname]\n  )\n\n  if (isIsr && !isDraftMode) {\n    const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname])\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage]\n\n    if (prerenderInfo) {\n      if (prerenderInfo.fallback === false && !isPrerendered) {\n        throw new NoFallbackError()\n      }\n    }\n  }\n\n  let cacheKey: string | null = null\n\n  if (isIsr && !routeModule.isDev && !isDraftMode) {\n    cacheKey = resolvedPathname\n    // ensure /index and / is normalized to one key\n    cacheKey = cacheKey === '/index' ? '/' : cacheKey\n  }\n\n  const supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr\n\n  // This is a revalidation request if the request is for a static\n  // page and it is not being resumed from a postponed render and\n  // it is not a dynamic RSC request then it is a revalidation\n  // request.\n  const isRevalidate = isIsr && !supportsDynamicResponse\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  const context: AppRouteRouteHandlerContext = {\n    params,\n    prerenderManifest,\n    renderOpts: {\n      experimental: {\n        cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n      },\n      supportsDynamicResponse,\n      incrementalCache: getRequestMeta(req, 'incrementalCache'),\n      cacheLifeProfiles: nextConfig.experimental?.cacheLife,\n      isRevalidate,\n      waitUntil: ctx.waitUntil,\n      onClose: (cb) => {\n        res.on('close', cb)\n      },\n      onAfterTaskError: undefined,\n      onInstrumentationRequestError: (error, _request, errorContext) =>\n        routeModule.onRequestError(\n          req,\n          error,\n          errorContext,\n          routerServerContext\n        ),\n    },\n    sharedContext: {\n      buildId,\n    },\n  }\n  const nodeNextReq = new NodeNextRequest(req)\n  const nodeNextRes = new NodeNextResponse(res)\n\n  const nextReq = NextRequestAdapter.fromNodeNextRequest(\n    nodeNextReq,\n    signalFromNodeResponse(res)\n  )\n\n  try {\n    const invokeRouteModule = async (span?: Span) => {\n      return routeModule.handle(nextReq, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const handleResponse = async (currentSpan?: Span) => {\n      const responseGenerator: ResponseGenerator = async ({\n        previousCacheEntry,\n      }) => {\n        try {\n          if (\n            !getRequestMeta(req, 'minimalMode') &&\n            isOnDemandRevalidate &&\n            revalidateOnlyGenerated &&\n            !previousCacheEntry\n          ) {\n            res.statusCode = 404\n            // on-demand revalidate always sets this header\n            res.setHeader('x-nextjs-cache', 'REVALIDATED')\n            res.end('This page could not be found')\n            return null\n          }\n\n          const response = await invokeRouteModule(currentSpan)\n\n          ;(req as any).fetchMetrics = (context.renderOpts as any).fetchMetrics\n          let pendingWaitUntil = context.renderOpts.pendingWaitUntil\n\n          // Attempt using provided waitUntil if available\n          // if it's not we fallback to sendResponse's handling\n          if (pendingWaitUntil) {\n            if (ctx.waitUntil) {\n              ctx.waitUntil(pendingWaitUntil)\n              pendingWaitUntil = undefined\n            }\n          }\n          const cacheTags = context.renderOpts.collectedTags\n\n          // If the request is for a static response, we can cache it so long\n          // as it's not edge.\n          if (isIsr) {\n            const blob = await response.blob()\n\n            // Copy the headers from the response.\n            const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n            if (cacheTags) {\n              headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n            }\n\n            if (!headers['content-type'] && blob.type) {\n              headers['content-type'] = blob.type\n            }\n\n            const revalidate =\n              typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n              context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                ? false\n                : context.renderOpts.collectedRevalidate\n\n            const expire =\n              typeof context.renderOpts.collectedExpire === 'undefined' ||\n              context.renderOpts.collectedExpire >= INFINITE_CACHE\n                ? undefined\n                : context.renderOpts.collectedExpire\n\n            // Create the cache entry for the response.\n            const cacheEntry: ResponseCacheEntry = {\n              value: {\n                kind: CachedRouteKind.APP_ROUTE,\n                status: response.status,\n                body: Buffer.from(await blob.arrayBuffer()),\n                headers,\n              },\n              cacheControl: { revalidate, expire },\n            }\n\n            return cacheEntry\n          } else {\n            // send response without caching if not ISR\n            await sendResponse(\n              nodeNextReq,\n              nodeNextRes,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          }\n        } catch (err) {\n          // if this is a background revalidate we need to report\n          // the request error here as it won't be bubbled\n          if (previousCacheEntry?.isStale) {\n            await routeModule.onRequestError(\n              req,\n              err,\n              {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate,\n                  isOnDemandRevalidate,\n                }),\n              },\n              routerServerContext\n            )\n          }\n          throw err\n        }\n      }\n\n      const cacheEntry = await routeModule.handleResponse({\n        req,\n        nextConfig,\n        cacheKey,\n        routeKind: RouteKind.APP_ROUTE,\n        isFallback: false,\n        prerenderManifest,\n        isRoutePPREnabled: false,\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated,\n        responseGenerator,\n        waitUntil: ctx.waitUntil,\n      })\n\n      // we don't create a cacheEntry for ISR\n      if (!isIsr) {\n        return null\n      }\n\n      if (cacheEntry?.value?.kind !== CachedRouteKind.APP_ROUTE) {\n        throw new Error(\n          `Invariant: app-route received invalid cache entry ${cacheEntry?.value?.kind}`\n        )\n      }\n\n      if (!getRequestMeta(req, 'minimalMode')) {\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n\n      // Draft mode should never be cached\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers)\n\n      if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        cacheEntry.cacheControl &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      await sendResponse(\n        nodeNextReq,\n        nodeNextRes,\n        new Response(cacheEntry.value.body, {\n          headers,\n          status: cacheEntry.value.status || 200,\n        })\n      )\n      return null\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan && !(err instanceof NoFallbackError)) {\n      await routeModule.onRequestError(req, err, {\n        routerKind: 'App Router',\n        routePath: normalizedSrcPage,\n        routeType: 'route',\n        revalidateReason: getRevalidateReason({\n          isRevalidate,\n          isOnDemandRevalidate,\n        }),\n      })\n    }\n\n    // rethrow so that we can handle serving error page\n\n    // If this is during static generation, throw the error again.\n    if (isIsr) throw err\n\n    // Otherwise, send a 500 response.\n    await sendResponse(\n      nodeNextReq,\n      nodeNextRes,\n      new Response(null, { status: 500 })\n    )\n    return null\n  }\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "getRequestMeta", "getTracer", "SpanKind", "normalizeAppPath", "NodeNextRequest", "NodeNextResponse", "NextRequestAdapter", "signalFromNodeResponse", "BaseServerSpan", "getRevalidateReason", "sendResponse", "fromNodeOutgoingHttpHeaders", "toNodeOutgoingHttpHeaders", "getCacheControlHeader", "INFINITE_CACHE", "NEXT_CACHE_TAGS_HEADER", "NoFallbackError", "CachedRouteKind", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "relativeProjectDir", "__NEXT_RELATIVE_PROJECT_DIR", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks", "handler", "req", "res", "ctx", "nextConfig", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "params", "isDraftMode", "prerenderManifest", "routerServerContext", "isOnDemandRevalidate", "revalidateOnlyGenerated", "resolvedPathname", "normalizedSrcPage", "isIsr", "Boolean", "dynamicRoutes", "routes", "isP<PERSON>endered", "prerenderInfo", "fallback", "cache<PERSON>ey", "isDev", "supportsDynamicResponse", "isRevalidate", "method", "tracer", "activeSpan", "getActiveScopeSpan", "context", "renderOpts", "experimental", "cacheComponents", "authInterrupts", "incrementalCache", "cacheLifeProfiles", "cacheLife", "onClose", "cb", "on", "onAfterTaskError", "undefined", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "sharedContext", "nodeNextReq", "nodeNextRes", "nextReq", "fromNodeNextRequest", "invokeRouteModule", "span", "handle", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "handleResponse", "currentSpan", "cacheEntry", "responseGenerator", "previousCacheEntry", "<PERSON><PERSON><PERSON><PERSON>", "response", "fetchMetrics", "pendingWaitUntil", "cacheTags", "collectedTags", "blob", "headers", "type", "revalidate", "collectedRevalidate", "expire", "collectedExpire", "value", "status", "body", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "err", "isStale", "routerKind", "routePath", "routeType", "revalidateReason", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "Error", "isMiss", "delete", "<PERSON><PERSON><PERSON><PERSON>", "set", "Response", "withPropagatedContext", "trace", "spanName", "SERVER", "attributes"], "mappings": "AAAA,SACEA,mBAAmB,QAGd,uDAAsD;AAC7D,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,cAAcC,WAAW,QAAQ,+BAA8B;AAExE,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,SAAS,EAAaC,QAAQ,QAAQ,gCAA+B;AAC9E,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,8BAA6B;AAC/E,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,wDAAuD;AAC9D,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SACEC,2BAA2B,EAC3BC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,SAASC,cAAc,EAAEC,sBAAsB,QAAQ,sBAAqB;AAC5E,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SACEC,eAAe,QAGV,8BAA6B;AAEpC,YAAYC,cAAc,eAAc;AAOxC,2EAA2E;AAC3E,UAAU;AACV,0BAA0B;AAE1B,MAAMC,cAAc,IAAIvB,oBAAoB;IAC1CwB,YAAY;QACVC,MAAMxB,UAAUyB,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,oBAAoBH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IAC/DC,kBAAkB;IAClBC;IACAhB;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EAAEiB,gBAAgB,EAAEC,oBAAoB,EAAEC,WAAW,EAAE,GAAGlB;AAEhE,SAASrB;IACP,OAAOC,YAAY;QACjBoC;QACAC;IACF;AACF;AAEA,SACEjB,WAAW,EACXgB,gBAAgB,EAChBC,oBAAoB,EACpBC,WAAW,EACXvC,UAAU,KACX;AAED,OAAO,eAAewC,QACpBC,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;QA6FsBC;IA3FvB,IAAIC,UAAU;IAEd,wDAAwD;IACxD,mDAAmD;IACnD,6DAA6D;IAC7D,IAAIf,QAAQC,GAAG,CAACe,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;IAC/C,OAAO,IAAIF,YAAY,UAAU;QAC/B,0CAA0C;QAC1CA,UAAU;IACZ;IACA,MAAMG,qBAAqBlB,QAAQC,GAAG,CACnCkB,4BAA4B;IAE/B,MAAMC,gBAAgB,MAAM7B,YAAY8B,OAAO,CAACV,KAAKC,KAAK;QACxDG;QACAG;IACF;IAEA,IAAI,CAACE,eAAe;QAClBR,IAAIU,UAAU,GAAG;QACjBV,IAAIW,GAAG,CAAC;QACRV,IAAIW,SAAS,oBAAbX,IAAIW,SAAS,MAAbX,KAAgBY,QAAQC,OAAO;QAC/B,OAAO;IACT;IAEA,MAAM,EACJC,OAAO,EACPC,MAAM,EACNd,UAAU,EACVe,WAAW,EACXC,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,uBAAuB,EACvBC,gBAAgB,EACjB,GAAGd;IAEJ,MAAMe,oBAAoB5D,iBAAiBwC;IAE3C,IAAIqB,QAAQC,QACVP,kBAAkBQ,aAAa,CAACH,kBAAkB,IAChDL,kBAAkBS,MAAM,CAACL,iBAAiB;IAG9C,IAAIE,SAAS,CAACP,aAAa;QACzB,MAAMW,gBAAgBH,QAAQP,kBAAkBS,MAAM,CAACL,iBAAiB;QACxE,MAAMO,gBAAgBX,kBAAkBQ,aAAa,CAACH,kBAAkB;QAExE,IAAIM,eAAe;YACjB,IAAIA,cAAcC,QAAQ,KAAK,SAAS,CAACF,eAAe;gBACtD,MAAM,IAAIpD;YACZ;QACF;IACF;IAEA,IAAIuD,WAA0B;IAE9B,IAAIP,SAAS,CAAC7C,YAAYqD,KAAK,IAAI,CAACf,aAAa;QAC/Cc,WAAWT;QACX,+CAA+C;QAC/CS,WAAWA,aAAa,WAAW,MAAMA;IAC3C;IAEA,MAAME,0BACJ,0DAA0D;IAC1DtD,YAAYqD,KAAK,KAAK,QACtB,qEAAqE;IACrE,gBAAgB;IAChB,CAACR;IAEH,gEAAgE;IAChE,+DAA+D;IAC/D,4DAA4D;IAC5D,WAAW;IACX,MAAMU,eAAeV,SAAS,CAACS;IAE/B,MAAME,SAASpC,IAAIoC,MAAM,IAAI;IAC7B,MAAMC,SAAS3E;IACf,MAAM4E,aAAaD,OAAOE,kBAAkB;IAE5C,MAAMC,UAAuC;QAC3CvB;QACAE;QACAsB,YAAY;YACVC,cAAc;gBACZC,iBAAiBjB,QAAQvB,WAAWuC,YAAY,CAACC,eAAe;gBAChEC,gBAAgBlB,QAAQvB,WAAWuC,YAAY,CAACE,cAAc;YAChE;YACAV;YACAW,kBAAkBpF,eAAeuC,KAAK;YACtC8C,iBAAiB,GAAE3C,2BAAAA,WAAWuC,YAAY,qBAAvBvC,yBAAyB4C,SAAS;YACrDZ;YACAtB,WAAWX,IAAIW,SAAS;YACxBmC,SAAS,CAACC;gBACRhD,IAAIiD,EAAE,CAAC,SAASD;YAClB;YACAE,kBAAkBC;YAClBC,+BAA+B,CAACC,OAAOC,UAAUC,eAC/C5E,YAAY6E,cAAc,CACxBzD,KACAsD,OACAE,cACApC;QAEN;QACAsC,eAAe;YACb1C;QACF;IACF;IACA,MAAM2C,cAAc,IAAI9F,gBAAgBmC;IACxC,MAAM4D,cAAc,IAAI9F,iBAAiBmC;IAEzC,MAAM4D,UAAU9F,mBAAmB+F,mBAAmB,CACpDH,aACA3F,uBAAuBiC;IAGzB,IAAI;QACF,MAAM8D,oBAAoB,OAAOC;YAC/B,OAAOpF,YAAYqF,MAAM,CAACJ,SAASrB,SAAS0B,OAAO,CAAC;gBAClD,IAAI,CAACF,MAAM;gBAEXA,KAAKG,aAAa,CAAC;oBACjB,oBAAoBlE,IAAIU,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAMyD,qBAAqB/B,OAAOgC,qBAAqB;gBACvD,iEAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBrG,eAAesG,aAAa,EAC5B;oBACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEL,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oBAE1E;gBACF;gBAEA,MAAMI,QAAQN,mBAAmBE,GAAG,CAAC;gBACrC,IAAII,OAAO;oBACT,MAAMC,OAAO,GAAGvC,OAAO,CAAC,EAAEsC,OAAO;oBAEjCV,KAAKG,aAAa,CAAC;wBACjB,cAAcO;wBACd,cAAcA;wBACd,kBAAkBC;oBACpB;oBACAX,KAAKY,UAAU,CAACD;gBAClB,OAAO;oBACLX,KAAKY,UAAU,CAAC,GAAGxC,OAAO,CAAC,EAAEpC,IAAI6E,GAAG,EAAE;gBACxC;YACF;QACF;QAEA,MAAMC,iBAAiB,OAAOC;gBA6HxBC;YA5HJ,MAAMC,oBAAuC,OAAO,EAClDC,kBAAkB,EACnB;gBACC,IAAI;oBACF,IACE,CAACzH,eAAeuC,KAAK,kBACrBqB,wBACAC,2BACA,CAAC4D,oBACD;wBACAjF,IAAIU,UAAU,GAAG;wBACjB,+CAA+C;wBAC/CV,IAAIkF,SAAS,CAAC,kBAAkB;wBAChClF,IAAIW,GAAG,CAAC;wBACR,OAAO;oBACT;oBAEA,MAAMwE,WAAW,MAAMrB,kBAAkBgB;oBAEvC/E,IAAYqF,YAAY,GAAG,AAAC7C,QAAQC,UAAU,CAAS4C,YAAY;oBACrE,IAAIC,mBAAmB9C,QAAQC,UAAU,CAAC6C,gBAAgB;oBAE1D,gDAAgD;oBAChD,qDAAqD;oBACrD,IAAIA,kBAAkB;wBACpB,IAAIpF,IAAIW,SAAS,EAAE;4BACjBX,IAAIW,SAAS,CAACyE;4BACdA,mBAAmBlC;wBACrB;oBACF;oBACA,MAAMmC,YAAY/C,QAAQC,UAAU,CAAC+C,aAAa;oBAElD,mEAAmE;oBACnE,oBAAoB;oBACpB,IAAI/D,OAAO;wBACT,MAAMgE,OAAO,MAAML,SAASK,IAAI;wBAEhC,sCAAsC;wBACtC,MAAMC,UAAUrH,0BAA0B+G,SAASM,OAAO;wBAE1D,IAAIH,WAAW;4BACbG,OAAO,CAAClH,uBAAuB,GAAG+G;wBACpC;wBAEA,IAAI,CAACG,OAAO,CAAC,eAAe,IAAID,KAAKE,IAAI,EAAE;4BACzCD,OAAO,CAAC,eAAe,GAAGD,KAAKE,IAAI;wBACrC;wBAEA,MAAMC,aACJ,OAAOpD,QAAQC,UAAU,CAACoD,mBAAmB,KAAK,eAClDrD,QAAQC,UAAU,CAACoD,mBAAmB,IAAItH,iBACtC,QACAiE,QAAQC,UAAU,CAACoD,mBAAmB;wBAE5C,MAAMC,SACJ,OAAOtD,QAAQC,UAAU,CAACsD,eAAe,KAAK,eAC9CvD,QAAQC,UAAU,CAACsD,eAAe,IAAIxH,iBAClC6E,YACAZ,QAAQC,UAAU,CAACsD,eAAe;wBAExC,2CAA2C;wBAC3C,MAAMf,aAAiC;4BACrCgB,OAAO;gCACLlH,MAAMJ,gBAAgBK,SAAS;gCAC/BkH,QAAQb,SAASa,MAAM;gCACvBC,MAAMC,OAAOC,IAAI,CAAC,MAAMX,KAAKY,WAAW;gCACxCX;4BACF;4BACAY,cAAc;gCAAEV;gCAAYE;4BAAO;wBACrC;wBAEA,OAAOd;oBACT,OAAO;wBACL,2CAA2C;wBAC3C,MAAM7G,aACJwF,aACAC,aACAwB,UACA5C,QAAQC,UAAU,CAAC6C,gBAAgB;wBAErC,OAAO;oBACT;gBACF,EAAE,OAAOiB,KAAK;oBACZ,uDAAuD;oBACvD,gDAAgD;oBAChD,IAAIrB,sCAAAA,mBAAoBsB,OAAO,EAAE;wBAC/B,MAAM5H,YAAY6E,cAAc,CAC9BzD,KACAuG,KACA;4BACEE,YAAY;4BACZC,WAAWtG;4BACXuG,WAAW;4BACXC,kBAAkB1I,oBAAoB;gCACpCiE;gCACAd;4BACF;wBACF,GACAD;oBAEJ;oBACA,MAAMmF;gBACR;YACF;YAEA,MAAMvB,aAAa,MAAMpG,YAAYkG,cAAc,CAAC;gBAClD9E;gBACAG;gBACA6B;gBACA6E,WAAWvJ,UAAUyB,SAAS;gBAC9B+H,YAAY;gBACZ3F;gBACA4F,mBAAmB;gBACnB1F;gBACAC;gBACA2D;gBACApE,WAAWX,IAAIW,SAAS;YAC1B;YAEA,uCAAuC;YACvC,IAAI,CAACY,OAAO;gBACV,OAAO;YACT;YAEA,IAAIuD,CAAAA,+BAAAA,oBAAAA,WAAYgB,KAAK,qBAAjBhB,kBAAmBlG,IAAI,MAAKJ,gBAAgBK,SAAS,EAAE;oBAEFiG;gBADvD,MAAM,qBAEL,CAFK,IAAIgC,MACR,CAAC,kDAAkD,EAAEhC,+BAAAA,qBAAAA,WAAYgB,KAAK,qBAAjBhB,mBAAmBlG,IAAI,EAAE,GAD1E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACrB,eAAeuC,KAAK,gBAAgB;gBACvCC,IAAIkF,SAAS,CACX,kBACA9D,uBACI,gBACA2D,WAAWiC,MAAM,GACf,SACAjC,WAAWwB,OAAO,GAChB,UACA;YAEZ;YAEA,oCAAoC;YACpC,IAAItF,aAAa;gBACfjB,IAAIkF,SAAS,CACX,iBACA;YAEJ;YAEA,MAAMO,UAAUtH,4BAA4B4G,WAAWgB,KAAK,CAACN,OAAO;YAEpE,IAAI,CAAEjI,CAAAA,eAAeuC,KAAK,kBAAkByB,KAAI,GAAI;gBAClDiE,QAAQwB,MAAM,CAAC1I;YACjB;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACEwG,WAAWsB,YAAY,IACvB,CAACrG,IAAIkH,SAAS,CAAC,oBACf,CAACzB,QAAQpB,GAAG,CAAC,kBACb;gBACAoB,QAAQ0B,GAAG,CACT,iBACA9I,sBAAsB0G,WAAWsB,YAAY;YAEjD;YAEA,MAAMnI,aACJwF,aACAC,aACA,IAAIyD,SAASrC,WAAWgB,KAAK,CAACE,IAAI,EAAE;gBAClCR;gBACAO,QAAQjB,WAAWgB,KAAK,CAACC,MAAM,IAAI;YACrC;YAEF,OAAO;QACT;QAEA,oDAAoD;QACpD,yDAAyD;QACzD,IAAI3D,YAAY;YACd,MAAMwC,eAAexC;QACvB,OAAO;YACL,MAAMD,OAAOiF,qBAAqB,CAACtH,IAAI0F,OAAO,EAAE,IAC9CrD,OAAOkF,KAAK,CACVtJ,eAAesG,aAAa,EAC5B;oBACEiD,UAAU,GAAGpF,OAAO,CAAC,EAAEpC,IAAI6E,GAAG,EAAE;oBAChC/F,MAAMnB,SAAS8J,MAAM;oBACrBC,YAAY;wBACV,eAAetF;wBACf,eAAepC,IAAI6E,GAAG;oBACxB;gBACF,GACAC;QAGN;IACF,EAAE,OAAOyB,KAAK;QACZ,kDAAkD;QAClD,IAAI,CAACjE,cAAc,CAAEiE,CAAAA,eAAe9H,eAAc,GAAI;YACpD,MAAMG,YAAY6E,cAAc,CAACzD,KAAKuG,KAAK;gBACzCE,YAAY;gBACZC,WAAWlF;gBACXmF,WAAW;gBACXC,kBAAkB1I,oBAAoB;oBACpCiE;oBACAd;gBACF;YACF;QACF;QAEA,mDAAmD;QAEnD,8DAA8D;QAC9D,IAAII,OAAO,MAAM8E;QAEjB,kCAAkC;QAClC,MAAMpI,aACJwF,aACAC,aACA,IAAIyD,SAAS,MAAM;YAAEpB,QAAQ;QAAI;QAEnC,OAAO;IACT;AACF", "ignoreList": [0]}