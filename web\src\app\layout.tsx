// app/layout.tsx
import * as React from 'react';
import Script from 'next/script';
import { ColorModeScript } from '@chakra-ui/react';
// N<PERSON>u bạn chưa có file theme, hãy dùng theme mặc định của Chakra UI:
import { extendTheme } from '@chakra-ui/react';
const theme = extendTheme({ config: { initialColorMode: 'light', useSystemColorMode: false } });
import Providers from './providers';

export const metadata = {
    title: 'Chatbot',
    description: 'RAG + FastAPI demo',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="vi">
            <head>
                {/* Script xóa thẻ extension trước khi hydrate */}
                <Script
                    id="pre-hydration-guard"
                    strategy="beforeInteractive"
                    dangerouslySetInnerHTML={{
                        __html: `
(function(){
  const knownIds = ['extwaiokist'];
  knownIds.forEach(id => {
    const el = document.getElementById(id);
    if (el && el.parentNode) el.parentNode.removeChild(el);
  });
  const suspicious = Array.from(document.querySelectorAll('[id^="ext"]'));
  suspicious.forEach((el) => {
    const attrs = ['v','q','c','i','u','s','sg','d','w','a','m','vn'];
    const looksWeird = attrs.some(a => el.hasAttribute(a));
    if (looksWeird && el.parentNode) el.parentNode.removeChild(el);
  });
})();
            `,
                    }}
                />
            </head>
            <body>
                {/* ColorModeScript giúp đồng bộ dark/light mode */}
                <ColorModeScript initialColorMode={theme.config.initialColorMode} />
                <Providers>{children}</Providers>
            </body>
        </html>
    );
}
