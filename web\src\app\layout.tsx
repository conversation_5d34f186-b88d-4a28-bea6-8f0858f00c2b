import type { Metadata } from 'next';
import Script from 'next/script';
import Providers from './providers';

export const metadata: Metadata = {
    title: 'AI Chatbot - RAG System',
    description: '<PERSON>ệ thống chatbot thông minh sử dụng RAG (Retrieval-Augmented Generation)',
    keywords: ['chatbot', 'AI', 'RAG', 'FastAPI', 'Next.js'],
    authors: [{ name: 'AI Train Team' }],
    viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="vi" suppressHydrationWarning>
            <head>
                {/* Script xóa các thẻ do browser extension chèn vào trước khi React hydrate */}
                <Script
                    id="extension-cleanup"
                    strategy="beforeInteractive"
                    dangerouslySetInnerHTML={{
                        __html: `
              (function() {
                try {
                  // Danh sách các ID thường gặp từ extensions
                  const extensionIds = [
                    'extwaiokist', 'ext-gen', 'ext-comp', 'extension-root',
                    'adblock-root', 'ublock-root', 'grammarly-root'
                  ];

                  // Xóa các element có ID từ extensions
                  extensionIds.forEach(id => {
                    const el = document.getElementById(id);
                    if (el && el.parentNode) {
                      el.parentNode.removeChild(el);
                    }
                  });

                  // Xóa các element có pattern của extensions
                  const suspiciousElements = document.querySelectorAll('[id^="ext"], [class*="extension"], [data-extension]');
                  suspiciousElements.forEach(el => {
                    // Kiểm tra các thuộc tính đặc trưng của extensions
                    const extensionAttrs = ['v', 'q', 'c', 'i', 'u', 's', 'sg', 'd', 'w', 'a', 'm', 'vn'];
                    const hasExtensionAttrs = extensionAttrs.some(attr => el.hasAttribute(attr));

                    if (hasExtensionAttrs && el.parentNode) {
                      el.parentNode.removeChild(el);
                    }
                  });
                } catch (e) {
                  // Bỏ qua lỗi để không ảnh hưởng đến ứng dụng
                  console.warn('Extension cleanup failed:', e);
                }
              })();
            `,
                    }}
                />
            </head>
            <body suppressHydrationWarning>
                <Providers>{children}</Providers>
            </body>
        </html>
    );
}
