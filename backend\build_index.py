import os, glob, uuid, pickle, hashlib, warnings, json, logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Generator
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

# --- Splitter: hỗ trợ cả 2 kiểu import tùy phiên bản langchain ---
try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
except Exception:
    from langchain.text_splitter import RecursiveCharacterTextSplitter

from langchain_community.document_loaders import PyPDFLoader, TextLoader

# Core libraries
import pandas as pd
from docx import Document
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

# Optional libraries với graceful fallback
try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False

try:
    import pptx
    HAS_PPTX = True
except ImportError:
    HAS_PPTX = False

try:
    import openpyxl
    import xlrd
    HAS_EXCEL_ENGINES = True
except ImportError:
    HAS_EXCEL_ENGINES = False

# OCR & hình ảnh
try:
    from PIL import Image
    import pytesseract
    from pdf2image import convert_from_path
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

try:
    from charset_normalizer import from_path
    HAS_CHARSET_NORMALIZER = True
except ImportError:
    HAS_CHARSET_NORMALIZER = False

# ================= Enhanced Configuration =================
@dataclass
class Config:
    embed_model: str = os.getenv("EMBED_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
    chunk_size: int = int(os.getenv("CHUNK_SIZE", "500"))
    chunk_overlap: int = int(os.getenv("CHUNK_OVERLAP", "100"))
    ocr_lang: str = os.getenv("OCR_LANG", "vie+eng")
    min_chars: int = int(os.getenv("MIN_CHARS", "30"))
    data_glob: str = os.getenv("DATA_GLOB", "data/**/*")
    show_processing: bool = os.getenv("SHOW_PROCESSING", "1") not in {"0", "false", "False"}
    max_workers: int = int(os.getenv("MAX_WORKERS", "4"))
    batch_size: int = int(os.getenv("BATCH_SIZE", "32"))
    max_file_size_mb: int = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    use_parallel_processing: bool = os.getenv("USE_PARALLEL", "1") not in {"0", "false", "False"}

config = Config()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_index.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Setup Tesseract for Windows
if os.name == "nt" and HAS_OCR:
    tesseract_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe"
    ]
    for path in tesseract_paths:
        if os.path.exists(path):
            pytesseract.pytesseract.tesseract_cmd = path
            break

# ================= Enhanced Utilities =================
def dedupe_texts(texts: List[str]) -> List[str]:
    """Loại trùng theo hash nội dung với tối ưu performance."""
    if not texts:
        return []
    
    seen, unique = set(), []
    for t in texts:
        if not t or not t.strip():
            continue
        # Sử dụng hash nhanh hơn cho dedupe
        key = hash(t.strip())
        if key not in seen:
            seen.add(key)
            unique.append(t)
    return unique

def safe_read_text(path: str) -> str:
    """Đọc TXT/MD với enhanced encoding detection."""
    try:
        if HAS_CHARSET_NORMALIZER:
            encoding = from_path(path).best().encoding or "utf-8"
        else:
            # Fallback encoding detection
            encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
            for enc in encodings:
                try:
                    with open(path, 'r', encoding=enc) as f:
                        content = f.read()
                    return content
                except UnicodeDecodeError:
                    continue
            encoding = 'utf-8'
        
        return TextLoader(path, encoding=encoding).load()[0].page_content
    except Exception as e:
        logger.warning(f"Text read failed for {path}: {e}")
        return ""

def read_docx_enhanced(path: str) -> str:
    """Enhanced DOCX reading với better error handling."""
    try:
        doc = Document(path)
        parts = []
        
        # Read paragraphs
        for p in doc.paragraphs:
            if p.text and p.text.strip():
                parts.append(p.text.strip())
        
        # Read tables với improved formatting
        for tbl in doc.tables:
            table_data = []
            for row in tbl.rows:
                cells = [c.text.strip() for c in row.cells if c.text.strip()]
                if cells:
                    table_data.append(" | ".join(cells))
            if table_data:
                parts.append("\n".join(table_data))
        
        # Read headers/footers if available
        try:
            for section in doc.sections:
                if section.header:
                    for p in section.header.paragraphs:
                        if p.text.strip():
                            parts.append(f"[Header] {p.text.strip()}")
        except Exception:
            pass  # Headers optional
            
        return "\n\n".join(parts)
        
    except Exception as e:
        logger.warning(f"DOCX load failed for {path}: {e}")
        return ""

def read_excel_robust(path: str) -> str:
    """Robust Excel reading với multiple engines và strategies."""
    if not HAS_EXCEL_ENGINES:
        logger.warning("Excel engines not available, skipping Excel files")
        return ""
    
    # Check file size
    file_size = Path(path).stat().st_size / (1024 * 1024)  # MB
    if file_size > config.max_file_size_mb:
        logger.warning(f"Excel file {path} too large ({file_size:.1f}MB), skipping")
        return ""
    
    engines = ['openpyxl', 'xlrd']
    
    for engine in engines:
        try:
            if file_size > 50:  # Large files
                # Read in chunks
                df_chunks = []
                for chunk in pd.read_excel(path, engine=engine, chunksize=1000):
                    df_chunks.append(chunk)
                df = pd.concat(df_chunks, ignore_index=True)
            else:
                df = pd.read_excel(path, engine=engine)
            
            # Enhanced formatting
            text_parts = []
            
            # Add column headers
            headers = " | ".join([str(col) for col in df.columns])
            text_parts.append(f"[Headers] {headers}")
            
            # Add data with better formatting
            for idx, row in df.iterrows():
                row_data = []
                for col, val in row.items():
                    if pd.notna(val):
                        row_data.append(f"{col}: {str(val).strip()}")
                if row_data:
                    text_parts.append(" | ".join(row_data))
                    
                # Limit rows for very large files
                if idx > 10000:  # Max 10k rows
                    text_parts.append("[... truncated due to size limit ...]")
                    break
            
            return "\n".join(text_parts)
            
        except Exception as e:
            logger.warning(f"Excel engine {engine} failed for {path}: {e}")
            continue
    
    logger.error(f"All Excel engines failed for {path}")
    return ""

def ocr_image_enhanced(image_path: str) -> str:
    """Enhanced OCR với preprocessing."""
    if not HAS_OCR:
        logger.warning("OCR libraries not available")
        return ""
        
    try:
        img = Image.open(image_path)
        
        # Preprocessing để improve OCR accuracy
        img = img.convert('RGB')
        
        # Resize nếu quá nhỏ hoặc quá lớn
        width, height = img.size
        if width < 300 or height < 300:
            scale = max(300/width, 300/height)
            new_size = (int(width * scale), int(height * scale))
            img = img.resize(new_size, Image.Resampling.LANCZOS)
        elif width > 3000 or height > 3000:
            scale = min(3000/width, 3000/height)
            new_size = (int(width * scale), int(height * scale))
            img = img.resize(new_size, Image.Resampling.LANCZOS)
        
        # OCR với config tối ưu
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠ-ỹ .,!?;:'
        text = pytesseract.image_to_string(img, lang=config.ocr_lang, config=custom_config)
        
        return text.strip()
        
    except Exception as e:
        logger.warning(f"OCR failed for image {image_path}: {e}")
        return ""

def ocr_pdf_enhanced(pdf_path: str, dpi: int = 300) -> str:
    """Enhanced PDF OCR với memory management."""
    if not HAS_OCR:
        return ""
        
    try:
        # Check file size
        file_size = Path(pdf_path).stat().st_size / (1024 * 1024)
        if file_size > config.max_file_size_mb:
            logger.warning(f"PDF file too large ({file_size:.1f}MB), reducing DPI")
            dpi = 150
        
        logger.info(f"Starting OCR for PDF: {pdf_path}")
        pages = convert_from_path(pdf_path, dpi=dpi)
        
        texts = []
        for i, img in enumerate(pages):
            try:
                # Save memory by processing one page at a time
                custom_config = r'--oem 3 --psm 6'
                t = pytesseract.image_to_string(img, lang=config.ocr_lang, config=custom_config)
                
                if t and len(t.strip()) >= config.min_chars:
                    texts.append(t.strip())
                    
                # Limit pages for very large PDFs
                if i > 100:  # Max 100 pages
                    logger.warning(f"PDF {pdf_path} too many pages, truncating at page {i}")
                    break
                    
            except Exception as e:
                logger.warning(f"OCR failed at page {i} for {pdf_path}: {e}")
                continue
        
        return "\n\n--- PAGE BREAK ---\n\n".join(texts)
        
    except Exception as e:
        logger.error(f"pdf2image render failed for {pdf_path}: {e}")
        return ""

def load_jsonl_enhanced(path: str) -> List[Dict]:
    """Enhanced JSONL loading."""
    docs = []
    try:
        with open(path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    data = json.loads(line)
                    if isinstance(data, dict):
                        # Support different JSONL formats
                        text = data.get("text") or data.get("content") or data.get("body")
                        if text and len(str(text).strip()) >= config.min_chars:
                            docs.append({
                                "text": str(text).strip(),
                                "meta": {
                                    "source": path,
                                    "line_number": line_num,
                                    "original_data": data
                                }
                            })
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON at line {line_num} in {path}: {e}")
                    continue
    except Exception as e:
        logger.error(f"JSONL load failed for {path}: {e}")
    
    return docs

# ================= Enhanced Document Loading =================
def process_single_file(file_info: Tuple[str, str]) -> Tuple[List[Dict], str, Optional[str]]:
    """Process một file đơn lẻ - để dùng với parallel processing."""
    path, suffix = file_info
    docs = []
    error = None
    
    try:
        if suffix == ".pdf":
            texts = []
            # Try text extraction first
            try:
                pages = PyPDFLoader(path).load()
                for page in pages:
                    if page.page_content and len(page.page_content.strip()) >= config.min_chars:
                        texts.append(page.page_content.strip())
            except Exception as e:
                logger.warning(f"PDF text extract failed for {path}: {e}")
            
            # OCR fallback
            if not texts and HAS_OCR:
                ocr_txt = ocr_pdf_enhanced(path)
                if ocr_txt and len(ocr_txt.strip()) >= config.min_chars:
                    texts = [ocr_txt]
            
            for t in texts:
                docs.append({"text": t, "meta": {"source": path, "type": "pdf"}})
                
        elif suffix in {".txt", ".md"}:
            t = safe_read_text(path)
            if t and len(t.strip()) >= config.min_chars:
                docs.append({"text": t, "meta": {"source": path, "type": "text"}})
                
        elif suffix == ".docx":
            t = read_docx_enhanced(path)
            if t and len(t.strip()) >= config.min_chars:
                docs.append({"text": t, "meta": {"source": path, "type": "docx"}})
                
        elif suffix == ".xlsx":
            t = read_excel_robust(path)
            if t and len(t.strip()) >= config.min_chars:
                docs.append({"text": t, "meta": {"source": path, "type": "excel"}})
                
        elif suffix == ".csv":
            df = pd.read_csv(path, encoding='utf-8', dtype=str, na_filter=False)
            # Better CSV formatting
            formatted_rows = []
            headers = " | ".join(df.columns)
            formatted_rows.append(f"[Headers] {headers}")
            
            for _, row in df.iterrows():
                row_str = " | ".join([f"{col}: {val}" for col, val in row.items() if val])
                if row_str:
                    formatted_rows.append(row_str)
            
            text = "\n".join(formatted_rows)
            if text and len(text.strip()) >= config.min_chars:
                docs.append({"text": text, "meta": {"source": path, "type": "csv"}})
                
        elif suffix == ".json":
            with open(path, "r", encoding="utf-8") as f:
                data = json.load(f)
            text = json.dumps(data, ensure_ascii=False, indent=2)
            if text and len(text.strip()) >= config.min_chars:
                docs.append({"text": text, "meta": {"source": path, "type": "json"}})
                
        elif suffix == ".jsonl":
            jsonl_docs = load_jsonl_enhanced(path)
            docs.extend(jsonl_docs)
            
        elif suffix in {".png", ".jpg", ".jpeg", ".webp"} and HAS_OCR:
            t = ocr_image_enhanced(path)
            if t and len(t.strip()) >= config.min_chars:
                docs.append({"text": t, "meta": {"source": path, "type": "image-ocr"}})
                
        elif suffix == ".pptx" and HAS_PPTX:
            try:
                pres = pptx.Presentation(path)
                slides_text = []
                for i, slide in enumerate(pres.slides, 1):
                    slide_content = []
                    for shape in slide.shapes:
                        if hasattr(shape, "text") and shape.text.strip():
                            slide_content.append(shape.text.strip())
                    if slide_content:
                        slides_text.append(f"[Slide {i}]\n" + "\n".join(slide_content))
                
                if slides_text:
                    text = "\n\n".join(slides_text)
                    if len(text.strip()) >= config.min_chars:
                        docs.append({"text": text, "meta": {"source": path, "type": "pptx"}})
            except Exception as e:
                error = f"pptx error: {e}"
                
        elif suffix in {".html", ".htm"} and HAS_BS4:
            raw = safe_read_text(path)
            soup = BeautifulSoup(raw, "html.parser")
            for tag in soup(["script", "style", "noscript"]):
                tag.decompose()
            text = soup.get_text(separator="\n")
            clean_text = "\n".join(line.strip() for line in text.splitlines() if line.strip())
            if clean_text and len(clean_text.strip()) >= config.min_chars:
                docs.append({"text": clean_text, "meta": {"source": path, "type": "html"}})
        
    except Exception as e:
        error = f"exception: {e}"
    
    return docs, path, error

def load_docs_enhanced() -> Tuple[List[Dict], Dict[str, List]]:
    """Enhanced document loading với parallel processing và detailed reporting."""
    all_files = []
    
    # Collect all files
    for path in glob.glob(config.data_glob, recursive=True):
        p = Path(path)
        if p.is_file():
            all_files.append((str(p), p.suffix.lower()))
    
    logger.info(f"Found {len(all_files)} files to process")
    
    docs = []
    results = {"successes": [], "skipped": [], "failures": []}
    
    if config.use_parallel_processing and len(all_files) > 10:
        # Parallel processing for large datasets
        logger.info(f"Using parallel processing with {config.max_workers} workers")
        
        with ThreadPoolExecutor(max_workers=config.max_workers) as executor:
            future_to_file = {
                executor.submit(process_single_file, file_info): file_info 
                for file_info in all_files
            }
            
            for future in as_completed(future_to_file):
                file_info = future_to_file[future]
                path = file_info[0]
                
                try:
                    file_docs, processed_path, error = future.result()
                    
                    if error:
                        results["failures"].append((processed_path, error))
                    elif file_docs:
                        docs.extend(file_docs)
                        results["successes"].append(processed_path)
                    else:
                        results["skipped"].append((processed_path, "no extractable content"))
                        
                except Exception as e:
                    results["failures"].append((path, f"processing error: {e}"))
    else:
        # Sequential processing
        logger.info("Using sequential processing")
        
        for file_info in all_files:
            path = file_info[0]
            if config.show_processing:
                print(f"→ Processing: {path}")
                
            file_docs, processed_path, error = process_single_file(file_info)
            
            if error:
                results["failures"].append((processed_path, error))
            elif file_docs:
                docs.extend(file_docs)
                results["successes"].append(processed_path)
            else:
                results["skipped"].append((processed_path, "no extractable content"))
    
    return docs, results

# ================= Enhanced Main Function =================
def main():
    """Enhanced main function với comprehensive reporting."""
    start_time = time.time()
    logger.info("Starting document indexing process...")
    logger.info(f"Configuration: {config}")
    
    # Load documents
    raw_docs, results = load_docs_enhanced()
    
    if not raw_docs:
        logger.error("No documents could be loaded. Check your data directory.")
        return
    
    logger.info(f"Successfully loaded {len(raw_docs)} documents")
    
    # Enhanced chunking với deduplication
    logger.info("Starting text chunking...")
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        separators=["\n\n", "\n", ". ", "? ", "! ", " ", ""]
    )
    
    all_chunks = []
    for d in raw_docs:
        txt = d["text"]
        if not txt or len(txt.strip()) < config.min_chars:
            continue
            
        # Split and dedupe
        split_chunks = splitter.split_text(txt)
        unique_chunks = dedupe_texts(split_chunks)
        
        for c in unique_chunks:
            c = c.strip()
            if len(c) >= config.min_chars:
                chunk_data = {
                    "id": str(uuid.uuid4()),
                    "text": c,
                    "meta": {
                        **d["meta"],
                        "chunk_length": len(c),
                        "chunk_words": len(c.split())
                    }
                }
                all_chunks.append(chunk_data)
    
    if not all_chunks:
        logger.error("No chunks created. Check your chunking parameters.")
        return
    
    logger.info(f"Created {len(all_chunks)} chunks")
    
    # Load embedding model
    logger.info(f"Loading embedding model: {config.embed_model}")
    try:
        model = SentenceTransformer(config.embed_model)
    except Exception as e:
        logger.error(f"Failed to load embedding model: {e}")
        return
    
    # Generate embeddings in batches
    logger.info("Generating embeddings...")
    all_embeddings = []
    
    for i in range(0, len(all_chunks), config.batch_size):
        batch_chunks = all_chunks[i:i + config.batch_size]
        batch_texts = [c["text"] for c in batch_chunks]
        
        try:
            batch_embeddings = model.encode(
                batch_texts,
                normalize_embeddings=True,
                show_progress_bar=True if i == 0 else False
            )
            all_embeddings.extend(batch_embeddings)
            
            if config.show_processing:
                print(f"Processed embeddings: {len(all_embeddings)}/{len(all_chunks)}")
                
        except Exception as e:
            logger.error(f"Embedding generation failed for batch {i}: {e}")
            return
    
    # Build FAISS index
    logger.info("Building FAISS index...")
    embeddings_array = np.array(all_embeddings, dtype="float32")
    
    index = faiss.IndexFlatIP(embeddings_array.shape[1])
    index.add(embeddings_array)
    
    # Save everything
    logger.info("Saving index and chunks...")
    faiss.write_index(index, "index.faiss")
    
    with open("chunks.pkl", "wb") as f:
        pickle.dump(all_chunks, f)
    
    # Save metadata
    metadata = {
        "total_documents": len(raw_docs),
        "total_chunks": len(all_chunks),
        "embedding_model": config.embed_model,
        "chunk_size": config.chunk_size,
        "chunk_overlap": config.chunk_overlap,
        "index_created": time.strftime("%Y-%m-%d %H:%M:%S"),
        "processing_time_seconds": time.time() - start_time,
        "results": results
    }
    
    with open("index_metadata.json", "w", encoding="utf-8") as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)
    
    # Final report
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n{'='*50}")
    print(f"🎉 INDEXING COMPLETED SUCCESSFULLY!")
    print(f"{'='*50}")
    print(f"📊 Statistics:")
    print(f"  • Documents processed: {len(raw_docs)}")
    print(f"  • Chunks created: {len(all_chunks)}")
    print(f"  • Embedding model: {config.embed_model}")
    print(f"  • Processing time: {processing_time:.2f} seconds")
    print(f"  • Average chunks per document: {len(all_chunks)/len(raw_docs):.1f}")
    print(f"\n📁 Files created:")
    print(f"  • index.faiss (FAISS vector index)")
    print(f"  • chunks.pkl (chunk data)")
    print(f"  • index_metadata.json (metadata)")
    print(f"  • build_index.log (processing log)")
    
    print(f"\n📈 Processing Results:")
    print(f"  ✅ Successful: {len(results['successes'])}")
    print(f"  ⚠️  Skipped: {len(results['skipped'])}")
    print(f"  ❌ Failed: {len(results['failures'])}")
    
    if results['failures'] and config.show_processing:
        print(f"\n❌ Failed files:")
        for path, error in results['failures'][:10]:  # Show first 10
            print(f"  • {Path(path).name}: {error}")
        if len(results['failures']) > 10:
            print(f"  ... and {len(results['failures']) - 10} more")
    
    print(f"{'='*50}\n")
    
    logger.info("Indexing process completed successfully!")

    print(f"\n📄 Chi tiết file đã đọc thành công ({len(results['successes'])}):")
    for path in results['successes']:
        print(f"  ✅ {path}")

    print(f"\n⚠️  Chi tiết file đã skip ({len(results['skipped'])}):")
    for path, reason in results['skipped']:
        print(f"  ⚠️  {path} - {reason}")

    print(f"\n❌ Chi tiết file lỗi ({len(results['failures'])}):")
    for path, error in results['failures']:
        print(f"  ❌ {path} - {error}")

    print(f"{'='*50}\n")
    logger.info("Indexing process completed successfully!")

if __name__ == "__main__":
    main()