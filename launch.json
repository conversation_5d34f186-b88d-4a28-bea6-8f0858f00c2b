{"version": "0.2.0", "configurations": [{"name": "Backend: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "u<PERSON><PERSON>", "args": ["app:app", "--reload", "--port", "8000"], "cwd": "${workspaceFolder}/backend", "envFile": "${workspaceFolder}/backend/.env", "console": "integratedTerminal"}, {"name": "Frontend: Next.js", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "cwd": "${workspaceFolder}/web", "env": {"PORT": "3000"}}], "compounds": [{"name": "Dev (backend + web)", "configurations": ["Backend: <PERSON><PERSON><PERSON>", "Frontend: Next.js"], "stopAll": true}]}