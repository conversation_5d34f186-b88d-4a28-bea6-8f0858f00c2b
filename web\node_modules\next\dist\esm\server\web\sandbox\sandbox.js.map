{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "sourcesContent": ["import type { NodejsRequestData, FetchEventResult, RequestData } from '../types'\nimport type { EdgeFunctionDefinition } from '../../../build/webpack/plugins/middleware-plugin'\nimport type { EdgeRuntime } from 'next/dist/compiled/edge-runtime'\nimport {\n  getModuleContext,\n  requestStore,\n  edgeSandboxNextRequestContext,\n} from './context'\nimport { requestToBodyStream } from '../../body-streams'\nimport type { ServerComponentsHmrCache } from '../../response-cache'\nimport {\n  getBuiltinRequestContext,\n  type BuiltinRequestContextValue,\n} from '../../after/builtin-request-context'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from '../../lib/router-utils/router-server-context'\n\nexport const ErrorSource = Symbol('SandboxError')\n\nconst FORBIDDEN_HEADERS = [\n  'content-length',\n  'content-encoding',\n  'transfer-encoding',\n]\n\ninterface RunnerFnParams {\n  name: string\n  onError?: (err: unknown) => void\n  onWarning?: (warn: Error) => void\n  paths: string[]\n  request: NodejsRequestData\n  useCache: boolean\n  edgeFunctionEntry: Pick<EdgeFunctionDefinition, 'assets' | 'wasm' | 'env'>\n  distDir: string\n  incrementalCache?: any\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n}\n\ntype RunnerFn = (params: RunnerFnParams) => Promise<FetchEventResult>\n\n/**\n * Decorates the runner function making sure all errors it can produce are\n * tagged with `edge-server` so they can properly be rendered in dev.\n */\nfunction withTaggedErrors(fn: RunnerFn): RunnerFn {\n  if (process.env.NODE_ENV === 'development') {\n    const { getServerError } =\n      require('../../dev/node-stack-frames') as typeof import('../../dev/node-stack-frames')\n\n    return (params) =>\n      fn(params)\n        .then((result) => ({\n          ...result,\n          waitUntil: result?.waitUntil?.catch((error) => {\n            // TODO: used COMPILER_NAMES.edgeServer instead. Verify that it does not increase the runtime size.\n            throw getServerError(error, 'edge-server')\n          }),\n        }))\n        .catch((error) => {\n          // TODO: used COMPILER_NAMES.edgeServer instead\n          throw getServerError(error, 'edge-server')\n        })\n  }\n\n  return fn\n}\n\nexport async function getRuntimeContext(\n  params: Omit<RunnerFnParams, 'request'>\n): Promise<EdgeRuntime<any>> {\n  const { runtime, evaluateInContext } = await getModuleContext({\n    moduleName: params.name,\n    onWarning: params.onWarning ?? (() => {}),\n    onError: params.onError ?? (() => {}),\n    useCache: params.useCache !== false,\n    edgeFunctionEntry: params.edgeFunctionEntry,\n    distDir: params.distDir,\n  })\n\n  if (params.incrementalCache) {\n    runtime.context.globalThis.__incrementalCacheShared = true\n    runtime.context.globalThis.__incrementalCache = params.incrementalCache\n  }\n\n  // expose router server context for access to dev handlers like\n  // logErrorWithOriginalStack\n  ;(runtime.context.globalThis as any as typeof routerServerGlobal)[\n    RouterServerContextSymbol\n  ] = routerServerGlobal[RouterServerContextSymbol]\n\n  if (params.serverComponentsHmrCache) {\n    runtime.context.globalThis.__serverComponentsHmrCache =\n      params.serverComponentsHmrCache\n  }\n\n  for (const paramPath of params.paths) {\n    evaluateInContext(paramPath)\n  }\n  return runtime\n}\n\nexport const run = withTaggedErrors(async function runWithTaggedErrors(params) {\n  const runtime = await getRuntimeContext(params)\n\n  const edgeFunction: (args: {\n    request: RequestData\n  }) => Promise<FetchEventResult> = (\n    await runtime.context._ENTRIES[`middleware_${params.name}`]\n  ).default\n\n  const cloned = !['HEAD', 'GET'].includes(params.request.method)\n    ? params.request.body?.cloneBodyStream()\n    : undefined\n\n  const KUint8Array = runtime.evaluate('Uint8Array')\n  const urlInstance = new URL(params.request.url)\n\n  params.request.url = urlInstance.toString()\n\n  const headers = new Headers()\n  for (const [key, value] of Object.entries(params.request.headers)) {\n    headers.set(key, value?.toString() ?? '')\n  }\n\n  try {\n    let result: FetchEventResult | undefined = undefined\n    const builtinRequestCtx: BuiltinRequestContextValue = {\n      ...getBuiltinRequestContext(),\n      // FIXME(after):\n      // arguably, this is an abuse of \"@next/request-context\" --\n      // it'd make more sense to simply forward its existing value into the sandbox (in `createModuleContext`)\n      // but here we're using it to just pass in `waitUntil` regardless if we were running in this context or not.\n      waitUntil: params.request.waitUntil,\n    }\n    await edgeSandboxNextRequestContext.run(builtinRequestCtx, () =>\n      requestStore.run({ headers }, async () => {\n        result = await edgeFunction({\n          request: {\n            ...params.request,\n            body:\n              cloned &&\n              requestToBodyStream(runtime.context, KUint8Array, cloned),\n          },\n        })\n        for (const headerName of FORBIDDEN_HEADERS) {\n          result.response.headers.delete(headerName)\n        }\n      })\n    )\n\n    if (!result) throw new Error('Edge function did not return a response')\n    return result\n  } finally {\n    await params.request.body?.finalize()\n  }\n})\n"], "names": ["getModuleContext", "requestStore", "edgeSandboxNextRequestContext", "requestToBodyStream", "getBuiltinRequestContext", "RouterServerContextSymbol", "routerServerGlobal", "ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "getRuntimeContext", "runtime", "evaluateInContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCacheShared", "__incrementalCache", "serverComponentsHmrCache", "__serverComponentsHmrCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "toString", "headers", "Headers", "key", "value", "Object", "entries", "set", "builtinRequestCtx", "headerName", "response", "delete", "Error", "finalize"], "mappings": "AAGA,SACEA,gBAAgB,EAChBC,YAAY,EACZC,6BAA6B,QACxB,YAAW;AAClB,SAASC,mBAAmB,QAAQ,qBAAoB;AAExD,SACEC,wBAAwB,QAEnB,sCAAqC;AAC5C,SACEC,yBAAyB,EACzBC,kBAAkB,QACb,+CAA8C;AAErD,OAAO,MAAMC,cAAcC,OAAO,gBAAe;AAEjD,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAiBD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEA,OAAO,eAAeY,kBACpBN,MAAuC;IAEvC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMzB,iBAAiB;QAC5D0B,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,wBAAwB,GAAG;QACtDZ,QAAQU,OAAO,CAACC,UAAU,CAACE,kBAAkB,GAAGpB,OAAOgB,gBAAgB;IACzE;IAEA,+DAA+D;IAC/D,4BAA4B;;IAC1BT,QAAQU,OAAO,CAACC,UAAU,AAAqC,CAC/D9B,0BACD,GAAGC,kBAAkB,CAACD,0BAA0B;IAEjD,IAAIY,OAAOqB,wBAAwB,EAAE;QACnCd,QAAQU,OAAO,CAACC,UAAU,CAACI,0BAA0B,GACnDtB,OAAOqB,wBAAwB;IACnC;IAEA,KAAK,MAAME,aAAavB,OAAOwB,KAAK,CAAE;QACpChB,kBAAkBe;IACpB;IACA,OAAOhB;AACT;AAEA,OAAO,MAAMkB,MAAMhC,iBAAiB,eAAeiC,oBAAoB1B,MAAM;QAUvEA;IATJ,MAAMO,UAAU,MAAMD,kBAAkBN;IAExC,MAAM2B,eAE4B,AAChC,CAAA,MAAMpB,QAAQU,OAAO,CAACW,QAAQ,CAAC,CAAC,WAAW,EAAE5B,OAAOU,IAAI,EAAE,CAAC,AAAD,EAC1DmB,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC/B,OAAOgC,OAAO,CAACC,MAAM,KAC1DjC,uBAAAA,OAAOgC,OAAO,CAACE,IAAI,qBAAnBlC,qBAAqBmC,eAAe,KACpCC;IAEJ,MAAMC,cAAc9B,QAAQ+B,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIxC,OAAOgC,OAAO,CAACS,GAAG;IAE9CzC,OAAOgC,OAAO,CAACS,GAAG,GAAGF,YAAYG,QAAQ;IAEzC,MAAMC,UAAU,IAAIC;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAChD,OAAOgC,OAAO,CAACW,OAAO,EAAG;QACjEA,QAAQM,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOJ,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIxC,SAAuCkC;QAC3C,MAAMc,oBAAgD;YACpD,GAAG/D,0BAA0B;YAC7B,gBAAgB;YAChB,2DAA2D;YAC3D,wGAAwG;YACxG,4GAA4G;YAC5GgB,WAAWH,OAAOgC,OAAO,CAAC7B,SAAS;QACrC;QACA,MAAMlB,8BAA8BwC,GAAG,CAACyB,mBAAmB,IACzDlE,aAAayC,GAAG,CAAC;gBAAEkB;YAAQ,GAAG;gBAC5BzC,SAAS,MAAMyB,aAAa;oBAC1BK,SAAS;wBACP,GAAGhC,OAAOgC,OAAO;wBACjBE,MACEJ,UACA5C,oBAAoBqB,QAAQU,OAAO,EAAEoB,aAAaP;oBACtD;gBACF;gBACA,KAAK,MAAMqB,cAAc3D,kBAAmB;oBAC1CU,OAAOkD,QAAQ,CAACT,OAAO,CAACU,MAAM,CAACF;gBACjC;YACF;QAGF,IAAI,CAACjD,QAAQ,MAAM,qBAAoD,CAApD,IAAIoD,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;QACtE,OAAOpD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOgC,OAAO,CAACE,IAAI,qBAAnBlC,sBAAqBuD,QAAQ;IACrC;AACF,GAAE", "ignoreList": [0]}