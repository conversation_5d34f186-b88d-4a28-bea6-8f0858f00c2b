'use client';
import { useState } from 'react';
import { Box, HStack, VStack, Input, Button, Text, Spinner } from '@chakra-ui/react';


export default function ChatBox() {
    const [input, setInput] = useState('');
    const [messages, setMessages] = useState<{ role: 'user' | 'assistant'; content: string }[]>([]);
    const [loading, setLoading] = useState(false);


    const send = async () => {
        if (!input.trim()) return;
        const q = input;
        setMessages(m => [...m, { role: 'user', content: q }]);
        setInput('');
        setLoading(true);
        const res = await fetch('/api/chat', {
            method: 'POST', headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query: q })
        });
        const data = await res.json();
        setMessages(m => [...m, { role: 'assistant', content: data.answer + "\n\nNguồn: " + data.sources.join(', ') }]);
        setLoading(false);
    };


    return (
        <VStack align="stretch" spacing={3} p={4} borderWidth="1px" rounded="2xl">
            <Box fontSize="xl" fontWeight="bold">Chatbot</Box>
            <VStack align="stretch" spacing={2} maxH="60vh" overflowY="auto" p={2} borderWidth="1px" rounded="xl">
                {messages.map((m, idx) => (
                    <Box key={idx} alignSelf={m.role === 'user' ? 'flex-end' : 'flex-start'}
                        bg={m.role === 'user' ? 'gray.200' : 'blue.50'} p={3} rounded="xl" whiteSpace="pre-wrap">
                        <Text fontSize="sm" opacity={0.7}>{m.role}</Text>
                        <Text>{m.content}</Text>
                    </Box>
                ))}
                {loading && <HStack><Spinner size="sm" /><Text>Đang trả lời…</Text></HStack>}
            </VStack>
            <HStack>
                <Input value={input} onChange={e => setInput(e.target.value)}
                    placeholder="Nhập câu hỏi..." onKeyDown={e => { if (e.key === 'Enter') send(); }} />
                <Button onClick={send}>Gửi</Button>
            </HStack>
        </VStack>
    );
}