'use client';

import { useEffect, useRef, useState } from 'react';
import {
  Box,
  HStack,
  VStack,
  Textarea,
  Button,
  Text,
  Spinner,
  Link,
  Badge,
  Flex,
  createToaster,
} from '@chakra-ui/react';

const toaster = createToaster({
  placement: 'top-end',
  pauseOnPageIdle: true,
});

// Icons sử dụng emoji
const SendIcon = () => <span style={{ fontSize: '16px' }}>📤</span>;
const UserIcon = () => <span style={{ fontSize: '14px' }}>👤</span>;
const BotIcon = () => <span style={{ fontSize: '14px' }}>🤖</span>;
const SourceIcon = () => <span style={{ fontSize: '12px' }}>📄</span>;

interface Message {
  role: 'user' | 'assistant';
  content: string;
  sources?: string[];
  timestamp: Date;
}

interface ChatBoxProps {
  placeholder?: string;
  maxMessages?: number;
}

export default function ChatBox({
  placeholder = "Nhập câu hỏi của bạn... (Enter: gử<PERSON>, Shift+Enter: xuống dòng)",
  maxMessages = 50
}: ChatBoxProps) {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 👋 Chào mừng tự động 1 lần khi mở hộp chat
  useEffect(() => {
    const greeted = typeof window !== 'undefined' && sessionStorage.getItem('bot_greeted');
    if (!greeted) {
      setMessages(m => [
        ...m,
        { role: 'assistant', content: `Xin chào bạn, tôi là AI chatbox của ${BOT_NAME}.` },
      ]);
      sessionStorage.setItem('bot_greeted', '1');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // chỉ chạy 1 lần khi mount

  // Tự cuộn xuống cuối khi có tin nhắn mới
  useEffect(() => {
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: 'smooth' });
  }, [messages, loading]);

  const send = async () => {
    const q = input.trim();
    if (!q || loading) return;

    setMessages((m) => [...m, { role: 'user', content: q }]);
    setInput('');
    setLoading(true);

    try {
      const res = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: q }),
      });

      if (!res.ok) {
        const txt = await res.text();
        throw new Error(txt || `HTTP ${res.status}`);
      }

      const data = await res.json();
      const answer: string = data?.answer ?? '(không có nội dung trả lời)';
      const sources: string[] = Array.isArray(data?.sources) ? data.sources : [];
      setMessages((m) => [...m, { role: 'assistant', content: answer, sources }]);
    } catch (err: any) {
      const msg = err?.message || String(err);
      setMessages((m) => [...m, { role: 'assistant', content: `Xin lỗi, đã xảy ra lỗi: ${msg}` }]);
      toaster.create({ title: 'Lỗi gọi API', description: msg, type: 'error', duration: 3000 });
    } finally {
      setLoading(false);
    }
  };

  const onKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      send();
    }
  };

  return (
    <VStack align="stretch" gap={3} p={4} borderWidth="1px" rounded="2xl">
      <Box fontSize="xl" fontWeight="bold">Chatbot</Box>

      <VStack
        ref={listRef}
        align="stretch"
        gap={2}
        maxH="60vh"
        overflowY="auto"
        p={2}
        borderWidth="1px"
        rounded="xl"
      >
        {messages.map((m, idx) => (
          <Box
            key={idx}
            alignSelf={m.role === 'user' ? 'flex-end' : 'flex-start'}
            bg={m.role === 'user' ? 'gray.100' : 'blue.50'}
            p={3}
            rounded="xl"
            whiteSpace="pre-wrap"
            maxW="85%"
          >
            <Text fontSize="xs" opacity={0.7} mb={1}>
              {m.role}
            </Text>
            <Text>{m.content}</Text>

            {m.sources && m.sources.length > 0 && (
              <Box mt={2} fontSize="sm" color="gray.600">
                Nguồn:{' '}
                {m.sources.map((s, i) => (
                  <span key={i}>
                    {looksLikeUrl(s) ? (
                      <Link href={s} target="_blank" color="blue.600">
                        {truncate(s)}
                      </Link>
                    ) : (
                      <Text as="span">{truncate(s)}</Text>
                    )}
                    {i < m.sources!.length - 1 ? ', ' : ''}
                  </span>
                ))}
              </Box>
            )}
          </Box>
        ))}

        {loading && (
          <HStack>
            <Spinner size="sm" />
            <Text>Đang trả lời…</Text>
          </HStack>
        )}
      </VStack>

      <HStack>
        <Textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Nhập câu hỏi... (Enter: gửi, Shift+Enter: xuống dòng)"
          onKeyDown={onKeyDown}
          rows={2}
        // Nếu muốn chỉ chào khi người dùng bấm vào ô nhập, thêm:
        // onFocus={() => {
        //   if (!sessionStorage.getItem('bot_greeted')) {
        //     setMessages(m => [...m, { role: 'assistant', content: `Xin chào bạn, tôi là AI chatbox của ${BOT_NAME}.` }]);
        //     sessionStorage.setItem('bot_greeted', '1');
        //   }
        // }}
        />
        <Button onClick={send} disabled={!input.trim() || loading} loading={loading}>
          Gửi
        </Button>
      </HStack>
    </VStack>
  );
}

function looksLikeUrl(s: string) {
  try {
    const u = new URL(s);
    return !!u.protocol && !!u.host;
  } catch {
    return false;
  }
}
function truncate(s: string, n = 60) {
  return s.length > n ? s.slice(0, n - 1) + '…' : s;
}
