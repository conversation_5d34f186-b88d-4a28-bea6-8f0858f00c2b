{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-app-loader/index.ts"], "sourcesContent": ["import type webpack from 'next/dist/compiled/webpack/webpack'\nimport {\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  type ValueOf,\n} from '../../../../shared/lib/constants'\nimport type { ModuleTuple, CollectedMetadata } from '../metadata/types'\n\nimport path from 'path'\nimport { bold } from '../../../../lib/picocolors'\nimport { getModuleBuildInfo } from '../get-module-build-info'\nimport { verifyRootLayout } from '../../../../lib/verify-root-layout'\nimport * as Log from '../../../output/log'\nimport { APP_DIR_ALIAS } from '../../../../lib/constants'\nimport {\n  createMetadataExportsCode,\n  createStaticMetadataFromRoute,\n} from '../metadata/discover'\nimport { promises as fs } from 'fs'\nimport { isAppRouteRoute } from '../../../../lib/is-app-route-route'\nimport type { NextConfig } from '../../../../server/config-shared'\nimport { AppPathnameNormalizer } from '../../../../server/normalizers/built/app/app-pathname-normalizer'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\nimport { isAppBuiltinNotFoundPage } from '../../../utils'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../../shared/lib/segment'\nimport { getFilesInDir } from '../../../../lib/get-files-in-dir'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../../../client/components/builtin/default'\nimport type { Compilation } from 'webpack'\nimport { createAppRouteCode } from './create-app-route-code'\n\nexport type AppLoaderOptions = {\n  name: string\n  page: string\n  pagePath: string\n  appDir: string\n  appPaths: readonly string[] | null\n  preferredRegion: string | string[] | undefined\n  pageExtensions: PageExtensions\n  assetPrefix: string\n  rootDir?: string\n  tsconfigPath?: string\n  isDev?: true\n  basePath: string\n  nextConfigOutput?: NextConfig['output']\n  middlewareConfig: string\n  isGlobalNotFoundEnabled: true | undefined\n}\ntype AppLoader = webpack.LoaderDefinitionFunction<AppLoaderOptions>\n\nconst HTTP_ACCESS_FALLBACKS = {\n  'not-found': 'not-found',\n  forbidden: 'forbidden',\n  unauthorized: 'unauthorized',\n} as const\nconst defaultHTTPAccessFallbackPaths = {\n  'not-found': 'next/dist/client/components/builtin/not-found.js',\n  forbidden: 'next/dist/client/components/builtin/forbidden.js',\n  unauthorized: 'next/dist/client/components/builtin/unauthorized.js',\n} as const\n\nconst FILE_TYPES = {\n  layout: 'layout',\n  template: 'template',\n  error: 'error',\n  loading: 'loading',\n  'global-error': 'global-error',\n  'global-not-found': 'global-not-found',\n  ...HTTP_ACCESS_FALLBACKS,\n} as const\n\nconst GLOBAL_ERROR_FILE_TYPE = 'global-error'\nconst GLOBAL_NOT_FOUND_FILE_TYPE = 'global-not-found'\nconst PAGE_SEGMENT = 'page$'\nconst PARALLEL_VIRTUAL_SEGMENT = 'slot$'\n\nconst defaultGlobalErrorPath =\n  'next/dist/client/components/builtin/global-error.js'\nconst defaultNotFoundPath = 'next/dist/client/components/builtin/not-found.js'\nconst defaultLayoutPath = 'next/dist/client/components/builtin/layout.js'\nconst defaultGlobalNotFoundPath =\n  'next/dist/client/components/builtin/global-not-found.js'\n\ntype DirResolver = (pathToResolve: string) => string\ntype PathResolver = (\n  pathname: string\n) => Promise<string | undefined> | string | undefined\nexport type MetadataResolver = (\n  dir: string,\n  filename: string,\n  extensions: readonly string[]\n) => Promise<string | undefined>\n\nexport type AppDirModules = {\n  readonly [moduleKey in ValueOf<typeof FILE_TYPES>]?: ModuleTuple\n} & {\n  readonly page?: ModuleTuple\n} & {\n  readonly metadata?: CollectedMetadata\n} & {\n  readonly defaultPage?: ModuleTuple\n}\n\nconst normalizeParallelKey = (key: string) =>\n  key.startsWith('@') ? key.slice(1) : key\n\nconst isDirectory = async (pathname: string) => {\n  try {\n    const stat = await fs.stat(pathname)\n    return stat.isDirectory()\n  } catch (err) {\n    return false\n  }\n}\n\nasync function createTreeCodeFromPath(\n  pagePath: string,\n  {\n    page,\n    resolveDir,\n    resolver,\n    resolveParallelSegments,\n    metadataResolver,\n    pageExtensions,\n    basePath,\n    collectedDeclarations,\n    isGlobalNotFoundEnabled,\n  }: {\n    page: string\n    resolveDir: DirResolver\n    resolver: PathResolver\n    metadataResolver: MetadataResolver\n    resolveParallelSegments: (\n      pathname: string\n    ) => [key: string, segment: string | string[]][]\n    loaderContext: webpack.LoaderContext<AppLoaderOptions>\n    pageExtensions: PageExtensions\n    basePath: string\n    collectedDeclarations: [string, string][]\n    isGlobalNotFoundEnabled: boolean\n  }\n): Promise<{\n  treeCode: string\n  pages: string\n  rootLayout: string | undefined\n  globalError: string\n  globalNotFound: string\n}> {\n  const splittedPath = pagePath.split(/[\\\\/]/, 1)\n  const isNotFoundRoute = page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n  const isDefaultNotFound = isAppBuiltinNotFoundPage(pagePath)\n\n  const appDirPrefix = isDefaultNotFound ? APP_DIR_ALIAS : splittedPath[0]\n  const pages: string[] = []\n\n  let rootLayout: string | undefined\n  let globalError: string = defaultGlobalErrorPath\n  let globalNotFound: string = defaultNotFoundPath\n\n  async function resolveAdjacentParallelSegments(\n    segmentPath: string\n  ): Promise<string[]> {\n    const absoluteSegmentPath = await resolveDir(\n      `${appDirPrefix}${segmentPath}`\n    )\n\n    if (!absoluteSegmentPath) {\n      return []\n    }\n\n    const segmentIsDirectory = await isDirectory(absoluteSegmentPath)\n\n    if (!segmentIsDirectory) {\n      return []\n    }\n\n    // We need to resolve all parallel routes in this level.\n    const files = await fs.opendir(absoluteSegmentPath)\n\n    const parallelSegments: string[] = ['children']\n\n    for await (const dirent of files) {\n      // Make sure name starts with \"@\" and is a directory.\n      if (dirent.isDirectory() && dirent.name.charCodeAt(0) === 64) {\n        parallelSegments.push(dirent.name)\n      }\n    }\n\n    return parallelSegments\n  }\n\n  async function createSubtreePropsFromSegmentPath(\n    segments: string[],\n    nestedCollectedDeclarations: [string, string][]\n  ): Promise<{\n    treeCode: string\n  }> {\n    const segmentPath = segments.join('/')\n\n    // Existing tree are the children of the current segment\n    const props: Record<string, string> = {}\n    // Root layer could be 1st layer of normal routes\n    const isRootLayer = segments.length === 0\n    const isRootLayoutOrRootPage = segments.length <= 1\n\n    // We need to resolve all parallel routes in this level.\n    const parallelSegments: [key: string, segment: string | string[]][] = []\n    if (isRootLayer) {\n      parallelSegments.push(['children', ''])\n    } else {\n      parallelSegments.push(...resolveParallelSegments(segmentPath))\n    }\n\n    let metadata: Awaited<ReturnType<typeof createStaticMetadataFromRoute>> =\n      null\n    const routerDirPath = `${appDirPrefix}${segmentPath}`\n    const resolvedRouteDir = resolveDir(routerDirPath)\n\n    if (resolvedRouteDir) {\n      metadata = await createStaticMetadataFromRoute(resolvedRouteDir, {\n        basePath,\n        segment: segmentPath,\n        metadataResolver,\n        isRootLayoutOrRootPage,\n        pageExtensions,\n      })\n    }\n\n    for (const [parallelKey, parallelSegment] of parallelSegments) {\n      // if parallelSegment is the page segment (ie, `page$` and not ['page$']), it gets loaded into the __PAGE__ slot\n      // as it's the page for the current route.\n      if (parallelSegment === PAGE_SEGMENT) {\n        const matchedPagePath = `${appDirPrefix}${segmentPath}${\n          parallelKey === 'children' ? '' : `/${parallelKey}`\n        }/page`\n\n        const resolvedPagePath = await resolver(matchedPagePath)\n        if (resolvedPagePath) {\n          pages.push(resolvedPagePath)\n\n          const varName = `page${nestedCollectedDeclarations.length}`\n          nestedCollectedDeclarations.push([varName, resolvedPagePath])\n\n          // Use '' for segment as it's the page. There can't be a segment called '' so this is the safest way to add it.\n          props[normalizeParallelKey(parallelKey)] =\n            `['${PAGE_SEGMENT_KEY}', {}, {\n          page: [${varName}, ${JSON.stringify(resolvedPagePath)}],\n          ${createMetadataExportsCode(metadata)}\n        }]`\n          continue\n        } else {\n          throw new Error(`Can't resolve ${matchedPagePath}`)\n        }\n      }\n\n      // if the parallelSegment was not matched to the __PAGE__ slot, then it's a parallel route at this level.\n      // the code below recursively traverses the parallel slots directory to match the corresponding __PAGE__ for each parallel slot\n      // while also filling in layout/default/etc files into the loader tree at each segment level.\n\n      const subSegmentPath = [...segments]\n      if (parallelKey !== 'children') {\n        // A `children` parallel key should have already been processed in the above segment\n        // So we exclude it when constructing the subsegment path for the remaining segment levels\n        subSegmentPath.push(parallelKey)\n      }\n\n      const normalizedParallelSegment = Array.isArray(parallelSegment)\n        ? parallelSegment[0]\n        : parallelSegment\n\n      if (\n        normalizedParallelSegment !== PAGE_SEGMENT &&\n        normalizedParallelSegment !== PARALLEL_VIRTUAL_SEGMENT\n      ) {\n        // If we don't have a page segment, nor a special $children marker, it means we need to traverse the next directory\n        // (ie, `normalizedParallelSegment` would correspond with the folder that contains the next level of pages/layout/etc)\n        // we push it to the subSegmentPath so that we can fill in the loader tree for that segment.\n        subSegmentPath.push(normalizedParallelSegment)\n      }\n\n      const parallelSegmentPath = subSegmentPath.join('/')\n\n      // Fill in the loader tree for all of the special files types (layout, default, etc) at this level\n      // `page` is not included here as it's added above.\n      const filePathEntries = await Promise.all(\n        Object.values(FILE_TYPES).map(async (file) => {\n          return [\n            file,\n            await resolver(\n              `${appDirPrefix}${\n                // TODO-APP: parallelSegmentPath sometimes ends in `/` but sometimes it doesn't. This should be consistent.\n                parallelSegmentPath.endsWith('/')\n                  ? parallelSegmentPath\n                  : parallelSegmentPath + '/'\n              }${file}`\n            ),\n          ] as const\n        })\n      )\n      const filePaths = new Map<ValueOf<typeof FILE_TYPES>, string | undefined>(\n        filePathEntries\n      )\n\n      // Only resolve global-* convention files at the root layer\n      if (isRootLayer) {\n        const resolvedGlobalErrorPath = await resolver(\n          `${appDirPrefix}/${GLOBAL_ERROR_FILE_TYPE}`\n        )\n        if (resolvedGlobalErrorPath) {\n          globalError = resolvedGlobalErrorPath\n        }\n        // Add global-error to root layer's filePaths, so that it's always available,\n        // by default it's the built-in global-error.js\n        filePaths.set(GLOBAL_ERROR_FILE_TYPE, globalError)\n\n        // TODO(global-not-found): remove this flag assertion condition\n        //  once global-not-found is stable\n        if (isGlobalNotFoundEnabled) {\n          const resolvedGlobalNotFoundPath = await resolver(\n            `${appDirPrefix}/${GLOBAL_NOT_FOUND_FILE_TYPE}`\n          )\n          if (resolvedGlobalNotFoundPath) {\n            globalNotFound = resolvedGlobalNotFoundPath\n          }\n          // Add global-not-found to root layer's filePaths, so that it's always available,\n          // by default it's the built-in global-not-found.js\n          filePaths.set(GLOBAL_NOT_FOUND_FILE_TYPE, globalNotFound)\n        }\n      }\n\n      let definedFilePaths = Array.from(filePaths.entries()).filter(\n        ([, filePath]) => filePath !== undefined\n      ) as [ValueOf<typeof FILE_TYPES>, string][]\n\n      // Add default access fallback as root fallback if not present\n      const existedConventionNames = new Set(\n        definedFilePaths.map(([type]) => type)\n      )\n      // If the first layer is a group route, we treat it as root layer\n      const isFirstLayerGroupRoute =\n        segments.length === 1 &&\n        subSegmentPath.filter((seg) => isGroupSegment(seg)).length === 1\n\n      if (isRootLayer || isFirstLayerGroupRoute) {\n        const accessFallbackTypes = Object.keys(\n          defaultHTTPAccessFallbackPaths\n        ) as (keyof typeof defaultHTTPAccessFallbackPaths)[]\n        for (const type of accessFallbackTypes) {\n          const hasRootFallbackFile = await resolver(\n            `${appDirPrefix}/${FILE_TYPES[type]}`\n          )\n          const hasLayerFallbackFile = existedConventionNames.has(type)\n\n          // If you already have a root access error fallback, don't insert default access error boundary to group routes root\n          if (\n            // Is treated as root layout and without boundary\n            !(hasRootFallbackFile && isFirstLayerGroupRoute) &&\n            // Does not have a fallback boundary file\n            !hasLayerFallbackFile\n          ) {\n            const defaultFallbackPath = defaultHTTPAccessFallbackPaths[type]\n            if (!(isDefaultNotFound && type === 'not-found')) {\n              definedFilePaths.push([type, defaultFallbackPath])\n            }\n          }\n        }\n      }\n\n      if (!rootLayout) {\n        const layoutPath = definedFilePaths.find(\n          ([type]) => type === 'layout'\n        )?.[1]\n        rootLayout = layoutPath\n\n        // When `global-not-found` is disabled, we insert a default layout if\n        // root layout is presented. This logic and the default layout will be removed\n        // once `global-not-found` is stabilized.\n        if (\n          !isGlobalNotFoundEnabled &&\n          isDefaultNotFound &&\n          !layoutPath &&\n          !rootLayout\n        ) {\n          rootLayout = defaultLayoutPath\n          definedFilePaths.push(['layout', rootLayout])\n        }\n      }\n\n      let parallelSegmentKey = Array.isArray(parallelSegment)\n        ? parallelSegment[0]\n        : parallelSegment\n\n      // normalize the parallel segment key to remove any special markers that we inserted in the\n      // earlier logic (such as children$ and page$). These should never appear in the loader tree, and\n      // should instead be the corresponding segment keys (ie `__PAGE__`) or the `children` parallel route.\n      parallelSegmentKey =\n        parallelSegmentKey === PARALLEL_VIRTUAL_SEGMENT\n          ? '(slot)'\n          : parallelSegmentKey === PAGE_SEGMENT\n            ? PAGE_SEGMENT_KEY\n            : parallelSegmentKey\n\n      const normalizedParallelKey = normalizeParallelKey(parallelKey)\n      let subtreeCode: string | undefined\n      // If it's root not found page, set not-found boundary as children page\n      if (isNotFoundRoute) {\n        if (normalizedParallelKey === 'children') {\n          const matchedGlobalNotFound = isGlobalNotFoundEnabled\n            ? definedFilePaths.find(\n                ([type]) => type === GLOBAL_NOT_FOUND_FILE_TYPE\n              )?.[1] ?? defaultGlobalNotFoundPath\n            : undefined\n\n          // If custom global-not-found.js is defined, use global-not-found.js\n          if (matchedGlobalNotFound) {\n            const varName = `notFound${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, matchedGlobalNotFound])\n            subtreeCode = `{\n              children: [${JSON.stringify(UNDERSCORE_NOT_FOUND_ROUTE)}, {\n                children: ['${PAGE_SEGMENT_KEY}', {}, {\n                  page: [\n                    ${varName},\n                    ${JSON.stringify(matchedGlobalNotFound)}\n                  ]\n                }]\n              }, {}]\n            }`\n          } else {\n            // If custom not-found.js is found, use it and layout to compose the page,\n            // and fallback to built-in not-found component if doesn't exist.\n            const notFoundPath =\n              definedFilePaths.find(([type]) => type === 'not-found')?.[1] ??\n              defaultNotFoundPath\n            const varName = `notFound${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, notFoundPath])\n            subtreeCode = `{\n              children: [${JSON.stringify(UNDERSCORE_NOT_FOUND_ROUTE)}, {\n                children: ['${PAGE_SEGMENT_KEY}', {}, {\n                  page: [\n                    ${varName},\n                    ${JSON.stringify(notFoundPath)}\n                  ]\n                }]\n              }, {}]\n            }`\n          }\n        }\n      }\n\n      // For 404 route\n      // if global-not-found is in definedFilePaths, remove root layout for /_not-found\n      // TODO: remove this once global-not-found is stable.\n      if (isNotFoundRoute && isGlobalNotFoundEnabled) {\n        definedFilePaths = definedFilePaths.filter(\n          ([type]) => type !== 'layout'\n        )\n      }\n\n      const modulesCode = `{\n        ${definedFilePaths\n          .map(([file, filePath]) => {\n            const varName = `module${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, filePath])\n            return `'${file}': [${varName}, ${JSON.stringify(filePath)}],`\n          })\n          .join('\\n')}\n        ${createMetadataExportsCode(metadata)}\n      }`\n\n      if (!subtreeCode) {\n        const { treeCode: pageSubtreeCode } =\n          await createSubtreePropsFromSegmentPath(\n            subSegmentPath,\n            nestedCollectedDeclarations\n          )\n\n        subtreeCode = pageSubtreeCode\n      }\n\n      props[normalizedParallelKey] = `[\n        '${parallelSegmentKey}',\n        ${subtreeCode},\n        ${modulesCode}\n      ]`\n    }\n\n    const adjacentParallelSegments =\n      await resolveAdjacentParallelSegments(segmentPath)\n\n    for (const adjacentParallelSegment of adjacentParallelSegments) {\n      if (!props[normalizeParallelKey(adjacentParallelSegment)]) {\n        const actualSegment =\n          adjacentParallelSegment === 'children'\n            ? ''\n            : `/${adjacentParallelSegment}`\n\n        // if a default is found, use that. Otherwise use the fallback, which will trigger a `notFound()`\n        const defaultPath =\n          (await resolver(\n            `${appDirPrefix}${segmentPath}${actualSegment}/default`\n          )) ?? PARALLEL_ROUTE_DEFAULT_PATH\n\n        const varName = `default${nestedCollectedDeclarations.length}`\n        nestedCollectedDeclarations.push([varName, defaultPath])\n        props[normalizeParallelKey(adjacentParallelSegment)] = `[\n          '${DEFAULT_SEGMENT_KEY}',\n          {},\n          {\n            defaultPage: [${varName}, ${JSON.stringify(defaultPath)}],\n          }\n        ]`\n      }\n    }\n    return {\n      treeCode: `{\n        ${Object.entries(props)\n          .map(([key, value]) => `${key}: ${value}`)\n          .join(',\\n')}\n      }`,\n    }\n  }\n\n  const { treeCode } = await createSubtreePropsFromSegmentPath(\n    [],\n    collectedDeclarations\n  )\n\n  return {\n    treeCode: `${treeCode}.children;`,\n    pages: `${JSON.stringify(pages)};`,\n    rootLayout,\n    globalError,\n    globalNotFound,\n  }\n}\n\nfunction createAbsolutePath(appDir: string, pathToTurnAbsolute: string) {\n  return (\n    pathToTurnAbsolute\n      // Replace all POSIX path separators with the current OS path separator\n      .replace(/\\//g, path.sep)\n      .replace(/^private-next-app-dir/, appDir)\n  )\n}\n\nconst filesInDirMapMap: WeakMap<\n  Compilation,\n  Map<string, Promise<Set<string>>>\n> = new WeakMap()\nconst nextAppLoader: AppLoader = async function nextAppLoader() {\n  const loaderOptions = this.getOptions()\n  const {\n    name,\n    appDir,\n    appPaths,\n    pagePath,\n    pageExtensions,\n    rootDir,\n    tsconfigPath,\n    isDev,\n    nextConfigOutput,\n    preferredRegion,\n    basePath,\n    middlewareConfig: middlewareConfigBase64,\n  } = loaderOptions\n\n  const isGlobalNotFoundEnabled = !!loaderOptions.isGlobalNotFoundEnabled\n\n  // Update FILE_TYPES on the very top-level of the loader\n  if (!isGlobalNotFoundEnabled) {\n    // @ts-expect-error this delete is only necessary while experimental\n    delete FILE_TYPES['global-not-found']\n  }\n\n  const buildInfo = getModuleBuildInfo((this as any)._module)\n  const collectedDeclarations: [string, string][] = []\n  const page = name.replace(/^app/, '')\n  const middlewareConfig: MiddlewareConfig = JSON.parse(\n    Buffer.from(middlewareConfigBase64, 'base64').toString()\n  )\n  buildInfo.route = {\n    page,\n    absolutePagePath: createAbsolutePath(appDir, pagePath),\n    preferredRegion,\n    middlewareConfig,\n    relatedModules: [],\n  }\n\n  const extensions =\n    typeof pageExtensions === 'string'\n      ? [pageExtensions]\n      : pageExtensions.map((extension) => `.${extension}`)\n\n  const normalizedAppPaths =\n    typeof appPaths === 'string' ? [appPaths] : appPaths || []\n\n  const resolveParallelSegments = (\n    pathname: string\n  ): [string, string | string[]][] => {\n    const matched: Record<string, string | string[]> = {}\n    let existingChildrenPath: string | undefined\n    for (const appPath of normalizedAppPaths) {\n      if (appPath.startsWith(pathname + '/')) {\n        const rest = appPath.slice(pathname.length + 1).split('/')\n\n        // It is the actual page, mark it specially.\n        if (rest.length === 1 && rest[0] === 'page') {\n          existingChildrenPath = appPath\n          matched.children = PAGE_SEGMENT\n          continue\n        }\n\n        const isParallelRoute = rest[0].startsWith('@')\n        if (isParallelRoute) {\n          if (rest.length === 2 && rest[1] === 'page') {\n            // We found a parallel route at this level. We don't want to mark it explicitly as the page segment,\n            // as that should be matched to the `children` slot. Instead, we use an array, to signal to `createSubtreePropsFromSegmentPath`\n            // that it needs to recursively fill in the loader tree code for the parallel route at the appropriate levels.\n            matched[rest[0]] = [PAGE_SEGMENT]\n            continue\n          }\n          // If it was a parallel route but we weren't able to find the page segment (ie, maybe the page is nested further)\n          // we first insert a special marker to ensure that we still process layout/default/etc at the slot level prior to continuing\n          // on to the page segment.\n          matched[rest[0]] = [PARALLEL_VIRTUAL_SEGMENT, ...rest.slice(1)]\n          continue\n        }\n\n        if (existingChildrenPath && matched.children !== rest[0]) {\n          // If we get here, it means we already set a `page` segment earlier in the loop,\n          // meaning we already matched a page to the `children` parallel segment.\n          const isIncomingParallelPage = appPath.includes('@')\n          const hasCurrentParallelPage = existingChildrenPath.includes('@')\n\n          if (isIncomingParallelPage) {\n            // The duplicate segment was for a parallel slot. In this case,\n            // rather than throwing an error, we can ignore it since this can happen for valid reasons.\n            // For example, when we attempt to normalize catch-all routes, we'll push potential slot matches so\n            // that they are available in the loader tree when we go to render the page.\n            // We only need to throw an error if the duplicate segment was for a regular page.\n            // For example, /app/(groupa)/page & /app/(groupb)/page is an error since it corresponds\n            // with the same path.\n            continue\n          } else if (!hasCurrentParallelPage && !isIncomingParallelPage) {\n            // Both the current `children` and the incoming `children` are regular pages.\n            throw new Error(\n              `You cannot have two parallel pages that resolve to the same path. Please check ${existingChildrenPath} and ${appPath}. Refer to the route group docs for more information: https://nextjs.org/docs/app/building-your-application/routing/route-groups`\n            )\n          }\n        }\n\n        existingChildrenPath = appPath\n        matched.children = rest[0]\n      }\n    }\n\n    return Object.entries(matched)\n  }\n\n  const resolveDir: DirResolver = (pathToResolve) => {\n    return createAbsolutePath(appDir, pathToResolve)\n  }\n\n  const resolveAppRoute: PathResolver = (pathToResolve) => {\n    return createAbsolutePath(appDir, pathToResolve)\n  }\n\n  // Cached checker to see if a file exists in a given directory.\n  // This can be more efficient than checking them with `fs.stat` one by one\n  // because all the thousands of files are likely in a few possible directories.\n  // Note that it should only be cached for this compilation, not globally.\n  const fileExistsInDirectory = async (dirname: string, fileName: string) => {\n    // I don't think we should ever hit this code path, but if we do we should handle it gracefully.\n    if (this._compilation === undefined) {\n      try {\n        return (await getFilesInDir(dirname).catch(() => new Set())).has(\n          fileName\n        )\n      } catch (e) {\n        return false\n      }\n    }\n    const map =\n      filesInDirMapMap.get(this._compilation) ||\n      new Map<string, Promise<Set<string>>>()\n    if (!filesInDirMapMap.has(this._compilation)) {\n      filesInDirMapMap.set(this._compilation, map)\n    }\n    if (!map.has(dirname)) {\n      map.set(\n        dirname,\n        getFilesInDir(dirname).catch(() => new Set())\n      )\n    }\n    return ((await map.get(dirname)) || new Set()).has(fileName)\n  }\n\n  const resolver: PathResolver = async (pathname) => {\n    const absolutePath = createAbsolutePath(appDir, pathname)\n\n    const filenameIndex = absolutePath.lastIndexOf(path.sep)\n    const dirname = absolutePath.slice(0, filenameIndex)\n    const filename = absolutePath.slice(filenameIndex + 1)\n\n    let result: string | undefined\n\n    for (const ext of extensions) {\n      const absolutePathWithExtension = `${absolutePath}${ext}`\n      if (\n        !result &&\n        (await fileExistsInDirectory(dirname, `${filename}${ext}`))\n      ) {\n        result = absolutePathWithExtension\n      }\n      // Call `addMissingDependency` for all files even if they didn't match,\n      // because they might be added or removed during development.\n      this.addMissingDependency(absolutePathWithExtension)\n    }\n\n    return result\n  }\n\n  const metadataResolver: MetadataResolver = async (\n    dirname,\n    filename,\n    exts\n  ) => {\n    const absoluteDir = createAbsolutePath(appDir, dirname)\n\n    let result: string | undefined\n\n    for (const ext of exts) {\n      // Compared to `resolver` above the exts do not have the `.` included already, so it's added here.\n      const filenameWithExt = `${filename}.${ext}`\n      const absolutePathWithExtension = `${absoluteDir}${path.sep}${filenameWithExt}`\n      if (!result && (await fileExistsInDirectory(dirname, filenameWithExt))) {\n        result = absolutePathWithExtension\n      }\n      // Call `addMissingDependency` for all files even if they didn't match,\n      // because they might be added or removed during development.\n      this.addMissingDependency(absolutePathWithExtension)\n    }\n\n    return result\n  }\n\n  if (isAppRouteRoute(name)) {\n    return createAppRouteCode({\n      appDir,\n      // TODO: investigate if the local `page` is the same as the loaderOptions.page\n      page: loaderOptions.page,\n      name,\n      pagePath,\n      resolveAppRoute,\n      pageExtensions,\n      nextConfigOutput,\n    })\n  }\n\n  let treeCodeResult = await createTreeCodeFromPath(pagePath, {\n    page,\n    resolveDir,\n    resolver,\n    metadataResolver,\n    resolveParallelSegments,\n    loaderContext: this,\n    pageExtensions,\n    basePath,\n    collectedDeclarations,\n    isGlobalNotFoundEnabled,\n  })\n\n  const isGlobalNotFoundPath =\n    page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY &&\n    !!treeCodeResult.globalNotFound &&\n    isGlobalNotFoundEnabled\n\n  if (!treeCodeResult.rootLayout && !isGlobalNotFoundPath) {\n    if (!isDev) {\n      // If we're building and missing a root layout, exit the build\n      Log.error(\n        `${bold(\n          pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n        )} doesn't have a root layout. To fix this error, make sure every page has a root layout.`\n      )\n      process.exit(1)\n    } else {\n      // In dev we'll try to create a root layout\n      const [createdRootLayout, rootLayoutPath] = await verifyRootLayout({\n        appDir: appDir,\n        dir: rootDir!,\n        tsconfigPath: tsconfigPath!,\n        pagePath,\n        pageExtensions,\n      })\n      if (!createdRootLayout) {\n        let message = `${bold(\n          pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n        )} doesn't have a root layout. `\n\n        if (rootLayoutPath) {\n          message += `We tried to create ${bold(\n            path.relative(this._compiler?.context ?? '', rootLayoutPath)\n          )} for you but something went wrong.`\n        } else {\n          message +=\n            'To fix this error, make sure every page has a root layout.'\n        }\n\n        throw new Error(message)\n      }\n\n      // Clear fs cache, get the new result with the created root layout.\n      if (this._compilation) filesInDirMapMap.get(this._compilation)?.clear()\n      treeCodeResult = await createTreeCodeFromPath(pagePath, {\n        page,\n        resolveDir,\n        resolver,\n        metadataResolver,\n        resolveParallelSegments,\n        loaderContext: this,\n        pageExtensions,\n        basePath,\n        collectedDeclarations,\n        isGlobalNotFoundEnabled,\n      })\n    }\n  }\n\n  const pathname = new AppPathnameNormalizer().normalize(page)\n\n  // Prefer to modify next/src/server/app-render/entry-base.ts since this is shared with Turbopack.\n  // Any changes to this code should be reflected in Turbopack's app_source.rs and/or app-renderer.tsx as well.\n  const code = await loadEntrypoint(\n    'app-page',\n    {\n      VAR_DEFINITION_PAGE: page,\n      VAR_DEFINITION_PATHNAME: pathname,\n      VAR_MODULE_GLOBAL_ERROR: treeCodeResult.globalError,\n    },\n    {\n      tree: treeCodeResult.treeCode,\n      pages: treeCodeResult.pages,\n      __next_app_require__: '__webpack_require__',\n      // all modules are in the entry chunk, so we never actually need to load chunks in webpack\n      __next_app_load_chunk__: '() => Promise.resolve()',\n    }\n  )\n\n  // Lazily evaluate the imported modules in the generated code\n  const header = collectedDeclarations\n    .map(([varName, modulePath]) => {\n      return `const ${varName} = () => import(/* webpackMode: \"eager\" */ ${JSON.stringify(\n        modulePath\n      )});\\n`\n    })\n    .join('')\n\n  return header + code\n}\n\nexport default nextAppLoader\n"], "names": ["UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "path", "bold", "getModuleBuildInfo", "verifyRootLayout", "Log", "APP_DIR_ALIAS", "createMetadataExportsCode", "createStaticMetadataFromRoute", "promises", "fs", "isAppRouteRoute", "AppPathnameNormalizer", "isAppBuiltinNotFoundPage", "loadEntrypoint", "isGroupSegment", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "getFilesInDir", "PARALLEL_ROUTE_DEFAULT_PATH", "createAppRouteCode", "HTTP_ACCESS_FALLBACKS", "forbidden", "unauthorized", "defaultHTTPAccessFallbackPaths", "FILE_TYPES", "layout", "template", "error", "loading", "GLOBAL_ERROR_FILE_TYPE", "GLOBAL_NOT_FOUND_FILE_TYPE", "PAGE_SEGMENT", "PARALLEL_VIRTUAL_SEGMENT", "defaultGlobalErrorPath", "defaultNotFoundPath", "defaultLayoutPath", "defaultGlobalNotFoundPath", "normalizeP<PERSON><PERSON><PERSON><PERSON>ey", "key", "startsWith", "slice", "isDirectory", "pathname", "stat", "err", "createTreeCodeFromPath", "pagePath", "page", "resolveDir", "resolver", "resolveParallelSegments", "metadataResolver", "pageExtensions", "basePath", "collectedDeclarations", "isGlobalNotFoundEnabled", "splittedPath", "split", "isNotFoundRoute", "isDefaultNotFound", "appDirPrefix", "pages", "rootLayout", "globalError", "globalNotFound", "resolveAdjacentParallelSegments", "segmentPath", "absoluteSegmentPath", "segmentIsDirectory", "files", "opendir", "parallelSegments", "dirent", "name", "charCodeAt", "push", "createSubtreePropsFromSegmentPath", "segments", "nestedCollectedDeclarations", "join", "props", "isRootLayer", "length", "isRootLayoutOrRootPage", "metadata", "routerDirPath", "resolvedRouteDir", "segment", "parallel<PERSON>ey", "parallelSegment", "matchedPagePath", "resolvedPagePath", "varName", "JSON", "stringify", "Error", "subSegmentPath", "normalizedParallelSegment", "Array", "isArray", "parallelSegmentPath", "filePathEntries", "Promise", "all", "Object", "values", "map", "file", "endsWith", "filePaths", "Map", "resolvedGlobalErrorPath", "set", "resolvedGlobalNotFoundPath", "definedFilePaths", "from", "entries", "filter", "filePath", "undefined", "existedConventionNames", "Set", "type", "isFirstLayerGroupRoute", "seg", "accessFallbackTypes", "keys", "hasRootFallbackFile", "hasLayerFallbackFile", "has", "defaultFallbackPath", "<PERSON><PERSON><PERSON>", "find", "parallelSegmentKey", "normalizedParallel<PERSON>ey", "subtreeCode", "matchedGlobalNotFound", "notFoundPath", "modulesCode", "treeCode", "pageSubtreeCode", "adjacentParallelSegments", "adjacentParallelSegment", "actualSegment", "defaultPath", "value", "createAbsolutePath", "appDir", "pathToTurnAbsolute", "replace", "sep", "filesInDirMapMap", "WeakMap", "nextApp<PERSON><PERSON>der", "loaderOptions", "getOptions", "appPaths", "rootDir", "tsconfigPath", "isDev", "nextConfigOutput", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "buildInfo", "_module", "parse", "<PERSON><PERSON><PERSON>", "toString", "route", "absolutePagePath", "relatedModules", "extensions", "extension", "normalizedAppPaths", "matched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appPath", "rest", "children", "isParallelRoute", "isIncomingParallelPage", "includes", "hasCurrentParallelPage", "pathToResolve", "resolveAppRoute", "fileExistsInDirectory", "dirname", "fileName", "_compilation", "catch", "e", "get", "absolutePath", "filenameIndex", "lastIndexOf", "filename", "result", "ext", "absolutePathWithExtension", "addMissingDependency", "exts", "absoluteDir", "filenameWithExt", "treeCodeResult", "loaderContext", "isGlobalNotFoundPath", "process", "exit", "createdRootLayout", "rootLayoutPath", "dir", "message", "relative", "_compiler", "context", "clear", "normalize", "code", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "VAR_MODULE_GLOBAL_ERROR", "tree", "__next_app_require__", "__next_app_load_chunk__", "header", "modulePath"], "mappings": "AACA,SACEA,0BAA0B,EAC1BC,gCAAgC,QAE3B,mCAAkC;AAGzC,OAAOC,UAAU,OAAM;AACvB,SAASC,IAAI,QAAQ,6BAA4B;AACjD,SAASC,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,aAAa,QAAQ,4BAA2B;AACzD,SACEC,yBAAyB,EACzBC,6BAA6B,QACxB,uBAAsB;AAC7B,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,eAAe,QAAQ,qCAAoC;AAEpE,SAASC,qBAAqB,QAAQ,mEAAkE;AAExG,SAASC,wBAAwB,QAAQ,iBAAgB;AACzD,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SACEC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,iCAAgC;AACvC,SAASC,aAAa,QAAQ,mCAAkC;AAEhE,SAASC,2BAA2B,QAAQ,gDAA+C;AAE3F,SAASC,kBAAkB,QAAQ,0BAAyB;AAqB5D,MAAMC,wBAAwB;IAC5B,aAAa;IACbC,WAAW;IACXC,cAAc;AAChB;AACA,MAAMC,iCAAiC;IACrC,aAAa;IACbF,WAAW;IACXC,cAAc;AAChB;AAEA,MAAME,aAAa;IACjBC,QAAQ;IACRC,UAAU;IACVC,OAAO;IACPC,SAAS;IACT,gBAAgB;IAChB,oBAAoB;IACpB,GAAGR,qBAAqB;AAC1B;AAEA,MAAMS,yBAAyB;AAC/B,MAAMC,6BAA6B;AACnC,MAAMC,eAAe;AACrB,MAAMC,2BAA2B;AAEjC,MAAMC,yBACJ;AACF,MAAMC,sBAAsB;AAC5B,MAAMC,oBAAoB;AAC1B,MAAMC,4BACJ;AAsBF,MAAMC,uBAAuB,CAACC,MAC5BA,IAAIC,UAAU,CAAC,OAAOD,IAAIE,KAAK,CAAC,KAAKF;AAEvC,MAAMG,cAAc,OAAOC;IACzB,IAAI;QACF,MAAMC,OAAO,MAAMlC,GAAGkC,IAAI,CAACD;QAC3B,OAAOC,KAAKF,WAAW;IACzB,EAAE,OAAOG,KAAK;QACZ,OAAO;IACT;AACF;AAEA,eAAeC,uBACbC,QAAgB,EAChB,EACEC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,uBAAuB,EACvBC,gBAAgB,EAChBC,cAAc,EACdC,QAAQ,EACRC,qBAAqB,EACrBC,uBAAuB,EAcxB;IAQD,MAAMC,eAAeV,SAASW,KAAK,CAAC,SAAS;IAC7C,MAAMC,kBAAkBX,SAAShD;IACjC,MAAM4D,oBAAoB/C,yBAAyBkC;IAEnD,MAAMc,eAAeD,oBAAoBtD,gBAAgBmD,YAAY,CAAC,EAAE;IACxE,MAAMK,QAAkB,EAAE;IAE1B,IAAIC;IACJ,IAAIC,cAAsB9B;IAC1B,IAAI+B,iBAAyB9B;IAE7B,eAAe+B,gCACbC,WAAmB;QAEnB,MAAMC,sBAAsB,MAAMnB,WAChC,GAAGY,eAAeM,aAAa;QAGjC,IAAI,CAACC,qBAAqB;YACxB,OAAO,EAAE;QACX;QAEA,MAAMC,qBAAqB,MAAM3B,YAAY0B;QAE7C,IAAI,CAACC,oBAAoB;YACvB,OAAO,EAAE;QACX;QAEA,wDAAwD;QACxD,MAAMC,QAAQ,MAAM5D,GAAG6D,OAAO,CAACH;QAE/B,MAAMI,mBAA6B;YAAC;SAAW;QAE/C,WAAW,MAAMC,UAAUH,MAAO;YAChC,qDAAqD;YACrD,IAAIG,OAAO/B,WAAW,MAAM+B,OAAOC,IAAI,CAACC,UAAU,CAAC,OAAO,IAAI;gBAC5DH,iBAAiBI,IAAI,CAACH,OAAOC,IAAI;YACnC;QACF;QAEA,OAAOF;IACT;IAEA,eAAeK,kCACbC,QAAkB,EAClBC,2BAA+C;QAI/C,MAAMZ,cAAcW,SAASE,IAAI,CAAC;QAElC,wDAAwD;QACxD,MAAMC,QAAgC,CAAC;QACvC,iDAAiD;QACjD,MAAMC,cAAcJ,SAASK,MAAM,KAAK;QACxC,MAAMC,yBAAyBN,SAASK,MAAM,IAAI;QAElD,wDAAwD;QACxD,MAAMX,mBAAgE,EAAE;QACxE,IAAIU,aAAa;YACfV,iBAAiBI,IAAI,CAAC;gBAAC;gBAAY;aAAG;QACxC,OAAO;YACLJ,iBAAiBI,IAAI,IAAIzB,wBAAwBgB;QACnD;QAEA,IAAIkB,WACF;QACF,MAAMC,gBAAgB,GAAGzB,eAAeM,aAAa;QACrD,MAAMoB,mBAAmBtC,WAAWqC;QAEpC,IAAIC,kBAAkB;YACpBF,WAAW,MAAM7E,8BAA8B+E,kBAAkB;gBAC/DjC;gBACAkC,SAASrB;gBACTf;gBACAgC;gBACA/B;YACF;QACF;QAEA,KAAK,MAAM,CAACoC,aAAaC,gBAAgB,IAAIlB,iBAAkB;YAC7D,gHAAgH;YAChH,0CAA0C;YAC1C,IAAIkB,oBAAoB1D,cAAc;gBACpC,MAAM2D,kBAAkB,GAAG9B,eAAeM,cACxCsB,gBAAgB,aAAa,KAAK,CAAC,CAAC,EAAEA,aAAa,CACpD,KAAK,CAAC;gBAEP,MAAMG,mBAAmB,MAAM1C,SAASyC;gBACxC,IAAIC,kBAAkB;oBACpB9B,MAAMc,IAAI,CAACgB;oBAEX,MAAMC,UAAU,CAAC,IAAI,EAAEd,4BAA4BI,MAAM,EAAE;oBAC3DJ,4BAA4BH,IAAI,CAAC;wBAACiB;wBAASD;qBAAiB;oBAE5D,+GAA+G;oBAC/GX,KAAK,CAAC3C,qBAAqBmD,aAAa,GACtC,CAAC,EAAE,EAAExE,iBAAiB;iBACjB,EAAE4E,QAAQ,EAAE,EAAEC,KAAKC,SAAS,CAACH,kBAAkB;UACtD,EAAErF,0BAA0B8E,UAAU;UACtC,CAAC;oBACD;gBACF,OAAO;oBACL,MAAM,qBAA6C,CAA7C,IAAIW,MAAM,CAAC,cAAc,EAAEL,iBAAiB,GAA5C,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;YACF;YAEA,yGAAyG;YACzG,+HAA+H;YAC/H,6FAA6F;YAE7F,MAAMM,iBAAiB;mBAAInB;aAAS;YACpC,IAAIW,gBAAgB,YAAY;gBAC9B,oFAAoF;gBACpF,0FAA0F;gBAC1FQ,eAAerB,IAAI,CAACa;YACtB;YAEA,MAAMS,4BAA4BC,MAAMC,OAAO,CAACV,mBAC5CA,eAAe,CAAC,EAAE,GAClBA;YAEJ,IACEQ,8BAA8BlE,gBAC9BkE,8BAA8BjE,0BAC9B;gBACA,mHAAmH;gBACnH,sHAAsH;gBACtH,4FAA4F;gBAC5FgE,eAAerB,IAAI,CAACsB;YACtB;YAEA,MAAMG,sBAAsBJ,eAAejB,IAAI,CAAC;YAEhD,kGAAkG;YAClG,mDAAmD;YACnD,MAAMsB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCC,OAAOC,MAAM,CAACjF,YAAYkF,GAAG,CAAC,OAAOC;gBACnC,OAAO;oBACLA;oBACA,MAAM1D,SACJ,GAAGW,eACD,2GAA2G;oBAC3GwC,oBAAoBQ,QAAQ,CAAC,OACzBR,sBACAA,sBAAsB,MACzBO,MAAM;iBAEZ;YACH;YAEF,MAAME,YAAY,IAAIC,IACpBT;YAGF,2DAA2D;YAC3D,IAAIpB,aAAa;gBACf,MAAM8B,0BAA0B,MAAM9D,SACpC,GAAGW,aAAa,CAAC,EAAE/B,wBAAwB;gBAE7C,IAAIkF,yBAAyB;oBAC3BhD,cAAcgD;gBAChB;gBACA,6EAA6E;gBAC7E,+CAA+C;gBAC/CF,UAAUG,GAAG,CAACnF,wBAAwBkC;gBAEtC,+DAA+D;gBAC/D,mCAAmC;gBACnC,IAAIR,yBAAyB;oBAC3B,MAAM0D,6BAA6B,MAAMhE,SACvC,GAAGW,aAAa,CAAC,EAAE9B,4BAA4B;oBAEjD,IAAImF,4BAA4B;wBAC9BjD,iBAAiBiD;oBACnB;oBACA,iFAAiF;oBACjF,mDAAmD;oBACnDJ,UAAUG,GAAG,CAAClF,4BAA4BkC;gBAC5C;YACF;YAEA,IAAIkD,mBAAmBhB,MAAMiB,IAAI,CAACN,UAAUO,OAAO,IAAIC,MAAM,CAC3D,CAAC,GAAGC,SAAS,GAAKA,aAAaC;YAGjC,8DAA8D;YAC9D,MAAMC,yBAAyB,IAAIC,IACjCP,iBAAiBR,GAAG,CAAC,CAAC,CAACgB,KAAK,GAAKA;YAEnC,iEAAiE;YACjE,MAAMC,yBACJ9C,SAASK,MAAM,KAAK,KACpBc,eAAeqB,MAAM,CAAC,CAACO,MAAQ9G,eAAe8G,MAAM1C,MAAM,KAAK;YAEjE,IAAID,eAAe0C,wBAAwB;gBACzC,MAAME,sBAAsBrB,OAAOsB,IAAI,CACrCvG;gBAEF,KAAK,MAAMmG,QAAQG,oBAAqB;oBACtC,MAAME,sBAAsB,MAAM9E,SAChC,GAAGW,aAAa,CAAC,EAAEpC,UAAU,CAACkG,KAAK,EAAE;oBAEvC,MAAMM,uBAAuBR,uBAAuBS,GAAG,CAACP;oBAExD,oHAAoH;oBACpH,IACE,iDAAiD;oBACjD,CAAEK,CAAAA,uBAAuBJ,sBAAqB,KAC9C,yCAAyC;oBACzC,CAACK,sBACD;wBACA,MAAME,sBAAsB3G,8BAA8B,CAACmG,KAAK;wBAChE,IAAI,CAAE/D,CAAAA,qBAAqB+D,SAAS,WAAU,GAAI;4BAChDR,iBAAiBvC,IAAI,CAAC;gCAAC+C;gCAAMQ;6BAAoB;wBACnD;oBACF;gBACF;YACF;YAEA,IAAI,CAACpE,YAAY;oBACIoD;gBAAnB,MAAMiB,cAAajB,yBAAAA,iBAAiBkB,IAAI,CACtC,CAAC,CAACV,KAAK,GAAKA,SAAS,8BADJR,sBAEhB,CAAC,EAAE;gBACNpD,aAAaqE;gBAEb,qEAAqE;gBACrE,8EAA8E;gBAC9E,yCAAyC;gBACzC,IACE,CAAC5E,2BACDI,qBACA,CAACwE,cACD,CAACrE,YACD;oBACAA,aAAa3B;oBACb+E,iBAAiBvC,IAAI,CAAC;wBAAC;wBAAUb;qBAAW;gBAC9C;YACF;YAEA,IAAIuE,qBAAqBnC,MAAMC,OAAO,CAACV,mBACnCA,eAAe,CAAC,EAAE,GAClBA;YAEJ,2FAA2F;YAC3F,iGAAiG;YACjG,qGAAqG;YACrG4C,qBACEA,uBAAuBrG,2BACnB,WACAqG,uBAAuBtG,eACrBf,mBACAqH;YAER,MAAMC,wBAAwBjG,qBAAqBmD;YACnD,IAAI+C;YACJ,uEAAuE;YACvE,IAAI7E,iBAAiB;gBACnB,IAAI4E,0BAA0B,YAAY;wBAEpCpB;oBADJ,MAAMsB,wBAAwBjF,0BAC1B2D,EAAAA,0BAAAA,iBAAiBkB,IAAI,CACnB,CAAC,CAACV,KAAK,GAAKA,SAAS5F,gDADvBoF,uBAEG,CAAC,EAAE,KAAI9E,4BACVmF;oBAEJ,oEAAoE;oBACpE,IAAIiB,uBAAuB;wBACzB,MAAM5C,UAAU,CAAC,QAAQ,EAAEd,4BAA4BI,MAAM,EAAE;wBAC/DJ,4BAA4BH,IAAI,CAAC;4BAACiB;4BAAS4C;yBAAsB;wBACjED,cAAc,CAAC;yBACF,EAAE1C,KAAKC,SAAS,CAAChG,4BAA4B;4BAC1C,EAAEkB,iBAAiB;;oBAE3B,EAAE4E,QAAQ;oBACV,EAAEC,KAAKC,SAAS,CAAC0C,uBAAuB;;;;aAI/C,CAAC;oBACJ,OAAO;4BAIHtB;wBAHF,0EAA0E;wBAC1E,iEAAiE;wBACjE,MAAMuB,eACJvB,EAAAA,0BAAAA,iBAAiBkB,IAAI,CAAC,CAAC,CAACV,KAAK,GAAKA,SAAS,iCAA3CR,uBAAyD,CAAC,EAAE,KAC5DhF;wBACF,MAAM0D,UAAU,CAAC,QAAQ,EAAEd,4BAA4BI,MAAM,EAAE;wBAC/DJ,4BAA4BH,IAAI,CAAC;4BAACiB;4BAAS6C;yBAAa;wBACxDF,cAAc,CAAC;yBACF,EAAE1C,KAAKC,SAAS,CAAChG,4BAA4B;4BAC1C,EAAEkB,iBAAiB;;oBAE3B,EAAE4E,QAAQ;oBACV,EAAEC,KAAKC,SAAS,CAAC2C,cAAc;;;;aAItC,CAAC;oBACJ;gBACF;YACF;YAEA,gBAAgB;YAChB,iFAAiF;YACjF,qDAAqD;YACrD,IAAI/E,mBAAmBH,yBAAyB;gBAC9C2D,mBAAmBA,iBAAiBG,MAAM,CACxC,CAAC,CAACK,KAAK,GAAKA,SAAS;YAEzB;YAEA,MAAMgB,cAAc,CAAC;QACnB,EAAExB,iBACCR,GAAG,CAAC,CAAC,CAACC,MAAMW,SAAS;gBACpB,MAAM1B,UAAU,CAAC,MAAM,EAAEd,4BAA4BI,MAAM,EAAE;gBAC7DJ,4BAA4BH,IAAI,CAAC;oBAACiB;oBAAS0B;iBAAS;gBACpD,OAAO,CAAC,CAAC,EAAEX,KAAK,IAAI,EAAEf,QAAQ,EAAE,EAAEC,KAAKC,SAAS,CAACwB,UAAU,EAAE,CAAC;YAChE,GACCvC,IAAI,CAAC,MAAM;QACd,EAAEzE,0BAA0B8E,UAAU;OACvC,CAAC;YAEF,IAAI,CAACmD,aAAa;gBAChB,MAAM,EAAEI,UAAUC,eAAe,EAAE,GACjC,MAAMhE,kCACJoB,gBACAlB;gBAGJyD,cAAcK;YAChB;YAEA5D,KAAK,CAACsD,sBAAsB,GAAG,CAAC;SAC7B,EAAED,mBAAmB;QACtB,EAAEE,YAAY;QACd,EAAEG,YAAY;OACf,CAAC;QACJ;QAEA,MAAMG,2BACJ,MAAM5E,gCAAgCC;QAExC,KAAK,MAAM4E,2BAA2BD,yBAA0B;YAC9D,IAAI,CAAC7D,KAAK,CAAC3C,qBAAqByG,yBAAyB,EAAE;gBACzD,MAAMC,gBACJD,4BAA4B,aACxB,KACA,CAAC,CAAC,EAAEA,yBAAyB;gBAEnC,iGAAiG;gBACjG,MAAME,cACJ,AAAC,MAAM/F,SACL,GAAGW,eAAeM,cAAc6E,cAAc,QAAQ,CAAC,KACnD7H;gBAER,MAAM0E,UAAU,CAAC,OAAO,EAAEd,4BAA4BI,MAAM,EAAE;gBAC9DJ,4BAA4BH,IAAI,CAAC;oBAACiB;oBAASoD;iBAAY;gBACvDhE,KAAK,CAAC3C,qBAAqByG,yBAAyB,GAAG,CAAC;WACrD,EAAE/H,oBAAoB;;;0BAGP,EAAE6E,QAAQ,EAAE,EAAEC,KAAKC,SAAS,CAACkD,aAAa;;SAE3D,CAAC;YACJ;QACF;QACA,OAAO;YACLL,UAAU,CAAC;QACT,EAAEnC,OAAOY,OAAO,CAACpC,OACd0B,GAAG,CAAC,CAAC,CAACpE,KAAK2G,MAAM,GAAK,GAAG3G,IAAI,EAAE,EAAE2G,OAAO,EACxClE,IAAI,CAAC,OAAO;OAChB,CAAC;QACJ;IACF;IAEA,MAAM,EAAE4D,QAAQ,EAAE,GAAG,MAAM/D,kCACzB,EAAE,EACFtB;IAGF,OAAO;QACLqF,UAAU,GAAGA,SAAS,UAAU,CAAC;QACjC9E,OAAO,GAAGgC,KAAKC,SAAS,CAACjC,OAAO,CAAC,CAAC;QAClCC;QACAC;QACAC;IACF;AACF;AAEA,SAASkF,mBAAmBC,MAAc,EAAEC,kBAA0B;IACpE,OACEA,kBACE,uEAAuE;KACtEC,OAAO,CAAC,OAAOrJ,KAAKsJ,GAAG,EACvBD,OAAO,CAAC,yBAAyBF;AAExC;AAEA,MAAMI,mBAGF,IAAIC;AACR,MAAMC,gBAA2B,eAAeA;IAC9C,MAAMC,gBAAgB,IAAI,CAACC,UAAU;IACrC,MAAM,EACJlF,IAAI,EACJ0E,MAAM,EACNS,QAAQ,EACR9G,QAAQ,EACRM,cAAc,EACdyG,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,eAAe,EACf5G,QAAQ,EACR6G,kBAAkBC,sBAAsB,EACzC,GAAGT;IAEJ,MAAMnG,0BAA0B,CAAC,CAACmG,cAAcnG,uBAAuB;IAEvE,wDAAwD;IACxD,IAAI,CAACA,yBAAyB;QAC5B,oEAAoE;QACpE,OAAO/B,UAAU,CAAC,mBAAmB;IACvC;IAEA,MAAM4I,YAAYlK,mBAAmB,AAAC,IAAI,CAASmK,OAAO;IAC1D,MAAM/G,wBAA4C,EAAE;IACpD,MAAMP,OAAO0B,KAAK4E,OAAO,CAAC,QAAQ;IAClC,MAAMa,mBAAqCrE,KAAKyE,KAAK,CACnDC,OAAOpD,IAAI,CAACgD,wBAAwB,UAAUK,QAAQ;IAExDJ,UAAUK,KAAK,GAAG;QAChB1H;QACA2H,kBAAkBxB,mBAAmBC,QAAQrG;QAC7CmH;QACAC;QACAS,gBAAgB,EAAE;IACpB;IAEA,MAAMC,aACJ,OAAOxH,mBAAmB,WACtB;QAACA;KAAe,GAChBA,eAAesD,GAAG,CAAC,CAACmE,YAAc,CAAC,CAAC,EAAEA,WAAW;IAEvD,MAAMC,qBACJ,OAAOlB,aAAa,WAAW;QAACA;KAAS,GAAGA,YAAY,EAAE;IAE5D,MAAM1G,0BAA0B,CAC9BR;QAEA,MAAMqI,UAA6C,CAAC;QACpD,IAAIC;QACJ,KAAK,MAAMC,WAAWH,mBAAoB;YACxC,IAAIG,QAAQ1I,UAAU,CAACG,WAAW,MAAM;gBACtC,MAAMwI,OAAOD,QAAQzI,KAAK,CAACE,SAASwC,MAAM,GAAG,GAAGzB,KAAK,CAAC;gBAEtD,4CAA4C;gBAC5C,IAAIyH,KAAKhG,MAAM,KAAK,KAAKgG,IAAI,CAAC,EAAE,KAAK,QAAQ;oBAC3CF,uBAAuBC;oBACvBF,QAAQI,QAAQ,GAAGpJ;oBACnB;gBACF;gBAEA,MAAMqJ,kBAAkBF,IAAI,CAAC,EAAE,CAAC3I,UAAU,CAAC;gBAC3C,IAAI6I,iBAAiB;oBACnB,IAAIF,KAAKhG,MAAM,KAAK,KAAKgG,IAAI,CAAC,EAAE,KAAK,QAAQ;wBAC3C,oGAAoG;wBACpG,+HAA+H;wBAC/H,8GAA8G;wBAC9GH,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG;4BAACnJ;yBAAa;wBACjC;oBACF;oBACA,iHAAiH;oBACjH,4HAA4H;oBAC5H,0BAA0B;oBAC1BgJ,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG;wBAAClJ;2BAA6BkJ,KAAK1I,KAAK,CAAC;qBAAG;oBAC/D;gBACF;gBAEA,IAAIwI,wBAAwBD,QAAQI,QAAQ,KAAKD,IAAI,CAAC,EAAE,EAAE;oBACxD,gFAAgF;oBAChF,wEAAwE;oBACxE,MAAMG,yBAAyBJ,QAAQK,QAAQ,CAAC;oBAChD,MAAMC,yBAAyBP,qBAAqBM,QAAQ,CAAC;oBAE7D,IAAID,wBAAwB;wBAQ1B;oBACF,OAAO,IAAI,CAACE,0BAA0B,CAACF,wBAAwB;wBAC7D,6EAA6E;wBAC7E,MAAM,qBAEL,CAFK,IAAItF,MACR,CAAC,+EAA+E,EAAEiF,qBAAqB,KAAK,EAAEC,QAAQ,gIAAgI,CAAC,GADnP,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAD,uBAAuBC;gBACvBF,QAAQI,QAAQ,GAAGD,IAAI,CAAC,EAAE;YAC5B;QACF;QAEA,OAAO1E,OAAOY,OAAO,CAAC2D;IACxB;IAEA,MAAM/H,aAA0B,CAACwI;QAC/B,OAAOtC,mBAAmBC,QAAQqC;IACpC;IAEA,MAAMC,kBAAgC,CAACD;QACrC,OAAOtC,mBAAmBC,QAAQqC;IACpC;IAEA,+DAA+D;IAC/D,0EAA0E;IAC1E,+EAA+E;IAC/E,yEAAyE;IACzE,MAAME,wBAAwB,OAAOC,SAAiBC;QACpD,gGAAgG;QAChG,IAAI,IAAI,CAACC,YAAY,KAAKtE,WAAW;YACnC,IAAI;gBACF,OAAO,AAAC,CAAA,MAAMtG,cAAc0K,SAASG,KAAK,CAAC,IAAM,IAAIrE,MAAK,EAAGQ,GAAG,CAC9D2D;YAEJ,EAAE,OAAOG,GAAG;gBACV,OAAO;YACT;QACF;QACA,MAAMrF,MACJ6C,iBAAiByC,GAAG,CAAC,IAAI,CAACH,YAAY,KACtC,IAAI/E;QACN,IAAI,CAACyC,iBAAiBtB,GAAG,CAAC,IAAI,CAAC4D,YAAY,GAAG;YAC5CtC,iBAAiBvC,GAAG,CAAC,IAAI,CAAC6E,YAAY,EAAEnF;QAC1C;QACA,IAAI,CAACA,IAAIuB,GAAG,CAAC0D,UAAU;YACrBjF,IAAIM,GAAG,CACL2E,SACA1K,cAAc0K,SAASG,KAAK,CAAC,IAAM,IAAIrE;QAE3C;QACA,OAAO,AAAC,CAAA,AAAC,MAAMf,IAAIsF,GAAG,CAACL,YAAa,IAAIlE,KAAI,EAAGQ,GAAG,CAAC2D;IACrD;IAEA,MAAM3I,WAAyB,OAAOP;QACpC,MAAMuJ,eAAe/C,mBAAmBC,QAAQzG;QAEhD,MAAMwJ,gBAAgBD,aAAaE,WAAW,CAACnM,KAAKsJ,GAAG;QACvD,MAAMqC,UAAUM,aAAazJ,KAAK,CAAC,GAAG0J;QACtC,MAAME,WAAWH,aAAazJ,KAAK,CAAC0J,gBAAgB;QAEpD,IAAIG;QAEJ,KAAK,MAAMC,OAAO1B,WAAY;YAC5B,MAAM2B,4BAA4B,GAAGN,eAAeK,KAAK;YACzD,IACE,CAACD,UACA,MAAMX,sBAAsBC,SAAS,GAAGS,WAAWE,KAAK,GACzD;gBACAD,SAASE;YACX;YACA,uEAAuE;YACvE,6DAA6D;YAC7D,IAAI,CAACC,oBAAoB,CAACD;QAC5B;QAEA,OAAOF;IACT;IAEA,MAAMlJ,mBAAqC,OACzCwI,SACAS,UACAK;QAEA,MAAMC,cAAcxD,mBAAmBC,QAAQwC;QAE/C,IAAIU;QAEJ,KAAK,MAAMC,OAAOG,KAAM;YACtB,kGAAkG;YAClG,MAAME,kBAAkB,GAAGP,SAAS,CAAC,EAAEE,KAAK;YAC5C,MAAMC,4BAA4B,GAAGG,cAAc1M,KAAKsJ,GAAG,GAAGqD,iBAAiB;YAC/E,IAAI,CAACN,UAAW,MAAMX,sBAAsBC,SAASgB,kBAAmB;gBACtEN,SAASE;YACX;YACA,uEAAuE;YACvE,6DAA6D;YAC7D,IAAI,CAACC,oBAAoB,CAACD;QAC5B;QAEA,OAAOF;IACT;IAEA,IAAI3L,gBAAgB+D,OAAO;QACzB,OAAOtD,mBAAmB;YACxBgI;YACA,8EAA8E;YAC9EpG,MAAM2G,cAAc3G,IAAI;YACxB0B;YACA3B;YACA2I;YACArI;YACA4G;QACF;IACF;IAEA,IAAI4C,iBAAiB,MAAM/J,uBAAuBC,UAAU;QAC1DC;QACAC;QACAC;QACAE;QACAD;QACA2J,eAAe,IAAI;QACnBzJ;QACAC;QACAC;QACAC;IACF;IAEA,MAAMuJ,uBACJ/J,SAAShD,oCACT,CAAC,CAAC6M,eAAe5I,cAAc,IAC/BT;IAEF,IAAI,CAACqJ,eAAe9I,UAAU,IAAI,CAACgJ,sBAAsB;QACvD,IAAI,CAAC/C,OAAO;YACV,8DAA8D;YAC9D3J,IAAIuB,KAAK,CACP,GAAG1B,KACD6C,SAASuG,OAAO,CAAC,GAAGhJ,cAAc,CAAC,CAAC,EAAE,KACtC,uFAAuF,CAAC;YAE5F0M,QAAQC,IAAI,CAAC;QACf,OAAO;gBA2BkBzD;YA1BvB,2CAA2C;YAC3C,MAAM,CAAC0D,mBAAmBC,eAAe,GAAG,MAAM/M,iBAAiB;gBACjEgJ,QAAQA;gBACRgE,KAAKtD;gBACLC,cAAcA;gBACdhH;gBACAM;YACF;YACA,IAAI,CAAC6J,mBAAmB;gBACtB,IAAIG,UAAU,GAAGnN,KACf6C,SAASuG,OAAO,CAAC,GAAGhJ,cAAc,CAAC,CAAC,EAAE,KACtC,6BAA6B,CAAC;gBAEhC,IAAI6M,gBAAgB;wBAEF;oBADhBE,WAAW,CAAC,mBAAmB,EAAEnN,KAC/BD,KAAKqN,QAAQ,CAAC,EAAA,kBAAA,IAAI,CAACC,SAAS,qBAAd,gBAAgBC,OAAO,KAAI,IAAIL,iBAC7C,kCAAkC,CAAC;gBACvC,OAAO;oBACLE,WACE;gBACJ;gBAEA,MAAM,qBAAkB,CAAlB,IAAIrH,MAAMqH,UAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiB;YACzB;YAEA,mEAAmE;YACnE,IAAI,IAAI,CAACvB,YAAY,GAAEtC,wBAAAA,iBAAiByC,GAAG,CAAC,IAAI,CAACH,YAAY,sBAAtCtC,sBAAyCiE,KAAK;YACrEZ,iBAAiB,MAAM/J,uBAAuBC,UAAU;gBACtDC;gBACAC;gBACAC;gBACAE;gBACAD;gBACA2J,eAAe,IAAI;gBACnBzJ;gBACAC;gBACAC;gBACAC;YACF;QACF;IACF;IAEA,MAAMb,WAAW,IAAI/B,wBAAwB8M,SAAS,CAAC1K;IAEvD,iGAAiG;IACjG,6GAA6G;IAC7G,MAAM2K,OAAO,MAAM7M,eACjB,YACA;QACE8M,qBAAqB5K;QACrB6K,yBAAyBlL;QACzBmL,yBAAyBjB,eAAe7I,WAAW;IACrD,GACA;QACE+J,MAAMlB,eAAejE,QAAQ;QAC7B9E,OAAO+I,eAAe/I,KAAK;QAC3BkK,sBAAsB;QACtB,0FAA0F;QAC1FC,yBAAyB;IAC3B;IAGF,6DAA6D;IAC7D,MAAMC,SAAS3K,sBACZoD,GAAG,CAAC,CAAC,CAACd,SAASsI,WAAW;QACzB,OAAO,CAAC,MAAM,EAAEtI,QAAQ,2CAA2C,EAAEC,KAAKC,SAAS,CACjFoI,YACA,IAAI,CAAC;IACT,GACCnJ,IAAI,CAAC;IAER,OAAOkJ,SAASP;AAClB;AAEA,eAAejE,cAAa", "ignoreList": [0]}