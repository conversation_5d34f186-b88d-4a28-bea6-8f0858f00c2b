{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "sourcesContent": ["import type {\n  CssImports,\n  ClientComponentImports,\n} from '../loaders/next-flight-client-entry-loader'\n\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { parse, stringify } from 'querystring'\nimport path from 'path'\nimport { sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getInvalidator,\n  getEntries,\n  EntryTypes,\n  getEntryKey,\n} from '../../../server/dev/on-demand-entry-handler'\nimport {\n  WEBPACK_LAYERS,\n  WEBPACK_RESOURCE_QUERIES,\n} from '../../../lib/constants'\nimport {\n  APP_CLIENT_INTERNALS,\n  BARREL_OPTIMIZATION_PREFIX,\n  COMPILER_NAMES,\n  DEFAULT_RUNTIME_WEBPACK,\n  EDGE_RUNTIME_WEBPACK,\n  SERVER_REFERENCE_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../../../shared/lib/constants'\nimport {\n  isClientComponentEntryModule,\n  isCSSMod,\n  regexCSS,\n} from '../loaders/utils'\nimport {\n  traverseModules,\n  forEachEntryModule,\n  formatBarrelOptimizedResource,\n  getModuleReferencesInOrder,\n} from '../utils'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { getProxiedPluginState } from '../../build-context'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getAssumedSourceType } from '../loaders/next-flight-loader'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\nimport {\n  DEFAULT_METADATA_ROUTE_EXTENSIONS,\n  isMetadataRouteFile,\n} from '../../../lib/metadata/is-metadata-route'\nimport type { MetadataRouteLoaderOptions } from '../loaders/next-metadata-route-loader'\nimport type { FlightActionEntryLoaderActions } from '../loaders/next-flight-action-entry-loader'\nimport getWebpackBundler from '../../../shared/lib/get-webpack-bundler'\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  isEdgeServer: boolean\n  encryptionKey: string\n}\n\nconst PLUGIN_NAME = 'FlightClientEntryPlugin'\n\ntype Actions = {\n  [actionId: string]: {\n    exportedName?: string\n    filename?: string\n    workers: {\n      [name: string]: {\n        moduleId: string | number\n        async: boolean\n      }\n    }\n    // Record which layer the action is in (rsc or sc_action), in the specific entry.\n    layer: {\n      [name: string]: string\n    }\n  }\n}\n\ntype ActionIdNamePair = { id: string; exportedName?: string; filename?: string }\n\nexport type ActionManifest = {\n  // Assign a unique encryption key during production build.\n  encryptionKey: string\n  node: Actions\n  edge: Actions\n}\n\nexport interface ModuleInfo {\n  moduleId: string | number\n  async: boolean\n}\n\nconst pluginState = getProxiedPluginState({\n  // A map to track \"action\" -> \"list of bundles\".\n  serverActions: {} as ActionManifest['node'],\n  edgeServerActions: {} as ActionManifest['edge'],\n\n  serverActionModules: {} as {\n    [workerName: string]: {\n      server?: ModuleInfo\n      client?: ModuleInfo\n    }\n  },\n\n  edgeServerActionModules: {} as {\n    [workerName: string]: {\n      server?: ModuleInfo\n      client?: ModuleInfo\n    }\n  },\n\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n\n  injectedClientEntries: {} as Record<string, string>,\n})\n\nconst POSSIBLE_SHARED_CONVENTIONS = ['template', 'layout']\nconst STANDALONE_BUNDLE_CONVENTION = 'global-not-found'\n\nfunction deduplicateCSSImportsForEntry(mergedCSSimports: CssImports) {\n  // If multiple entry module connections are having the same CSS import,\n  // we only need to have one module to keep track of that CSS import.\n  // It is based on the fact that if a page or a layout is rendered in the\n  // given entry, all its parent layouts are always rendered too.\n  // This can avoid duplicate CSS imports in the generated CSS manifest,\n  // for example, if a page and its parent layout are both using the same\n  // CSS import, we only need to have the layout to keep track of that CSS\n  // import.\n  // To achieve this, we need to first collect all the CSS imports from\n  // every connection, and deduplicate them in the order of layers from\n  // top to bottom. The implementation can be generally described as:\n  // - Sort by number of `/` in the request path (the more `/`, the deeper)\n  // - When in the same depth, sort by the filename (template < layout < page and others)\n\n  // Sort the connections as described above.\n  const sortedCSSImports = Object.entries(mergedCSSimports).sort((a, b) => {\n    const [aPath] = a\n    const [bPath] = b\n\n    const aDepth = aPath.split('/').length\n    const bDepth = bPath.split('/').length\n\n    if (aDepth !== bDepth) {\n      return aDepth - bDepth\n    }\n\n    const aName = path.parse(aPath).name\n    const bName = path.parse(bPath).name\n\n    const indexA = POSSIBLE_SHARED_CONVENTIONS.indexOf(aName)\n    const indexB = POSSIBLE_SHARED_CONVENTIONS.indexOf(bName)\n\n    if (indexA === -1) return 1\n    if (indexB === -1) return -1\n    return indexA - indexB\n  })\n\n  const dedupedCSSImports: CssImports = {}\n  const trackedCSSImports = new Set<string>()\n\n  for (const [entryFilePath, cssImports] of sortedCSSImports) {\n    const entryConventionName = path.parse(entryFilePath).name\n\n    for (const cssImport of cssImports) {\n      // If the CSS import is already tracked, we can skip it.\n      // Or if it's any standalone entry such as `global-not-found`, it won't share any resources with other entry, skip it.\n      if (\n        trackedCSSImports.has(cssImport) &&\n        STANDALONE_BUNDLE_CONVENTION !== entryConventionName\n      ) {\n        continue\n      }\n\n      // Only track CSS imports that are in files that can inherit CSS.\n      if (POSSIBLE_SHARED_CONVENTIONS.includes(entryConventionName)) {\n        trackedCSSImports.add(cssImport)\n      }\n\n      if (!dedupedCSSImports[entryFilePath]) {\n        dedupedCSSImports[entryFilePath] = []\n      }\n      dedupedCSSImports[entryFilePath].push(cssImport)\n    }\n  }\n\n  return dedupedCSSImports\n}\n\nexport class FlightClientEntryPlugin {\n  dev: boolean\n  appDir: string\n  projectDir: string\n  encryptionKey: string\n  isEdgeServer: boolean\n  assetPrefix: string\n  webpackRuntime: string\n\n  constructor(options: Options) {\n    this.dev = options.dev\n    this.appDir = options.appDir\n    this.projectDir = path.join(options.appDir, '..')\n    this.isEdgeServer = options.isEdgeServer\n    this.assetPrefix = !this.dev && !this.isEdgeServer ? '../' : ''\n    this.encryptionKey = options.encryptionKey\n    this.webpackRuntime = this.isEdgeServer\n      ? EDGE_RUNTIME_WEBPACK\n      : DEFAULT_RUNTIME_WEBPACK\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.finishMake.tapPromise(PLUGIN_NAME, (compilation) =>\n      this.createClientEntries(compiler, compilation)\n    )\n\n    compiler.hooks.afterCompile.tap(PLUGIN_NAME, (compilation) => {\n      const recordModule = (modId: string, mod: any) => {\n        // Match Resource is undefined unless an import is using the inline match resource syntax\n        // https://webpack.js.org/api/loaders/#inline-matchresource\n        const modPath = mod.matchResource || mod.resourceResolveData?.path\n        const modQuery = mod.resourceResolveData?.query || ''\n        // query is already part of mod.resource\n        // so it's only necessary to add it for matchResource or mod.resourceResolveData\n        const modResource = modPath\n          ? modPath.startsWith(BARREL_OPTIMIZATION_PREFIX)\n            ? formatBarrelOptimizedResource(mod.resource, modPath)\n            : modPath + modQuery\n          : mod.resource\n\n        if (typeof modId !== 'undefined' && modResource) {\n          if (mod.layer === WEBPACK_LAYERS.reactServerComponents) {\n            const key = path\n              .relative(compiler.context, modResource)\n              .replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n\n            const moduleInfo: ModuleInfo = {\n              moduleId: modId,\n              async: compilation.moduleGraph.isAsync(mod),\n            }\n\n            if (this.isEdgeServer) {\n              pluginState.edgeRscModules[key] = moduleInfo\n            } else {\n              pluginState.rscModules[key] = moduleInfo\n            }\n          }\n        }\n\n        if (mod.layer !== WEBPACK_LAYERS.serverSideRendering) {\n          return\n        }\n\n        // Check mod resource to exclude the empty resource module like virtual module created by next-flight-client-entry-loader\n        if (typeof modId !== 'undefined' && modResource) {\n          // Note that this isn't that reliable as webpack is still possible to assign\n          // additional queries to make sure there's no conflict even using the `named`\n          // module ID strategy.\n          let ssrNamedModuleId = path.relative(compiler.context, modResource)\n\n          if (!ssrNamedModuleId.startsWith('.')) {\n            // TODO use getModuleId instead\n            ssrNamedModuleId = `./${normalizePathSep(ssrNamedModuleId)}`\n          }\n\n          const moduleInfo: ModuleInfo = {\n            moduleId: modId,\n            async: compilation.moduleGraph.isAsync(mod),\n          }\n\n          if (this.isEdgeServer) {\n            pluginState.edgeSsrModules[\n              ssrNamedModuleId.replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n            ] = moduleInfo\n          } else {\n            pluginState.ssrModules[ssrNamedModuleId] = moduleInfo\n          }\n        }\n      }\n\n      traverseModules(compilation, (mod, _chunk, _chunkGroup, modId) => {\n        if (modId) recordModule(modId, mod)\n      })\n    })\n\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        () => this.createActionAssets(compilation)\n      )\n    })\n  }\n\n  async createClientEntries(\n    compiler: webpack.Compiler,\n    compilation: webpack.Compilation\n  ) {\n    const addClientEntryAndSSRModulesList: Array<\n      ReturnType<typeof this.injectClientEntryAndSSRModules>\n    > = []\n    const createdSSRDependenciesForEntry: Record<\n      string,\n      ReturnType<typeof this.injectClientEntryAndSSRModules>[3][]\n    > = {}\n\n    const addActionEntryList: Array<ReturnType<typeof this.injectActionEntry>> =\n      []\n    const actionMapsPerEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n    const createdActionIds = new Set<string>()\n\n    // For each SC server compilation entry, we need to create its corresponding\n    // client component entry.\n    forEachEntryModule(compilation, ({ name, entryModule }) => {\n      const internalClientComponentEntryImports: ClientComponentImports = {}\n      const actionEntryImports = new Map<string, ActionIdNamePair[]>()\n      const clientEntriesToInject = []\n      const mergedCSSimports: CssImports = {}\n\n      for (const connection of getModuleReferencesInOrder(\n        entryModule,\n        compilation.moduleGraph\n      )) {\n        // Entry can be any user defined entry files such as layout, page, error, loading, etc.\n        let entryRequest = (\n          connection.dependency as unknown as webpack.NormalModule\n        ).request\n\n        if (entryRequest.endsWith(WEBPACK_RESOURCE_QUERIES.metadataRoute)) {\n          const { filePath, isDynamicRouteExtension } =\n            getMetadataRouteResource(entryRequest)\n\n          if (isDynamicRouteExtension === '1') {\n            entryRequest = filePath\n          }\n        }\n\n        const { clientComponentImports, actionImports, cssImports } =\n          this.collectComponentInfoFromServerEntryDependency({\n            entryRequest,\n            compilation,\n            resolvedModule: connection.resolvedModule,\n          })\n\n        actionImports.forEach(([dep, actions]) =>\n          actionEntryImports.set(dep, actions)\n        )\n\n        const isAbsoluteRequest = path.isAbsolute(entryRequest)\n\n        // Next.js internals are put into a separate entry.\n        if (!isAbsoluteRequest) {\n          Object.keys(clientComponentImports).forEach(\n            (value) => (internalClientComponentEntryImports[value] = new Set())\n          )\n          continue\n        }\n\n        // TODO-APP: Enable these lines. This ensures no entrypoint is created for layout/page when there are no client components.\n        // Currently disabled because it causes test failures in CI.\n        // if (clientImports.length === 0 && actionImports.length === 0) {\n        //   continue\n        // }\n\n        const relativeRequest = isAbsoluteRequest\n          ? path.relative(compilation.options.context!, entryRequest)\n          : entryRequest\n\n        // Replace file suffix as `.js` will be added.\n        // bundlePath will have app/ prefix but not src/.\n        // e.g. src/app/foo/page.js -> app/foo/page\n        let bundlePath = normalizePathSep(\n          relativeRequest.replace(/\\.[^.\\\\/]+$/, '').replace(/^src[\\\\/]/, '')\n        )\n\n        // For metadata routes, the entry name can be used as the bundle path,\n        // as it has been normalized already.\n        // e.g.\n        // When `relativeRequest` is 'src/app/sitemap.js',\n        // `appDirRelativeRequest` will be '/sitemap.js'\n        // then `isMetadataEntryFile` will be `true`\n        const appDirRelativeRequest = relativeRequest\n          .replace(/^src[\\\\/]/, '')\n          .replace(/^app[\\\\/]/, '/')\n        const isMetadataEntryFile = isMetadataRouteFile(\n          appDirRelativeRequest,\n          DEFAULT_METADATA_ROUTE_EXTENSIONS,\n          true\n        )\n        if (isMetadataEntryFile) {\n          bundlePath = name\n        }\n\n        Object.assign(mergedCSSimports, cssImports)\n        clientEntriesToInject.push({\n          compiler,\n          compilation,\n          entryName: name,\n          clientComponentImports,\n          bundlePath,\n          absolutePagePath: entryRequest,\n        })\n\n        // The webpack implementation of writing the client reference manifest relies on all entrypoints writing a page.js even when there is no client components in the page.\n        // It needs the file in order to write the reference manifest for the path in the `.next/server` folder.\n        // TODO-APP: This could be better handled, however Turbopack does not have the same problem as we resolve client components in a single graph.\n        if (\n          name === `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}` &&\n          bundlePath === 'app/not-found'\n        ) {\n          clientEntriesToInject.push({\n            compiler,\n            compilation,\n            entryName: name,\n            clientComponentImports: {},\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            absolutePagePath: entryRequest,\n          })\n        }\n\n        if (\n          name === `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}` &&\n          bundlePath === 'app/global-not-found'\n        ) {\n          clientEntriesToInject.push({\n            compiler,\n            compilation,\n            entryName: name,\n            clientComponentImports,\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            absolutePagePath: entryRequest,\n          })\n        }\n      }\n\n      // Make sure CSS imports are deduplicated before injecting the client entry\n      // and SSR modules.\n      const dedupedCSSImports = deduplicateCSSImportsForEntry(mergedCSSimports)\n      for (const clientEntryToInject of clientEntriesToInject) {\n        const injected = this.injectClientEntryAndSSRModules({\n          ...clientEntryToInject,\n          clientImports: {\n            ...clientEntryToInject.clientComponentImports,\n            ...(\n              dedupedCSSImports[clientEntryToInject.absolutePagePath] || []\n            ).reduce<ClientComponentImports>((res, curr) => {\n              res[curr] = new Set()\n              return res\n            }, {}),\n          },\n        })\n\n        // Track all created SSR dependencies for each entry from the server layer.\n        if (!createdSSRDependenciesForEntry[clientEntryToInject.entryName]) {\n          createdSSRDependenciesForEntry[clientEntryToInject.entryName] = []\n        }\n        createdSSRDependenciesForEntry[clientEntryToInject.entryName].push(\n          injected[3]\n        )\n\n        addClientEntryAndSSRModulesList.push(injected)\n      }\n\n      if (!isAppRouteRoute(name)) {\n        // Create internal app\n        addClientEntryAndSSRModulesList.push(\n          this.injectClientEntryAndSSRModules({\n            compiler,\n            compilation,\n            entryName: name,\n            clientImports: { ...internalClientComponentEntryImports },\n            bundlePath: APP_CLIENT_INTERNALS,\n          })\n        )\n      }\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerEntry[name]) {\n          actionMapsPerEntry[name] = new Map()\n        }\n        actionMapsPerEntry[name] = new Map([\n          ...actionMapsPerEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    })\n\n    for (const [name, actionEntryImports] of Object.entries(\n      actionMapsPerEntry\n    )) {\n      addActionEntryList.push(\n        this.injectActionEntry({\n          compiler,\n          compilation,\n          actions: actionEntryImports,\n          entryName: name,\n          bundlePath: name,\n          createdActionIds,\n        })\n      )\n    }\n\n    // Invalidate in development to trigger recompilation\n    const invalidator = getInvalidator(compiler.outputPath)\n    // Check if any of the entry injections need an invalidation\n    if (\n      invalidator &&\n      addClientEntryAndSSRModulesList.some(\n        ([shouldInvalidate]) => shouldInvalidate === true\n      )\n    ) {\n      invalidator.invalidate([COMPILER_NAMES.client])\n    }\n\n    // Client compiler is invalidated before awaiting the compilation of the SSR\n    // and RSC client component entries so that the client compiler is running\n    // in parallel to the server compiler.\n    await Promise.all(\n      addClientEntryAndSSRModulesList.flatMap((addClientEntryAndSSRModules) => [\n        addClientEntryAndSSRModules[1],\n        addClientEntryAndSSRModules[2],\n      ])\n    )\n\n    // Wait for action entries to be added.\n    await Promise.all(addActionEntryList)\n\n    const addedClientActionEntryList: Promise<any>[] = []\n    const actionMapsPerClientEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n\n    // We need to create extra action entries that are created from the\n    // client layer.\n    // Start from each entry's created SSR dependency from our previous step.\n    for (const [name, ssrEntryDependencies] of Object.entries(\n      createdSSRDependenciesForEntry\n    )) {\n      // Collect from all entries, e.g. layout.js, page.js, loading.js, ...\n      // add aggregate them.\n      const actionEntryImports = this.collectClientActionsFromDependencies({\n        compilation,\n        dependencies: ssrEntryDependencies,\n      })\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerClientEntry[name]) {\n          actionMapsPerClientEntry[name] = new Map()\n        }\n        actionMapsPerClientEntry[name] = new Map([\n          ...actionMapsPerClientEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    }\n\n    for (const [entryName, actionEntryImports] of Object.entries(\n      actionMapsPerClientEntry\n    )) {\n      // If an action method is already created in the server layer, we don't\n      // need to create it again in the action layer.\n      // This is to avoid duplicate action instances and make sure the module\n      // state is shared.\n      let remainingClientImportedActions = false\n      const remainingActionEntryImports = new Map<string, ActionIdNamePair[]>()\n      for (const [dep, actions] of actionEntryImports) {\n        const remainingActionNames = []\n        for (const action of actions) {\n          if (!createdActionIds.has(entryName + '@' + action.id)) {\n            remainingActionNames.push(action)\n          }\n        }\n        if (remainingActionNames.length > 0) {\n          remainingActionEntryImports.set(dep, remainingActionNames)\n          remainingClientImportedActions = true\n        }\n      }\n\n      if (remainingClientImportedActions) {\n        addedClientActionEntryList.push(\n          this.injectActionEntry({\n            compiler,\n            compilation,\n            actions: remainingActionEntryImports,\n            entryName,\n            bundlePath: entryName,\n            fromClient: true,\n            createdActionIds,\n          })\n        )\n      }\n    }\n\n    await Promise.all(addedClientActionEntryList)\n  }\n\n  collectClientActionsFromDependencies({\n    compilation,\n    dependencies,\n  }: {\n    compilation: webpack.Compilation\n    dependencies: ReturnType<typeof webpack.EntryPlugin.createDependency>[]\n  }) {\n    // action file path -> action names\n    const collectedActions = new Map<string, ActionIdNamePair[]>()\n\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedModule = new Set<string>()\n    const visitedEntry = new Set<string>()\n\n    const collectActions = ({\n      entryRequest,\n      resolvedModule,\n    }: {\n      entryRequest: string\n      resolvedModule: any\n    }) => {\n      const collectActionsInDep = (mod: webpack.NormalModule): void => {\n        if (!mod) return\n\n        const modResource = getModuleResource(mod)\n\n        if (!modResource) return\n\n        if (visitedModule.has(modResource)) return\n        visitedModule.add(modResource)\n\n        const actionIds = getModuleBuildInfo(mod).rsc?.actionIds\n        if (actionIds) {\n          collectedActions.set(\n            modResource,\n            Object.entries(actionIds).map(([id, exportedName]) => ({\n              id,\n              exportedName,\n              filename: path.posix.relative(this.projectDir, modResource),\n            }))\n          )\n        }\n\n        // Collect used exported actions transversely.\n        getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n          (connection: any) => {\n            collectActionsInDep(\n              connection.resolvedModule as webpack.NormalModule\n            )\n          }\n        )\n      }\n\n      // Don't traverse the module graph anymore once hitting the action layer.\n      if (\n        entryRequest &&\n        !entryRequest.includes('next-flight-action-entry-loader')\n      ) {\n        // Traverse the module graph to find all client components.\n        collectActionsInDep(resolvedModule)\n      }\n    }\n\n    for (const entryDependency of dependencies) {\n      const ssrEntryModule =\n        compilation.moduleGraph.getResolvedModule(entryDependency)!\n      for (const connection of getModuleReferencesInOrder(\n        ssrEntryModule,\n        compilation.moduleGraph\n      )) {\n        const depModule = connection.dependency\n        const request = (depModule as unknown as webpack.NormalModule).request\n\n        // It is possible that the same entry is added multiple times in the\n        // connection graph. We can just skip these to speed up the process.\n        if (visitedEntry.has(request)) continue\n        visitedEntry.add(request)\n\n        collectActions({\n          entryRequest: request,\n          resolvedModule: connection.resolvedModule,\n        })\n      }\n    }\n\n    return collectedActions\n  }\n\n  collectComponentInfoFromServerEntryDependency({\n    entryRequest,\n    compilation,\n    resolvedModule,\n  }: {\n    entryRequest: string\n    compilation: webpack.Compilation\n    resolvedModule: any /* Dependency */\n  }): {\n    cssImports: CssImports\n    clientComponentImports: ClientComponentImports\n    actionImports: [string, ActionIdNamePair[]][]\n  } {\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedOfClientComponentsTraverse = new Set()\n\n    // Info to collect.\n    const clientComponentImports: ClientComponentImports = {}\n    const actionImports: [string, ActionIdNamePair[]][] = []\n    const CSSImports = new Set<string>()\n\n    const filterClientComponents = (\n      mod: webpack.NormalModule,\n      importedIdentifiers: string[]\n    ): void => {\n      if (!mod) return\n\n      const modResource = getModuleResource(mod)\n\n      if (!modResource) return\n      if (visitedOfClientComponentsTraverse.has(modResource)) {\n        if (clientComponentImports[modResource]) {\n          addClientImport(\n            mod,\n            modResource,\n            clientComponentImports,\n            importedIdentifiers,\n            false\n          )\n        }\n        return\n      }\n      visitedOfClientComponentsTraverse.add(modResource)\n\n      const actionIds = getModuleBuildInfo(mod).rsc?.actionIds\n      if (actionIds) {\n        actionImports.push([\n          modResource,\n          Object.entries(actionIds).map(([id, exportedName]) => ({\n            id,\n            exportedName,\n            filename: path.posix.relative(this.projectDir, modResource),\n          })),\n        ])\n      }\n\n      if (isCSSMod(mod)) {\n        const sideEffectFree =\n          mod.factoryMeta && (mod.factoryMeta as any).sideEffectFree\n\n        if (sideEffectFree) {\n          const unused = !compilation.moduleGraph\n            .getExportsInfo(mod)\n            .isModuleUsed(this.webpackRuntime)\n\n          if (unused) return\n        }\n\n        CSSImports.add(modResource)\n      } else if (isClientComponentEntryModule(mod)) {\n        if (!clientComponentImports[modResource]) {\n          clientComponentImports[modResource] = new Set()\n        }\n        addClientImport(\n          mod,\n          modResource,\n          clientComponentImports,\n          importedIdentifiers,\n          true\n        )\n\n        return\n      }\n\n      getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n        (connection: any) => {\n          let dependencyIds: string[] = []\n\n          // `ids` are the identifiers that are imported from the dependency,\n          // if it's present, it's an array of strings.\n          if (connection.dependency?.ids) {\n            dependencyIds.push(...connection.dependency.ids)\n          } else {\n            dependencyIds = ['*']\n          }\n\n          filterClientComponents(connection.resolvedModule, dependencyIds)\n        }\n      )\n    }\n\n    // Traverse the module graph to find all client components.\n    filterClientComponents(resolvedModule, [])\n\n    return {\n      clientComponentImports,\n      cssImports: CSSImports.size\n        ? {\n            [entryRequest]: Array.from(CSSImports),\n          }\n        : {},\n      actionImports,\n    }\n  }\n\n  injectClientEntryAndSSRModules({\n    compiler,\n    compilation,\n    entryName,\n    clientImports,\n    bundlePath,\n    absolutePagePath,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    entryName: string\n    clientImports: ClientComponentImports\n    bundlePath: string\n    absolutePagePath?: string\n  }): [\n    shouldInvalidate: boolean,\n    addSSREntryPromise: Promise<void>,\n    addRSCEntryPromise: Promise<void>,\n    ssrDep: ReturnType<typeof webpack.EntryPlugin.createDependency>,\n  ] {\n    const bundler = getWebpackBundler()\n    let shouldInvalidate = false\n\n    const modules = Object.keys(clientImports)\n      .sort((a, b) => (regexCSS.test(b) ? 1 : a.localeCompare(b)))\n      .map((clientImportPath) => ({\n        request: clientImportPath,\n        ids: [...clientImports[clientImportPath]],\n      }))\n\n    // For the client entry, we always use the CJS build of Next.js. If the\n    // server is using the ESM build (when using the Edge runtime), we need to\n    // replace them.\n    const clientBrowserLoader = `next-flight-client-entry-loader?${stringify({\n      modules: (this.isEdgeServer\n        ? modules.map(({ request, ids }) => ({\n            request: request.replace(\n              /[\\\\/]next[\\\\/]dist[\\\\/]esm[\\\\/]/,\n              '/next/dist/'.replace(/\\//g, path.sep)\n            ),\n            ids,\n          }))\n        : modules\n      ).map((x) => JSON.stringify(x)),\n      server: false,\n    })}!`\n\n    const clientServerLoader = `next-flight-client-entry-loader?${stringify({\n      modules: modules.map((x) => JSON.stringify(x)),\n      server: true,\n    })}!`\n\n    // Add for the client compilation\n    // Inject the entry to the client compiler.\n    if (this.dev) {\n      const entries = getEntries(compiler.outputPath)\n      const pageKey = getEntryKey(\n        COMPILER_NAMES.client,\n        PAGE_TYPES.APP,\n        bundlePath\n      )\n\n      if (!entries[pageKey]) {\n        entries[pageKey] = {\n          type: EntryTypes.CHILD_ENTRY,\n          parentEntries: new Set([entryName]),\n          absoluteEntryFilePath: absolutePagePath,\n          bundlePath,\n          request: clientBrowserLoader,\n          dispose: false,\n          lastActiveTime: Date.now(),\n        }\n        shouldInvalidate = true\n      } else {\n        const entryData = entries[pageKey]\n        // New version of the client loader\n        if (entryData.request !== clientBrowserLoader) {\n          entryData.request = clientBrowserLoader\n          shouldInvalidate = true\n        }\n        if (entryData.type === EntryTypes.CHILD_ENTRY) {\n          entryData.parentEntries.add(entryName)\n        }\n        entryData.dispose = false\n        entryData.lastActiveTime = Date.now()\n      }\n    } else {\n      pluginState.injectedClientEntries[bundlePath] = clientBrowserLoader\n    }\n\n    const clientComponentSSREntryDep = bundler.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    const clientComponentRSCEntryDep = bundler.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    return [\n      shouldInvalidate,\n      // Add the entries to the server compiler for the SSR and RSC layers. The\n      // promises are awaited later using `Promise.all` in order to parallelize\n      // adding the entries.\n      this.addEntry(compilation, compiler.context, clientComponentSSREntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.serverSideRendering,\n      }),\n      this.addEntry(compilation, compiler.context, clientComponentRSCEntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.reactServerComponents,\n      }),\n      clientComponentSSREntryDep,\n    ]\n  }\n\n  injectActionEntry({\n    compiler,\n    compilation,\n    actions,\n    entryName,\n    bundlePath,\n    fromClient,\n    createdActionIds,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    actions: Map<string, ActionIdNamePair[]>\n    entryName: string\n    bundlePath: string\n    createdActionIds: Set<string>\n    fromClient?: boolean\n  }) {\n    const bundler = getWebpackBundler()\n    const actionsArray = Array.from(actions.entries())\n    for (const [, actionsFromModule] of actions) {\n      for (const { id } of actionsFromModule) {\n        createdActionIds.add(entryName + '@' + id)\n      }\n    }\n\n    if (actionsArray.length === 0) {\n      return Promise.resolve()\n    }\n\n    const actionLoader = `next-flight-action-entry-loader?${stringify({\n      actions: JSON.stringify(\n        actionsArray satisfies FlightActionEntryLoaderActions\n      ),\n      __client_imported__: fromClient,\n    })}!`\n\n    const currentCompilerServerActions = this.isEdgeServer\n      ? pluginState.edgeServerActions\n      : pluginState.serverActions\n\n    for (const [, actionsFromModule] of actionsArray) {\n      for (const { id, exportedName, filename } of actionsFromModule) {\n        if (typeof currentCompilerServerActions[id] === 'undefined') {\n          currentCompilerServerActions[id] = {\n            workers: {},\n            layer: {},\n            filename,\n            exportedName,\n          }\n        }\n        currentCompilerServerActions[id].workers[bundlePath] = {\n          moduleId: '', // TODO: What's the meaning of this?\n          async: false,\n        }\n\n        currentCompilerServerActions[id].layer[bundlePath] = fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents\n      }\n    }\n\n    // Inject the entry to the server compiler\n    const actionEntryDep = bundler.EntryPlugin.createDependency(actionLoader, {\n      name: bundlePath,\n    })\n\n    return this.addEntry(\n      compilation,\n      // Reuse compilation context.\n      compiler.context,\n      actionEntryDep,\n      {\n        name: entryName,\n        layer: fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents,\n      }\n    )\n  }\n\n  addEntry(\n    compilation: webpack.Compilation,\n    context: string,\n    dependency: webpack.Dependency,\n    options: webpack.EntryOptions\n  ): Promise<any> /* Promise<module> */ {\n    return new Promise((resolve, reject) => {\n      if ('rspack' in compilation.compiler) {\n        compilation.addInclude(context, dependency, options, (err, module) => {\n          if (err) {\n            return reject(err)\n          }\n\n          compilation.moduleGraph\n            .getExportsInfo(module!)\n            .setUsedInUnknownWay(\n              this.isEdgeServer ? EDGE_RUNTIME_WEBPACK : DEFAULT_RUNTIME_WEBPACK\n            )\n          return resolve(module)\n        })\n      } else {\n        const entry = compilation.entries.get(options.name!)!\n        entry.includeDependencies.push(dependency)\n        compilation.hooks.addEntry.call(entry as any, options)\n        compilation.addModuleTree(\n          {\n            context,\n            dependency,\n            contextInfo: { issuerLayer: options.layer },\n          },\n          (err: any, module: any) => {\n            if (err) {\n              compilation.hooks.failedEntry.call(dependency, options, err)\n              return reject(err)\n            }\n\n            compilation.hooks.succeedEntry.call(dependency, options, module)\n\n            compilation.moduleGraph\n              .getExportsInfo(module)\n              .setUsedInUnknownWay(\n                this.isEdgeServer\n                  ? EDGE_RUNTIME_WEBPACK\n                  : DEFAULT_RUNTIME_WEBPACK\n              )\n\n            return resolve(module)\n          }\n        )\n      }\n    })\n  }\n\n  async createActionAssets(compilation: webpack.Compilation) {\n    const serverActions: ActionManifest['node'] = {}\n    const edgeServerActions: ActionManifest['edge'] = {}\n\n    traverseModules(compilation, (mod, _chunk, chunkGroup, modId) => {\n      // Go through all action entries and record the module ID for each entry.\n      if (\n        chunkGroup.name &&\n        mod.request &&\n        modId &&\n        /next-flight-action-entry-loader/.test(mod.request)\n      ) {\n        const fromClient = /&__client_imported__=true/.test(mod.request)\n\n        const mapping = this.isEdgeServer\n          ? pluginState.edgeServerActionModules\n          : pluginState.serverActionModules\n\n        if (!mapping[chunkGroup.name]) {\n          mapping[chunkGroup.name] = {}\n        }\n\n        mapping[chunkGroup.name][fromClient ? 'client' : 'server'] = {\n          moduleId: modId,\n          async: compilation.moduleGraph.isAsync(mod),\n        }\n      }\n    })\n\n    for (let id in pluginState.serverActions) {\n      const action = pluginState.serverActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.serverActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      serverActions[id] = action\n    }\n\n    for (let id in pluginState.edgeServerActions) {\n      const action = pluginState.edgeServerActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.edgeServerActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      edgeServerActions[id] = action\n    }\n\n    const serverManifest = {\n      node: serverActions,\n      edge: edgeServerActions,\n      encryptionKey: this.encryptionKey,\n    }\n    const edgeServerManifest = {\n      ...serverManifest,\n      encryptionKey: 'process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY',\n    }\n\n    const json = JSON.stringify(serverManifest, null, this.dev ? 2 : undefined)\n    const edgeJson = JSON.stringify(\n      edgeServerManifest,\n      null,\n      this.dev ? 2 : undefined\n    )\n\n    compilation.emitAsset(\n      `${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.js`,\n      new sources.RawSource(\n        `self.__RSC_SERVER_MANIFEST=${JSON.stringify(edgeJson)}`\n      ) as unknown as webpack.sources.RawSource\n    )\n    compilation.emitAsset(\n      `${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.json`,\n      new sources.RawSource(json) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n\nfunction addClientImport(\n  mod: webpack.NormalModule,\n  modRequest: string,\n  clientComponentImports: ClientComponentImports,\n  importedIdentifiers: string[],\n  isFirstVisitModule: boolean\n) {\n  const clientEntryType = getModuleBuildInfo(mod).rsc?.clientEntryType\n  const isCjsModule = clientEntryType === 'cjs'\n  const assumedSourceType = getAssumedSourceType(\n    mod,\n    isCjsModule ? 'commonjs' : 'auto'\n  )\n\n  const clientImportsSet = clientComponentImports[modRequest]\n\n  if (importedIdentifiers[0] === '*') {\n    // If there's collected import path with named import identifiers,\n    // or there's nothing in collected imports are empty.\n    // we should include the whole module.\n    if (!isFirstVisitModule && [...clientImportsSet][0] !== '*') {\n      clientComponentImports[modRequest] = new Set(['*'])\n    }\n  } else {\n    const isAutoModuleSourceType = assumedSourceType === 'auto'\n    if (isAutoModuleSourceType) {\n      clientComponentImports[modRequest] = new Set(['*'])\n    } else {\n      // If it's not analyzed as named ESM exports, e.g. if it's mixing `export *` with named exports,\n      // We'll include all modules since it's not able to do tree-shaking.\n      for (const name of importedIdentifiers) {\n        // For cjs module default import, we include the whole module since\n        const isCjsDefaultImport = isCjsModule && name === 'default'\n\n        // Always include __esModule along with cjs module default export,\n        // to make sure it work with client module proxy from React.\n        if (isCjsDefaultImport) {\n          clientComponentImports[modRequest].add('__esModule')\n        }\n\n        clientComponentImports[modRequest].add(name)\n      }\n    }\n  }\n}\n\nfunction getModuleResource(mod: webpack.NormalModule): string {\n  const modPath: string = mod.resourceResolveData?.path || ''\n  const modQuery = mod.resourceResolveData?.query || ''\n  // We have to always use the resolved request here to make sure the\n  // server and client are using the same module path (required by RSC), as\n  // the server compiler and client compiler have different resolve configs.\n  let modResource: string = modPath + modQuery\n\n  // Context modules don't have a resource path, we use the identifier instead.\n  if (mod.constructor.name === 'ContextModule') {\n    modResource = mod.identifier()\n  }\n\n  // For the barrel optimization, we need to use the match resource instead\n  // because there will be 2 modules for the same file (same resource path)\n  // but they're different modules and can't be deduped via `visitedModule`.\n  // The first module is a virtual re-export module created by the loader.\n  if (mod.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n    modResource = mod.matchResource + ':' + modResource\n  }\n\n  if (mod.resource === `?${WEBPACK_RESOURCE_QUERIES.metadataRoute}`) {\n    return getMetadataRouteResource(mod.rawRequest).filePath\n  }\n\n  return modResource\n}\n\nfunction getMetadataRouteResource(request: string): MetadataRouteLoaderOptions {\n  // e.g. next-metadata-route-loader?filePath=<some-url-encoded-path>&isDynamicRouteExtension=1!?__next_metadata_route__\n  const query = request.split('!')[0].split('next-metadata-route-loader?')[1]\n\n  return parse(query) as MetadataRouteLoaderOptions\n}\n"], "names": ["webpack", "parse", "stringify", "path", "sources", "getInvalidator", "getEntries", "EntryTypes", "getEntry<PERSON>ey", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "APP_CLIENT_INTERNALS", "BARREL_OPTIMIZATION_PREFIX", "COMPILER_NAMES", "DEFAULT_RUNTIME_WEBPACK", "EDGE_RUNTIME_WEBPACK", "SERVER_REFERENCE_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isClientComponentEntryModule", "isCSSMod", "regexCSS", "traverseModules", "forEachEntryModule", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "normalizePathSep", "getProxiedPluginState", "PAGE_TYPES", "getModuleBuildInfo", "getAssumedSourceType", "isAppRouteRoute", "DEFAULT_METADATA_ROUTE_EXTENSIONS", "isMetadataRouteFile", "getWebpackBundler", "PLUGIN_NAME", "pluginState", "serverActions", "edgeServerActions", "serverActionModules", "edgeServerActionModules", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "injectedClientEntries", "POSSIBLE_SHARED_CONVENTIONS", "STANDALONE_BUNDLE_CONVENTION", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryFile<PERSON><PERSON>", "cssImports", "entryConventionName", "cssImport", "has", "includes", "add", "push", "FlightClientEntryPlugin", "constructor", "options", "dev", "appDir", "projectDir", "join", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "webpackRuntime", "apply", "compiler", "hooks", "finishMake", "tapPromise", "compilation", "createClientEntries", "afterCompile", "tap", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "resource", "layer", "reactServerComponents", "key", "relative", "context", "replace", "moduleInfo", "moduleId", "async", "moduleGraph", "isAsync", "serverSideRendering", "ssrNamedModuleId", "_chunk", "_chunkGroup", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActionIds", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "entryRequest", "dependency", "request", "endsWith", "metadataRoute", "filePath", "isDynamicRouteExtension", "getMetadataRouteResource", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "actions", "set", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "appDirRelativeRequest", "isMetadataEntryFile", "assign", "entryName", "absolutePagePath", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "size", "injectActionEntry", "invalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "client", "Promise", "all", "flatMap", "addClientEntryAndSSRModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "dependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "action", "id", "fromClient", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "getModuleResource", "actionIds", "rsc", "map", "exportedName", "filename", "posix", "entryDependency", "ssrEntryModule", "getResolvedModule", "depModule", "visitedOfClientComponentsTraverse", "CSSImports", "filterClientComponents", "importedIdentifiers", "addClientImport", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "dependencyIds", "ids", "Array", "from", "bundler", "modules", "test", "localeCompare", "clientImportPath", "clientBrowserLoader", "sep", "x", "JSON", "server", "clientServerLoader", "page<PERSON><PERSON>", "APP", "type", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentSSREntryDep", "EntryPlugin", "createDependency", "clientComponentRSCEntryDep", "addEntry", "actionsArray", "actionsFromModule", "resolve", "actionLoader", "__client_imported__", "currentCompilerServerActions", "workers", "<PERSON><PERSON><PERSON><PERSON>", "actionEntryDep", "reject", "addInclude", "err", "module", "setUsedInUnknownWay", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "serverManifest", "node", "edge", "edgeServerManifest", "json", "undefined", "edgeJson", "emitAsset", "RawSource", "modRequest", "isFirstVisitModule", "clientEntryType", "isCjsModule", "assumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport", "identifier", "rawRequest"], "mappings": "AAKA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,KAAK,EAAEC,SAAS,QAAQ,cAAa;AAC9C,OAAOC,UAAU,OAAM;AACvB,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,SACEC,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,8CAA6C;AACpD,SACEC,cAAc,EACdC,wBAAwB,QACnB,yBAAwB;AAC/B,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,yBAAyB,EACzBC,gCAAgC,QAC3B,gCAA+B;AACtC,SACEC,4BAA4B,EAC5BC,QAAQ,EACRC,QAAQ,QACH,mBAAkB;AACzB,SACEC,eAAe,EACfC,kBAAkB,EAClBC,6BAA6B,EAC7BC,0BAA0B,QACrB,WAAU;AACjB,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,oBAAoB,QAAQ,gCAA+B;AACpE,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SACEC,iCAAiC,EACjCC,mBAAmB,QACd,0CAAyC;AAGhD,OAAOC,uBAAuB,0CAAyC;AASvE,MAAMC,cAAc;AAiCpB,MAAMC,cAAcT,sBAAsB;IACxC,gDAAgD;IAChDU,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,qBAAqB,CAAC;IAOtBC,yBAAyB,CAAC;IAO1BC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,uBAAuB,CAAC;AAC1B;AAEA,MAAMC,8BAA8B;IAAC;IAAY;CAAS;AAC1D,MAAMC,+BAA+B;AAErC,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQ1D,KAAKF,KAAK,CAACsD,OAAOO,IAAI;QACpC,MAAMC,QAAQ5D,KAAKF,KAAK,CAACuD,OAAOM,IAAI;QAEpC,MAAME,SAASnB,4BAA4BoB,OAAO,CAACJ;QACnD,MAAMK,SAASrB,4BAA4BoB,OAAO,CAACF;QAEnD,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAE9B,KAAK,MAAM,CAACC,eAAeC,WAAW,IAAItB,iBAAkB;QAC1D,MAAMuB,sBAAsBrE,KAAKF,KAAK,CAACqE,eAAeR,IAAI;QAE1D,KAAK,MAAMW,aAAaF,WAAY;YAClC,wDAAwD;YACxD,sHAAsH;YACtH,IACEH,kBAAkBM,GAAG,CAACD,cACtB3B,iCAAiC0B,qBACjC;gBACA;YACF;YAEA,iEAAiE;YACjE,IAAI3B,4BAA4B8B,QAAQ,CAACH,sBAAsB;gBAC7DJ,kBAAkBQ,GAAG,CAACH;YACxB;YAEA,IAAI,CAACN,iBAAiB,CAACG,cAAc,EAAE;gBACrCH,iBAAiB,CAACG,cAAc,GAAG,EAAE;YACvC;YACAH,iBAAiB,CAACG,cAAc,CAACO,IAAI,CAACJ;QACxC;IACF;IAEA,OAAON;AACT;AAEA,OAAO,MAAMW;IASXC,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGhF,KAAKiF,IAAI,CAACJ,QAAQE,MAAM,EAAE;QAC5C,IAAI,CAACG,YAAY,GAAGL,QAAQK,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACL,GAAG,IAAI,CAAC,IAAI,CAACI,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGP,QAAQO,aAAa;QAC1C,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,YAAY,GACnCtE,uBACAD;IACN;IAEA2E,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,UAAU,CAACC,UAAU,CAAC3D,aAAa,CAAC4D,cACjD,IAAI,CAACC,mBAAmB,CAACL,UAAUI;QAGrCJ,SAASC,KAAK,CAACK,YAAY,CAACC,GAAG,CAAC/D,aAAa,CAAC4D;YAC5C,MAAMI,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBjG,IAAI;gBAClE,MAAMqG,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAAC/F,8BACjBW,8BAA8B6E,IAAIQ,QAAQ,EAAEP,WAC5CA,UAAUG,WACZJ,IAAIQ,QAAQ;gBAEhB,IAAI,OAAOT,UAAU,eAAeO,aAAa;oBAC/C,IAAIN,IAAIS,KAAK,KAAKpG,eAAeqG,qBAAqB,EAAE;wBACtD,MAAMC,MAAM5G,KACT6G,QAAQ,CAACtB,SAASuB,OAAO,EAAEP,aAC3BQ,OAAO,CAAC,uBAAuB;wBAElC,MAAMC,aAAyB;4BAC7BC,UAAUjB;4BACVkB,OAAOvB,YAAYwB,WAAW,CAACC,OAAO,CAACnB;wBACzC;wBAEA,IAAI,IAAI,CAACf,YAAY,EAAE;4BACrBlD,YAAYQ,cAAc,CAACoE,IAAI,GAAGI;wBACpC,OAAO;4BACLhF,YAAYO,UAAU,CAACqE,IAAI,GAAGI;wBAChC;oBACF;gBACF;gBAEA,IAAIf,IAAIS,KAAK,KAAKpG,eAAe+G,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOrB,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIe,mBAAmBtH,KAAK6G,QAAQ,CAACtB,SAASuB,OAAO,EAAEP;oBAEvD,IAAI,CAACe,iBAAiBd,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/Bc,mBAAmB,CAAC,EAAE,EAAEhG,iBAAiBgG,mBAAmB;oBAC9D;oBAEA,MAAMN,aAAyB;wBAC7BC,UAAUjB;wBACVkB,OAAOvB,YAAYwB,WAAW,CAACC,OAAO,CAACnB;oBACzC;oBAEA,IAAI,IAAI,CAACf,YAAY,EAAE;wBACrBlD,YAAYM,cAAc,CACxBgF,iBAAiBP,OAAO,CAAC,uBAAuB,eACjD,GAAGC;oBACN,OAAO;wBACLhF,YAAYK,UAAU,CAACiF,iBAAiB,GAAGN;oBAC7C;gBACF;YACF;YAEA9F,gBAAgByE,aAAa,CAACM,KAAKsB,QAAQC,aAAaxB;gBACtD,IAAIA,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAV,SAASC,KAAK,CAACiC,IAAI,CAAC3B,GAAG,CAAC/D,aAAa,CAAC4D;YACpCA,YAAYH,KAAK,CAACkC,aAAa,CAAChC,UAAU,CACxC;gBACE/B,MAAM5B;gBACN4F,OAAO9H,QAAQ+H,WAAW,CAACC,kCAAkC;YAC/D,GACA,IAAM,IAAI,CAACC,kBAAkB,CAACnC;QAElC;IACF;IAEA,MAAMC,oBACJL,QAA0B,EAC1BI,WAAgC,EAChC;QACA,MAAMoC,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAGF,CAAC;QACL,MAAMC,mBAAmB,IAAIjE;QAE7B,4EAA4E;QAC5E,0BAA0B;QAC1B/C,mBAAmBwE,aAAa,CAAC,EAAEhC,IAAI,EAAEyE,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAM3F,mBAA+B,CAAC;YAEtC,KAAK,MAAM4F,cAAcpH,2BACvB+G,aACAzC,YAAYwB,WAAW,EACtB;gBACD,uFAAuF;gBACvF,IAAIuB,eAAe,AACjBD,WAAWE,UAAU,CACrBC,OAAO;gBAET,IAAIF,aAAaG,QAAQ,CAACtI,yBAAyBuI,aAAa,GAAG;oBACjE,MAAM,EAAEC,QAAQ,EAAEC,uBAAuB,EAAE,GACzCC,yBAAyBP;oBAE3B,IAAIM,4BAA4B,KAAK;wBACnCN,eAAeK;oBACjB;gBACF;gBAEA,MAAM,EAAEG,sBAAsB,EAAEC,aAAa,EAAE/E,UAAU,EAAE,GACzD,IAAI,CAACgF,6CAA6C,CAAC;oBACjDV;oBACA/C;oBACA0D,gBAAgBZ,WAAWY,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,QAAQ,GACnClB,mBAAmBmB,GAAG,CAACF,KAAKC;gBAG9B,MAAME,oBAAoB1J,KAAK2J,UAAU,CAACjB;gBAE1C,mDAAmD;gBACnD,IAAI,CAACgB,mBAAmB;oBACtB3G,OAAO6G,IAAI,CAACV,wBAAwBI,OAAO,CACzC,CAACO,QAAWxB,mCAAmC,CAACwB,MAAM,GAAG,IAAI3F;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAM4F,kBAAkBJ,oBACpB1J,KAAK6G,QAAQ,CAAClB,YAAYd,OAAO,CAACiC,OAAO,EAAG4B,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,iDAAiD;gBACjD,2CAA2C;gBAC3C,IAAIqB,aAAazI,iBACfwI,gBAAgB/C,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlE,sEAAsE;gBACtE,qCAAqC;gBACrC,OAAO;gBACP,kDAAkD;gBAClD,gDAAgD;gBAChD,4CAA4C;gBAC5C,MAAMiD,wBAAwBF,gBAC3B/C,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,aAAa;gBACxB,MAAMkD,sBAAsBpI,oBAC1BmI,uBACApI,mCACA;gBAEF,IAAIqI,qBAAqB;oBACvBF,aAAapG;gBACf;gBAEAZ,OAAOmH,MAAM,CAACrH,kBAAkBuB;gBAChCoE,sBAAsB9D,IAAI,CAAC;oBACzBa;oBACAI;oBACAwE,WAAWxG;oBACXuF;oBACAa;oBACAK,kBAAkB1B;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACE/E,SAAS,CAAC,GAAG,EAAE7C,kCAAkC,IACjDiJ,eAAe,iBACf;oBACAvB,sBAAsB9D,IAAI,CAAC;wBACzBa;wBACAI;wBACAwE,WAAWxG;wBACXuF,wBAAwB,CAAC;wBACzBa,YAAY,CAAC,GAAG,EAAEjJ,kCAAkC;wBACpDsJ,kBAAkB1B;oBACpB;gBACF;gBAEA,IACE/E,SAAS,CAAC,GAAG,EAAE7C,kCAAkC,IACjDiJ,eAAe,wBACf;oBACAvB,sBAAsB9D,IAAI,CAAC;wBACzBa;wBACAI;wBACAwE,WAAWxG;wBACXuF;wBACAa,YAAY,CAAC,GAAG,EAAEjJ,kCAAkC;wBACpDsJ,kBAAkB1B;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAM1E,oBAAoBpB,8BAA8BC;YACxD,KAAK,MAAMwH,uBAAuB7B,sBAAuB;gBACvD,MAAM8B,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBnB,sBAAsB;wBAC7C,GAAG,AACDlF,CAAAA,iBAAiB,CAACqG,oBAAoBD,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DK,MAAM,CAAyB,CAACC,KAAKC;4BACrCD,GAAG,CAACC,KAAK,GAAG,IAAIzG;4BAChB,OAAOwG;wBACT,GAAG,CAAC,EAAE;oBACR;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAAC1C,8BAA8B,CAACqC,oBAAoBF,SAAS,CAAC,EAAE;oBAClEnC,8BAA8B,CAACqC,oBAAoBF,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACAnC,8BAA8B,CAACqC,oBAAoBF,SAAS,CAAC,CAACzF,IAAI,CAChE4F,QAAQ,CAAC,EAAE;gBAGbvC,gCAAgCrD,IAAI,CAAC4F;YACvC;YAEA,IAAI,CAAC3I,gBAAgBgC,OAAO;gBAC1B,sBAAsB;gBACtBoE,gCAAgCrD,IAAI,CAClC,IAAI,CAAC6F,8BAA8B,CAAC;oBAClChF;oBACAI;oBACAwE,WAAWxG;oBACX6G,eAAe;wBAAE,GAAGnC,mCAAmC;oBAAC;oBACxD0B,YAAYvJ;gBACd;YAEJ;YAEA,IAAI8H,mBAAmBsC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAAC1C,kBAAkB,CAACvE,KAAK,EAAE;oBAC7BuE,kBAAkB,CAACvE,KAAK,GAAG,IAAI4E;gBACjC;gBACAL,kBAAkB,CAACvE,KAAK,GAAG,IAAI4E,IAAI;uBAC9BL,kBAAkB,CAACvE,KAAK;uBACxB2E;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAC3E,MAAM2E,mBAAmB,IAAIvF,OAAOC,OAAO,CACrDkF,oBACC;YACDD,mBAAmBvD,IAAI,CACrB,IAAI,CAACmG,iBAAiB,CAAC;gBACrBtF;gBACAI;gBACA6D,SAASlB;gBACT6B,WAAWxG;gBACXoG,YAAYpG;gBACZwE;YACF;QAEJ;QAEA,qDAAqD;QACrD,MAAM2C,cAAc5K,eAAeqF,SAASwF,UAAU;QACtD,4DAA4D;QAC5D,IACED,eACA/C,gCAAgCiD,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAH,YAAYI,UAAU,CAAC;gBAACxK,eAAeyK,MAAM;aAAC;QAChD;QAEA,4EAA4E;QAC5E,0EAA0E;QAC1E,sCAAsC;QACtC,MAAMC,QAAQC,GAAG,CACftD,gCAAgCuD,OAAO,CAAC,CAACC,8BAAgC;gBACvEA,2BAA2B,CAAC,EAAE;gBAC9BA,2BAA2B,CAAC,EAAE;aAC/B;QAGH,uCAAuC;QACvC,MAAMH,QAAQC,GAAG,CAACpD;QAElB,MAAMuD,6BAA6C,EAAE;QACrD,MAAMC,2BAGF,CAAC;QAEL,mEAAmE;QACnE,gBAAgB;QAChB,yEAAyE;QACzE,KAAK,MAAM,CAAC9H,MAAM+H,qBAAqB,IAAI3I,OAAOC,OAAO,CACvDgF,gCACC;YACD,qEAAqE;YACrE,sBAAsB;YACtB,MAAMM,qBAAqB,IAAI,CAACqD,oCAAoC,CAAC;gBACnEhG;gBACAiG,cAAcF;YAChB;YAEA,IAAIpD,mBAAmBsC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACa,wBAAwB,CAAC9H,KAAK,EAAE;oBACnC8H,wBAAwB,CAAC9H,KAAK,GAAG,IAAI4E;gBACvC;gBACAkD,wBAAwB,CAAC9H,KAAK,GAAG,IAAI4E,IAAI;uBACpCkD,wBAAwB,CAAC9H,KAAK;uBAC9B2E;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAC6B,WAAW7B,mBAAmB,IAAIvF,OAAOC,OAAO,CAC1DyI,0BACC;YACD,uEAAuE;YACvE,+CAA+C;YAC/C,uEAAuE;YACvE,mBAAmB;YACnB,IAAII,iCAAiC;YACrC,MAAMC,8BAA8B,IAAIvD;YACxC,KAAK,MAAM,CAACgB,KAAKC,QAAQ,IAAIlB,mBAAoB;gBAC/C,MAAMyD,uBAAuB,EAAE;gBAC/B,KAAK,MAAMC,UAAUxC,QAAS;oBAC5B,IAAI,CAACrB,iBAAiB5D,GAAG,CAAC4F,YAAY,MAAM6B,OAAOC,EAAE,GAAG;wBACtDF,qBAAqBrH,IAAI,CAACsH;oBAC5B;gBACF;gBACA,IAAID,qBAAqBvI,MAAM,GAAG,GAAG;oBACnCsI,4BAA4BrC,GAAG,CAACF,KAAKwC;oBACrCF,iCAAiC;gBACnC;YACF;YAEA,IAAIA,gCAAgC;gBAClCL,2BAA2B9G,IAAI,CAC7B,IAAI,CAACmG,iBAAiB,CAAC;oBACrBtF;oBACAI;oBACA6D,SAASsC;oBACT3B;oBACAJ,YAAYI;oBACZ+B,YAAY;oBACZ/D;gBACF;YAEJ;QACF;QAEA,MAAMiD,QAAQC,GAAG,CAACG;IACpB;IAEAG,qCAAqC,EACnChG,WAAW,EACXiG,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAMO,mBAAmB,IAAI5D;QAE7B,gFAAgF;QAChF,MAAM6D,gBAAgB,IAAIlI;QAC1B,MAAMmI,eAAe,IAAInI;QAEzB,MAAMoI,iBAAiB,CAAC,EACtB5D,YAAY,EACZW,cAAc,EAIf;YACC,MAAMkD,sBAAsB,CAACtG;oBAUTxE;gBATlB,IAAI,CAACwE,KAAK;gBAEV,MAAMM,cAAciG,kBAAkBvG;gBAEtC,IAAI,CAACM,aAAa;gBAElB,IAAI6F,cAAc7H,GAAG,CAACgC,cAAc;gBACpC6F,cAAc3H,GAAG,CAAC8B;gBAElB,MAAMkG,aAAYhL,0BAAAA,mBAAmBwE,KAAKyG,GAAG,qBAA3BjL,wBAA6BgL,SAAS;gBACxD,IAAIA,WAAW;oBACbN,iBAAiB1C,GAAG,CAClBlD,aACAxD,OAAOC,OAAO,CAACyJ,WAAWE,GAAG,CAAC,CAAC,CAACV,IAAIW,aAAa,GAAM,CAAA;4BACrDX;4BACAW;4BACAC,UAAU7M,KAAK8M,KAAK,CAACjG,QAAQ,CAAC,IAAI,CAAC7B,UAAU,EAAEuB;wBACjD,CAAA;gBAEJ;gBAEA,8CAA8C;gBAC9ClF,2BAA2B4E,KAAKN,YAAYwB,WAAW,EAAEmC,OAAO,CAC9D,CAACb;oBACC8D,oBACE9D,WAAWY,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEX,gBACA,CAACA,aAAalE,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3D+H,oBAAoBlD;YACtB;QACF;QAEA,KAAK,MAAM0D,mBAAmBnB,aAAc;YAC1C,MAAMoB,iBACJrH,YAAYwB,WAAW,CAAC8F,iBAAiB,CAACF;YAC5C,KAAK,MAAMtE,cAAcpH,2BACvB2L,gBACArH,YAAYwB,WAAW,EACtB;gBACD,MAAM+F,YAAYzE,WAAWE,UAAU;gBACvC,MAAMC,UAAU,AAACsE,UAA8CtE,OAAO;gBAEtE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIyD,aAAa9H,GAAG,CAACqE,UAAU;gBAC/ByD,aAAa5H,GAAG,CAACmE;gBAEjB0D,eAAe;oBACb5D,cAAcE;oBACdS,gBAAgBZ,WAAWY,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO8C;IACT;IAEA/C,8CAA8C,EAC5CV,YAAY,EACZ/C,WAAW,EACX0D,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAM8D,oCAAoC,IAAIjJ;QAE9C,mBAAmB;QACnB,MAAMgF,yBAAiD,CAAC;QACxD,MAAMC,gBAAgD,EAAE;QACxD,MAAMiE,aAAa,IAAIlJ;QAEvB,MAAMmJ,yBAAyB,CAC7BpH,KACAqH;gBAqBkB7L;YAnBlB,IAAI,CAACwE,KAAK;YAEV,MAAMM,cAAciG,kBAAkBvG;YAEtC,IAAI,CAACM,aAAa;YAClB,IAAI4G,kCAAkC5I,GAAG,CAACgC,cAAc;gBACtD,IAAI2C,sBAAsB,CAAC3C,YAAY,EAAE;oBACvCgH,gBACEtH,KACAM,aACA2C,wBACAoE,qBACA;gBAEJ;gBACA;YACF;YACAH,kCAAkC1I,GAAG,CAAC8B;YAEtC,MAAMkG,aAAYhL,0BAAAA,mBAAmBwE,KAAKyG,GAAG,qBAA3BjL,wBAA6BgL,SAAS;YACxD,IAAIA,WAAW;gBACbtD,cAAczE,IAAI,CAAC;oBACjB6B;oBACAxD,OAAOC,OAAO,CAACyJ,WAAWE,GAAG,CAAC,CAAC,CAACV,IAAIW,aAAa,GAAM,CAAA;4BACrDX;4BACAW;4BACAC,UAAU7M,KAAK8M,KAAK,CAACjG,QAAQ,CAAC,IAAI,CAAC7B,UAAU,EAAEuB;wBACjD,CAAA;iBACD;YACH;YAEA,IAAIvF,SAASiF,MAAM;gBACjB,MAAMuH,iBACJvH,IAAIwH,WAAW,IAAI,AAACxH,IAAIwH,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAC/H,YAAYwB,WAAW,CACpCwG,cAAc,CAAC1H,KACf2H,YAAY,CAAC,IAAI,CAACvI,cAAc;oBAEnC,IAAIqI,QAAQ;gBACd;gBAEAN,WAAW3I,GAAG,CAAC8B;YACjB,OAAO,IAAIxF,6BAA6BkF,MAAM;gBAC5C,IAAI,CAACiD,sBAAsB,CAAC3C,YAAY,EAAE;oBACxC2C,sBAAsB,CAAC3C,YAAY,GAAG,IAAIrC;gBAC5C;gBACAqJ,gBACEtH,KACAM,aACA2C,wBACAoE,qBACA;gBAGF;YACF;YAEAjM,2BAA2B4E,KAAKN,YAAYwB,WAAW,EAAEmC,OAAO,CAC9D,CAACb;oBAKKA;gBAJJ,IAAIoF,gBAA0B,EAAE;gBAEhC,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAIpF,yBAAAA,WAAWE,UAAU,qBAArBF,uBAAuBqF,GAAG,EAAE;oBAC9BD,cAAcnJ,IAAI,IAAI+D,WAAWE,UAAU,CAACmF,GAAG;gBACjD,OAAO;oBACLD,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAR,uBAAuB5E,WAAWY,cAAc,EAAEwE;YACpD;QAEJ;QAEA,2DAA2D;QAC3DR,uBAAuBhE,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACA9E,YAAYgJ,WAAWxC,IAAI,GACvB;gBACE,CAAClC,aAAa,EAAEqF,MAAMC,IAAI,CAACZ;YAC7B,IACA,CAAC;YACLjE;QACF;IACF;IAEAoB,+BAA+B,EAC7BhF,QAAQ,EACRI,WAAW,EACXwE,SAAS,EACTK,aAAa,EACbT,UAAU,EACVK,gBAAgB,EAQjB,EAKC;QACA,MAAM6D,UAAUnM;QAChB,IAAImJ,mBAAmB;QAEvB,MAAMiD,UAAUnL,OAAO6G,IAAI,CAACY,eACzBvH,IAAI,CAAC,CAACC,GAAGC,IAAOlC,SAASkN,IAAI,CAAChL,KAAK,IAAID,EAAEkL,aAAa,CAACjL,IACvDwJ,GAAG,CAAC,CAAC0B,mBAAsB,CAAA;gBAC1BzF,SAASyF;gBACTP,KAAK;uBAAItD,aAAa,CAAC6D,iBAAiB;iBAAC;YAC3C,CAAA;QAEF,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAEvO,UAAU;YACvEmO,SAAS,AAAC,CAAA,IAAI,CAAChJ,YAAY,GACvBgJ,QAAQvB,GAAG,CAAC,CAAC,EAAE/D,OAAO,EAAEkF,GAAG,EAAE,GAAM,CAAA;oBACjClF,SAASA,QAAQ7B,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAO/G,KAAKuO,GAAG;oBAEvCT;gBACF,CAAA,KACAI,OAAM,EACRvB,GAAG,CAAC,CAAC6B,IAAMC,KAAK1O,SAAS,CAACyO;YAC5BE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMC,qBAAqB,CAAC,gCAAgC,EAAE5O,UAAU;YACtEmO,SAASA,QAAQvB,GAAG,CAAC,CAAC6B,IAAMC,KAAK1O,SAAS,CAACyO;YAC3CE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAAC5J,GAAG,EAAE;YACZ,MAAM9B,UAAU7C,WAAWoF,SAASwF,UAAU;YAC9C,MAAM6D,UAAUvO,YACdK,eAAeyK,MAAM,EACrB3J,WAAWqN,GAAG,EACd9E;YAGF,IAAI,CAAC/G,OAAO,CAAC4L,QAAQ,EAAE;gBACrB5L,OAAO,CAAC4L,QAAQ,GAAG;oBACjBE,MAAM1O,WAAW2O,WAAW;oBAC5BC,eAAe,IAAI9K,IAAI;wBAACiG;qBAAU;oBAClC8E,uBAAuB7E;oBACvBL;oBACAnB,SAAS0F;oBACTY,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACApE,mBAAmB;YACrB,OAAO;gBACL,MAAMqE,YAAYtM,OAAO,CAAC4L,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIU,UAAU1G,OAAO,KAAK0F,qBAAqB;oBAC7CgB,UAAU1G,OAAO,GAAG0F;oBACpBrD,mBAAmB;gBACrB;gBACA,IAAIqE,UAAUR,IAAI,KAAK1O,WAAW2O,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAACvK,GAAG,CAAC0F;gBAC9B;gBACAmF,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACLrN,YAAYS,qBAAqB,CAACsH,WAAW,GAAGuE;QAClD;QAEA,MAAMiB,6BAA6BtB,QAAQuB,WAAW,CAACC,gBAAgB,CACrEd,oBACA;YAAEhL,MAAMoG;QAAW;QAGrB,MAAM2F,6BAA6BzB,QAAQuB,WAAW,CAACC,gBAAgB,CACrEd,oBACA;YAAEhL,MAAMoG;QAAW;QAGrB,OAAO;YACLkB;YACA,yEAAyE;YACzE,yEAAyE;YACzE,sBAAsB;YACtB,IAAI,CAAC0E,QAAQ,CAAChK,aAAaJ,SAASuB,OAAO,EAAEyI,4BAA4B;gBACvE5L,MAAMwG;gBACNzD,OAAOpG,eAAe+G,mBAAmB;YAC3C;YACA,IAAI,CAACsI,QAAQ,CAAChK,aAAaJ,SAASuB,OAAO,EAAE4I,4BAA4B;gBACvE/L,MAAMwG;gBACNzD,OAAOpG,eAAeqG,qBAAqB;YAC7C;YACA4I;SACD;IACH;IAEA1E,kBAAkB,EAChBtF,QAAQ,EACRI,WAAW,EACX6D,OAAO,EACPW,SAAS,EACTJ,UAAU,EACVmC,UAAU,EACV/D,gBAAgB,EASjB,EAAE;QACD,MAAM8F,UAAUnM;QAChB,MAAM8N,eAAe7B,MAAMC,IAAI,CAACxE,QAAQxG,OAAO;QAC/C,KAAK,MAAM,GAAG6M,kBAAkB,IAAIrG,QAAS;YAC3C,KAAK,MAAM,EAAEyC,EAAE,EAAE,IAAI4D,kBAAmB;gBACtC1H,iBAAiB1D,GAAG,CAAC0F,YAAY,MAAM8B;YACzC;QACF;QAEA,IAAI2D,aAAapM,MAAM,KAAK,GAAG;YAC7B,OAAO4H,QAAQ0E,OAAO;QACxB;QAEA,MAAMC,eAAe,CAAC,gCAAgC,EAAEhQ,UAAU;YAChEyJ,SAASiF,KAAK1O,SAAS,CACrB6P;YAEFI,qBAAqB9D;QACvB,GAAG,CAAC,CAAC;QAEL,MAAM+D,+BAA+B,IAAI,CAAC/K,YAAY,GAClDlD,YAAYE,iBAAiB,GAC7BF,YAAYC,aAAa;QAE7B,KAAK,MAAM,GAAG4N,kBAAkB,IAAID,aAAc;YAChD,KAAK,MAAM,EAAE3D,EAAE,EAAEW,YAAY,EAAEC,QAAQ,EAAE,IAAIgD,kBAAmB;gBAC9D,IAAI,OAAOI,4BAA4B,CAAChE,GAAG,KAAK,aAAa;oBAC3DgE,4BAA4B,CAAChE,GAAG,GAAG;wBACjCiE,SAAS,CAAC;wBACVxJ,OAAO,CAAC;wBACRmG;wBACAD;oBACF;gBACF;gBACAqD,4BAA4B,CAAChE,GAAG,CAACiE,OAAO,CAACnG,WAAW,GAAG;oBACrD9C,UAAU;oBACVC,OAAO;gBACT;gBAEA+I,4BAA4B,CAAChE,GAAG,CAACvF,KAAK,CAACqD,WAAW,GAAGmC,aACjD5L,eAAe6P,aAAa,GAC5B7P,eAAeqG,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMyJ,iBAAiBnC,QAAQuB,WAAW,CAACC,gBAAgB,CAACM,cAAc;YACxEpM,MAAMoG;QACR;QAEA,OAAO,IAAI,CAAC4F,QAAQ,CAClBhK,aACA,6BAA6B;QAC7BJ,SAASuB,OAAO,EAChBsJ,gBACA;YACEzM,MAAMwG;YACNzD,OAAOwF,aACH5L,eAAe6P,aAAa,GAC5B7P,eAAeqG,qBAAqB;QAC1C;IAEJ;IAEAgJ,SACEhK,WAAgC,EAChCmB,OAAe,EACf6B,UAA8B,EAC9B9D,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIuG,QAAQ,CAAC0E,SAASO;YAC3B,IAAI,YAAY1K,YAAYJ,QAAQ,EAAE;gBACpCI,YAAY2K,UAAU,CAACxJ,SAAS6B,YAAY9D,SAAS,CAAC0L,KAAKC;oBACzD,IAAID,KAAK;wBACP,OAAOF,OAAOE;oBAChB;oBAEA5K,YAAYwB,WAAW,CACpBwG,cAAc,CAAC6C,QACfC,mBAAmB,CAClB,IAAI,CAACvL,YAAY,GAAGtE,uBAAuBD;oBAE/C,OAAOmP,QAAQU;gBACjB;YACF,OAAO;gBACL,MAAME,QAAQ/K,YAAY3C,OAAO,CAAC2N,GAAG,CAAC9L,QAAQlB,IAAI;gBAClD+M,MAAME,mBAAmB,CAAClM,IAAI,CAACiE;gBAC/BhD,YAAYH,KAAK,CAACmK,QAAQ,CAACkB,IAAI,CAACH,OAAc7L;gBAC9Cc,YAAYmL,aAAa,CACvB;oBACEhK;oBACA6B;oBACAoI,aAAa;wBAAEC,aAAanM,QAAQ6B,KAAK;oBAAC;gBAC5C,GACA,CAAC6J,KAAUC;oBACT,IAAID,KAAK;wBACP5K,YAAYH,KAAK,CAACyL,WAAW,CAACJ,IAAI,CAAClI,YAAY9D,SAAS0L;wBACxD,OAAOF,OAAOE;oBAChB;oBAEA5K,YAAYH,KAAK,CAAC0L,YAAY,CAACL,IAAI,CAAClI,YAAY9D,SAAS2L;oBAEzD7K,YAAYwB,WAAW,CACpBwG,cAAc,CAAC6C,QACfC,mBAAmB,CAClB,IAAI,CAACvL,YAAY,GACbtE,uBACAD;oBAGR,OAAOmP,QAAQU;gBACjB;YAEJ;QACF;IACF;IAEA,MAAM1I,mBAAmBnC,WAAgC,EAAE;QACzD,MAAM1D,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDhB,gBAAgByE,aAAa,CAACM,KAAKsB,QAAQ4J,YAAYnL;YACrD,yEAAyE;YACzE,IACEmL,WAAWxN,IAAI,IACfsC,IAAI2C,OAAO,IACX5C,SACA,kCAAkCmI,IAAI,CAAClI,IAAI2C,OAAO,GAClD;gBACA,MAAMsD,aAAa,4BAA4BiC,IAAI,CAAClI,IAAI2C,OAAO;gBAE/D,MAAMwI,UAAU,IAAI,CAAClM,YAAY,GAC7BlD,YAAYI,uBAAuB,GACnCJ,YAAYG,mBAAmB;gBAEnC,IAAI,CAACiP,OAAO,CAACD,WAAWxN,IAAI,CAAC,EAAE;oBAC7ByN,OAAO,CAACD,WAAWxN,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBAEAyN,OAAO,CAACD,WAAWxN,IAAI,CAAC,CAACuI,aAAa,WAAW,SAAS,GAAG;oBAC3DjF,UAAUjB;oBACVkB,OAAOvB,YAAYwB,WAAW,CAACC,OAAO,CAACnB;gBACzC;YACF;QACF;QAEA,IAAK,IAAIgG,MAAMjK,YAAYC,aAAa,CAAE;YACxC,MAAM+J,SAAShK,YAAYC,aAAa,CAACgK,GAAG;YAC5C,IAAK,IAAItI,QAAQqI,OAAOkE,OAAO,CAAE;gBAC/B,MAAMlK,QACJhE,YAAYG,mBAAmB,CAACwB,KAAK,CACnCqI,OAAOtF,KAAK,CAAC/C,KAAK,KAAKrD,eAAe6P,aAAa,GAC/C,WACA,SACL;gBACHnE,OAAOkE,OAAO,CAACvM,KAAK,GAAGqC;YACzB;YACA/D,aAAa,CAACgK,GAAG,GAAGD;QACtB;QAEA,IAAK,IAAIC,MAAMjK,YAAYE,iBAAiB,CAAE;YAC5C,MAAM8J,SAAShK,YAAYE,iBAAiB,CAAC+J,GAAG;YAChD,IAAK,IAAItI,QAAQqI,OAAOkE,OAAO,CAAE;gBAC/B,MAAMlK,QACJhE,YAAYI,uBAAuB,CAACuB,KAAK,CACvCqI,OAAOtF,KAAK,CAAC/C,KAAK,KAAKrD,eAAe6P,aAAa,GAC/C,WACA,SACL;gBACHnE,OAAOkE,OAAO,CAACvM,KAAK,GAAGqC;YACzB;YACA9D,iBAAiB,CAAC+J,GAAG,GAAGD;QAC1B;QAEA,MAAMqF,iBAAiB;YACrBC,MAAMrP;YACNsP,MAAMrP;YACNkD,eAAe,IAAI,CAACA,aAAa;QACnC;QACA,MAAMoM,qBAAqB;YACzB,GAAGH,cAAc;YACjBjM,eAAe;QACjB;QAEA,MAAMqM,OAAOhD,KAAK1O,SAAS,CAACsR,gBAAgB,MAAM,IAAI,CAACvM,GAAG,GAAG,IAAI4M;QACjE,MAAMC,WAAWlD,KAAK1O,SAAS,CAC7ByR,oBACA,MACA,IAAI,CAAC1M,GAAG,GAAG,IAAI4M;QAGjB/L,YAAYiM,SAAS,CACnB,GAAG,IAAI,CAACzM,WAAW,GAAGtE,0BAA0B,GAAG,CAAC,EACpD,IAAIZ,QAAQ4R,SAAS,CACnB,CAAC,2BAA2B,EAAEpD,KAAK1O,SAAS,CAAC4R,WAAW;QAG5DhM,YAAYiM,SAAS,CACnB,GAAG,IAAI,CAACzM,WAAW,GAAGtE,0BAA0B,KAAK,CAAC,EACtD,IAAIZ,QAAQ4R,SAAS,CAACJ;IAE1B;AACF;AAEA,SAASlE,gBACPtH,GAAyB,EACzB6L,UAAkB,EAClB5I,sBAA8C,EAC9CoE,mBAA6B,EAC7ByE,kBAA2B;QAEHtQ;IAAxB,MAAMuQ,mBAAkBvQ,0BAAAA,mBAAmBwE,KAAKyG,GAAG,qBAA3BjL,wBAA6BuQ,eAAe;IACpE,MAAMC,cAAcD,oBAAoB;IACxC,MAAME,oBAAoBxQ,qBACxBuE,KACAgM,cAAc,aAAa;IAG7B,MAAME,mBAAmBjJ,sBAAsB,CAAC4I,WAAW;IAE3D,IAAIxE,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACyE,sBAAsB;eAAII;SAAiB,CAAC,EAAE,KAAK,KAAK;YAC3DjJ,sBAAsB,CAAC4I,WAAW,GAAG,IAAI5N,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAMkO,yBAAyBF,sBAAsB;QACrD,IAAIE,wBAAwB;YAC1BlJ,sBAAsB,CAAC4I,WAAW,GAAG,IAAI5N,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQ2J,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM+E,qBAAqBJ,eAAetO,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAI0O,oBAAoB;oBACtBnJ,sBAAsB,CAAC4I,WAAW,CAACrN,GAAG,CAAC;gBACzC;gBAEAyE,sBAAsB,CAAC4I,WAAW,CAACrN,GAAG,CAACd;YACzC;QACF;IACF;AACF;AAEA,SAAS6I,kBAAkBvG,GAAyB;QAC1BA,0BACPA,2BAebA;IAhBJ,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBjG,IAAI,KAAI;IACzD,MAAMqG,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;IACnD,mEAAmE;IACnE,yEAAyE;IACzE,0EAA0E;IAC1E,IAAIC,cAAsBL,UAAUG;IAEpC,6EAA6E;IAC7E,IAAIJ,IAAIrB,WAAW,CAACjB,IAAI,KAAK,iBAAiB;QAC5C4C,cAAcN,IAAIqM,UAAU;IAC9B;IAEA,yEAAyE;IACzE,yEAAyE;IACzE,0EAA0E;IAC1E,wEAAwE;IACxE,KAAIrM,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAAC/F,6BAA6B;QAC7D8F,cAAcN,IAAIE,aAAa,GAAG,MAAMI;IAC1C;IAEA,IAAIN,IAAIQ,QAAQ,KAAK,CAAC,CAAC,EAAElG,yBAAyBuI,aAAa,EAAE,EAAE;QACjE,OAAOG,yBAAyBhD,IAAIsM,UAAU,EAAExJ,QAAQ;IAC1D;IAEA,OAAOxC;AACT;AAEA,SAAS0C,yBAAyBL,OAAe;IAC/C,sHAAsH;IACtH,MAAMtC,QAAQsC,QAAQrF,KAAK,CAAC,IAAI,CAAC,EAAE,CAACA,KAAK,CAAC,8BAA8B,CAAC,EAAE;IAE3E,OAAOzD,MAAMwG;AACf", "ignoreList": [0]}