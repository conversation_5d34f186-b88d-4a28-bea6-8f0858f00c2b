{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "sourcesContent": ["'use client'\n\n// TODO: Explicitly import from client.browser\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { createFromReadableStream as createFromReadableStreamBrowser } from 'react-server-dom-webpack/client'\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\nimport { urlToUrlWithoutFlightMarker } from '../../route-params'\n\nconst createFromReadableStream =\n  createFromReadableStreamBrowser as (typeof import('react-server-dom-webpack/client.browser'))['createFromReadableStream']\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\nexport type FetchServerResponseResult = {\n  flightData: NormalizedFlightData[] | string\n  canonicalUrl: URL | undefined\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n}\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1' | '2'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return {\n    flightData: urlToUrlWithoutFlightMarker(\n      new URL(url, location.origin)\n    ).toString(),\n    canonicalUrl: undefined,\n    couldBeIntercepted: false,\n    prerendered: false,\n    postponed: false,\n    staleTime: -1,\n  }\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n      flightRouterState,\n      options.isHmrRefresh\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    const res = await createFetch(\n      url,\n      headers,\n      fetchPriority,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(new URL(res.url))\n    const canonicalUrl = res.redirected ? responseUrl : undefined\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeaderSeconds = res.headers.get(\n      NEXT_ROUTER_STALE_TIME_HEADER\n    )\n    const staleTime =\n      staleTimeHeaderSeconds !== null\n        ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n        : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await (\n        require('../../dev/hot-reloader/app/hot-reloader-app') as typeof import('../../dev/hot-reloader/app/hot-reloader-app')\n      ).waitForWebpackRuntimeHotUpdate()\n    }\n\n    // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n    const flightStream = postponed\n      ? createUnclosingPrefetchStream(res.body)\n      : res.body\n    const response = await (createFromNextReadableStream(\n      flightStream\n    ) as Promise<NavigationFlightResponse>)\n\n    if (getAppBuildId() !== response.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    return {\n      flightData: normalizeFlightData(response.f),\n      canonicalUrl: canonicalUrl,\n      couldBeIntercepted: interception,\n      prerendered: response.S,\n      postponed,\n      staleTime,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${url}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return {\n      flightData: url.toString(),\n      canonicalUrl: undefined,\n      couldBeIntercepted: false,\n      prerendered: false,\n      postponed: false,\n      staleTime: -1,\n    }\n  }\n}\n\n// This is a subset of the standard Response type. We use a custom type for\n// this so we can limit which details about the response leak into the rest of\n// the codebase. For example, there's some custom logic for manually following\n// redirects, so \"redirected\" in this type could be a composite of multiple\n// browser fetch calls; however, this fact should not leak to the caller.\nexport type RSCResponse = {\n  ok: boolean\n  redirected: boolean\n  headers: Headers\n  body: ReadableStream<Uint8Array> | null\n  status: number\n  url: string\n}\n\nexport async function createFetch(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  signal?: AbortSignal\n): Promise<RSCResponse> {\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  const fetchOptions: RequestInit = {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  }\n  // `fetchUrl` is slightly different from `url` because we add a cache-busting\n  // search param to it. This should not leak outside of this function, so we\n  // track them separately.\n  let fetchUrl = new URL(url)\n  setCacheBustingSearchParam(fetchUrl, headers)\n  let browserResponse = await fetch(fetchUrl, fetchOptions)\n\n  // If the server responds with a redirect (e.g. 307), and the redirected\n  // location does not contain the cache busting search param set in the\n  // original request, the response is likely invalid — when following the\n  // redirect, the browser forwards the request headers, but since the cache\n  // busting search param is missing, the server will reject the request due to\n  // a mismatch.\n  //\n  // Ideally, we would be able to intercept the redirect response and perform it\n  // manually, instead of letting the browser automatically follow it, but this\n  // is not allowed by the fetch API.\n  //\n  // So instead, we must \"replay\" the redirect by fetching the new location\n  // again, but this time we'll append the cache busting search param to prevent\n  // a mismatch.\n  //\n  // TODO: We can optimize Next.js's built-in middleware APIs by returning a\n  // custom status code, to prevent the browser from automatically following it.\n  //\n  // This does not affect Server Action-based redirects; those are encoded\n  // differently, as part of the Flight body. It only affects redirects that\n  // occur in a middleware or a third-party proxy.\n\n  let redirected = browserResponse.redirected\n  if (process.env.__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS) {\n    // This is to prevent a redirect loop. Same limit used by Chrome.\n    const MAX_REDIRECTS = 20\n    for (let n = 0; n < MAX_REDIRECTS; n++) {\n      if (!browserResponse.redirected) {\n        // The server did not perform a redirect.\n        break\n      }\n      const responseUrl = new URL(browserResponse.url, fetchUrl)\n      if (responseUrl.origin !== fetchUrl.origin) {\n        // The server redirected to an external URL. The rest of the logic below\n        // is not relevant, because it only applies to internal redirects.\n        break\n      }\n      if (\n        responseUrl.searchParams.get(NEXT_RSC_UNION_QUERY) ===\n        fetchUrl.searchParams.get(NEXT_RSC_UNION_QUERY)\n      ) {\n        // The redirected URL already includes the cache busting search param.\n        // This was probably intentional. Regardless, there's no reason to\n        // issue another request to this URL because it already has the param\n        // value that we would have added below.\n        break\n      }\n      // The RSC request was redirected. Assume the response is invalid.\n      //\n      // Append the cache busting search param to the redirected URL and\n      // fetch again.\n      fetchUrl = new URL(responseUrl)\n      setCacheBustingSearchParam(fetchUrl, headers)\n      browserResponse = await fetch(fetchUrl, fetchOptions)\n      // We just performed a manual redirect, so this is now true.\n      redirected = true\n    }\n  }\n\n  // Remove the cache busting search param from the response URL, to prevent it\n  // from leaking outside of this function.\n  const responseUrl = new URL(browserResponse.url, fetchUrl)\n  responseUrl.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  const rscResponse: RSCResponse = {\n    url: responseUrl.href,\n\n    // This is true if any redirects occurred, either automatically by the\n    // browser, or manually by us. So it's different from\n    // `browserResponse.redirected`, which only tells us whether the browser\n    // followed a redirect, and only for the last response in the chain.\n    redirected,\n\n    // These can be copied from the last browser response we received. We\n    // intentionally only expose the subset of fields that are actually used\n    // elsewhere in the codebase.\n    ok: browserResponse.ok,\n    headers: browserResponse.headers,\n    body: browserResponse.body,\n    status: browserResponse.status,\n  }\n\n  return rscResponse\n}\n\nexport function createFromNextReadableStream(\n  flightStream: ReadableStream<Uint8Array>\n): Promise<unknown> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n"], "names": ["createFetch", "createFromNextReadableStream", "fetchServerResponse", "createFromReadableStream", "createFromReadableStreamBrowser", "doMpaNavigation", "url", "flightData", "urlToUrlWithoutFlightMarker", "URL", "location", "origin", "toString", "canonicalUrl", "undefined", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "abortController", "AbortController", "window", "addEventListener", "abort", "options", "flightRouterState", "nextUrl", "prefetchKind", "headers", "RSC_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "prepareFlightRouterStateForRequest", "isHmrRefresh", "PrefetchKind", "AUTO", "NEXT_ROUTER_PREFETCH_HEADER", "process", "env", "NODE_ENV", "NEXT_HMR_REFRESH_HEADER", "NEXT_URL", "res", "fetchPriority", "TEMPORARY", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "signal", "responseUrl", "redirected", "contentType", "get", "interception", "includes", "NEXT_DID_POSTPONE_HEADER", "staleTimeHeaderSeconds", "NEXT_ROUTER_STALE_TIME_HEADER", "parseInt", "isFlightResponse", "startsWith", "RSC_CONTENT_TYPE_HEADER", "ok", "body", "hash", "TURBOPACK", "require", "waitForWebpackRuntimeHotUpdate", "flightStream", "createUnclosingPrefetchStream", "response", "getAppBuildId", "b", "normalizeFlightData", "f", "S", "err", "aborted", "console", "error", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "fetchOptions", "credentials", "priority", "fetchUrl", "setCacheBustingSearchParam", "browserResponse", "fetch", "__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS", "MAX_REDIRECTS", "n", "searchParams", "NEXT_RSC_UNION_QUERY", "delete", "rscResponse", "href", "status", "callServer", "findSourceMapURL", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue"], "mappings": "AAAA;;;;;;;;;;;;;;;;;IA2QsBA,WAAW;eAAXA;;IAqHNC,4BAA4B;eAA5BA;;IA3RMC,mBAAmB;eAAnBA;;;wBAjGsD;kCAkBrE;+BACoB;qCACM;oCACJ;mCAKtB;4BACuB;4CACa;6BACC;AAE5C,MAAMC,2BACJC,gCAA+B;AA8BjC,SAASC,gBAAgBC,GAAW;IAClC,OAAO;QACLC,YAAYC,IAAAA,wCAA2B,EACrC,IAAIC,IAAIH,KAAKI,SAASC,MAAM,GAC5BC,QAAQ;QACVC,cAAcC;QACdC,oBAAoB;QACpBC,aAAa;QACbC,WAAW;QACXC,WAAW,CAAC;IACd;AACF;AAEA,IAAIC,kBAAkB,IAAIC;AAE1B,IAAI,OAAOC,WAAW,aAAa;IACjC,sEAAsE;IACtE,8EAA8E;IAC9E,0EAA0E;IAC1E,YAAY;IACZA,OAAOC,gBAAgB,CAAC,YAAY;QAClCH,gBAAgBI,KAAK;IACvB;IAEA,8EAA8E;IAC9E,mEAAmE;IACnEF,OAAOC,gBAAgB,CAAC,YAAY;QAClCH,kBAAkB,IAAIC;IACxB;AACF;AAMO,eAAelB,oBACpBI,GAAQ,EACRkB,OAAmC;IAEnC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGH;IAErD,MAAMI,UAA0B;QAC9B,yBAAyB;QACzB,CAACC,4BAAU,CAAC,EAAE;QACd,mCAAmC;QACnC,CAACC,+CAA6B,CAAC,EAAEC,IAAAA,qDAAkC,EACjEN,mBACAD,QAAQQ,YAAY;IAExB;IAEA;;;;;GAKC,GACD,IAAIL,iBAAiBM,gCAAY,CAACC,IAAI,EAAE;QACtCN,OAAO,CAACO,6CAA2B,CAAC,GAAG;IACzC;IAEA,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBd,QAAQQ,YAAY,EAAE;QAClEJ,OAAO,CAACW,yCAAuB,CAAC,GAAG;IACrC;IAEA,IAAIb,SAAS;QACXE,OAAO,CAACY,0BAAQ,CAAC,GAAGd;IACtB;IAEA,IAAI;YAoCqBe;QAnCvB,wHAAwH;QACxH,4HAA4H;QAC5H,kEAAkE;QAClE,yHAAyH;QACzH,MAAMC,gBAAgBf,eAClBA,iBAAiBM,gCAAY,CAACU,SAAS,GACrC,SACA,QACF;QAEJ,IAAIP,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAIF,QAAQC,GAAG,CAACO,oBAAoB,KAAK,UAAU;gBACjD,oEAAoE;gBACpE,oEAAoE;gBACpE,kBAAkB;gBAClBtC,MAAM,IAAIG,IAAIH;gBACd,IAAIA,IAAIuC,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBAC9BxC,IAAIuC,QAAQ,IAAI;gBAClB,OAAO;oBACLvC,IAAIuC,QAAQ,IAAI;gBAClB;YACF;QACF;QAEA,MAAMJ,MAAM,MAAMzC,YAChBM,KACAsB,SACAc,eACAvB,gBAAgB4B,MAAM;QAGxB,MAAMC,cAAcxC,IAAAA,wCAA2B,EAAC,IAAIC,IAAIgC,IAAInC,GAAG;QAC/D,MAAMO,eAAe4B,IAAIQ,UAAU,GAAGD,cAAclC;QAEpD,MAAMoC,cAAcT,IAAIb,OAAO,CAACuB,GAAG,CAAC,mBAAmB;QACvD,MAAMC,eAAe,CAAC,GAACX,mBAAAA,IAAIb,OAAO,CAACuB,GAAG,CAAC,4BAAhBV,iBAAyBY,QAAQ,CAACb,0BAAQ;QACjE,MAAMvB,YAAY,CAAC,CAACwB,IAAIb,OAAO,CAACuB,GAAG,CAACG,0CAAwB;QAC5D,MAAMC,yBAAyBd,IAAIb,OAAO,CAACuB,GAAG,CAC5CK,+CAA6B;QAE/B,MAAMtC,YACJqC,2BAA2B,OACvBE,SAASF,wBAAwB,MAAM,OACvC,CAAC;QACP,IAAIG,mBAAmBR,YAAYS,UAAU,CAACC,yCAAuB;QAErE,IAAIxB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAIF,QAAQC,GAAG,CAACO,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAACc,kBAAkB;oBACrBA,mBAAmBR,YAAYS,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACD,oBAAoB,CAACjB,IAAIoB,EAAE,IAAI,CAACpB,IAAIqB,IAAI,EAAE;YAC7C,2FAA2F;YAC3F,IAAIxD,IAAIyD,IAAI,EAAE;gBACZf,YAAYe,IAAI,GAAGzD,IAAIyD,IAAI;YAC7B;YAEA,OAAO1D,gBAAgB2C,YAAYpC,QAAQ;QAC7C;QAEA,uEAAuE;QACvE,0DAA0D;QAC1D,wDAAwD;QACxD,oGAAoG;QACpG,IAAIwB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,CAACF,QAAQC,GAAG,CAAC2B,SAAS,EAAE;YACnE,MAAM,AACJC,QAAQ,+CACRC,8BAA8B;QAClC;QAEA,2EAA2E;QAC3E,MAAMC,eAAelD,YACjBmD,8BAA8B3B,IAAIqB,IAAI,IACtCrB,IAAIqB,IAAI;QACZ,MAAMO,WAAW,MAAOpE,6BACtBkE;QAGF,IAAIG,IAAAA,yBAAa,QAAOD,SAASE,CAAC,EAAE;YAClC,OAAOlE,gBAAgBoC,IAAInC,GAAG;QAChC;QAEA,OAAO;YACLC,YAAYiE,IAAAA,sCAAmB,EAACH,SAASI,CAAC;YAC1C5D,cAAcA;YACdE,oBAAoBqC;YACpBpC,aAAaqD,SAASK,CAAC;YACvBzD;YACAC;QACF;IACF,EAAE,OAAOyD,KAAK;QACZ,IAAI,CAACxD,gBAAgB4B,MAAM,CAAC6B,OAAO,EAAE;YACnCC,QAAQC,KAAK,CACX,AAAC,qCAAkCxE,MAAI,yCACvCqE;QAEJ;QAEA,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YACLpE,YAAYD,IAAIM,QAAQ;YACxBC,cAAcC;YACdC,oBAAoB;YACpBC,aAAa;YACbC,WAAW;YACXC,WAAW,CAAC;QACd;IACF;AACF;AAgBO,eAAelB,YACpBM,GAAQ,EACRsB,OAAuB,EACvBc,aAA6C,EAC7CK,MAAoB;IAEpB,6EAA6E;IAC7E,0DAA0D;IAC1D,uBAAuB;IAEvB,IAAIX,QAAQC,GAAG,CAAC0C,gBAAgB,IAAIrC,kBAAkB,MAAM;QAC1Dd,OAAO,CAAC,2BAA2B,GAAGc;IACxC;IAEA,IAAIN,QAAQC,GAAG,CAAC2C,kBAAkB,EAAE;QAClCpD,OAAO,CAAC,kBAAkB,GAAGQ,QAAQC,GAAG,CAAC2C,kBAAkB;IAC7D;IAEA,MAAMC,eAA4B;QAChC,wFAAwF;QACxFC,aAAa;QACbtD;QACAuD,UAAUzC,iBAAiB5B;QAC3BiC;IACF;IACA,6EAA6E;IAC7E,2EAA2E;IAC3E,yBAAyB;IACzB,IAAIqC,WAAW,IAAI3E,IAAIH;IACvB+E,IAAAA,sDAA0B,EAACD,UAAUxD;IACrC,IAAI0D,kBAAkB,MAAMC,MAAMH,UAAUH;IAE5C,wEAAwE;IACxE,sEAAsE;IACtE,wEAAwE;IACxE,0EAA0E;IAC1E,6EAA6E;IAC7E,cAAc;IACd,EAAE;IACF,8EAA8E;IAC9E,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,yEAAyE;IACzE,8EAA8E;IAC9E,cAAc;IACd,EAAE;IACF,0EAA0E;IAC1E,8EAA8E;IAC9E,EAAE;IACF,wEAAwE;IACxE,0EAA0E;IAC1E,gDAAgD;IAEhD,IAAIhC,aAAaqC,gBAAgBrC,UAAU;IAC3C,IAAIb,QAAQC,GAAG,CAACmD,0CAA0C,EAAE;QAC1D,iEAAiE;QACjE,MAAMC,gBAAgB;QACtB,IAAK,IAAIC,IAAI,GAAGA,IAAID,eAAeC,IAAK;YACtC,IAAI,CAACJ,gBAAgBrC,UAAU,EAAE;gBAE/B;YACF;YACA,MAAMD,cAAc,IAAIvC,IAAI6E,gBAAgBhF,GAAG,EAAE8E;YACjD,IAAIpC,YAAYrC,MAAM,KAAKyE,SAASzE,MAAM,EAAE;gBAG1C;YACF;YACA,IACEqC,YAAY2C,YAAY,CAACxC,GAAG,CAACyC,sCAAoB,MACjDR,SAASO,YAAY,CAACxC,GAAG,CAACyC,sCAAoB,GAC9C;gBAKA;YACF;YACA,kEAAkE;YAClE,EAAE;YACF,kEAAkE;YAClE,eAAe;YACfR,WAAW,IAAI3E,IAAIuC;YACnBqC,IAAAA,sDAA0B,EAACD,UAAUxD;YACrC0D,kBAAkB,MAAMC,MAAMH,UAAUH;YACxC,4DAA4D;YAC5DhC,aAAa;QACf;IACF;IAEA,6EAA6E;IAC7E,yCAAyC;IACzC,MAAMD,cAAc,IAAIvC,IAAI6E,gBAAgBhF,GAAG,EAAE8E;IACjDpC,YAAY2C,YAAY,CAACE,MAAM,CAACD,sCAAoB;IAEpD,MAAME,cAA2B;QAC/BxF,KAAK0C,YAAY+C,IAAI;QAErB,sEAAsE;QACtE,qDAAqD;QACrD,wEAAwE;QACxE,oEAAoE;QACpE9C;QAEA,qEAAqE;QACrE,wEAAwE;QACxE,6BAA6B;QAC7BY,IAAIyB,gBAAgBzB,EAAE;QACtBjC,SAAS0D,gBAAgB1D,OAAO;QAChCkC,MAAMwB,gBAAgBxB,IAAI;QAC1BkC,QAAQV,gBAAgBU,MAAM;IAChC;IAEA,OAAOF;AACT;AAEO,SAAS7F,6BACdkE,YAAwC;IAExC,OAAOhE,yBAAyBgE,cAAc;QAC5C8B,YAAAA,yBAAU;QACVC,kBAAAA,qCAAgB;IAClB;AACF;AAEA,SAAS9B,8BACP+B,oBAAgD;IAEhD,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,MAAMC,SAASD,qBAAqBE,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBACnB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF", "ignoreList": [0]}