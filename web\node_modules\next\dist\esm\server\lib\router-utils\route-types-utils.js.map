{"version": 3, "sources": ["../../../../src/server/lib/router-utils/route-types-utils.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  getRouteRegex,\n  type Group,\n} from '../../../shared/lib/router/utils/route-regex'\nimport type { NextConfigComplete } from '../../config-shared'\n\nimport fs from 'fs'\nimport {\n  generateRouteTypesFile,\n  generateLinkTypesFile,\n  generateValidatorFile,\n} from './typegen'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from '../../../shared/lib/router/utils/interception-routes'\n\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\n\ninterface RouteInfo {\n  path: string\n  groups: { [groupName: string]: Group }\n}\n\nexport interface RouteTypesManifest {\n  appRoutes: Record<string, RouteInfo>\n  pageRoutes: Record<string, RouteInfo>\n  layoutRoutes: Record<string, RouteInfo & { slots: string[] }>\n  appRouteHandlerRoutes: Record<string, RouteInfo>\n  /** Map of redirect source => RouteInfo */\n  redirectRoutes: Record<string, RouteInfo>\n  /** Map of rewrite source => RouteInfo */\n  rewriteRoutes: Record<string, RouteInfo>\n  /** File paths for validation */\n  appPagePaths: Set<string>\n  pagesRouterPagePaths: Set<string>\n  layoutPaths: Set<string>\n  appRouteHandlers: Set<string>\n  pageApiRoutes: Set<string>\n  /** Direct mapping from file paths to routes for validation */\n  filePathToRoute: Map<string, string>\n}\n\n// Convert a custom-route source string (`/blog/:slug`, `/docs/:path*`, ...)\n// into the bracket-syntax used by other Next.js route helpers so that we can\n// reuse `getRouteRegex()` to extract groups.\nexport function convertCustomRouteSource(source: string): string[] {\n  const parseResult = tryToParsePath(source)\n\n  if (parseResult.error || !parseResult.tokens) {\n    // Fallback to original source if parsing fails\n    return source.startsWith('/') ? [source] : ['/' + source]\n  }\n\n  const possibleNormalizedRoutes = ['']\n  let slugCnt = 1\n\n  function append(suffix: string) {\n    for (let i = 0; i < possibleNormalizedRoutes.length; i++) {\n      possibleNormalizedRoutes[i] += suffix\n    }\n  }\n\n  function fork(suffix: string) {\n    const currentLength = possibleNormalizedRoutes.length\n    for (let i = 0; i < currentLength; i++) {\n      possibleNormalizedRoutes.push(possibleNormalizedRoutes[i] + suffix)\n    }\n  }\n\n  for (const token of parseResult.tokens) {\n    if (typeof token === 'object') {\n      // Make sure the slug is always named.\n      const slug = token.name || (slugCnt++ === 1 ? 'slug' : `slug${slugCnt}`)\n      if (token.modifier === '*') {\n        append(`${token.prefix}[[...${slug}]]`)\n      } else if (token.modifier === '+') {\n        append(`${token.prefix}[...${slug}]`)\n      } else if (token.modifier === '') {\n        if (token.pattern === '[^\\\\/#\\\\?]+?') {\n          // A safe slug\n          append(`${token.prefix}[${slug}]`)\n        } else if (token.pattern === '.*') {\n          // An optional catch-all slug\n          append(`${token.prefix}[[...${slug}]]`)\n        } else if (token.pattern === '.+') {\n          // A catch-all slug\n          append(`${token.prefix}[...${slug}]`)\n        } else {\n          // Other regex patterns are not supported. Skip this route.\n          return []\n        }\n      } else if (token.modifier === '?') {\n        if (/^[a-zA-Z0-9_/]*$/.test(token.pattern)) {\n          // An optional slug with plain text only, fork the route.\n          append(token.prefix)\n          fork(token.pattern)\n        } else {\n          // Optional modifier `?` and regex patterns are not supported.\n          return []\n        }\n      }\n    } else if (typeof token === 'string') {\n      append(token)\n    }\n  }\n\n  // Ensure leading slash\n  return possibleNormalizedRoutes.map((route) =>\n    route.startsWith('/') ? route : '/' + route\n  )\n}\n\n/**\n * Extracts route parameters from a route pattern\n */\nexport function extractRouteParams(route: string) {\n  const regex = getRouteRegex(route)\n  return regex.groups\n}\n\n/**\n * Resolves an intercepting route to its canonical equivalent\n * Example: /gallery/test/(..)photo/[id] -> /gallery/photo/[id]\n */\nfunction resolveInterceptingRoute(route: string): string {\n  // Reuse centralized interception route normalization logic\n  try {\n    if (!isInterceptionRouteAppPath(route)) return route\n    const { interceptedRoute } = extractInterceptionRouteInformation(route)\n    return interceptedRoute\n  } catch {\n    // If parsing fails, fall back to the original route\n    return route\n  }\n}\n\n/**\n * Creates a route types manifest from processed route data\n * (used for both build and dev)\n */\nexport async function createRouteTypesManifest({\n  dir,\n  pageRoutes,\n  appRoutes,\n  appRouteHandlers,\n  pageApiRoutes,\n  layoutRoutes,\n  slots,\n  redirects,\n  rewrites,\n  validatorFilePath,\n}: {\n  dir: string\n  pageRoutes: Array<{ route: string; filePath: string }>\n  appRoutes: Array<{ route: string; filePath: string }>\n  appRouteHandlers: Array<{ route: string; filePath: string }>\n  pageApiRoutes: Array<{ route: string; filePath: string }>\n  layoutRoutes: Array<{ route: string; filePath: string }>\n  slots: Array<{ name: string; parent: string }>\n  redirects?: NextConfigComplete['redirects']\n  rewrites?: NextConfigComplete['rewrites']\n  validatorFilePath?: string\n}): Promise<RouteTypesManifest> {\n  // Helper function to calculate the correct relative path\n  const getRelativePath = (filePath: string) => {\n    if (validatorFilePath) {\n      // For validator generation, calculate path relative to validator directory\n      return normalizePathSep(\n        path.relative(path.dirname(validatorFilePath), filePath)\n      )\n    }\n    // For other uses, calculate path relative to project directory\n    return normalizePathSep(path.relative(dir, filePath))\n  }\n\n  const manifest: RouteTypesManifest = {\n    appRoutes: {},\n    pageRoutes: {},\n    layoutRoutes: {},\n    appRouteHandlerRoutes: {},\n    redirectRoutes: {},\n    rewriteRoutes: {},\n    appRouteHandlers: new Set(\n      appRouteHandlers.map(({ filePath }) => getRelativePath(filePath))\n    ),\n    pageApiRoutes: new Set(\n      pageApiRoutes.map(({ filePath }) => getRelativePath(filePath))\n    ),\n    appPagePaths: new Set(\n      appRoutes.map(({ filePath }) => getRelativePath(filePath))\n    ),\n    pagesRouterPagePaths: new Set(\n      pageRoutes.map(({ filePath }) => getRelativePath(filePath))\n    ),\n    layoutPaths: new Set(\n      layoutRoutes.map(({ filePath }) => getRelativePath(filePath))\n    ),\n    filePathToRoute: new Map([\n      ...appRoutes.map(\n        ({ route, filePath }) =>\n          [getRelativePath(filePath), resolveInterceptingRoute(route)] as [\n            string,\n            string,\n          ]\n      ),\n      ...layoutRoutes.map(\n        ({ route, filePath }) =>\n          [getRelativePath(filePath), resolveInterceptingRoute(route)] as [\n            string,\n            string,\n          ]\n      ),\n      ...appRouteHandlers.map(\n        ({ route, filePath }) =>\n          [getRelativePath(filePath), resolveInterceptingRoute(route)] as [\n            string,\n            string,\n          ]\n      ),\n      ...pageRoutes.map(\n        ({ route, filePath }) =>\n          [getRelativePath(filePath), route] as [string, string]\n      ),\n      ...pageApiRoutes.map(\n        ({ route, filePath }) =>\n          [getRelativePath(filePath), route] as [string, string]\n      ),\n    ]),\n  }\n\n  // Process page routes\n  for (const { route, filePath } of pageRoutes) {\n    manifest.pageRoutes[route] = {\n      path: getRelativePath(filePath),\n      groups: extractRouteParams(route),\n    }\n  }\n\n  // Process layout routes\n  for (const { route, filePath } of layoutRoutes) {\n    // Use the resolved route (for interception routes, this gives us the canonical route)\n    const resolvedRoute = resolveInterceptingRoute(route)\n\n    if (!manifest.layoutRoutes[resolvedRoute]) {\n      manifest.layoutRoutes[resolvedRoute] = {\n        path: getRelativePath(filePath),\n        groups: extractRouteParams(resolvedRoute),\n        slots: [],\n      }\n    }\n  }\n\n  // Process slots\n  for (const slot of slots) {\n    if (manifest.layoutRoutes[slot.parent]) {\n      manifest.layoutRoutes[slot.parent].slots.push(slot.name)\n    }\n  }\n\n  // Process app routes\n  for (const { route, filePath } of appRoutes) {\n    // Don't include metadata routes or pages\n    if (\n      !filePath.endsWith('page.ts') &&\n      !filePath.endsWith('page.tsx') &&\n      !filePath.endsWith('.mdx') &&\n      !filePath.endsWith('.md')\n    ) {\n      continue\n    }\n\n    // Use the resolved route (for interception routes, this gives us the canonical route)\n    const resolvedRoute = resolveInterceptingRoute(route)\n\n    if (!manifest.appRoutes[resolvedRoute]) {\n      manifest.appRoutes[resolvedRoute] = {\n        path: getRelativePath(filePath),\n        groups: extractRouteParams(resolvedRoute),\n      }\n    }\n  }\n\n  // Process app route handlers\n  for (const { route, filePath } of appRouteHandlers) {\n    // Use the resolved route (for interception routes, this gives us the canonical route)\n    const resolvedRoute = resolveInterceptingRoute(route)\n\n    if (!manifest.appRouteHandlerRoutes[resolvedRoute]) {\n      manifest.appRouteHandlerRoutes[resolvedRoute] = {\n        path: getRelativePath(filePath),\n        groups: extractRouteParams(resolvedRoute),\n      }\n    }\n  }\n\n  // Process redirects\n  if (typeof redirects === 'function') {\n    const rd = await redirects()\n\n    for (const item of rd) {\n      const possibleRoutes = convertCustomRouteSource(item.source)\n      for (const route of possibleRoutes) {\n        manifest.redirectRoutes[route] = {\n          path: route,\n          groups: extractRouteParams(route),\n        }\n      }\n    }\n  }\n\n  // Process rewrites\n  if (typeof rewrites === 'function') {\n    const rw = await rewrites()\n\n    const allSources = Array.isArray(rw)\n      ? rw\n      : [\n          ...(rw?.beforeFiles || []),\n          ...(rw?.afterFiles || []),\n          ...(rw?.fallback || []),\n        ]\n\n    for (const item of allSources) {\n      const possibleRoutes = convertCustomRouteSource(item.source)\n      for (const route of possibleRoutes) {\n        manifest.rewriteRoutes[route] = {\n          path: route,\n          groups: extractRouteParams(route),\n        }\n      }\n    }\n  }\n\n  return manifest\n}\n\nexport async function writeRouteTypesManifest(\n  manifest: RouteTypesManifest,\n  filePath: string,\n  config: NextConfigComplete\n) {\n  const dirname = path.dirname(filePath)\n\n  if (!fs.existsSync(dirname)) {\n    await fs.promises.mkdir(dirname, { recursive: true })\n  }\n\n  // Write the main routes.d.ts file\n  await fs.promises.writeFile(filePath, generateRouteTypesFile(manifest))\n\n  // Write the link.d.ts file if typedRoutes is enabled\n  if (config.typedRoutes === true) {\n    const linkTypesPath = path.join(dirname, 'link.d.ts')\n    await fs.promises.writeFile(linkTypesPath, generateLinkTypesFile(manifest))\n  }\n}\n\nexport async function writeValidatorFile(\n  manifest: RouteTypesManifest,\n  filePath: string\n) {\n  const dirname = path.dirname(filePath)\n\n  if (!fs.existsSync(dirname)) {\n    await fs.promises.mkdir(dirname, { recursive: true })\n  }\n\n  await fs.promises.writeFile(filePath, generateValidatorFile(manifest))\n}\n"], "names": ["path", "getRouteRegex", "fs", "generateRouteTypesFile", "generateLinkTypesFile", "generateValidatorFile", "tryToParsePath", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "normalizePathSep", "convertCustomRouteSource", "source", "parseResult", "error", "tokens", "startsWith", "possibleNormalizedRoutes", "slugCnt", "append", "suffix", "i", "length", "fork", "<PERSON><PERSON><PERSON><PERSON>", "push", "token", "slug", "name", "modifier", "prefix", "pattern", "test", "map", "route", "extractRouteParams", "regex", "groups", "resolveInterceptingRoute", "interceptedRoute", "createRouteTypesManifest", "dir", "pageRoutes", "appRoutes", "appRouteHandlers", "pageApiRoutes", "layoutRoutes", "slots", "redirects", "rewrites", "validatorFilePath", "getRelativePath", "filePath", "relative", "dirname", "manifest", "appRouteHandlerRoutes", "redirectRoutes", "rewriteRoutes", "Set", "appPagePaths", "pagesRouterPagePaths", "layoutPaths", "filePathToRoute", "Map", "resolvedRoute", "slot", "parent", "endsWith", "rd", "item", "possibleRoutes", "rw", "allSources", "Array", "isArray", "beforeFiles", "afterFiles", "fallback", "writeRouteTypesManifest", "config", "existsSync", "promises", "mkdir", "recursive", "writeFile", "typedRoutes", "linkTypesPath", "join", "writeValidatorFile"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SACEC,aAAa,QAER,+CAA8C;AAGrD,OAAOC,QAAQ,KAAI;AACnB,SACEC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,QAChB,YAAW;AAClB,SAASC,cAAc,QAAQ,iCAAgC;AAC/D,SACEC,mCAAmC,EACnCC,0BAA0B,QACrB,uDAAsD;AAE7D,SAASC,gBAAgB,QAAQ,mDAAkD;AA0BnF,4EAA4E;AAC5E,6EAA6E;AAC7E,6CAA6C;AAC7C,OAAO,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,cAAcN,eAAeK;IAEnC,IAAIC,YAAYC,KAAK,IAAI,CAACD,YAAYE,MAAM,EAAE;QAC5C,+CAA+C;QAC/C,OAAOH,OAAOI,UAAU,CAAC,OAAO;YAACJ;SAAO,GAAG;YAAC,MAAMA;SAAO;IAC3D;IAEA,MAAMK,2BAA2B;QAAC;KAAG;IACrC,IAAIC,UAAU;IAEd,SAASC,OAAOC,MAAc;QAC5B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,yBAAyBK,MAAM,EAAED,IAAK;YACxDJ,wBAAwB,CAACI,EAAE,IAAID;QACjC;IACF;IAEA,SAASG,KAAKH,MAAc;QAC1B,MAAMI,gBAAgBP,yBAAyBK,MAAM;QACrD,IAAK,IAAID,IAAI,GAAGA,IAAIG,eAAeH,IAAK;YACtCJ,yBAAyBQ,IAAI,CAACR,wBAAwB,CAACI,EAAE,GAAGD;QAC9D;IACF;IAEA,KAAK,MAAMM,SAASb,YAAYE,MAAM,CAAE;QACtC,IAAI,OAAOW,UAAU,UAAU;YAC7B,sCAAsC;YACtC,MAAMC,OAAOD,MAAME,IAAI,IAAKV,CAAAA,cAAc,IAAI,SAAS,CAAC,IAAI,EAAEA,SAAS,AAAD;YACtE,IAAIQ,MAAMG,QAAQ,KAAK,KAAK;gBAC1BV,OAAO,GAAGO,MAAMI,MAAM,CAAC,KAAK,EAAEH,KAAK,EAAE,CAAC;YACxC,OAAO,IAAID,MAAMG,QAAQ,KAAK,KAAK;gBACjCV,OAAO,GAAGO,MAAMI,MAAM,CAAC,IAAI,EAAEH,KAAK,CAAC,CAAC;YACtC,OAAO,IAAID,MAAMG,QAAQ,KAAK,IAAI;gBAChC,IAAIH,MAAMK,OAAO,KAAK,gBAAgB;oBACpC,cAAc;oBACdZ,OAAO,GAAGO,MAAMI,MAAM,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC;gBACnC,OAAO,IAAID,MAAMK,OAAO,KAAK,MAAM;oBACjC,6BAA6B;oBAC7BZ,OAAO,GAAGO,MAAMI,MAAM,CAAC,KAAK,EAAEH,KAAK,EAAE,CAAC;gBACxC,OAAO,IAAID,MAAMK,OAAO,KAAK,MAAM;oBACjC,mBAAmB;oBACnBZ,OAAO,GAAGO,MAAMI,MAAM,CAAC,IAAI,EAAEH,KAAK,CAAC,CAAC;gBACtC,OAAO;oBACL,2DAA2D;oBAC3D,OAAO,EAAE;gBACX;YACF,OAAO,IAAID,MAAMG,QAAQ,KAAK,KAAK;gBACjC,IAAI,mBAAmBG,IAAI,CAACN,MAAMK,OAAO,GAAG;oBAC1C,yDAAyD;oBACzDZ,OAAOO,MAAMI,MAAM;oBACnBP,KAAKG,MAAMK,OAAO;gBACpB,OAAO;oBACL,8DAA8D;oBAC9D,OAAO,EAAE;gBACX;YACF;QACF,OAAO,IAAI,OAAOL,UAAU,UAAU;YACpCP,OAAOO;QACT;IACF;IAEA,uBAAuB;IACvB,OAAOT,yBAAyBgB,GAAG,CAAC,CAACC,QACnCA,MAAMlB,UAAU,CAAC,OAAOkB,QAAQ,MAAMA;AAE1C;AAEA;;CAEC,GACD,OAAO,SAASC,mBAAmBD,KAAa;IAC9C,MAAME,QAAQlC,cAAcgC;IAC5B,OAAOE,MAAMC,MAAM;AACrB;AAEA;;;CAGC,GACD,SAASC,yBAAyBJ,KAAa;IAC7C,2DAA2D;IAC3D,IAAI;QACF,IAAI,CAACzB,2BAA2ByB,QAAQ,OAAOA;QAC/C,MAAM,EAAEK,gBAAgB,EAAE,GAAG/B,oCAAoC0B;QACjE,OAAOK;IACT,EAAE,OAAM;QACN,oDAAoD;QACpD,OAAOL;IACT;AACF;AAEA;;;CAGC,GACD,OAAO,eAAeM,yBAAyB,EAC7CC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,aAAa,EACbC,YAAY,EACZC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,iBAAiB,EAYlB;IACC,yDAAyD;IACzD,MAAMC,kBAAkB,CAACC;QACvB,IAAIF,mBAAmB;YACrB,2EAA2E;YAC3E,OAAOxC,iBACLT,KAAKoD,QAAQ,CAACpD,KAAKqD,OAAO,CAACJ,oBAAoBE;QAEnD;QACA,+DAA+D;QAC/D,OAAO1C,iBAAiBT,KAAKoD,QAAQ,CAACZ,KAAKW;IAC7C;IAEA,MAAMG,WAA+B;QACnCZ,WAAW,CAAC;QACZD,YAAY,CAAC;QACbI,cAAc,CAAC;QACfU,uBAAuB,CAAC;QACxBC,gBAAgB,CAAC;QACjBC,eAAe,CAAC;QAChBd,kBAAkB,IAAIe,IACpBf,iBAAiBX,GAAG,CAAC,CAAC,EAAEmB,QAAQ,EAAE,GAAKD,gBAAgBC;QAEzDP,eAAe,IAAIc,IACjBd,cAAcZ,GAAG,CAAC,CAAC,EAAEmB,QAAQ,EAAE,GAAKD,gBAAgBC;QAEtDQ,cAAc,IAAID,IAChBhB,UAAUV,GAAG,CAAC,CAAC,EAAEmB,QAAQ,EAAE,GAAKD,gBAAgBC;QAElDS,sBAAsB,IAAIF,IACxBjB,WAAWT,GAAG,CAAC,CAAC,EAAEmB,QAAQ,EAAE,GAAKD,gBAAgBC;QAEnDU,aAAa,IAAIH,IACfb,aAAab,GAAG,CAAC,CAAC,EAAEmB,QAAQ,EAAE,GAAKD,gBAAgBC;QAErDW,iBAAiB,IAAIC,IAAI;eACpBrB,UAAUV,GAAG,CACd,CAAC,EAAEC,KAAK,EAAEkB,QAAQ,EAAE,GAClB;oBAACD,gBAAgBC;oBAAWd,yBAAyBJ;iBAAO;eAK7DY,aAAab,GAAG,CACjB,CAAC,EAAEC,KAAK,EAAEkB,QAAQ,EAAE,GAClB;oBAACD,gBAAgBC;oBAAWd,yBAAyBJ;iBAAO;eAK7DU,iBAAiBX,GAAG,CACrB,CAAC,EAAEC,KAAK,EAAEkB,QAAQ,EAAE,GAClB;oBAACD,gBAAgBC;oBAAWd,yBAAyBJ;iBAAO;eAK7DQ,WAAWT,GAAG,CACf,CAAC,EAAEC,KAAK,EAAEkB,QAAQ,EAAE,GAClB;oBAACD,gBAAgBC;oBAAWlB;iBAAM;eAEnCW,cAAcZ,GAAG,CAClB,CAAC,EAAEC,KAAK,EAAEkB,QAAQ,EAAE,GAClB;oBAACD,gBAAgBC;oBAAWlB;iBAAM;SAEvC;IACH;IAEA,sBAAsB;IACtB,KAAK,MAAM,EAAEA,KAAK,EAAEkB,QAAQ,EAAE,IAAIV,WAAY;QAC5Ca,SAASb,UAAU,CAACR,MAAM,GAAG;YAC3BjC,MAAMkD,gBAAgBC;YACtBf,QAAQF,mBAAmBD;QAC7B;IACF;IAEA,wBAAwB;IACxB,KAAK,MAAM,EAAEA,KAAK,EAAEkB,QAAQ,EAAE,IAAIN,aAAc;QAC9C,sFAAsF;QACtF,MAAMmB,gBAAgB3B,yBAAyBJ;QAE/C,IAAI,CAACqB,SAAST,YAAY,CAACmB,cAAc,EAAE;YACzCV,SAAST,YAAY,CAACmB,cAAc,GAAG;gBACrChE,MAAMkD,gBAAgBC;gBACtBf,QAAQF,mBAAmB8B;gBAC3BlB,OAAO,EAAE;YACX;QACF;IACF;IAEA,gBAAgB;IAChB,KAAK,MAAMmB,QAAQnB,MAAO;QACxB,IAAIQ,SAAST,YAAY,CAACoB,KAAKC,MAAM,CAAC,EAAE;YACtCZ,SAAST,YAAY,CAACoB,KAAKC,MAAM,CAAC,CAACpB,KAAK,CAACtB,IAAI,CAACyC,KAAKtC,IAAI;QACzD;IACF;IAEA,qBAAqB;IACrB,KAAK,MAAM,EAAEM,KAAK,EAAEkB,QAAQ,EAAE,IAAIT,UAAW;QAC3C,yCAAyC;QACzC,IACE,CAACS,SAASgB,QAAQ,CAAC,cACnB,CAAChB,SAASgB,QAAQ,CAAC,eACnB,CAAChB,SAASgB,QAAQ,CAAC,WACnB,CAAChB,SAASgB,QAAQ,CAAC,QACnB;YACA;QACF;QAEA,sFAAsF;QACtF,MAAMH,gBAAgB3B,yBAAyBJ;QAE/C,IAAI,CAACqB,SAASZ,SAAS,CAACsB,cAAc,EAAE;YACtCV,SAASZ,SAAS,CAACsB,cAAc,GAAG;gBAClChE,MAAMkD,gBAAgBC;gBACtBf,QAAQF,mBAAmB8B;YAC7B;QACF;IACF;IAEA,6BAA6B;IAC7B,KAAK,MAAM,EAAE/B,KAAK,EAAEkB,QAAQ,EAAE,IAAIR,iBAAkB;QAClD,sFAAsF;QACtF,MAAMqB,gBAAgB3B,yBAAyBJ;QAE/C,IAAI,CAACqB,SAASC,qBAAqB,CAACS,cAAc,EAAE;YAClDV,SAASC,qBAAqB,CAACS,cAAc,GAAG;gBAC9ChE,MAAMkD,gBAAgBC;gBACtBf,QAAQF,mBAAmB8B;YAC7B;QACF;IACF;IAEA,oBAAoB;IACpB,IAAI,OAAOjB,cAAc,YAAY;QACnC,MAAMqB,KAAK,MAAMrB;QAEjB,KAAK,MAAMsB,QAAQD,GAAI;YACrB,MAAME,iBAAiB5D,yBAAyB2D,KAAK1D,MAAM;YAC3D,KAAK,MAAMsB,SAASqC,eAAgB;gBAClChB,SAASE,cAAc,CAACvB,MAAM,GAAG;oBAC/BjC,MAAMiC;oBACNG,QAAQF,mBAAmBD;gBAC7B;YACF;QACF;IACF;IAEA,mBAAmB;IACnB,IAAI,OAAOe,aAAa,YAAY;QAClC,MAAMuB,KAAK,MAAMvB;QAEjB,MAAMwB,aAAaC,MAAMC,OAAO,CAACH,MAC7BA,KACA;eACMA,CAAAA,sBAAAA,GAAII,WAAW,KAAI,EAAE;eACrBJ,CAAAA,sBAAAA,GAAIK,UAAU,KAAI,EAAE;eACpBL,CAAAA,sBAAAA,GAAIM,QAAQ,KAAI,EAAE;SACvB;QAEL,KAAK,MAAMR,QAAQG,WAAY;YAC7B,MAAMF,iBAAiB5D,yBAAyB2D,KAAK1D,MAAM;YAC3D,KAAK,MAAMsB,SAASqC,eAAgB;gBAClChB,SAASG,aAAa,CAACxB,MAAM,GAAG;oBAC9BjC,MAAMiC;oBACNG,QAAQF,mBAAmBD;gBAC7B;YACF;QACF;IACF;IAEA,OAAOqB;AACT;AAEA,OAAO,eAAewB,wBACpBxB,QAA4B,EAC5BH,QAAgB,EAChB4B,MAA0B;IAE1B,MAAM1B,UAAUrD,KAAKqD,OAAO,CAACF;IAE7B,IAAI,CAACjD,GAAG8E,UAAU,CAAC3B,UAAU;QAC3B,MAAMnD,GAAG+E,QAAQ,CAACC,KAAK,CAAC7B,SAAS;YAAE8B,WAAW;QAAK;IACrD;IAEA,kCAAkC;IAClC,MAAMjF,GAAG+E,QAAQ,CAACG,SAAS,CAACjC,UAAUhD,uBAAuBmD;IAE7D,qDAAqD;IACrD,IAAIyB,OAAOM,WAAW,KAAK,MAAM;QAC/B,MAAMC,gBAAgBtF,KAAKuF,IAAI,CAAClC,SAAS;QACzC,MAAMnD,GAAG+E,QAAQ,CAACG,SAAS,CAACE,eAAelF,sBAAsBkD;IACnE;AACF;AAEA,OAAO,eAAekC,mBACpBlC,QAA4B,EAC5BH,QAAgB;IAEhB,MAAME,UAAUrD,KAAKqD,OAAO,CAACF;IAE7B,IAAI,CAACjD,GAAG8E,UAAU,CAAC3B,UAAU;QAC3B,MAAMnD,GAAG+E,QAAQ,CAACC,KAAK,CAAC7B,SAAS;YAAE8B,WAAW;QAAK;IACrD;IAEA,MAAMjF,GAAG+E,QAAQ,CAACG,SAAS,CAACjC,UAAU9C,sBAAsBiD;AAC9D", "ignoreList": [0]}