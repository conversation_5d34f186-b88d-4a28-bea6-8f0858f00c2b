{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-webpack.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport type { Duplex } from 'stream'\nimport type { Telemetry } from '../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\n\nimport { type webpack, StringXor } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from './middleware-webpack'\nimport { WebpackHotMiddleware } from './hot-middleware'\nimport { join, relative, isAbsolute, posix, dirname } from 'path'\nimport {\n  createEntrypoints,\n  createPagesMapping,\n  finalizeEntrypoint,\n  getClientEntry,\n  getEdgeServerEntry,\n  getAppEntry,\n  runDependingOnPageType,\n  getStaticInfoIncludingLayouts,\n  getInstrumentationEntry,\n} from '../../build/entries'\nimport { watchCompilers } from '../../build/output'\nimport * as Log from '../../build/output/log'\nimport getBaseWebpackConfig, {\n  getCacheDirectories,\n  loadProjectInfo,\n} from '../../build/webpack-config'\nimport { APP_DIR_ALIAS, WEBPACK_LAYERS } from '../../lib/constants'\nimport { recursiveDelete } from '../../lib/recursive-delete'\nimport {\n  BLOCKED_PAGES,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  RSC_MODULE_TYPES,\n} from '../../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../api-utils'\nimport { getPathMatch } from '../../shared/lib/router/utils/path-match'\nimport { findPageFile } from '../lib/find-page-file'\nimport {\n  BUILDING,\n  getEntries,\n  EntryTypes,\n  getInvalidator,\n  onDemandEntryHandler,\n} from './on-demand-entry-handler'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\nimport {\n  difference,\n  isInstrumentationHookFile,\n  isMiddlewareFile,\n  isMiddlewareFilename,\n} from '../../build/utils'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { type Span, trace } from '../../trace'\nimport { getProperError } from '../../lib/is-error'\nimport ws from 'next/dist/compiled/ws'\nimport { existsSync, promises as fs } from 'fs'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport { parseVersionInfo } from './parse-version-info'\nimport type { VersionInfo } from './parse-version-info'\nimport { isAPIRoute } from '../../lib/is-api-route'\nimport { getRouteLoaderEntry } from '../../build/webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../../lib/is-internal-component'\nimport { RouteKind } from '../route-kind'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type NextJsHotReloaderInterface,\n} from './hot-reloader-types'\nimport type { HMR_ACTION_TYPES } from './hot-reloader-types'\nimport type { WebpackError } from 'webpack'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { getNodeDebugType } from '../lib/utils'\nimport { getNextErrorFeedbackMiddleware } from '../../next-devtools/server/get-next-error-feedback-middleware'\nimport { getDevOverlayFontMiddleware } from '../../next-devtools/server/font/get-dev-overlay-font-middleware'\nimport { getDisableDevIndicatorMiddleware } from '../../next-devtools/server/dev-indicator-middleware'\nimport getWebpackBundler from '../../shared/lib/get-webpack-bundler'\nimport { getRestartDevServerMiddleware } from '../../next-devtools/server/restart-dev-server-middleware'\nimport { checkPersistentCacheInvalidationAndCleanup } from '../../build/webpack/cache-invalidation'\nimport { receiveBrowserLogsWebpack } from './browser-logs/receive-logs'\nimport {\n  devToolsConfigMiddleware,\n  getDevToolsConfig,\n} from '../../next-devtools/server/devtools-config-middleware'\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nfunction diff(a: Set<any>, b: Set<any>) {\n  return new Set([...a].filter((v) => !b.has(v)))\n}\n\nconst wsServer = new ws.Server({ noServer: true })\n\nexport async function renderScriptError(\n  res: ServerResponse,\n  error: Error,\n  { verbose = true } = {}\n): Promise<{ finished: true | undefined }> {\n  // Asks CDNs and others to not to cache the errored page\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  if ((error as any).code === 'ENOENT') {\n    return { finished: undefined }\n  }\n\n  if (verbose) {\n    console.error(error.stack)\n  }\n  res.statusCode = 500\n  res.end('500 - Internal Error')\n  return { finished: true }\n}\n\nfunction addCorsSupport(req: IncomingMessage, res: ServerResponse) {\n  // Only rewrite CORS handling when URL matches a hot-reloader middleware\n  if (!req.url!.startsWith('/__next')) {\n    return { preflight: false }\n  }\n\n  if (!req.headers.origin) {\n    return { preflight: false }\n  }\n\n  res.setHeader('Access-Control-Allow-Origin', req.headers.origin)\n  res.setHeader('Access-Control-Allow-Methods', 'OPTIONS, GET')\n  // Based on https://github.com/primus/access-control/blob/4cf1bc0e54b086c91e6aa44fb14966fa5ef7549c/index.js#L158\n  if (req.headers['access-control-request-headers']) {\n    res.setHeader(\n      'Access-Control-Allow-Headers',\n      req.headers['access-control-request-headers'] as string\n    )\n  }\n\n  if (req.method === 'OPTIONS') {\n    res.writeHead(200)\n    res.end()\n    return { preflight: true }\n  }\n\n  return { preflight: false }\n}\n\nexport const matchNextPageBundleRequest = getPathMatch(\n  '/_next/static/chunks/pages/:path*.js(\\\\.map|)'\n)\n\n// Iteratively look up the issuer till it ends up at the root\nfunction findEntryModule(\n  module: webpack.Module,\n  compilation: webpack.Compilation\n): any {\n  for (;;) {\n    const issuer = compilation.moduleGraph.getIssuer(module)\n    if (!issuer) return module\n    module = issuer\n  }\n}\n\nfunction erroredPages(compilation: webpack.Compilation) {\n  const failedPages: { [page: string]: WebpackError[] } = {}\n  for (const error of compilation.errors) {\n    if (!error.module) {\n      continue\n    }\n\n    const entryModule = findEntryModule(error.module, compilation)\n    const { name } = entryModule\n    if (!name) {\n      continue\n    }\n\n    // Only pages have to be reloaded\n    const enhancedName = getRouteFromEntrypoint(name)\n\n    if (!enhancedName) {\n      continue\n    }\n\n    if (!failedPages[enhancedName]) {\n      failedPages[enhancedName] = []\n    }\n\n    failedPages[enhancedName].push(error)\n  }\n\n  return failedPages\n}\n\nexport async function getVersionInfo(): Promise<VersionInfo> {\n  let installed = '0.0.0'\n\n  try {\n    installed = require('next/package.json').version\n\n    let res\n\n    try {\n      // use NPM registry regardless user using Yarn\n      res = await fetch('https://registry.npmjs.org/-/package/next/dist-tags')\n    } catch {\n      // ignore fetch errors\n    }\n\n    if (!res || !res.ok) return { installed, staleness: 'unknown' }\n\n    const { latest, canary } = await res.json()\n\n    return parseVersionInfo({ installed, latest, canary })\n  } catch (e: any) {\n    console.error(e)\n    return { installed, staleness: 'unknown' }\n  }\n}\n\nexport default class HotReloaderWebpack implements NextJsHotReloaderInterface {\n  private hasAmpEntrypoints: boolean\n  private hasAppRouterEntrypoints: boolean\n  private hasPagesRouterEntrypoints: boolean\n  private dir: string\n  private buildId: string\n  private encryptionKey: string\n  private middlewares: ((\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ) => Promise<void>)[]\n  private pagesDir?: string\n  private distDir: string\n  private webpackHotMiddleware?: WebpackHotMiddleware\n  private config: NextConfigComplete\n  private clientStats: webpack.Stats | null\n  private clientError: Error | null = null\n  private serverError: Error | null = null\n  private hmrServerError: Error | null = null\n  private serverPrevDocumentHash: string | null\n  private serverChunkNames?: Set<string>\n  private prevChunkNames?: Set<any>\n  private onDemandEntries?: ReturnType<typeof onDemandEntryHandler>\n  private previewProps: __ApiPreviewProps\n  private watcher: any\n  private rewrites: CustomRoutes['rewrites']\n  private fallbackWatcher: any\n  private hotReloaderSpan: Span\n  private pagesMapping: { [key: string]: string } = {}\n  private appDir?: string\n  private telemetry: Telemetry\n  private resetFetch: () => void\n  private versionInfo: VersionInfo = {\n    staleness: 'unknown',\n    installed: '0.0.0',\n  }\n  private devtoolsFrontendUrl: string | undefined\n  private reloadAfterInvalidation: boolean = false\n  private isSrcDir: boolean\n\n  public serverStats: webpack.Stats | null\n  public edgeServerStats: webpack.Stats | null\n  public multiCompiler?: webpack.MultiCompiler\n  public activeWebpackConfigs?: Array<\n    UnwrapPromise<ReturnType<typeof getBaseWebpackConfig>>\n  >\n\n  constructor(\n    dir: string,\n    {\n      config,\n      isSrcDir,\n      pagesDir,\n      distDir,\n      buildId,\n      encryptionKey,\n      previewProps,\n      rewrites,\n      appDir,\n      telemetry,\n      resetFetch,\n    }: {\n      config: NextConfigComplete\n      isSrcDir: boolean\n      pagesDir?: string\n      distDir: string\n      buildId: string\n      encryptionKey: string\n      previewProps: __ApiPreviewProps\n      rewrites: CustomRoutes['rewrites']\n      appDir?: string\n      telemetry: Telemetry\n      resetFetch: () => void\n    }\n  ) {\n    this.hasAmpEntrypoints = false\n    this.hasAppRouterEntrypoints = false\n    this.hasPagesRouterEntrypoints = false\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n    this.dir = dir\n    this.isSrcDir = isSrcDir\n    this.middlewares = []\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n    this.distDir = distDir\n    this.clientStats = null\n    this.serverStats = null\n    this.edgeServerStats = null\n    this.serverPrevDocumentHash = null\n    this.telemetry = telemetry\n    this.resetFetch = resetFetch\n\n    this.config = config\n    this.previewProps = previewProps\n    this.rewrites = rewrites\n    this.hotReloaderSpan = trace('hot-reloader', undefined, {\n      version: process.env.__NEXT_VERSION as string,\n    })\n    // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n    // of the current `next dev` invocation.\n    this.hotReloaderSpan.stop()\n  }\n\n  public async run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }> {\n    // Usually CORS support is not needed for the hot-reloader (this is dev only feature)\n    // With when the app runs for multi-zones support behind a proxy,\n    // the current page is trying to access this URL via assetPrefix.\n    // That's when the CORS support is needed.\n    const { preflight } = addCorsSupport(req, res)\n    if (preflight) {\n      return {}\n    }\n\n    // When a request comes in that is a page bundle, e.g. /_next/static/<buildid>/pages/index.js\n    // we have to compile the page using on-demand-entries, this middleware will handle doing that\n    // by adding the page to on-demand-entries, waiting till it's done\n    // and then the bundle will be served like usual by the actual route in server/index.js\n    const handlePageBundleRequest = async (\n      pageBundleRes: ServerResponse,\n      parsedPageBundleUrl: UrlObject\n    ): Promise<{ finished?: true }> => {\n      const { pathname } = parsedPageBundleUrl\n      if (!pathname) return {}\n\n      const params = matchNextPageBundleRequest(pathname)\n      if (!params) return {}\n\n      let decodedPagePath: string\n\n      try {\n        decodedPagePath = `/${params.path\n          .map((param: string) => decodeURIComponent(param))\n          .join('/')}`\n      } catch (_) {\n        throw new DecodeError('failed to decode param')\n      }\n\n      const page = denormalizePagePath(decodedPagePath)\n\n      if (page === '/_error' || BLOCKED_PAGES.indexOf(page) === -1) {\n        try {\n          await this.ensurePage({ page, clientOnly: true, url: req.url })\n        } catch (error) {\n          return await renderScriptError(pageBundleRes, getProperError(error))\n        }\n\n        const errors = await this.getCompilationErrors(page)\n        if (errors.length > 0) {\n          return await renderScriptError(pageBundleRes, errors[0], {\n            verbose: false,\n          })\n        }\n      }\n\n      return {}\n    }\n\n    const { finished } = await handlePageBundleRequest(res, parsedUrl)\n\n    for (const middleware of this.middlewares) {\n      let calledNext = false\n\n      await middleware(req, res, () => {\n        calledNext = true\n      })\n\n      if (!calledNext) {\n        return { finished: true }\n      }\n    }\n\n    return { finished }\n  }\n\n  public setHmrServerError(error: Error | null): void {\n    this.hmrServerError = error\n  }\n\n  public clearHmrServerError(): void {\n    if (this.hmrServerError) {\n      this.setHmrServerError(null)\n      this.send({\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: 'clear hmr server error',\n      })\n    }\n  }\n\n  protected async refreshServerComponents(hash: string): Promise<void> {\n    this.send({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n      hash,\n      // TODO: granular reloading of changes\n      // entrypoints: serverComponentChanges,\n    })\n  }\n\n  public onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    callback: (client: ws.WebSocket) => void\n  ) {\n    wsServer.handleUpgrade(req, req.socket, head, (client) => {\n      this.webpackHotMiddleware?.onHMR(client)\n      this.onDemandEntries?.onHMR(client, () => this.hmrServerError)\n      callback(client)\n\n      client.addEventListener('message', async ({ data }) => {\n        data = typeof data !== 'string' ? data.toString() : data\n\n        try {\n          const payload = JSON.parse(data)\n\n          let traceChild:\n            | {\n                name: string\n                startTime?: bigint\n                endTime?: bigint\n                attrs?: Record<string, number | string | undefined | string[]>\n              }\n            | undefined\n\n          switch (payload.event) {\n            case 'span-end': {\n              traceChild = {\n                name: payload.spanName,\n                startTime:\n                  BigInt(Math.floor(payload.startTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n                attrs: payload.attributes,\n                endTime:\n                  BigInt(Math.floor(payload.endTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n              }\n              break\n            }\n            case 'client-hmr-latency': {\n              traceChild = {\n                name: payload.event,\n                startTime:\n                  BigInt(payload.startTime) * MILLISECONDS_IN_NANOSECOND,\n                endTime: BigInt(payload.endTime) * MILLISECONDS_IN_NANOSECOND,\n                attrs: {\n                  updatedModules: payload.updatedModules.map((m: string) =>\n                    m\n                      .replace(`(${WEBPACK_LAYERS.appPagesBrowser})/`, '')\n                      .replace(/^\\.\\//, '[project]/')\n                  ),\n                  page: payload.page,\n                  isPageHidden: payload.isPageHidden,\n                },\n              }\n              break\n            }\n            case 'client-reload-page':\n            case 'client-success': {\n              traceChild = {\n                name: payload.event,\n              }\n              break\n            }\n            case 'client-error': {\n              traceChild = {\n                name: payload.event,\n                attrs: { errorCount: payload.errorCount },\n              }\n              break\n            }\n            case 'client-warning': {\n              traceChild = {\n                name: payload.event,\n                attrs: { warningCount: payload.warningCount },\n              }\n              break\n            }\n            case 'client-removed-page':\n            case 'client-added-page': {\n              traceChild = {\n                name: payload.event,\n                attrs: { page: payload.page || '' },\n              }\n              break\n            }\n            case 'client-full-reload': {\n              const { event, stackTrace, hadRuntimeError } = payload\n\n              traceChild = {\n                name: event,\n                attrs: { stackTrace: stackTrace ?? '' },\n              }\n\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n                break\n              }\n\n              let fileMessage = ''\n              if (stackTrace) {\n                const file = /Aborted because (.+) is not accepted/.exec(\n                  stackTrace\n                )?.[1]\n                if (file) {\n                  // `file` is filepath in `pages/` but it can be a webpack url.\n                  // If it's a webpack loader URL, it will include the app-pages layer\n                  if (file.startsWith(`(${WEBPACK_LAYERS.appPagesBrowser})/`)) {\n                    const fileUrl = new URL(file, 'file://')\n                    const cwd = process.cwd()\n                    const modules = fileUrl.searchParams\n                      .getAll('modules')\n                      .map((filepath) => filepath.slice(cwd.length + 1))\n                      .filter(\n                        (filepath) => !filepath.startsWith('node_modules')\n                      )\n\n                    if (modules.length > 0) {\n                      fileMessage = ` when ${modules.join(', ')} changed`\n                    }\n                  } else if (\n                    // Handle known webpack layers\n                    file.startsWith(`(${WEBPACK_LAYERS.pagesDirBrowser})/`)\n                  ) {\n                    const cleanedFilePath = file.slice(\n                      `(${WEBPACK_LAYERS.pagesDirBrowser})/`.length\n                    )\n\n                    fileMessage = ` when ${cleanedFilePath} changed`\n                  } else {\n                    fileMessage = ` when ${file} changed`\n                  }\n                }\n              }\n\n              Log.warn(\n                `Fast Refresh had to perform a full reload${fileMessage}. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n              )\n              break\n            }\n            case 'browser-logs': {\n              if (this.config.experimental.browserDebugInfoInTerminal) {\n                await receiveBrowserLogsWebpack({\n                  entries: payload.entries,\n                  router: payload.router,\n                  sourceType: payload.sourceType,\n                  clientStats: () => this.clientStats,\n                  serverStats: () => this.serverStats,\n                  edgeServerStats: () => this.edgeServerStats,\n                  rootDirectory: this.dir,\n                  distDir: this.distDir,\n                  config: this.config.experimental.browserDebugInfoInTerminal,\n                })\n              }\n              break\n            }\n            default: {\n              break\n            }\n          }\n\n          if (traceChild) {\n            this.hotReloaderSpan.manualTraceChild(\n              traceChild.name,\n              traceChild.startTime,\n              traceChild.endTime,\n              { ...traceChild.attrs, clientId: payload.id }\n            )\n          }\n        } catch (_) {\n          // invalid WebSocket message\n        }\n      })\n    })\n  }\n\n  private async clean(span: Span): Promise<void> {\n    return span\n      .traceChild('clean')\n      .traceAsyncFn(() =>\n        recursiveDelete(join(this.dir, this.config.distDir), /^cache/)\n      )\n  }\n\n  private async getWebpackConfig(span: Span) {\n    const webpackConfigSpan = span.traceChild('get-webpack-config')\n\n    const pageExtensions = this.config.pageExtensions\n\n    return webpackConfigSpan.traceAsyncFn(async () => {\n      const pagePaths = !this.pagesDir\n        ? ([] as (string | null)[])\n        : await webpackConfigSpan\n            .traceChild('get-page-paths')\n            .traceAsyncFn(() =>\n              Promise.all([\n                findPageFile(this.pagesDir!, '/_app', pageExtensions, false),\n                findPageFile(\n                  this.pagesDir!,\n                  '/_document',\n                  pageExtensions,\n                  false\n                ),\n              ])\n            )\n\n      this.pagesMapping = await webpackConfigSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: true,\n            pageExtensions: this.config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagePaths.filter(\n              (i: string | null): i is string => typeof i === 'string'\n            ),\n            pagesDir: this.pagesDir,\n            appDir: this.appDir,\n          })\n        )\n\n      const entrypoints = await webpackConfigSpan\n        .traceChild('create-entrypoints')\n        .traceAsyncFn(() =>\n          createEntrypoints({\n            appDir: this.appDir,\n            buildId: this.buildId,\n            config: this.config,\n            envFiles: [],\n            isDev: true,\n            pages: this.pagesMapping,\n            pagesDir: this.pagesDir,\n            previewMode: this.previewProps,\n            rootDir: this.dir,\n            pageExtensions: this.config.pageExtensions,\n          })\n        )\n\n      const commonWebpackOptions = {\n        dev: true,\n        buildId: this.buildId,\n        encryptionKey: this.encryptionKey,\n        config: this.config,\n        pagesDir: this.pagesDir,\n        rewrites: this.rewrites,\n        originalRewrites: this.config._originalRewrites,\n        originalRedirects: this.config._originalRedirects,\n        runWebpackSpan: this.hotReloaderSpan,\n        appDir: this.appDir,\n        previewProps: this.previewProps,\n      }\n\n      return webpackConfigSpan\n        .traceChild('generate-webpack-config')\n        .traceAsyncFn(async () => {\n          const info = await loadProjectInfo({\n            dir: this.dir,\n            config: commonWebpackOptions.config,\n            dev: true,\n          })\n          return Promise.all([\n            // order is important here\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.client,\n              entrypoints: entrypoints.client,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.server,\n              entrypoints: entrypoints.server,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.edgeServer,\n              entrypoints: entrypoints.edgeServer,\n              ...info,\n            }),\n          ])\n        })\n    })\n  }\n\n  public async buildFallbackError(): Promise<void> {\n    if (this.fallbackWatcher) return\n\n    const info = await loadProjectInfo({\n      dir: this.dir,\n      config: this.config,\n      dev: true,\n    })\n    const fallbackConfig = await getBaseWebpackConfig(this.dir, {\n      previewProps: this.previewProps,\n      runWebpackSpan: this.hotReloaderSpan,\n      dev: true,\n      compilerType: COMPILER_NAMES.client,\n      config: this.config,\n      buildId: this.buildId,\n      encryptionKey: this.encryptionKey,\n      appDir: this.appDir,\n      pagesDir: this.pagesDir,\n      rewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRedirects: [],\n      isDevFallback: true,\n      entrypoints: (\n        await createEntrypoints({\n          appDir: this.appDir,\n          buildId: this.buildId,\n          config: this.config,\n          envFiles: [],\n          isDev: true,\n          pages: {\n            '/_app': 'next/dist/pages/_app',\n            '/_error': 'next/dist/pages/_error',\n          },\n          pagesDir: this.pagesDir,\n          previewMode: this.previewProps,\n          rootDir: this.dir,\n          pageExtensions: this.config.pageExtensions,\n        })\n      ).client,\n      ...info,\n    })\n\n    const fallbackCompiler = getWebpackBundler()(fallbackConfig)\n\n    this.fallbackWatcher = await new Promise((resolve) => {\n      let bootedFallbackCompiler = false\n      fallbackCompiler.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        fallbackConfig.watchOptions,\n        // Errors are handled separately\n        (_err: any) => {\n          if (!bootedFallbackCompiler) {\n            bootedFallbackCompiler = true\n            resolve(true)\n          }\n        }\n      )\n    })\n  }\n\n  private async tracedGetVersionInfo(span: Span) {\n    const versionInfoSpan = span.traceChild('get-version-info')\n    return versionInfoSpan.traceAsyncFn<VersionInfo>(async () =>\n      getVersionInfo()\n    )\n  }\n\n  public async start(): Promise<void> {\n    const startSpan = this.hotReloaderSpan.traceChild('start')\n    startSpan.stop() // Stop immediately to create an artificial parent span\n\n    this.versionInfo = await this.tracedGetVersionInfo(startSpan)\n\n    const nodeDebugType = getNodeDebugType()\n    if (nodeDebugType && !this.devtoolsFrontendUrl) {\n      const debugPort = process.debugPort\n      let debugInfo\n      try {\n        // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n        const debugInfoList = await fetch(\n          `http://127.0.0.1:${debugPort}/json/list`\n        ).then((res) => res.json())\n        // There will be only one item for current process, so always get the first item.\n        debugInfo = debugInfoList[0]\n      } catch {}\n      if (debugInfo) {\n        this.devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n      }\n    }\n\n    await this.clean(startSpan)\n    // Ensure distDir exists before writing package.json\n    await fs.mkdir(this.distDir, { recursive: true })\n\n    const initialDevToolsConfig = await getDevToolsConfig(this.distDir)\n\n    const distPackageJsonPath = join(this.distDir, 'package.json')\n    // Ensure commonjs handling is used for files in the distDir (generally .next)\n    // Files outside of the distDir can be \"type\": \"module\"\n    await fs.writeFile(distPackageJsonPath, '{\"type\": \"commonjs\"}')\n\n    this.activeWebpackConfigs = await this.getWebpackConfig(startSpan)\n\n    for (const config of this.activeWebpackConfigs) {\n      const defaultEntry = config.entry\n      config.entry = async (...args) => {\n        const outputPath = this.multiCompiler?.outputPath || ''\n        const entries = getEntries(outputPath)\n        // @ts-ignore entry is always a function\n        const entrypoints = await defaultEntry(...args)\n        const isClientCompilation = config.name === COMPILER_NAMES.client\n        const isNodeServerCompilation = config.name === COMPILER_NAMES.server\n        const isEdgeServerCompilation =\n          config.name === COMPILER_NAMES.edgeServer\n\n        await Promise.all(\n          Object.keys(entries).map(async (entryKey) => {\n            const entryData = entries[entryKey]\n            const { bundlePath, dispose } = entryData\n\n            const result =\n              /^(client|server|edge-server)@(app|pages|root)@(.*)/g.exec(\n                entryKey\n              )\n            const [, key /* pageType */, , page] = result! // this match should always happen\n\n            if (key === COMPILER_NAMES.client && !isClientCompilation) return\n            if (key === COMPILER_NAMES.server && !isNodeServerCompilation)\n              return\n            if (key === COMPILER_NAMES.edgeServer && !isEdgeServerCompilation)\n              return\n\n            const isEntry = entryData.type === EntryTypes.ENTRY\n            const isChildEntry = entryData.type === EntryTypes.CHILD_ENTRY\n\n            // Check if the page was removed or disposed and remove it\n            if (isEntry) {\n              const pageExists =\n                !dispose && existsSync(entryData.absolutePagePath)\n              if (!pageExists) {\n                delete entries[entryKey]\n                return\n              }\n            }\n\n            // For child entries, if it has an entry file and it's gone, remove it\n            if (isChildEntry) {\n              if (entryData.absoluteEntryFilePath) {\n                const pageExists =\n                  !dispose && existsSync(entryData.absoluteEntryFilePath)\n                if (!pageExists) {\n                  delete entries[entryKey]\n                  return\n                }\n              }\n            }\n\n            // Ensure _error is considered a `pages` page.\n            if (page === '/_error') {\n              this.hasPagesRouterEntrypoints = true\n            }\n\n            const hasAppDir = !!this.appDir\n            const isAppPath = hasAppDir && bundlePath.startsWith('app/')\n            const staticInfo = isEntry\n              ? await getStaticInfoIncludingLayouts({\n                  isInsideAppDir: isAppPath,\n                  pageExtensions: this.config.pageExtensions,\n                  pageFilePath: entryData.absolutePagePath,\n                  appDir: this.appDir,\n                  config: this.config,\n                  isDev: true,\n                  page,\n                })\n              : undefined\n\n            if (staticInfo?.type === PAGE_TYPES.PAGES) {\n              if (\n                staticInfo.config?.config?.amp === true ||\n                staticInfo.config?.config?.amp === 'hybrid'\n              ) {\n                this.hasAmpEntrypoints = true\n              }\n            }\n\n            const isServerComponent =\n              isAppPath && staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n            const pageType: PAGE_TYPES = entryData.bundlePath.startsWith(\n              'pages/'\n            )\n              ? PAGE_TYPES.PAGES\n              : entryData.bundlePath.startsWith('app/')\n                ? PAGE_TYPES.APP\n                : PAGE_TYPES.ROOT\n\n            if (pageType === 'pages') {\n              this.hasPagesRouterEntrypoints = true\n            }\n            if (pageType === 'app') {\n              this.hasAppRouterEntrypoints = true\n            }\n\n            const isInstrumentation =\n              isInstrumentationHookFile(page) && pageType === PAGE_TYPES.ROOT\n\n            let pageRuntime = staticInfo?.runtime\n\n            runDependingOnPageType({\n              page,\n              pageRuntime,\n              pageType,\n              onEdgeServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isEdgeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n\n                if (isInstrumentation) {\n                  const normalizedBundlePath = bundlePath.replace('src/', '')\n                  entrypoints[normalizedBundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.edgeServer,\n                    name: normalizedBundlePath,\n                    value: getInstrumentationEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      isEdgeServer: true,\n                      isDev: true,\n                    }),\n                    isServerComponent: true,\n                    hasAppDir,\n                  })\n                  return\n                }\n                const appDirLoader = isAppPath\n                  ? getAppEntry({\n                      name: bundlePath,\n                      page,\n                      appPaths: entryData.appPaths,\n                      pagePath: posix.join(\n                        APP_DIR_ALIAS,\n                        relative(\n                          this.appDir!,\n                          entryData.absolutePagePath\n                        ).replace(/\\\\/g, '/')\n                      ),\n                      appDir: this.appDir!,\n                      pageExtensions: this.config.pageExtensions,\n                      rootDir: this.dir,\n                      isDev: true,\n                      tsconfigPath: this.config.typescript.tsconfigPath,\n                      basePath: this.config.basePath,\n                      assetPrefix: this.config.assetPrefix,\n                      nextConfigOutput: this.config.output,\n                      preferredRegion: staticInfo?.preferredRegion,\n                      middlewareConfig: Buffer.from(\n                        JSON.stringify(staticInfo?.middleware || {})\n                      ).toString('base64'),\n                      isGlobalNotFoundEnabled: this.config.experimental\n                        .globalNotFound\n                        ? true\n                        : undefined,\n                    }).import\n                  : undefined\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.edgeServer,\n                  name: bundlePath,\n                  value: getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    appDirLoader,\n                    pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  }),\n                  hasAppDir,\n                })\n              },\n              onClient: () => {\n                if (!isClientCompilation) return\n                if (isChildEntry) {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: entryData.request,\n                    hasAppDir,\n                  })\n                } else {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: getClientEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      page,\n                    }),\n                    hasAppDir,\n                  })\n                }\n              },\n              onServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isNodeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n                let relativeRequest = relative(\n                  config.context!,\n                  entryData.absolutePagePath\n                )\n                if (\n                  !isAbsolute(relativeRequest) &&\n                  !relativeRequest.startsWith('../')\n                ) {\n                  relativeRequest = `./${relativeRequest}`\n                }\n\n                let value: { import: string; layer?: string } | string\n                if (isInstrumentation) {\n                  value = getInstrumentationEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    isEdgeServer: false,\n                    isDev: true,\n                  })\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.server,\n                    name: bundlePath,\n                    isServerComponent: true,\n                    value,\n                    hasAppDir,\n                  })\n                } else if (isMiddlewareFile(page)) {\n                  value = getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    pagesType: PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  })\n                } else if (isAppPath) {\n                  // This path normalization is critical for webpack to resolve the next internals as entry.\n                  const pagePath = entryData.absolutePagePath.startsWith(\n                    dirname(require.resolve('next/package.json'))\n                  )\n                    ? entryData.absolutePagePath\n                    : posix.join(\n                        APP_DIR_ALIAS,\n                        relative(\n                          this.appDir!,\n                          entryData.absolutePagePath\n                        ).replace(/\\\\/g, '/')\n                      )\n                  value = getAppEntry({\n                    name: bundlePath,\n                    page,\n                    appPaths: entryData.appPaths,\n                    pagePath,\n                    appDir: this.appDir!,\n                    pageExtensions: this.config.pageExtensions,\n                    rootDir: this.dir,\n                    isDev: true,\n                    tsconfigPath: this.config.typescript.tsconfigPath,\n                    basePath: this.config.basePath,\n                    assetPrefix: this.config.assetPrefix,\n                    nextConfigOutput: this.config.output,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: Buffer.from(\n                      JSON.stringify(staticInfo?.middleware || {})\n                    ).toString('base64'),\n                    isGlobalNotFoundEnabled: this.config.experimental\n                      .globalNotFound\n                      ? true\n                      : undefined,\n                  })\n                } else if (isAPIRoute(page)) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES_API,\n                    page,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware || {},\n                  })\n                } else if (\n                  !isMiddlewareFile(page) &&\n                  !isInternalComponent(relativeRequest) &&\n                  !isNonRoutePagesPage(page) &&\n                  !isInstrumentation\n                ) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES,\n                    page,\n                    pages: this.pagesMapping,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware ?? {},\n                  })\n                } else {\n                  value = relativeRequest\n                }\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.server,\n                  name: bundlePath,\n                  isServerComponent,\n                  value,\n                  hasAppDir,\n                })\n              },\n            })\n          })\n        )\n\n        if (!this.hasAmpEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_AMP]\n        }\n        if (!this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN]\n          delete entrypoints['pages/_app']\n          delete entrypoints['pages/_error']\n          delete entrypoints['/_error']\n          delete entrypoints['pages/_document']\n        }\n        // Remove React Refresh entrypoint chunk as `app` doesn't require it.\n        if (!this.hasAmpEntrypoints && !this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]\n        }\n        if (!this.hasAppRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]\n        }\n\n        return entrypoints\n      }\n    }\n\n    // Enable building of client compilation before server compilation in development\n    // @ts-ignore webpack 5\n    this.activeWebpackConfigs.parallelism = 1\n\n    await Promise.all(\n      Array.from(getCacheDirectories(this.activeWebpackConfigs)).map(\n        checkPersistentCacheInvalidationAndCleanup\n      )\n    )\n    this.multiCompiler = getWebpackBundler()(\n      this.activeWebpackConfigs\n    ) as unknown as webpack.MultiCompiler\n\n    // Copy over the filesystem so that it is shared between all compilers.\n    const inputFileSystem = this.multiCompiler.compilers[0].inputFileSystem\n    for (const compiler of this.multiCompiler.compilers) {\n      compiler.inputFileSystem = inputFileSystem\n      // This is set for the initial compile. After that Watching class in webpack adds it.\n      compiler.fsStartTime = Date.now()\n      // Ensure NodeEnvironmentPlugin doesn't purge the inputFileSystem. Purging is handled in `done` below.\n      compiler.hooks.beforeRun.intercept({\n        register(tapInfo: any) {\n          if (tapInfo.name === 'NodeEnvironmentPlugin') {\n            return null\n          }\n          return tapInfo\n        },\n      })\n    }\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloader', () => {\n      inputFileSystem?.purge?.()\n    })\n    watchCompilers(\n      this.multiCompiler.compilers[0],\n      this.multiCompiler.compilers[1],\n      this.multiCompiler.compilers[2]\n    )\n\n    // Watch for changes to client/server page files so we can tell when just\n    // the server file changes and trigger a reload for GS(S)P pages\n    const changedClientPages = new Set<string>()\n    const changedServerPages = new Set<string>()\n    const changedEdgeServerPages = new Set<string>()\n\n    const changedServerComponentPages = new Set<string>()\n    const changedCSSImportPages = new Set<string>()\n\n    const prevClientPageHashes = new Map<string, string>()\n    const prevServerPageHashes = new Map<string, string>()\n    const prevEdgeServerPageHashes = new Map<string, string>()\n    const prevCSSImportModuleHashes = new Map<string, string>()\n\n    const pageExtensionRegex = new RegExp(\n      `\\\\.(?:${this.config.pageExtensions.join('|')})$`\n    )\n\n    const trackPageChanges =\n      (\n        pageHashMap: Map<string, string>,\n        changedItems: Set<string>,\n        serverComponentChangedItems?: Set<string>\n      ) =>\n      (stats: webpack.Compilation) => {\n        try {\n          stats.entrypoints.forEach((entry, key) => {\n            if (\n              key.startsWith('pages/') ||\n              key.startsWith('app/') ||\n              isMiddlewareFilename(key)\n            ) {\n              // TODO this doesn't handle on demand loaded chunks\n              entry.chunks.forEach((chunk) => {\n                if (chunk.id === key) {\n                  const modsIterable: any =\n                    stats.chunkGraph.getChunkModulesIterable(chunk)\n\n                  let hasCSSModuleChanges = false\n                  let chunksHash = new StringXor()\n                  let chunksHashServerLayer = new StringXor()\n\n                  modsIterable.forEach((mod: any) => {\n                    if (\n                      mod.resource &&\n                      mod.resource.replace(/\\\\/g, '/').includes(key) &&\n                      // Shouldn't match CSS modules, etc.\n                      pageExtensionRegex.test(mod.resource)\n                    ) {\n                      // use original source to calculate hash since mod.hash\n                      // includes the source map in development which changes\n                      // every time for both server and client so we calculate\n                      // the hash without the source map for the page module\n                      const hash = (\n                        require('crypto') as typeof import('crypto')\n                      )\n                        .createHash('sha1')\n                        .update(mod.originalSource().buffer())\n                        .digest()\n                        .toString('hex')\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n                    } else {\n                      // for non-pages we can use the module hash directly\n                      const hash = stats.chunkGraph.getModuleHash(\n                        mod,\n                        chunk.runtime\n                      )\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n\n                      // Both CSS import changes from server and client\n                      // components are tracked.\n                      if (\n                        key.startsWith('app/') &&\n                        /\\.(css|scss|sass)$/.test(mod.resource || '')\n                      ) {\n                        const resourceKey = mod.layer + ':' + mod.resource\n                        const prevHash =\n                          prevCSSImportModuleHashes.get(resourceKey)\n                        if (prevHash && prevHash !== hash) {\n                          hasCSSModuleChanges = true\n                        }\n                        prevCSSImportModuleHashes.set(resourceKey, hash)\n                      }\n                    }\n                  })\n\n                  const prevHash = pageHashMap.get(key)\n                  const curHash = chunksHash.toString()\n                  if (prevHash && prevHash !== curHash) {\n                    changedItems.add(key)\n                  }\n                  pageHashMap.set(key, curHash)\n\n                  if (serverComponentChangedItems) {\n                    const serverKey =\n                      WEBPACK_LAYERS.reactServerComponents + ':' + key\n                    const prevServerHash = pageHashMap.get(serverKey)\n                    const curServerHash = chunksHashServerLayer.toString()\n                    if (prevServerHash && prevServerHash !== curServerHash) {\n                      serverComponentChangedItems.add(key)\n                    }\n                    pageHashMap.set(serverKey, curServerHash)\n                  }\n\n                  if (hasCSSModuleChanges) {\n                    changedCSSImportPages.add(key)\n                  }\n                }\n              })\n            }\n          })\n        } catch (err) {\n          console.error(err)\n        }\n      }\n\n    this.multiCompiler.compilers[0].hooks.emit.tap(\n      'NextjsHotReloaderForClient',\n      trackPageChanges(prevClientPageHashes, changedClientPages)\n    )\n    this.multiCompiler.compilers[1].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevServerPageHashes,\n        changedServerPages,\n        changedServerComponentPages\n      )\n    )\n    this.multiCompiler.compilers[2].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevEdgeServerPageHashes,\n        changedEdgeServerPages,\n        changedServerComponentPages\n      )\n    )\n\n    // This plugin watches for changes to _document.js and notifies the client side that it should reload the page\n    this.multiCompiler.compilers[1].hooks.failed.tap(\n      'NextjsHotReloaderForServer',\n      (err: Error) => {\n        this.serverError = err\n        this.serverStats = null\n        this.serverChunkNames = undefined\n      }\n    )\n\n    this.multiCompiler.compilers[2].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.edgeServerStats = stats\n      }\n    )\n\n    this.multiCompiler.compilers[1].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.serverStats = stats\n\n        if (!this.pagesDir) {\n          return\n        }\n\n        const { compilation } = stats\n\n        // We only watch `_document` for changes on the server compilation\n        // the rest of the files will be triggered by the client compilation\n        const documentChunk = compilation.namedChunks.get('pages/_document')\n        // If the document chunk can't be found we do nothing\n        if (!documentChunk) {\n          return\n        }\n\n        // Initial value\n        if (this.serverPrevDocumentHash === null) {\n          this.serverPrevDocumentHash = documentChunk.hash || null\n          return\n        }\n\n        // If _document.js didn't change we don't trigger a reload.\n        if (documentChunk.hash === this.serverPrevDocumentHash) {\n          return\n        }\n\n        // As document chunk will change if new app pages are joined,\n        // since react bundle is different it will effect the chunk hash.\n        // So we diff the chunk changes, if there's only new app page chunk joins,\n        // then we don't trigger a reload by checking pages/_document chunk change.\n        if (this.appDir) {\n          const chunkNames = new Set(compilation.namedChunks.keys())\n          const diffChunkNames = difference<string>(\n            this.serverChunkNames || new Set(),\n            chunkNames\n          )\n\n          if (\n            diffChunkNames.length === 0 ||\n            diffChunkNames.every((chunkName) => chunkName.startsWith('app/'))\n          ) {\n            return\n          }\n          this.serverChunkNames = chunkNames\n        }\n\n        this.serverPrevDocumentHash = documentChunk.hash || null\n\n        // Notify reload to reload the page, as _document.js was changed (different hash)\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed',\n        })\n      }\n    )\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloaderForServer', (stats) => {\n      const reloadAfterInvalidation = this.reloadAfterInvalidation\n      this.reloadAfterInvalidation = false\n\n      const serverOnlyChanges = difference<string>(\n        changedServerPages,\n        changedClientPages\n      )\n\n      const edgeServerOnlyChanges = difference<string>(\n        changedEdgeServerPages,\n        changedClientPages\n      )\n\n      const pageChanges = serverOnlyChanges\n        .concat(edgeServerOnlyChanges)\n        .filter((key) => key.startsWith('pages/'))\n\n      const middlewareChanges = [\n        ...Array.from(changedEdgeServerPages),\n        ...Array.from(changedServerPages),\n      ].filter((name) => isMiddlewareFilename(name))\n\n      if (middlewareChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n        })\n      }\n\n      if (pageChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n          pages: serverOnlyChanges.map((pg) =>\n            denormalizePagePath(pg.slice('pages'.length))\n          ),\n        })\n      }\n\n      if (\n        changedServerComponentPages.size ||\n        changedCSSImportPages.size ||\n        reloadAfterInvalidation\n      ) {\n        this.resetFetch()\n        this.refreshServerComponents(stats.hash)\n      }\n\n      changedClientPages.clear()\n      changedServerPages.clear()\n      changedEdgeServerPages.clear()\n      changedServerComponentPages.clear()\n      changedCSSImportPages.clear()\n    })\n\n    this.multiCompiler.compilers[0].hooks.failed.tap(\n      'NextjsHotReloaderForClient',\n      (err: Error) => {\n        this.clientError = err\n        this.clientStats = null\n      }\n    )\n    this.multiCompiler.compilers[0].hooks.done.tap(\n      'NextjsHotReloaderForClient',\n      (stats) => {\n        this.clientError = null\n        this.clientStats = stats\n\n        const { compilation } = stats\n        const chunkNames = new Set(\n          [...compilation.namedChunks.keys()].filter(\n            (name) => !!getRouteFromEntrypoint(name)\n          )\n        )\n\n        if (this.prevChunkNames) {\n          // detect chunks which have to be replaced with a new template\n          // e.g, pages/index.js <-> pages/_error.js\n          const addedPages = diff(chunkNames, this.prevChunkNames!)\n          const removedPages = diff(this.prevChunkNames!, chunkNames)\n\n          if (addedPages.size > 0) {\n            for (const addedPage of addedPages) {\n              const page = getRouteFromEntrypoint(addedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n                data: [page],\n              })\n            }\n          }\n\n          if (removedPages.size > 0) {\n            for (const removedPage of removedPages) {\n              const page = getRouteFromEntrypoint(removedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n                data: [page],\n              })\n            }\n          }\n        }\n\n        this.prevChunkNames = chunkNames\n      }\n    )\n\n    this.webpackHotMiddleware = new WebpackHotMiddleware(\n      this.multiCompiler.compilers,\n      this.versionInfo,\n      this.devtoolsFrontendUrl,\n      initialDevToolsConfig\n    )\n\n    let booted = false\n\n    this.watcher = await new Promise((resolve) => {\n      const watcher = this.multiCompiler?.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        this.activeWebpackConfigs.map((config) => config.watchOptions!),\n        // Errors are handled separately\n        (_err: any) => {\n          if (!booted) {\n            booted = true\n            resolve(watcher)\n          }\n        }\n      )\n    })\n\n    this.onDemandEntries = onDemandEntryHandler({\n      hotReloader: this,\n      multiCompiler: this.multiCompiler,\n      pagesDir: this.pagesDir,\n      appDir: this.appDir,\n      rootDir: this.dir,\n      nextConfig: this.config,\n      ...(this.config.onDemandEntries as {\n        maxInactiveAge: number\n        pagesBufferLength: number\n      }),\n    })\n\n    this.middlewares = [\n      getOverlayMiddleware({\n        rootDirectory: this.dir,\n        isSrcDir: this.isSrcDir,\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getSourceMapMiddleware({\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getNextErrorFeedbackMiddleware(this.telemetry),\n      getDevOverlayFontMiddleware(),\n      getDisableDevIndicatorMiddleware(),\n      getRestartDevServerMiddleware({\n        telemetry: this.telemetry,\n        webpackCacheDirectories:\n          this.activeWebpackConfigs != null\n            ? getCacheDirectories(this.activeWebpackConfigs)\n            : undefined,\n      }),\n      devToolsConfigMiddleware({\n        distDir: this.distDir,\n        sendUpdateSignal: (data) => {\n          // Update the in-memory devToolsConfig value\n          // which will be used for the next onHMR call.\n          this.webpackHotMiddleware?.updateDevToolsConfig(data)\n\n          this.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG,\n            data,\n          })\n        },\n      }),\n    ]\n  }\n\n  public invalidate(\n    { reloadAfterInvalidation }: { reloadAfterInvalidation: boolean } = {\n      reloadAfterInvalidation: false,\n    }\n  ) {\n    // Cache the `reloadAfterInvalidation` flag, and use it to reload the page when compilation is done\n    this.reloadAfterInvalidation = reloadAfterInvalidation\n    const outputPath = this.multiCompiler?.outputPath\n    if (outputPath) {\n      getInvalidator(outputPath)?.invalidate()\n    }\n  }\n\n  public async getCompilationErrors(page: string) {\n    const getErrors = ({ compilation }: webpack.Stats) => {\n      const failedPages = erroredPages(compilation)\n      const normalizedPage = normalizePathSep(page)\n      // If there is an error related to the requesting page we display it instead of the first error\n      return failedPages[normalizedPage]?.length > 0\n        ? failedPages[normalizedPage]\n        : compilation.errors\n    }\n\n    if (this.clientError) {\n      return [this.clientError]\n    } else if (this.serverError) {\n      return [this.serverError]\n    } else if (this.clientStats?.hasErrors()) {\n      return getErrors(this.clientStats)\n    } else if (this.serverStats?.hasErrors()) {\n      return getErrors(this.serverStats)\n    } else if (this.edgeServerStats?.hasErrors()) {\n      return getErrors(this.edgeServerStats)\n    } else {\n      return []\n    }\n  }\n\n  public send(action: HMR_ACTION_TYPES): void {\n    this.webpackHotMiddleware!.publish(action)\n  }\n\n  public async ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition?: RouteDefinition\n    url?: string\n  }): Promise<void> {\n    return this.hotReloaderSpan\n      .traceChild('ensure-page', {\n        inputPage: page,\n      })\n      .traceAsyncFn(async () => {\n        // Make sure we don't re-build or dispose prebuilt pages\n        if (page !== '/_error' && BLOCKED_PAGES.indexOf(page) !== -1) {\n          return\n        }\n        const error = clientOnly\n          ? this.clientError\n          : this.serverError || this.clientError\n        if (error) {\n          throw error\n        }\n\n        return this.onDemandEntries?.ensurePage({\n          page,\n          appPaths,\n          definition,\n          isApp,\n          url,\n        })\n      })\n  }\n\n  public close() {\n    this.webpackHotMiddleware?.close()\n  }\n}\n"], "names": ["StringXor", "getOverlayMiddleware", "getSourceMapMiddleware", "WebpackHotMiddleware", "join", "relative", "isAbsolute", "posix", "dirname", "createEntrypoints", "createPagesMapping", "finalizeEntrypoint", "getClientEntry", "getEdgeServerEntry", "getAppEntry", "runDependingOnPageType", "getStaticInfoIncludingLayouts", "getInstrumentationEntry", "watchCompilers", "Log", "getBaseWebpackConfig", "getCacheDirectories", "loadProjectInfo", "APP_DIR_ALIAS", "WEBPACK_LAYERS", "recursiveDelete", "BLOCKED_PAGES", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "COMPILER_NAMES", "RSC_MODULE_TYPES", "getPathMatch", "findPageFile", "BUILDING", "getEntries", "EntryTypes", "getInvalidator", "onDemandEntryHandler", "denormalizePagePath", "normalizePathSep", "getRouteFromEntrypoint", "difference", "isInstrumentationHookFile", "isMiddlewareFile", "isMiddlewareFilename", "DecodeError", "trace", "getProperError", "ws", "existsSync", "promises", "fs", "parseVersionInfo", "isAPIRoute", "getRouteLoaderEntry", "isInternalComponent", "isNonRoutePagesPage", "RouteKind", "HMR_ACTIONS_SENT_TO_BROWSER", "PAGE_TYPES", "FAST_REFRESH_RUNTIME_RELOAD", "getNodeDebugType", "getNextErrorFeedbackMiddleware", "getDevOverlayFontMiddleware", "getDisableDevIndicatorMiddleware", "getWebpackBundler", "getRestartDevServerMiddleware", "checkPersistentCacheInvalidationAndCleanup", "receiveBrowserLogsWebpack", "devToolsConfigMiddleware", "getDevToolsConfig", "MILLISECONDS_IN_NANOSECOND", "BigInt", "diff", "a", "b", "Set", "filter", "v", "has", "wsServer", "Server", "noServer", "renderScriptError", "res", "error", "verbose", "<PERSON><PERSON><PERSON><PERSON>", "code", "finished", "undefined", "console", "stack", "statusCode", "end", "addCorsSupport", "req", "url", "startsWith", "preflight", "headers", "origin", "method", "writeHead", "matchNextPageBundleRequest", "findEntryModule", "module", "compilation", "issuer", "moduleGraph", "get<PERSON><PERSON><PERSON>", "erroredPages", "failedPages", "errors", "entryModule", "name", "enhancedName", "push", "getVersionInfo", "installed", "require", "version", "fetch", "ok", "staleness", "latest", "canary", "json", "e", "HotReloaderWebpack", "constructor", "dir", "config", "isSrcDir", "pagesDir", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "rewrites", "appDir", "telemetry", "resetFetch", "clientError", "serverError", "hmrServerError", "pagesMapping", "versionInfo", "reloadAfterInvalidation", "hasAmpEntrypoints", "hasAppRouterEntrypoints", "hasPagesRouterEntrypoints", "middlewares", "clientStats", "serverStats", "edgeServerStats", "serverPrevDocumentHash", "hotReloaderSpan", "process", "env", "__NEXT_VERSION", "stop", "run", "parsedUrl", "handlePageBundleRequest", "pageBundleRes", "parsedPageBundleUrl", "pathname", "params", "decodedPagePath", "path", "map", "param", "decodeURIComponent", "_", "page", "indexOf", "ensurePage", "clientOnly", "getCompilationErrors", "length", "middleware", "calledNext", "setHmrServerError", "clearHmrServerError", "send", "action", "RELOAD_PAGE", "data", "refreshServerComponents", "hash", "SERVER_COMPONENT_CHANGES", "onHMR", "_socket", "head", "callback", "handleUpgrade", "socket", "client", "webpackHotMiddleware", "onDemandEntries", "addEventListener", "toString", "payload", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON>", "event", "spanName", "startTime", "Math", "floor", "attrs", "attributes", "endTime", "updatedModules", "m", "replace", "appPagesBrowser", "isPageHidden", "errorCount", "warningCount", "stackTrace", "hadRuntimeError", "warn", "fileMessage", "file", "exec", "fileUrl", "URL", "cwd", "modules", "searchParams", "getAll", "filepath", "slice", "pagesDirBrowser", "cleanedFile<PERSON>ath", "experimental", "browserDebugInfoInTerminal", "entries", "router", "sourceType", "rootDirectory", "manualTraceChild", "clientId", "id", "clean", "span", "traceAsyncFn", "getWebpackConfig", "webpackConfigSpan", "pageExtensions", "pagePaths", "Promise", "all", "isDev", "pagesType", "PAGES", "i", "entrypoints", "envFiles", "pages", "previewMode", "rootDir", "commonWebpackOptions", "dev", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "runWebpackSpan", "info", "compilerType", "server", "edgeServer", "buildFallbackError", "fallback<PERSON><PERSON><PERSON>", "fallbackConfig", "beforeFiles", "afterFiles", "fallback", "isDev<PERSON><PERSON><PERSON>", "fallbackCompiler", "resolve", "bootedFallbackCompiler", "watch", "watchOptions", "_err", "tracedGetVersionInfo", "versionInfoSpan", "start", "startSpan", "nodeDebugType", "devtoolsFrontendUrl", "debugPort", "debugInfo", "debugInfoList", "then", "mkdir", "recursive", "initialDevToolsConfig", "distPackageJsonPath", "writeFile", "activeWebpackConfigs", "defaultEntry", "entry", "args", "outputPath", "multiCompiler", "isClientCompilation", "isNodeServerCompilation", "isEdgeServerCompilation", "Object", "keys", "<PERSON><PERSON><PERSON>", "entryData", "bundlePath", "dispose", "result", "key", "isEntry", "type", "ENTRY", "isChildEntry", "CHILD_ENTRY", "pageExists", "absolutePagePath", "absoluteEntryFilePath", "hasAppDir", "isAppPath", "staticInfo", "isInsideAppDir", "pageFilePath", "amp", "isServerComponent", "rsc", "pageType", "APP", "ROOT", "isInstrumentation", "pageRuntime", "runtime", "onEdgeServer", "status", "normalizedBundlePath", "value", "isEdgeServer", "appDirLoader", "appPaths", "pagePath", "tsconfigPath", "typescript", "basePath", "assetPrefix", "nextConfigOutput", "output", "preferredRegion", "middlewareConfig", "<PERSON><PERSON><PERSON>", "from", "stringify", "isGlobalNotFoundEnabled", "globalNotFound", "import", "onClient", "request", "onServer", "relativeRequest", "context", "kind", "PAGES_API", "parallelism", "Array", "inputFileSystem", "compilers", "compiler", "fsStartTime", "Date", "now", "hooks", "beforeRun", "intercept", "register", "tapInfo", "done", "tap", "purge", "changedClientPages", "changedServerPages", "changedEdgeServerPages", "changedServerComponentPages", "changedCSSImportPages", "prevClientPageHashes", "Map", "prevServerPageHashes", "prevEdgeServerPageHashes", "prevCSSImportModuleHashes", "pageExtensionRegex", "RegExp", "trackPageChanges", "pageHashMap", "changedItems", "serverComponentChangedItems", "stats", "for<PERSON>ach", "chunks", "chunk", "modsIterable", "chunkGraph", "getChunkModulesIterable", "hasCSSModuleChanges", "chunksHash", "chunksHashServerLayer", "mod", "resource", "includes", "test", "createHash", "update", "originalSource", "buffer", "digest", "layer", "reactServerComponents", "buildInfo", "add", "getModuleHash", "resourceKey", "prevHash", "get", "set", "curHash", "server<PERSON>ey", "prevServerHash", "curServerHash", "err", "emit", "failed", "serverChunkNames", "documentChunk", "namedChunks", "chunkNames", "diffChunkNames", "every", "chunkName", "serverOnlyChanges", "edgeServerOnlyChanges", "pageChanges", "concat", "middlewareChanges", "MIDDLEWARE_CHANGES", "SERVER_ONLY_CHANGES", "pg", "size", "clear", "prevChunkNames", "addedPages", "removedPages", "addedPage", "ADDED_PAGE", "removedPage", "REMOVED_PAGE", "booted", "watcher", "hotReloader", "nextConfig", "webpackCacheDirectories", "sendUpdateSignal", "updateDevToolsConfig", "DEVTOOLS_CONFIG", "invalidate", "getErrors", "normalizedPage", "hasErrors", "publish", "definition", "isApp", "inputPage", "close"], "mappings": "AAQA,SAAuBA,SAAS,QAAQ,qCAAoC;AAC5E,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,uBAAsB;AAC7B,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,OAAM;AACjE,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,kBAAkB,EAClBC,WAAW,EACXC,sBAAsB,EACtBC,6BAA6B,EAC7BC,uBAAuB,QAClB,sBAAqB;AAC5B,SAASC,cAAc,QAAQ,qBAAoB;AACnD,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,wBACLC,mBAAmB,EACnBC,eAAe,QACV,6BAA4B;AACnC,SAASC,aAAa,EAAEC,cAAc,QAAQ,sBAAqB;AACnE,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SACEC,aAAa,EACbC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,yCAAyC,EACzCC,cAAc,EACdC,gBAAgB,QACX,6BAA4B;AAEnC,SAASC,YAAY,QAAQ,2CAA0C;AACvE,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SACEC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,cAAc,EACdC,oBAAoB,QACf,4BAA2B;AAClC,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,OAAOC,4BAA4B,+BAA8B;AACjE,SACEC,UAAU,EACVC,yBAAyB,EACzBC,gBAAgB,EAChBC,oBAAoB,QACf,oBAAmB;AAC1B,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAAoBC,KAAK,QAAQ,cAAa;AAC9C,SAASC,cAAc,QAAQ,qBAAoB;AACnD,OAAOC,QAAQ,wBAAuB;AACtC,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAE/C,SAASC,gBAAgB,QAAQ,uBAAsB;AAEvD,SAASC,UAAU,QAAQ,yBAAwB;AACnD,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SACEC,mBAAmB,EACnBC,mBAAmB,QACd,kCAAiC;AACxC,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,2BAA2B,QAEtB,uBAAsB;AAG7B,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,2BAA2B,QAAQ,aAAY;AACxD,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,SAASC,8BAA8B,QAAQ,gEAA+D;AAC9G,SAASC,2BAA2B,QAAQ,kEAAiE;AAC7G,SAASC,gCAAgC,QAAQ,sDAAqD;AACtG,OAAOC,uBAAuB,uCAAsC;AACpE,SAASC,6BAA6B,QAAQ,2DAA0D;AACxG,SAASC,0CAA0C,QAAQ,yCAAwC;AACnG,SAASC,yBAAyB,QAAQ,8BAA6B;AACvE,SACEC,wBAAwB,EACxBC,iBAAiB,QACZ,wDAAuD;AAE9D,MAAMC,6BAA6BC,OAAO;AAE1C,SAASC,KAAKC,CAAW,EAAEC,CAAW;IACpC,OAAO,IAAIC,IAAI;WAAIF;KAAE,CAACG,MAAM,CAAC,CAACC,IAAM,CAACH,EAAEI,GAAG,CAACD;AAC7C;AAEA,MAAME,WAAW,IAAIhC,GAAGiC,MAAM,CAAC;IAAEC,UAAU;AAAK;AAEhD,OAAO,eAAeC,kBACpBC,GAAmB,EACnBC,KAAY,EACZ,EAAEC,UAAU,IAAI,EAAE,GAAG,CAAC,CAAC;IAEvB,wDAAwD;IACxDF,IAAIG,SAAS,CACX,iBACA;IAGF,IAAI,AAACF,MAAcG,IAAI,KAAK,UAAU;QACpC,OAAO;YAAEC,UAAUC;QAAU;IAC/B;IAEA,IAAIJ,SAAS;QACXK,QAAQN,KAAK,CAACA,MAAMO,KAAK;IAC3B;IACAR,IAAIS,UAAU,GAAG;IACjBT,IAAIU,GAAG,CAAC;IACR,OAAO;QAAEL,UAAU;IAAK;AAC1B;AAEA,SAASM,eAAeC,GAAoB,EAAEZ,GAAmB;IAC/D,wEAAwE;IACxE,IAAI,CAACY,IAAIC,GAAG,CAAEC,UAAU,CAAC,YAAY;QACnC,OAAO;YAAEC,WAAW;QAAM;IAC5B;IAEA,IAAI,CAACH,IAAII,OAAO,CAACC,MAAM,EAAE;QACvB,OAAO;YAAEF,WAAW;QAAM;IAC5B;IAEAf,IAAIG,SAAS,CAAC,+BAA+BS,IAAII,OAAO,CAACC,MAAM;IAC/DjB,IAAIG,SAAS,CAAC,gCAAgC;IAC9C,gHAAgH;IAChH,IAAIS,IAAII,OAAO,CAAC,iCAAiC,EAAE;QACjDhB,IAAIG,SAAS,CACX,gCACAS,IAAII,OAAO,CAAC,iCAAiC;IAEjD;IAEA,IAAIJ,IAAIM,MAAM,KAAK,WAAW;QAC5BlB,IAAImB,SAAS,CAAC;QACdnB,IAAIU,GAAG;QACP,OAAO;YAAEK,WAAW;QAAK;IAC3B;IAEA,OAAO;QAAEA,WAAW;IAAM;AAC5B;AAEA,OAAO,MAAMK,6BAA6BzE,aACxC,iDACD;AAED,6DAA6D;AAC7D,SAAS0E,gBACPC,MAAsB,EACtBC,WAAgC;IAEhC,OAAS;QACP,MAAMC,SAASD,YAAYE,WAAW,CAACC,SAAS,CAACJ;QACjD,IAAI,CAACE,QAAQ,OAAOF;QACpBA,SAASE;IACX;AACF;AAEA,SAASG,aAAaJ,WAAgC;IACpD,MAAMK,cAAkD,CAAC;IACzD,KAAK,MAAM3B,SAASsB,YAAYM,MAAM,CAAE;QACtC,IAAI,CAAC5B,MAAMqB,MAAM,EAAE;YACjB;QACF;QAEA,MAAMQ,cAAcT,gBAAgBpB,MAAMqB,MAAM,EAAEC;QAClD,MAAM,EAAEQ,IAAI,EAAE,GAAGD;QACjB,IAAI,CAACC,MAAM;YACT;QACF;QAEA,iCAAiC;QACjC,MAAMC,eAAe5E,uBAAuB2E;QAE5C,IAAI,CAACC,cAAc;YACjB;QACF;QAEA,IAAI,CAACJ,WAAW,CAACI,aAAa,EAAE;YAC9BJ,WAAW,CAACI,aAAa,GAAG,EAAE;QAChC;QAEAJ,WAAW,CAACI,aAAa,CAACC,IAAI,CAAChC;IACjC;IAEA,OAAO2B;AACT;AAEA,OAAO,eAAeM;IACpB,IAAIC,YAAY;IAEhB,IAAI;QACFA,YAAYC,QAAQ,qBAAqBC,OAAO;QAEhD,IAAIrC;QAEJ,IAAI;YACF,8CAA8C;YAC9CA,MAAM,MAAMsC,MAAM;QACpB,EAAE,OAAM;QACN,sBAAsB;QACxB;QAEA,IAAI,CAACtC,OAAO,CAACA,IAAIuC,EAAE,EAAE,OAAO;YAAEJ;YAAWK,WAAW;QAAU;QAE9D,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAM1C,IAAI2C,IAAI;QAEzC,OAAO3E,iBAAiB;YAAEmE;YAAWM;YAAQC;QAAO;IACtD,EAAE,OAAOE,GAAQ;QACfrC,QAAQN,KAAK,CAAC2C;QACd,OAAO;YAAET;YAAWK,WAAW;QAAU;IAC3C;AACF;AAEA,eAAe,MAAMK;IAgDnBC,YACEC,GAAW,EACX,EACEC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EAaX,CACD;aA1DMC,cAA4B;aAC5BC,cAA4B;aAC5BC,iBAA+B;aAU/BC,eAA0C,CAAC;aAI3CC,cAA2B;YACjCvB,WAAW;YACXL,WAAW;QACb;aAEQ6B,0BAAmC;QAsCzC,IAAI,CAACC,iBAAiB,GAAG;QACzB,IAAI,CAACC,uBAAuB,GAAG;QAC/B,IAAI,CAACC,yBAAyB,GAAG;QACjC,IAAI,CAACf,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACN,GAAG,GAAGA;QACX,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACmB,WAAW,GAAG,EAAE;QACrB,IAAI,CAAClB,QAAQ,GAAGA;QAChB,IAAI,CAACM,MAAM,GAAGA;QACd,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACkB,WAAW,GAAG;QACnB,IAAI,CAACC,WAAW,GAAG;QACnB,IAAI,CAACC,eAAe,GAAG;QACvB,IAAI,CAACC,sBAAsB,GAAG;QAC9B,IAAI,CAACf,SAAS,GAAGA;QACjB,IAAI,CAACC,UAAU,GAAGA;QAElB,IAAI,CAACV,MAAM,GAAGA;QACd,IAAI,CAACM,YAAY,GAAGA;QACpB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACkB,eAAe,GAAG/G,MAAM,gBAAgB4C,WAAW;YACtD+B,SAASqC,QAAQC,GAAG,CAACC,cAAc;QACrC;QACA,8FAA8F;QAC9F,wCAAwC;QACxC,IAAI,CAACH,eAAe,CAACI,IAAI;IAC3B;IAEA,MAAaC,IACXlE,GAAoB,EACpBZ,GAAmB,EACnB+E,SAAoB,EACU;QAC9B,qFAAqF;QACrF,iEAAiE;QACjE,iEAAiE;QACjE,0CAA0C;QAC1C,MAAM,EAAEhE,SAAS,EAAE,GAAGJ,eAAeC,KAAKZ;QAC1C,IAAIe,WAAW;YACb,OAAO,CAAC;QACV;QAEA,6FAA6F;QAC7F,8FAA8F;QAC9F,kEAAkE;QAClE,uFAAuF;QACvF,MAAMiE,0BAA0B,OAC9BC,eACAC;YAEA,MAAM,EAAEC,QAAQ,EAAE,GAAGD;YACrB,IAAI,CAACC,UAAU,OAAO,CAAC;YAEvB,MAAMC,SAAShE,2BAA2B+D;YAC1C,IAAI,CAACC,QAAQ,OAAO,CAAC;YAErB,IAAIC;YAEJ,IAAI;gBACFA,kBAAkB,CAAC,CAAC,EAAED,OAAOE,IAAI,CAC9BC,GAAG,CAAC,CAACC,QAAkBC,mBAAmBD,QAC1C1K,IAAI,CAAC,MAAM;YAChB,EAAE,OAAO4K,GAAG;gBACV,MAAM,qBAAyC,CAAzC,IAAIjI,YAAY,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;YAEA,MAAMkI,OAAOzI,oBAAoBmI;YAEjC,IAAIM,SAAS,aAAavJ,cAAcwJ,OAAO,CAACD,UAAU,CAAC,GAAG;gBAC5D,IAAI;oBACF,MAAM,IAAI,CAACE,UAAU,CAAC;wBAAEF;wBAAMG,YAAY;wBAAMjF,KAAKD,IAAIC,GAAG;oBAAC;gBAC/D,EAAE,OAAOZ,OAAO;oBACd,OAAO,MAAMF,kBAAkBkF,eAAetH,eAAesC;gBAC/D;gBAEA,MAAM4B,SAAS,MAAM,IAAI,CAACkE,oBAAoB,CAACJ;gBAC/C,IAAI9D,OAAOmE,MAAM,GAAG,GAAG;oBACrB,OAAO,MAAMjG,kBAAkBkF,eAAepD,MAAM,CAAC,EAAE,EAAE;wBACvD3B,SAAS;oBACX;gBACF;YACF;YAEA,OAAO,CAAC;QACV;QAEA,MAAM,EAAEG,QAAQ,EAAE,GAAG,MAAM2E,wBAAwBhF,KAAK+E;QAExD,KAAK,MAAMkB,cAAc,IAAI,CAAC7B,WAAW,CAAE;YACzC,IAAI8B,aAAa;YAEjB,MAAMD,WAAWrF,KAAKZ,KAAK;gBACzBkG,aAAa;YACf;YAEA,IAAI,CAACA,YAAY;gBACf,OAAO;oBAAE7F,UAAU;gBAAK;YAC1B;QACF;QAEA,OAAO;YAAEA;QAAS;IACpB;IAEO8F,kBAAkBlG,KAAmB,EAAQ;QAClD,IAAI,CAAC4D,cAAc,GAAG5D;IACxB;IAEOmG,sBAA4B;QACjC,IAAI,IAAI,CAACvC,cAAc,EAAE;YACvB,IAAI,CAACsC,iBAAiB,CAAC;YACvB,IAAI,CAACE,IAAI,CAAC;gBACRC,QAAQhI,4BAA4BiI,WAAW;gBAC/CC,MAAM;YACR;QACF;IACF;IAEA,MAAgBC,wBAAwBC,IAAY,EAAiB;QACnE,IAAI,CAACL,IAAI,CAAC;YACRC,QAAQhI,4BAA4BqI,wBAAwB;YAC5DD;QAGF;IACF;IAEOE,MACLhG,GAAoB,EACpBiG,OAAe,EACfC,IAAY,EACZC,QAAwC,EACxC;QACAnH,SAASoH,aAAa,CAACpG,KAAKA,IAAIqG,MAAM,EAAEH,MAAM,CAACI;gBAC7C,4BACA;aADA,6BAAA,IAAI,CAACC,oBAAoB,qBAAzB,2BAA2BP,KAAK,CAACM;aACjC,wBAAA,IAAI,CAACE,eAAe,qBAApB,sBAAsBR,KAAK,CAACM,QAAQ,IAAM,IAAI,CAACrD,cAAc;YAC7DkD,SAASG;YAETA,OAAOG,gBAAgB,CAAC,WAAW,OAAO,EAAEb,IAAI,EAAE;gBAChDA,OAAO,OAAOA,SAAS,WAAWA,KAAKc,QAAQ,KAAKd;gBAEpD,IAAI;oBACF,MAAMe,UAAUC,KAAKC,KAAK,CAACjB;oBAE3B,IAAIkB;oBASJ,OAAQH,QAAQI,KAAK;wBACnB,KAAK;4BAAY;gCACfD,aAAa;oCACX3F,MAAMwF,QAAQK,QAAQ;oCACtBC,WACEzI,OAAO0I,KAAKC,KAAK,CAACR,QAAQM,SAAS,KACnC1I;oCACF6I,OAAOT,QAAQU,UAAU;oCACzBC,SACE9I,OAAO0I,KAAKC,KAAK,CAACR,QAAQW,OAAO,KACjC/I;gCACJ;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzBuI,aAAa;oCACX3F,MAAMwF,QAAQI,KAAK;oCACnBE,WACEzI,OAAOmI,QAAQM,SAAS,IAAI1I;oCAC9B+I,SAAS9I,OAAOmI,QAAQW,OAAO,IAAI/I;oCACnC6I,OAAO;wCACLG,gBAAgBZ,QAAQY,cAAc,CAAC5C,GAAG,CAAC,CAAC6C,IAC1CA,EACGC,OAAO,CAAC,CAAC,CAAC,EAAEnM,eAAeoM,eAAe,CAAC,EAAE,CAAC,EAAE,IAChDD,OAAO,CAAC,SAAS;wCAEtB1C,MAAM4B,QAAQ5B,IAAI;wCAClB4C,cAAchB,QAAQgB,YAAY;oCACpC;gCACF;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAkB;gCACrBb,aAAa;oCACX3F,MAAMwF,QAAQI,KAAK;gCACrB;gCACA;4BACF;wBACA,KAAK;4BAAgB;gCACnBD,aAAa;oCACX3F,MAAMwF,QAAQI,KAAK;oCACnBK,OAAO;wCAAEQ,YAAYjB,QAAQiB,UAAU;oCAAC;gCAC1C;gCACA;4BACF;wBACA,KAAK;4BAAkB;gCACrBd,aAAa;oCACX3F,MAAMwF,QAAQI,KAAK;oCACnBK,OAAO;wCAAES,cAAclB,QAAQkB,YAAY;oCAAC;gCAC9C;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAqB;gCACxBf,aAAa;oCACX3F,MAAMwF,QAAQI,KAAK;oCACnBK,OAAO;wCAAErC,MAAM4B,QAAQ5B,IAAI,IAAI;oCAAG;gCACpC;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzB,MAAM,EAAEgC,KAAK,EAAEe,UAAU,EAAEC,eAAe,EAAE,GAAGpB;gCAE/CG,aAAa;oCACX3F,MAAM4F;oCACNK,OAAO;wCAAEU,YAAYA,cAAc;oCAAG;gCACxC;gCAEA,IAAIC,iBAAiB;oCACnB9M,IAAI+M,IAAI,CAACpK;oCACT;gCACF;gCAEA,IAAIqK,cAAc;gCAClB,IAAIH,YAAY;wCACD;oCAAb,MAAMI,QAAO,QAAA,uCAAuCC,IAAI,CACtDL,gCADW,KAEV,CAAC,EAAE;oCACN,IAAII,MAAM;wCACR,8DAA8D;wCAC9D,oEAAoE;wCACpE,IAAIA,KAAKhI,UAAU,CAAC,CAAC,CAAC,EAAE5E,eAAeoM,eAAe,CAAC,EAAE,CAAC,GAAG;4CAC3D,MAAMU,UAAU,IAAIC,IAAIH,MAAM;4CAC9B,MAAMI,MAAMxE,QAAQwE,GAAG;4CACvB,MAAMC,UAAUH,QAAQI,YAAY,CACjCC,MAAM,CAAC,WACP9D,GAAG,CAAC,CAAC+D,WAAaA,SAASC,KAAK,CAACL,IAAIlD,MAAM,GAAG,IAC9CvG,MAAM,CACL,CAAC6J,WAAa,CAACA,SAASxI,UAAU,CAAC;4CAGvC,IAAIqI,QAAQnD,MAAM,GAAG,GAAG;gDACtB6C,cAAc,CAAC,MAAM,EAAEM,QAAQrO,IAAI,CAAC,MAAM,QAAQ,CAAC;4CACrD;wCACF,OAAO,IACL,8BAA8B;wCAC9BgO,KAAKhI,UAAU,CAAC,CAAC,CAAC,EAAE5E,eAAesN,eAAe,CAAC,EAAE,CAAC,GACtD;4CACA,MAAMC,kBAAkBX,KAAKS,KAAK,CAChC,CAAC,CAAC,EAAErN,eAAesN,eAAe,CAAC,EAAE,CAAC,CAACxD,MAAM;4CAG/C6C,cAAc,CAAC,MAAM,EAAEY,gBAAgB,QAAQ,CAAC;wCAClD,OAAO;4CACLZ,cAAc,CAAC,MAAM,EAAEC,KAAK,QAAQ,CAAC;wCACvC;oCACF;gCACF;gCAEAjN,IAAI+M,IAAI,CACN,CAAC,yCAAyC,EAAEC,YAAY,iEAAiE,CAAC;gCAE5H;4BACF;wBACA,KAAK;4BAAgB;gCACnB,IAAI,IAAI,CAAC7F,MAAM,CAAC0G,YAAY,CAACC,0BAA0B,EAAE;oCACvD,MAAM3K,0BAA0B;wCAC9B4K,SAASrC,QAAQqC,OAAO;wCACxBC,QAAQtC,QAAQsC,MAAM;wCACtBC,YAAYvC,QAAQuC,UAAU;wCAC9BzF,aAAa,IAAM,IAAI,CAACA,WAAW;wCACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;wCACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;wCAC3CwF,eAAe,IAAI,CAAChH,GAAG;wCACvBI,SAAS,IAAI,CAACA,OAAO;wCACrBH,QAAQ,IAAI,CAACA,MAAM,CAAC0G,YAAY,CAACC,0BAA0B;oCAC7D;gCACF;gCACA;4BACF;wBACA;4BAAS;gCACP;4BACF;oBACF;oBAEA,IAAIjC,YAAY;wBACd,IAAI,CAACjD,eAAe,CAACuF,gBAAgB,CACnCtC,WAAW3F,IAAI,EACf2F,WAAWG,SAAS,EACpBH,WAAWQ,OAAO,EAClB;4BAAE,GAAGR,WAAWM,KAAK;4BAAEiC,UAAU1C,QAAQ2C,EAAE;wBAAC;oBAEhD;gBACF,EAAE,OAAOxE,GAAG;gBACV,4BAA4B;gBAC9B;YACF;QACF;IACF;IAEA,MAAcyE,MAAMC,IAAU,EAAiB;QAC7C,OAAOA,KACJ1C,UAAU,CAAC,SACX2C,YAAY,CAAC,IACZlO,gBAAgBrB,KAAK,IAAI,CAACiI,GAAG,EAAE,IAAI,CAACC,MAAM,CAACG,OAAO,GAAG;IAE3D;IAEA,MAAcmH,iBAAiBF,IAAU,EAAE;QACzC,MAAMG,oBAAoBH,KAAK1C,UAAU,CAAC;QAE1C,MAAM8C,iBAAiB,IAAI,CAACxH,MAAM,CAACwH,cAAc;QAEjD,OAAOD,kBAAkBF,YAAY,CAAC;YACpC,MAAMI,YAAY,CAAC,IAAI,CAACvH,QAAQ,GAC3B,EAAE,GACH,MAAMqH,kBACH7C,UAAU,CAAC,kBACX2C,YAAY,CAAC,IACZK,QAAQC,GAAG,CAAC;oBACV/N,aAAa,IAAI,CAACsG,QAAQ,EAAG,SAASsH,gBAAgB;oBACtD5N,aACE,IAAI,CAACsG,QAAQ,EACb,cACAsH,gBACA;iBAEH;YAGT,IAAI,CAAC1G,YAAY,GAAG,MAAMyG,kBACvB7C,UAAU,CAAC,wBACX2C,YAAY,CAAC,IACZjP,mBAAmB;oBACjBwP,OAAO;oBACPJ,gBAAgB,IAAI,CAACxH,MAAM,CAACwH,cAAc;oBAC1CK,WAAWtM,WAAWuM,KAAK;oBAC3BL,WAAWA,UAAUhL,MAAM,CACzB,CAACsL,IAAkC,OAAOA,MAAM;oBAElD7H,UAAU,IAAI,CAACA,QAAQ;oBACvBM,QAAQ,IAAI,CAACA,MAAM;gBACrB;YAGJ,MAAMwH,cAAc,MAAMT,kBACvB7C,UAAU,CAAC,sBACX2C,YAAY,CAAC,IACZlP,kBAAkB;oBAChBqI,QAAQ,IAAI,CAACA,MAAM;oBACnBJ,SAAS,IAAI,CAACA,OAAO;oBACrBJ,QAAQ,IAAI,CAACA,MAAM;oBACnBiI,UAAU,EAAE;oBACZL,OAAO;oBACPM,OAAO,IAAI,CAACpH,YAAY;oBACxBZ,UAAU,IAAI,CAACA,QAAQ;oBACvBiI,aAAa,IAAI,CAAC7H,YAAY;oBAC9B8H,SAAS,IAAI,CAACrI,GAAG;oBACjByH,gBAAgB,IAAI,CAACxH,MAAM,CAACwH,cAAc;gBAC5C;YAGJ,MAAMa,uBAAuB;gBAC3BC,KAAK;gBACLlI,SAAS,IAAI,CAACA,OAAO;gBACrBC,eAAe,IAAI,CAACA,aAAa;gBACjCL,QAAQ,IAAI,CAACA,MAAM;gBACnBE,UAAU,IAAI,CAACA,QAAQ;gBACvBK,UAAU,IAAI,CAACA,QAAQ;gBACvBgI,kBAAkB,IAAI,CAACvI,MAAM,CAACwI,iBAAiB;gBAC/CC,mBAAmB,IAAI,CAACzI,MAAM,CAAC0I,kBAAkB;gBACjDC,gBAAgB,IAAI,CAAClH,eAAe;gBACpCjB,QAAQ,IAAI,CAACA,MAAM;gBACnBF,cAAc,IAAI,CAACA,YAAY;YACjC;YAEA,OAAOiH,kBACJ7C,UAAU,CAAC,2BACX2C,YAAY,CAAC;gBACZ,MAAMuB,OAAO,MAAM5P,gBAAgB;oBACjC+G,KAAK,IAAI,CAACA,GAAG;oBACbC,QAAQqI,qBAAqBrI,MAAM;oBACnCsI,KAAK;gBACP;gBACA,OAAOZ,QAAQC,GAAG,CAAC;oBACjB,0BAA0B;oBAC1B7O,qBAAqB,IAAI,CAACiH,GAAG,EAAE;wBAC7B,GAAGsI,oBAAoB;wBACvBQ,cAAcpP,eAAeyK,MAAM;wBACnC8D,aAAaA,YAAY9D,MAAM;wBAC/B,GAAG0E,IAAI;oBACT;oBACA9P,qBAAqB,IAAI,CAACiH,GAAG,EAAE;wBAC7B,GAAGsI,oBAAoB;wBACvBQ,cAAcpP,eAAeqP,MAAM;wBACnCd,aAAaA,YAAYc,MAAM;wBAC/B,GAAGF,IAAI;oBACT;oBACA9P,qBAAqB,IAAI,CAACiH,GAAG,EAAE;wBAC7B,GAAGsI,oBAAoB;wBACvBQ,cAAcpP,eAAesP,UAAU;wBACvCf,aAAaA,YAAYe,UAAU;wBACnC,GAAGH,IAAI;oBACT;iBACD;YACH;QACJ;IACF;IAEA,MAAaI,qBAAoC;QAC/C,IAAI,IAAI,CAACC,eAAe,EAAE;QAE1B,MAAML,OAAO,MAAM5P,gBAAgB;YACjC+G,KAAK,IAAI,CAACA,GAAG;YACbC,QAAQ,IAAI,CAACA,MAAM;YACnBsI,KAAK;QACP;QACA,MAAMY,iBAAiB,MAAMpQ,qBAAqB,IAAI,CAACiH,GAAG,EAAE;YAC1DO,cAAc,IAAI,CAACA,YAAY;YAC/BqI,gBAAgB,IAAI,CAAClH,eAAe;YACpC6G,KAAK;YACLO,cAAcpP,eAAeyK,MAAM;YACnClE,QAAQ,IAAI,CAACA,MAAM;YACnBI,SAAS,IAAI,CAACA,OAAO;YACrBC,eAAe,IAAI,CAACA,aAAa;YACjCG,QAAQ,IAAI,CAACA,MAAM;YACnBN,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU;gBACR4I,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAd,kBAAkB;gBAChBY,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAZ,mBAAmB,EAAE;YACrBa,eAAe;YACftB,aAAa,AACX,CAAA,MAAM7P,kBAAkB;gBACtBqI,QAAQ,IAAI,CAACA,MAAM;gBACnBJ,SAAS,IAAI,CAACA,OAAO;gBACrBJ,QAAQ,IAAI,CAACA,MAAM;gBACnBiI,UAAU,EAAE;gBACZL,OAAO;gBACPM,OAAO;oBACL,SAAS;oBACT,WAAW;gBACb;gBACAhI,UAAU,IAAI,CAACA,QAAQ;gBACvBiI,aAAa,IAAI,CAAC7H,YAAY;gBAC9B8H,SAAS,IAAI,CAACrI,GAAG;gBACjByH,gBAAgB,IAAI,CAACxH,MAAM,CAACwH,cAAc;YAC5C,EAAC,EACDtD,MAAM;YACR,GAAG0E,IAAI;QACT;QAEA,MAAMW,mBAAmB1N,oBAAoBqN;QAE7C,IAAI,CAACD,eAAe,GAAG,MAAM,IAAIvB,QAAQ,CAAC8B;YACxC,IAAIC,yBAAyB;YAC7BF,iBAAiBG,KAAK,CACpB,kFAAkF;YAClFR,eAAeS,YAAY,EAC3B,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAACH,wBAAwB;oBAC3BA,yBAAyB;oBACzBD,QAAQ;gBACV;YACF;QAEJ;IACF;IAEA,MAAcK,qBAAqBzC,IAAU,EAAE;QAC7C,MAAM0C,kBAAkB1C,KAAK1C,UAAU,CAAC;QACxC,OAAOoF,gBAAgBzC,YAAY,CAAc,UAC/CnI;IAEJ;IAEA,MAAa6K,QAAuB;QAClC,MAAMC,YAAY,IAAI,CAACvI,eAAe,CAACiD,UAAU,CAAC;QAClDsF,UAAUnI,IAAI,GAAG,uDAAuD;;QAExE,IAAI,CAACd,WAAW,GAAG,MAAM,IAAI,CAAC8I,oBAAoB,CAACG;QAEnD,MAAMC,gBAAgBxO;QACtB,IAAIwO,iBAAiB,CAAC,IAAI,CAACC,mBAAmB,EAAE;YAC9C,MAAMC,YAAYzI,QAAQyI,SAAS;YACnC,IAAIC;YACJ,IAAI;gBACF,8EAA8E;gBAC9E,MAAMC,gBAAgB,MAAM/K,MAC1B,CAAC,iBAAiB,EAAE6K,UAAU,UAAU,CAAC,EACzCG,IAAI,CAAC,CAACtN,MAAQA,IAAI2C,IAAI;gBACxB,iFAAiF;gBACjFyK,YAAYC,aAAa,CAAC,EAAE;YAC9B,EAAE,OAAM,CAAC;YACT,IAAID,WAAW;gBACb,IAAI,CAACF,mBAAmB,GAAGE,UAAUF,mBAAmB;YAC1D;QACF;QAEA,MAAM,IAAI,CAAC/C,KAAK,CAAC6C;QACjB,oDAAoD;QACpD,MAAMjP,GAAGwP,KAAK,CAAC,IAAI,CAACpK,OAAO,EAAE;YAAEqK,WAAW;QAAK;QAE/C,MAAMC,wBAAwB,MAAMvO,kBAAkB,IAAI,CAACiE,OAAO;QAElE,MAAMuK,sBAAsB5S,KAAK,IAAI,CAACqI,OAAO,EAAE;QAC/C,8EAA8E;QAC9E,uDAAuD;QACvD,MAAMpF,GAAG4P,SAAS,CAACD,qBAAqB;QAExC,IAAI,CAACE,oBAAoB,GAAG,MAAM,IAAI,CAACtD,gBAAgB,CAAC0C;QAExD,KAAK,MAAMhK,UAAU,IAAI,CAAC4K,oBAAoB,CAAE;YAC9C,MAAMC,eAAe7K,OAAO8K,KAAK;YACjC9K,OAAO8K,KAAK,GAAG,OAAO,GAAGC;oBACJ;gBAAnB,MAAMC,aAAa,EAAA,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU,KAAI;gBACrD,MAAMpE,UAAU9M,WAAWkR;gBAC3B,wCAAwC;gBACxC,MAAMhD,cAAc,MAAM6C,gBAAgBE;gBAC1C,MAAMG,sBAAsBlL,OAAOjB,IAAI,KAAKtF,eAAeyK,MAAM;gBACjE,MAAMiH,0BAA0BnL,OAAOjB,IAAI,KAAKtF,eAAeqP,MAAM;gBACrE,MAAMsC,0BACJpL,OAAOjB,IAAI,KAAKtF,eAAesP,UAAU;gBAE3C,MAAMrB,QAAQC,GAAG,CACf0D,OAAOC,IAAI,CAAC1E,SAASrE,GAAG,CAAC,OAAOgJ;oBAC9B,MAAMC,YAAY5E,OAAO,CAAC2E,SAAS;oBACnC,MAAM,EAAEE,UAAU,EAAEC,OAAO,EAAE,GAAGF;oBAEhC,MAAMG,SACJ,sDAAsD5F,IAAI,CACxDwF;oBAEJ,MAAM,GAAGK,IAAI,YAAY,OAAMjJ,KAAK,GAAGgJ,MAAQ,kCAAkC;;oBAEjF,IAAIC,QAAQnS,eAAeyK,MAAM,IAAI,CAACgH,qBAAqB;oBAC3D,IAAIU,QAAQnS,eAAeqP,MAAM,IAAI,CAACqC,yBACpC;oBACF,IAAIS,QAAQnS,eAAesP,UAAU,IAAI,CAACqC,yBACxC;oBAEF,MAAMS,UAAUL,UAAUM,IAAI,KAAK/R,WAAWgS,KAAK;oBACnD,MAAMC,eAAeR,UAAUM,IAAI,KAAK/R,WAAWkS,WAAW;oBAE9D,0DAA0D;oBAC1D,IAAIJ,SAAS;wBACX,MAAMK,aACJ,CAACR,WAAW7Q,WAAW2Q,UAAUW,gBAAgB;wBACnD,IAAI,CAACD,YAAY;4BACf,OAAOtF,OAAO,CAAC2E,SAAS;4BACxB;wBACF;oBACF;oBAEA,sEAAsE;oBACtE,IAAIS,cAAc;wBAChB,IAAIR,UAAUY,qBAAqB,EAAE;4BACnC,MAAMF,aACJ,CAACR,WAAW7Q,WAAW2Q,UAAUY,qBAAqB;4BACxD,IAAI,CAACF,YAAY;gCACf,OAAOtF,OAAO,CAAC2E,SAAS;gCACxB;4BACF;wBACF;oBACF;oBAEA,8CAA8C;oBAC9C,IAAI5I,SAAS,WAAW;wBACtB,IAAI,CAACxB,yBAAyB,GAAG;oBACnC;oBAEA,MAAMkL,YAAY,CAAC,CAAC,IAAI,CAAC7L,MAAM;oBAC/B,MAAM8L,YAAYD,aAAaZ,WAAW3N,UAAU,CAAC;oBACrD,MAAMyO,aAAaV,UACf,MAAMnT,8BAA8B;wBAClC8T,gBAAgBF;wBAChB9E,gBAAgB,IAAI,CAACxH,MAAM,CAACwH,cAAc;wBAC1CiF,cAAcjB,UAAUW,gBAAgB;wBACxC3L,QAAQ,IAAI,CAACA,MAAM;wBACnBR,QAAQ,IAAI,CAACA,MAAM;wBACnB4H,OAAO;wBACPjF;oBACF,KACArF;oBAEJ,IAAIiP,CAAAA,8BAAAA,WAAYT,IAAI,MAAKvQ,WAAWuM,KAAK,EAAE;4BAEvCyE,2BAAAA,oBACAA,4BAAAA;wBAFF,IACEA,EAAAA,qBAAAA,WAAWvM,MAAM,sBAAjBuM,4BAAAA,mBAAmBvM,MAAM,qBAAzBuM,0BAA2BG,GAAG,MAAK,QACnCH,EAAAA,sBAAAA,WAAWvM,MAAM,sBAAjBuM,6BAAAA,oBAAmBvM,MAAM,qBAAzBuM,2BAA2BG,GAAG,MAAK,UACnC;4BACA,IAAI,CAACzL,iBAAiB,GAAG;wBAC3B;oBACF;oBAEA,MAAM0L,oBACJL,aAAaC,CAAAA,8BAAAA,WAAYK,GAAG,MAAKlT,iBAAiBwK,MAAM;oBAE1D,MAAM2I,WAAuBrB,UAAUC,UAAU,CAAC3N,UAAU,CAC1D,YAEEvC,WAAWuM,KAAK,GAChB0D,UAAUC,UAAU,CAAC3N,UAAU,CAAC,UAC9BvC,WAAWuR,GAAG,GACdvR,WAAWwR,IAAI;oBAErB,IAAIF,aAAa,SAAS;wBACxB,IAAI,CAAC1L,yBAAyB,GAAG;oBACnC;oBACA,IAAI0L,aAAa,OAAO;wBACtB,IAAI,CAAC3L,uBAAuB,GAAG;oBACjC;oBAEA,MAAM8L,oBACJ1S,0BAA0BqI,SAASkK,aAAatR,WAAWwR,IAAI;oBAEjE,IAAIE,cAAcV,8BAAAA,WAAYW,OAAO;oBAErCzU,uBAAuB;wBACrBkK;wBACAsK;wBACAJ;wBACAM,cAAc;4BACZ,kDAAkD;4BAClD,IAAI,CAAC/B,2BAA2B,CAACS,SAAS;4BAC1CjF,OAAO,CAAC2E,SAAS,CAAC6B,MAAM,GAAGvT;4BAE3B,IAAImT,mBAAmB;gCACrB,MAAMK,uBAAuB5B,WAAWpG,OAAO,CAAC,QAAQ;gCACxD2C,WAAW,CAACqF,qBAAqB,GAAGhV,mBAAmB;oCACrDwQ,cAAcpP,eAAesP,UAAU;oCACvChK,MAAMsO;oCACNC,OAAO3U,wBAAwB;wCAC7BwT,kBAAkBX,UAAUW,gBAAgB;wCAC5CoB,cAAc;wCACd3F,OAAO;oCACT;oCACA+E,mBAAmB;oCACnBN;gCACF;gCACA;4BACF;4BACA,MAAMmB,eAAelB,YACjB9T,YAAY;gCACVuG,MAAM0M;gCACN9I;gCACA8K,UAAUjC,UAAUiC,QAAQ;gCAC5BC,UAAUzV,MAAMH,IAAI,CAClBmB,eACAlB,SACE,IAAI,CAACyI,MAAM,EACXgL,UAAUW,gBAAgB,EAC1B9G,OAAO,CAAC,OAAO;gCAEnB7E,QAAQ,IAAI,CAACA,MAAM;gCACnBgH,gBAAgB,IAAI,CAACxH,MAAM,CAACwH,cAAc;gCAC1CY,SAAS,IAAI,CAACrI,GAAG;gCACjB6H,OAAO;gCACP+F,cAAc,IAAI,CAAC3N,MAAM,CAAC4N,UAAU,CAACD,YAAY;gCACjDE,UAAU,IAAI,CAAC7N,MAAM,CAAC6N,QAAQ;gCAC9BC,aAAa,IAAI,CAAC9N,MAAM,CAAC8N,WAAW;gCACpCC,kBAAkB,IAAI,CAAC/N,MAAM,CAACgO,MAAM;gCACpCC,eAAe,EAAE1B,8BAAAA,WAAY0B,eAAe;gCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3B5J,KAAK6J,SAAS,CAAC9B,CAAAA,8BAAAA,WAAYtJ,UAAU,KAAI,CAAC,IAC1CqB,QAAQ,CAAC;gCACXgK,yBAAyB,IAAI,CAACtO,MAAM,CAAC0G,YAAY,CAC9C6H,cAAc,GACb,OACAjR;4BACN,GAAGkR,MAAM,GACTlR;4BAEJ0K,WAAW,CAACyD,WAAW,GAAGpT,mBAAmB;gCAC3CwQ,cAAcpP,eAAesP,UAAU;gCACvChK,MAAM0M;gCACN6B,OAAO/U,mBAAmB;oCACxB4T,kBAAkBX,UAAUW,gBAAgB;oCAC5C/D,SAAS,IAAI,CAACrI,GAAG;oCACjBK,SAAS,IAAI,CAACA,OAAO;oCACrBqL;oCACAzL,QAAQ,IAAI,CAACA,MAAM;oCACnB4H,OAAO;oCACPjF;oCACAuF,OAAO,IAAI,CAACpH,YAAY;oCACxB6L;oCACAa;oCACA3F,WAAWyE,YAAY/Q,WAAWuR,GAAG,GAAGvR,WAAWuM,KAAK;oCACxDmG,eAAe,EAAE1B,8BAAAA,WAAY0B,eAAe;gCAC9C;gCACA5B;4BACF;wBACF;wBACAoC,UAAU;4BACR,IAAI,CAACvD,qBAAqB;4BAC1B,IAAIc,cAAc;gCAChBpF,OAAO,CAAC2E,SAAS,CAAC6B,MAAM,GAAGvT;gCAC3BmO,WAAW,CAACyD,WAAW,GAAGpT,mBAAmB;oCAC3C0G,MAAM0M;oCACN5C,cAAcpP,eAAeyK,MAAM;oCACnCoJ,OAAO9B,UAAUkD,OAAO;oCACxBrC;gCACF;4BACF,OAAO;gCACLzF,OAAO,CAAC2E,SAAS,CAAC6B,MAAM,GAAGvT;gCAC3BmO,WAAW,CAACyD,WAAW,GAAGpT,mBAAmB;oCAC3C0G,MAAM0M;oCACN5C,cAAcpP,eAAeyK,MAAM;oCACnCoJ,OAAOhV,eAAe;wCACpB6T,kBAAkBX,UAAUW,gBAAgB;wCAC5CxJ;oCACF;oCACA0J;gCACF;4BACF;wBACF;wBACAsC,UAAU;4BACR,kDAAkD;4BAClD,IAAI,CAACxD,2BAA2B,CAACU,SAAS;4BAC1CjF,OAAO,CAAC2E,SAAS,CAAC6B,MAAM,GAAGvT;4BAC3B,IAAI+U,kBAAkB7W,SACpBiI,OAAO6O,OAAO,EACdrD,UAAUW,gBAAgB;4BAE5B,IACE,CAACnU,WAAW4W,oBACZ,CAACA,gBAAgB9Q,UAAU,CAAC,QAC5B;gCACA8Q,kBAAkB,CAAC,EAAE,EAAEA,iBAAiB;4BAC1C;4BAEA,IAAItB;4BACJ,IAAIN,mBAAmB;gCACrBM,QAAQ3U,wBAAwB;oCAC9BwT,kBAAkBX,UAAUW,gBAAgB;oCAC5CoB,cAAc;oCACd3F,OAAO;gCACT;gCACAI,WAAW,CAACyD,WAAW,GAAGpT,mBAAmB;oCAC3CwQ,cAAcpP,eAAeqP,MAAM;oCACnC/J,MAAM0M;oCACNkB,mBAAmB;oCACnBW;oCACAjB;gCACF;4BACF,OAAO,IAAI9R,iBAAiBoI,OAAO;gCACjC2K,QAAQ/U,mBAAmB;oCACzB4T,kBAAkBX,UAAUW,gBAAgB;oCAC5C/D,SAAS,IAAI,CAACrI,GAAG;oCACjBK,SAAS,IAAI,CAACA,OAAO;oCACrBqL;oCACAzL,QAAQ,IAAI,CAACA,MAAM;oCACnB4H,OAAO;oCACPjF;oCACAuF,OAAO,IAAI,CAACpH,YAAY;oCACxB6L;oCACA9E,WAAWtM,WAAWuM,KAAK;oCAC3BmG,eAAe,EAAE1B,8BAAAA,WAAY0B,eAAe;gCAC9C;4BACF,OAAO,IAAI3B,WAAW;gCACpB,0FAA0F;gCAC1F,MAAMoB,WAAWlC,UAAUW,gBAAgB,CAACrO,UAAU,CACpD5F,QAAQkH,QAAQoK,OAAO,CAAC,yBAEtBgC,UAAUW,gBAAgB,GAC1BlU,MAAMH,IAAI,CACRmB,eACAlB,SACE,IAAI,CAACyI,MAAM,EACXgL,UAAUW,gBAAgB,EAC1B9G,OAAO,CAAC,OAAO;gCAEvBiI,QAAQ9U,YAAY;oCAClBuG,MAAM0M;oCACN9I;oCACA8K,UAAUjC,UAAUiC,QAAQ;oCAC5BC;oCACAlN,QAAQ,IAAI,CAACA,MAAM;oCACnBgH,gBAAgB,IAAI,CAACxH,MAAM,CAACwH,cAAc;oCAC1CY,SAAS,IAAI,CAACrI,GAAG;oCACjB6H,OAAO;oCACP+F,cAAc,IAAI,CAAC3N,MAAM,CAAC4N,UAAU,CAACD,YAAY;oCACjDE,UAAU,IAAI,CAAC7N,MAAM,CAAC6N,QAAQ;oCAC9BC,aAAa,IAAI,CAAC9N,MAAM,CAAC8N,WAAW;oCACpCC,kBAAkB,IAAI,CAAC/N,MAAM,CAACgO,MAAM;oCACpCC,eAAe,EAAE1B,8BAAAA,WAAY0B,eAAe;oCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3B5J,KAAK6J,SAAS,CAAC9B,CAAAA,8BAAAA,WAAYtJ,UAAU,KAAI,CAAC,IAC1CqB,QAAQ,CAAC;oCACXgK,yBAAyB,IAAI,CAACtO,MAAM,CAAC0G,YAAY,CAC9C6H,cAAc,GACb,OACAjR;gCACN;4BACF,OAAO,IAAIrC,WAAW0H,OAAO;gCAC3B2K,QAAQpS,oBAAoB;oCAC1B4T,MAAMzT,UAAU0T,SAAS;oCACzBpM;oCACAwJ,kBAAkByC;oCAClBX,eAAe,EAAE1B,8BAAAA,WAAY0B,eAAe;oCAC5CC,kBAAkB3B,CAAAA,8BAAAA,WAAYtJ,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO,IACL,CAAC1I,iBAAiBoI,SAClB,CAACxH,oBAAoByT,oBACrB,CAACxT,oBAAoBuH,SACrB,CAACqK,mBACD;gCACAM,QAAQpS,oBAAoB;oCAC1B4T,MAAMzT,UAAUyM,KAAK;oCACrBnF;oCACAuF,OAAO,IAAI,CAACpH,YAAY;oCACxBqL,kBAAkByC;oCAClBX,eAAe,EAAE1B,8BAAAA,WAAY0B,eAAe;oCAC5CC,kBAAkB3B,CAAAA,8BAAAA,WAAYtJ,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO;gCACLqK,QAAQsB;4BACV;4BAEA5G,WAAW,CAACyD,WAAW,GAAGpT,mBAAmB;gCAC3CwQ,cAAcpP,eAAeqP,MAAM;gCACnC/J,MAAM0M;gCACNkB;gCACAW;gCACAjB;4BACF;wBACF;oBACF;gBACF;gBAGF,IAAI,CAAC,IAAI,CAACpL,iBAAiB,EAAE;oBAC3B,OAAO+G,WAAW,CAAC3O,gCAAgC;gBACrD;gBACA,IAAI,CAAC,IAAI,CAAC8H,yBAAyB,EAAE;oBACnC,OAAO6G,WAAW,CAAC1O,iCAAiC;oBACpD,OAAO0O,WAAW,CAAC,aAAa;oBAChC,OAAOA,WAAW,CAAC,eAAe;oBAClC,OAAOA,WAAW,CAAC,UAAU;oBAC7B,OAAOA,WAAW,CAAC,kBAAkB;gBACvC;gBACA,qEAAqE;gBACrE,IAAI,CAAC,IAAI,CAAC/G,iBAAiB,IAAI,CAAC,IAAI,CAACE,yBAAyB,EAAE;oBAC9D,OAAO6G,WAAW,CAACxO,0CAA0C;gBAC/D;gBACA,IAAI,CAAC,IAAI,CAAC0H,uBAAuB,EAAE;oBACjC,OAAO8G,WAAW,CAACzO,qCAAqC;gBAC1D;gBAEA,OAAOyO;YACT;QACF;QAEA,iFAAiF;QACjF,uBAAuB;QACvB,IAAI,CAAC4C,oBAAoB,CAACoE,WAAW,GAAG;QAExC,MAAMtH,QAAQC,GAAG,CACfsH,MAAMb,IAAI,CAACrV,oBAAoB,IAAI,CAAC6R,oBAAoB,GAAGrI,GAAG,CAC5DxG;QAGJ,IAAI,CAACkP,aAAa,GAAGpP,oBACnB,IAAI,CAAC+O,oBAAoB;QAG3B,uEAAuE;QACvE,MAAMsE,kBAAkB,IAAI,CAACjE,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACD,eAAe;QACvE,KAAK,MAAME,YAAY,IAAI,CAACnE,aAAa,CAACkE,SAAS,CAAE;YACnDC,SAASF,eAAe,GAAGA;YAC3B,qFAAqF;YACrFE,SAASC,WAAW,GAAGC,KAAKC,GAAG;YAC/B,sGAAsG;YACtGH,SAASI,KAAK,CAACC,SAAS,CAACC,SAAS,CAAC;gBACjCC,UAASC,OAAY;oBACnB,IAAIA,QAAQ7Q,IAAI,KAAK,yBAAyB;wBAC5C,OAAO;oBACT;oBACA,OAAO6Q;gBACT;YACF;QACF;QAEA,IAAI,CAAC3E,aAAa,CAACuE,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,qBAAqB;gBACrDZ;YAAAA,oCAAAA,yBAAAA,gBAAiBa,KAAK,qBAAtBb,4BAAAA;QACF;QACAtW,eACE,IAAI,CAACqS,aAAa,CAACkE,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAClE,aAAa,CAACkE,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAClE,aAAa,CAACkE,SAAS,CAAC,EAAE;QAGjC,yEAAyE;QACzE,gEAAgE;QAChE,MAAMa,qBAAqB,IAAIxT;QAC/B,MAAMyT,qBAAqB,IAAIzT;QAC/B,MAAM0T,yBAAyB,IAAI1T;QAEnC,MAAM2T,8BAA8B,IAAI3T;QACxC,MAAM4T,wBAAwB,IAAI5T;QAElC,MAAM6T,uBAAuB,IAAIC;QACjC,MAAMC,uBAAuB,IAAID;QACjC,MAAME,2BAA2B,IAAIF;QACrC,MAAMG,4BAA4B,IAAIH;QAEtC,MAAMI,qBAAqB,IAAIC,OAC7B,CAAC,MAAM,EAAE,IAAI,CAAC3Q,MAAM,CAACwH,cAAc,CAAC1P,IAAI,CAAC,KAAK,EAAE,CAAC;QAGnD,MAAM8Y,mBACJ,CACEC,aACAC,cACAC,8BAEF,CAACC;gBACC,IAAI;oBACFA,MAAMhJ,WAAW,CAACiJ,OAAO,CAAC,CAACnG,OAAOc;wBAChC,IACEA,IAAI9N,UAAU,CAAC,aACf8N,IAAI9N,UAAU,CAAC,WACftD,qBAAqBoR,MACrB;4BACA,mDAAmD;4BACnDd,MAAMoG,MAAM,CAACD,OAAO,CAAC,CAACE;gCACpB,IAAIA,MAAMjK,EAAE,KAAK0E,KAAK;oCACpB,MAAMwF,eACJJ,MAAMK,UAAU,CAACC,uBAAuB,CAACH;oCAE3C,IAAII,sBAAsB;oCAC1B,IAAIC,aAAa,IAAI9Z;oCACrB,IAAI+Z,wBAAwB,IAAI/Z;oCAEhC0Z,aAAaH,OAAO,CAAC,CAACS;wCACpB,IACEA,IAAIC,QAAQ,IACZD,IAAIC,QAAQ,CAACtM,OAAO,CAAC,OAAO,KAAKuM,QAAQ,CAAChG,QAC1C,oCAAoC;wCACpC8E,mBAAmBmB,IAAI,CAACH,IAAIC,QAAQ,GACpC;gDAeED,oBAAAA;4CAdF,uDAAuD;4CACvD,uDAAuD;4CACvD,wDAAwD;4CACxD,sDAAsD;4CACtD,MAAMhO,OAAO,AACXtE,QAAQ,UAEP0S,UAAU,CAAC,QACXC,MAAM,CAACL,IAAIM,cAAc,GAAGC,MAAM,IAClCC,MAAM,GACN5N,QAAQ,CAAC;4CAEZ,IACEoN,IAAIS,KAAK,KAAKjZ,eAAekZ,qBAAqB,IAClDV,CAAAA,wBAAAA,iBAAAA,IAAKW,SAAS,sBAAdX,qBAAAA,eAAgB9E,GAAG,qBAAnB8E,mBAAqB5F,IAAI,MAAK,UAC9B;gDACA2F,sBAAsBa,GAAG,CAAC5O;4CAC5B;4CAEA8N,WAAWc,GAAG,CAAC5O;wCACjB,OAAO;gDASHgO,qBAAAA;4CARF,oDAAoD;4CACpD,MAAMhO,OAAOsN,MAAMK,UAAU,CAACkB,aAAa,CACzCb,KACAP,MAAMjE,OAAO;4CAGf,IACEwE,IAAIS,KAAK,KAAKjZ,eAAekZ,qBAAqB,IAClDV,CAAAA,wBAAAA,kBAAAA,IAAKW,SAAS,sBAAdX,sBAAAA,gBAAgB9E,GAAG,qBAAnB8E,oBAAqB5F,IAAI,MAAK,UAC9B;gDACA2F,sBAAsBa,GAAG,CAAC5O;4CAC5B;4CAEA8N,WAAWc,GAAG,CAAC5O;4CAEf,iDAAiD;4CACjD,0BAA0B;4CAC1B,IACEkI,IAAI9N,UAAU,CAAC,WACf,qBAAqB+T,IAAI,CAACH,IAAIC,QAAQ,IAAI,KAC1C;gDACA,MAAMa,cAAcd,IAAIS,KAAK,GAAG,MAAMT,IAAIC,QAAQ;gDAClD,MAAMc,WACJhC,0BAA0BiC,GAAG,CAACF;gDAChC,IAAIC,YAAYA,aAAa/O,MAAM;oDACjC6N,sBAAsB;gDACxB;gDACAd,0BAA0BkC,GAAG,CAACH,aAAa9O;4CAC7C;wCACF;oCACF;oCAEA,MAAM+O,WAAW5B,YAAY6B,GAAG,CAAC9G;oCACjC,MAAMgH,UAAUpB,WAAWlN,QAAQ;oCACnC,IAAImO,YAAYA,aAAaG,SAAS;wCACpC9B,aAAawB,GAAG,CAAC1G;oCACnB;oCACAiF,YAAY8B,GAAG,CAAC/G,KAAKgH;oCAErB,IAAI7B,6BAA6B;wCAC/B,MAAM8B,YACJ3Z,eAAekZ,qBAAqB,GAAG,MAAMxG;wCAC/C,MAAMkH,iBAAiBjC,YAAY6B,GAAG,CAACG;wCACvC,MAAME,gBAAgBtB,sBAAsBnN,QAAQ;wCACpD,IAAIwO,kBAAkBA,mBAAmBC,eAAe;4CACtDhC,4BAA4BuB,GAAG,CAAC1G;wCAClC;wCACAiF,YAAY8B,GAAG,CAACE,WAAWE;oCAC7B;oCAEA,IAAIxB,qBAAqB;wCACvBnB,sBAAsBkC,GAAG,CAAC1G;oCAC5B;gCACF;4BACF;wBACF;oBACF;gBACF,EAAE,OAAOoH,KAAK;oBACZzV,QAAQN,KAAK,CAAC+V;gBAChB;YACF;QAEF,IAAI,CAAC/H,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACyD,IAAI,CAACnD,GAAG,CAC5C,8BACAc,iBAAiBP,sBAAsBL;QAEzC,IAAI,CAAC/E,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACyD,IAAI,CAACnD,GAAG,CAC5C,8BACAc,iBACEL,sBACAN,oBACAE;QAGJ,IAAI,CAAClF,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACyD,IAAI,CAACnD,GAAG,CAC5C,8BACAc,iBACEJ,0BACAN,wBACAC;QAIJ,8GAA8G;QAC9G,IAAI,CAAClF,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC0D,MAAM,CAACpD,GAAG,CAC9C,8BACA,CAACkD;YACC,IAAI,CAACpS,WAAW,GAAGoS;YACnB,IAAI,CAAC1R,WAAW,GAAG;YACnB,IAAI,CAAC6R,gBAAgB,GAAG7V;QAC1B;QAGF,IAAI,CAAC2N,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACkB;YACC,IAAI,CAACpQ,WAAW,GAAG;YACnB,IAAI,CAACW,eAAe,GAAGyP;QACzB;QAGF,IAAI,CAAC/F,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACkB;YACC,IAAI,CAACpQ,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAG0P;YAEnB,IAAI,CAAC,IAAI,CAAC9Q,QAAQ,EAAE;gBAClB;YACF;YAEA,MAAM,EAAE3B,WAAW,EAAE,GAAGyS;YAExB,kEAAkE;YAClE,oEAAoE;YACpE,MAAMoC,gBAAgB7U,YAAY8U,WAAW,CAACX,GAAG,CAAC;YAClD,qDAAqD;YACrD,IAAI,CAACU,eAAe;gBAClB;YACF;YAEA,gBAAgB;YAChB,IAAI,IAAI,CAAC5R,sBAAsB,KAAK,MAAM;gBACxC,IAAI,CAACA,sBAAsB,GAAG4R,cAAc1P,IAAI,IAAI;gBACpD;YACF;YAEA,2DAA2D;YAC3D,IAAI0P,cAAc1P,IAAI,KAAK,IAAI,CAAClC,sBAAsB,EAAE;gBACtD;YACF;YAEA,6DAA6D;YAC7D,iEAAiE;YACjE,0EAA0E;YAC1E,2EAA2E;YAC3E,IAAI,IAAI,CAAChB,MAAM,EAAE;gBACf,MAAM8S,aAAa,IAAI9W,IAAI+B,YAAY8U,WAAW,CAAC/H,IAAI;gBACvD,MAAMiI,iBAAiBlZ,WACrB,IAAI,CAAC8Y,gBAAgB,IAAI,IAAI3W,OAC7B8W;gBAGF,IACEC,eAAevQ,MAAM,KAAK,KAC1BuQ,eAAeC,KAAK,CAAC,CAACC,YAAcA,UAAU3V,UAAU,CAAC,UACzD;oBACA;gBACF;gBACA,IAAI,CAACqV,gBAAgB,GAAGG;YAC1B;YAEA,IAAI,CAAC9R,sBAAsB,GAAG4R,cAAc1P,IAAI,IAAI;YAEpD,iFAAiF;YACjF,IAAI,CAACL,IAAI,CAAC;gBACRC,QAAQhI,4BAA4BiI,WAAW;gBAC/CC,MAAM;YACR;QACF;QAGF,IAAI,CAACyH,aAAa,CAACuE,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAACkB;YAC/D,MAAMhQ,0BAA0B,IAAI,CAACA,uBAAuB;YAC5D,IAAI,CAACA,uBAAuB,GAAG;YAE/B,MAAM0S,oBAAoBrZ,WACxB4V,oBACAD;YAGF,MAAM2D,wBAAwBtZ,WAC5B6V,wBACAF;YAGF,MAAM4D,cAAcF,kBACjBG,MAAM,CAACF,uBACPlX,MAAM,CAAC,CAACmP,MAAQA,IAAI9N,UAAU,CAAC;YAElC,MAAMgW,oBAAoB;mBACrB7E,MAAMb,IAAI,CAAC8B;mBACXjB,MAAMb,IAAI,CAAC6B;aACf,CAACxT,MAAM,CAAC,CAACsC,OAASvE,qBAAqBuE;YAExC,IAAI+U,kBAAkB9Q,MAAM,GAAG,GAAG;gBAChC,IAAI,CAACK,IAAI,CAAC;oBACRsB,OAAOrJ,4BAA4ByY,kBAAkB;gBACvD;YACF;YAEA,IAAIH,YAAY5Q,MAAM,GAAG,GAAG;gBAC1B,IAAI,CAACK,IAAI,CAAC;oBACRsB,OAAOrJ,4BAA4B0Y,mBAAmB;oBACtD9L,OAAOwL,kBAAkBnR,GAAG,CAAC,CAAC0R,KAC5B/Z,oBAAoB+Z,GAAG1N,KAAK,CAAC,QAAQvD,MAAM;gBAE/C;YACF;YAEA,IACEmN,4BAA4B+D,IAAI,IAChC9D,sBAAsB8D,IAAI,IAC1BlT,yBACA;gBACA,IAAI,CAACN,UAAU;gBACf,IAAI,CAAC+C,uBAAuB,CAACuN,MAAMtN,IAAI;YACzC;YAEAsM,mBAAmBmE,KAAK;YACxBlE,mBAAmBkE,KAAK;YACxBjE,uBAAuBiE,KAAK;YAC5BhE,4BAA4BgE,KAAK;YACjC/D,sBAAsB+D,KAAK;QAC7B;QAEA,IAAI,CAAClJ,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC0D,MAAM,CAACpD,GAAG,CAC9C,8BACA,CAACkD;YACC,IAAI,CAACrS,WAAW,GAAGqS;YACnB,IAAI,CAAC3R,WAAW,GAAG;QACrB;QAEF,IAAI,CAAC4J,aAAa,CAACkE,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACkB;YACC,IAAI,CAACrQ,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAG2P;YAEnB,MAAM,EAAEzS,WAAW,EAAE,GAAGyS;YACxB,MAAMsC,aAAa,IAAI9W,IACrB;mBAAI+B,YAAY8U,WAAW,CAAC/H,IAAI;aAAG,CAAC7O,MAAM,CACxC,CAACsC,OAAS,CAAC,CAAC3E,uBAAuB2E;YAIvC,IAAI,IAAI,CAACqV,cAAc,EAAE;gBACvB,8DAA8D;gBAC9D,0CAA0C;gBAC1C,MAAMC,aAAahY,KAAKiX,YAAY,IAAI,CAACc,cAAc;gBACvD,MAAME,eAAejY,KAAK,IAAI,CAAC+X,cAAc,EAAGd;gBAEhD,IAAIe,WAAWH,IAAI,GAAG,GAAG;oBACvB,KAAK,MAAMK,aAAaF,WAAY;wBAClC,MAAM1R,OAAOvI,uBAAuBma;wBACpC,IAAI,CAAClR,IAAI,CAAC;4BACRC,QAAQhI,4BAA4BkZ,UAAU;4BAC9ChR,MAAM;gCAACb;6BAAK;wBACd;oBACF;gBACF;gBAEA,IAAI2R,aAAaJ,IAAI,GAAG,GAAG;oBACzB,KAAK,MAAMO,eAAeH,aAAc;wBACtC,MAAM3R,OAAOvI,uBAAuBqa;wBACpC,IAAI,CAACpR,IAAI,CAAC;4BACRC,QAAQhI,4BAA4BoZ,YAAY;4BAChDlR,MAAM;gCAACb;6BAAK;wBACd;oBACF;gBACF;YACF;YAEA,IAAI,CAACyR,cAAc,GAAGd;QACxB;QAGF,IAAI,CAACnP,oBAAoB,GAAG,IAAItM,qBAC9B,IAAI,CAACoT,aAAa,CAACkE,SAAS,EAC5B,IAAI,CAACpO,WAAW,EAChB,IAAI,CAACmJ,mBAAmB,EACxBO;QAGF,IAAIkK,SAAS;QAEb,IAAI,CAACC,OAAO,GAAG,MAAM,IAAIlN,QAAQ,CAAC8B;gBAChB;YAAhB,MAAMoL,WAAU,sBAAA,IAAI,CAAC3J,aAAa,qBAAlB,oBAAoBvB,KAAK,CACvC,kFAAkF;YAClF,IAAI,CAACkB,oBAAoB,CAACrI,GAAG,CAAC,CAACvC,SAAWA,OAAO2J,YAAY,GAC7D,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAAC+K,QAAQ;oBACXA,SAAS;oBACTnL,QAAQoL;gBACV;YACF;QAEJ;QAEA,IAAI,CAACxQ,eAAe,GAAGnK,qBAAqB;YAC1C4a,aAAa,IAAI;YACjB5J,eAAe,IAAI,CAACA,aAAa;YACjC/K,UAAU,IAAI,CAACA,QAAQ;YACvBM,QAAQ,IAAI,CAACA,MAAM;YACnB4H,SAAS,IAAI,CAACrI,GAAG;YACjB+U,YAAY,IAAI,CAAC9U,MAAM;YACvB,GAAI,IAAI,CAACA,MAAM,CAACoE,eAAe;QAIjC;QAEA,IAAI,CAAChD,WAAW,GAAG;YACjBzJ,qBAAqB;gBACnBoP,eAAe,IAAI,CAAChH,GAAG;gBACvBE,UAAU,IAAI,CAACA,QAAQ;gBACvBoB,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACA3J,uBAAuB;gBACrByJ,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACA7F,+BAA+B,IAAI,CAAC+E,SAAS;YAC7C9E;YACAC;YACAE,8BAA8B;gBAC5B2E,WAAW,IAAI,CAACA,SAAS;gBACzBsU,yBACE,IAAI,CAACnK,oBAAoB,IAAI,OACzB7R,oBAAoB,IAAI,CAAC6R,oBAAoB,IAC7CtN;YACR;YACArB,yBAAyB;gBACvBkE,SAAS,IAAI,CAACA,OAAO;gBACrB6U,kBAAkB,CAACxR;wBACjB,4CAA4C;oBAC5C,8CAA8C;oBAC9C;qBAAA,6BAAA,IAAI,CAACW,oBAAoB,qBAAzB,2BAA2B8Q,oBAAoB,CAACzR;oBAEhD,IAAI,CAACH,IAAI,CAAC;wBACRC,QAAQhI,4BAA4B4Z,eAAe;wBACnD1R;oBACF;gBACF;YACF;SACD;IACH;IAEO2R,WACL,EAAEnU,uBAAuB,EAAwC,GAAG;QAClEA,yBAAyB;IAC3B,CAAC,EACD;YAGmB;QAFnB,mGAAmG;QACnG,IAAI,CAACA,uBAAuB,GAAGA;QAC/B,MAAMgK,cAAa,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU;QACjD,IAAIA,YAAY;gBACdhR;aAAAA,kBAAAA,eAAegR,gCAAfhR,gBAA4Bmb,UAAU;QACxC;IACF;IAEA,MAAapS,qBAAqBJ,IAAY,EAAE;YAcnC,mBAEA,mBAEA;QAjBX,MAAMyS,YAAY,CAAC,EAAE7W,WAAW,EAAiB;gBAIxCK;YAHP,MAAMA,cAAcD,aAAaJ;YACjC,MAAM8W,iBAAiBlb,iBAAiBwI;YACxC,+FAA+F;YAC/F,OAAO/D,EAAAA,8BAAAA,WAAW,CAACyW,eAAe,qBAA3BzW,4BAA6BoE,MAAM,IAAG,IACzCpE,WAAW,CAACyW,eAAe,GAC3B9W,YAAYM,MAAM;QACxB;QAEA,IAAI,IAAI,CAAC8B,WAAW,EAAE;YACpB,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE;YAC3B,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,KAAI,oBAAA,IAAI,CAACS,WAAW,qBAAhB,kBAAkBiU,SAAS,IAAI;YACxC,OAAOF,UAAU,IAAI,CAAC/T,WAAW;QACnC,OAAO,KAAI,oBAAA,IAAI,CAACC,WAAW,qBAAhB,kBAAkBgU,SAAS,IAAI;YACxC,OAAOF,UAAU,IAAI,CAAC9T,WAAW;QACnC,OAAO,KAAI,wBAAA,IAAI,CAACC,eAAe,qBAApB,sBAAsB+T,SAAS,IAAI;YAC5C,OAAOF,UAAU,IAAI,CAAC7T,eAAe;QACvC,OAAO;YACL,OAAO,EAAE;QACX;IACF;IAEO8B,KAAKC,MAAwB,EAAQ;QAC1C,IAAI,CAACa,oBAAoB,CAAEoR,OAAO,CAACjS;IACrC;IAEA,MAAaT,WAAW,EACtBF,IAAI,EACJG,UAAU,EACV2K,QAAQ,EACR+H,UAAU,EACVC,KAAK,EACL5X,GAAG,EAQJ,EAAiB;QAChB,OAAO,IAAI,CAAC4D,eAAe,CACxBiD,UAAU,CAAC,eAAe;YACzBgR,WAAW/S;QACb,GACC0E,YAAY,CAAC;gBAYL;YAXP,wDAAwD;YACxD,IAAI1E,SAAS,aAAavJ,cAAcwJ,OAAO,CAACD,UAAU,CAAC,GAAG;gBAC5D;YACF;YACA,MAAM1F,QAAQ6F,aACV,IAAI,CAACnC,WAAW,GAChB,IAAI,CAACC,WAAW,IAAI,IAAI,CAACD,WAAW;YACxC,IAAI1D,OAAO;gBACT,MAAMA;YACR;YAEA,QAAO,wBAAA,IAAI,CAACmH,eAAe,qBAApB,sBAAsBvB,UAAU,CAAC;gBACtCF;gBACA8K;gBACA+H;gBACAC;gBACA5X;YACF;QACF;IACJ;IAEO8X,QAAQ;YACb;SAAA,6BAAA,IAAI,CAACxR,oBAAoB,qBAAzB,2BAA2BwR,KAAK;IAClC;AACF", "ignoreList": [0]}