{"version": 3, "sources": ["../../../src/server/lib/router-server.ts"], "sourcesContent": ["// this must come first as it includes require hooks\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './types'\nimport type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Fields } from './router-utils/setup-dev-bundler'\nimport type { NextUrlWithParsedQuery, RequestMeta } from '../request-meta'\n\n// This is required before other imports to ensure the require hook is setup.\nimport '../node-environment'\nimport '../require-hook'\n\nimport url from 'url'\nimport path from 'path'\nimport loadConfig from '../config'\nimport { serveStatic } from '../serve-static'\nimport setupDebug from 'next/dist/compiled/debug'\nimport * as Log from '../../build/output/log'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport { setupFsCheck } from './router-utils/filesystem'\nimport { proxyRequest } from './router-utils/proxy-request'\nimport { isAbortError, pipeToNodeResponse } from '../pipe-readable'\nimport { getResolveRoutes } from './router-utils/resolve-routes'\nimport { addRequestMeta, getRequestMeta } from '../request-meta'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport setupCompression from 'next/dist/compiled/compression'\nimport { signalFromNodeResponse } from '../web/spec-extension/adapters/next-request'\nimport { isPostpone } from './router-utils/is-postpone'\nimport { parseUrl as parseUrlUtil } from '../../shared/lib/router/utils/parse-url'\n\nimport {\n  PHASE_PRODUCTION_SERVER,\n  PHASE_DEVELOPMENT_SERVER,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../../shared/lib/constants'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { DevBundlerService } from './dev-bundler-service'\nimport { type Span, trace } from '../../trace'\nimport { ensureLeadingSlash } from '../../shared/lib/page-path/ensure-leading-slash'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { MockedResponse } from './mock-request'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type AppIsrManifestAction,\n} from '../dev/hot-reloader-types'\nimport { normalizedAssetPrefix } from '../../shared/lib/normalized-asset-prefix'\nimport { NEXT_PATCH_SYMBOL } from './patch-fetch'\nimport type { ServerInitResult } from './render-server'\nimport { filterInternalHeaders } from './server-ipc/utils'\nimport { blockCrossSite } from './router-utils/block-cross-site'\nimport { traceGlobals } from '../../trace/shared'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from './router-utils/router-server-context'\nimport {\n  handleChromeDevtoolsWorkspaceRequest,\n  isChromeDevtoolsWorkspaceUrl,\n} from './chrome-devtools-workspace'\n\nconst debug = setupDebug('next:router-server:main')\nconst isNextFont = (pathname: string | null) =>\n  pathname && /\\/media\\/[^/]+\\.(woff|woff2|eot|ttf|otf)$/.test(pathname)\n\nexport type RenderServer = Pick<\n  typeof import('./render-server'),\n  | 'initialize'\n  | 'clearModuleContext'\n  | 'propagateServerField'\n  | 'getServerField'\n>\n\nexport interface LazyRenderServerInstance {\n  instance?: RenderServer\n}\n\nconst requestHandlers: Record<string, WorkerRequestHandler> = {}\n\nexport async function initialize(opts: {\n  dir: string\n  port: number\n  dev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  minimalMode?: boolean\n  hostname?: string\n  keepAliveTimeout?: number\n  customServer?: boolean\n  experimentalHttpsServer?: boolean\n  startServerSpan?: Span\n  quiet?: boolean\n}): Promise<ServerInitResult> {\n  if (!process.env.NODE_ENV) {\n    // @ts-ignore not readonly\n    process.env.NODE_ENV = opts.dev ? 'development' : 'production'\n  }\n\n  const config = await loadConfig(\n    opts.dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_SERVER,\n    opts.dir,\n    { silent: false }\n  )\n\n  let compress: ReturnType<typeof setupCompression> | undefined\n\n  if (config?.compress !== false) {\n    compress = setupCompression()\n  }\n\n  const fsChecker = await setupFsCheck({\n    dev: opts.dev,\n    dir: opts.dir,\n    config,\n    minimalMode: opts.minimalMode,\n  })\n\n  const renderServer: LazyRenderServerInstance = {}\n\n  let developmentBundler: DevBundler | undefined\n\n  let devBundlerService: DevBundlerService | undefined\n\n  let originalFetch = globalThis.fetch\n\n  if (opts.dev) {\n    const { Telemetry } =\n      require('../../telemetry/storage') as typeof import('../../telemetry/storage')\n\n    const telemetry = new Telemetry({\n      distDir: path.join(opts.dir, config.distDir),\n    })\n    traceGlobals.set('telemetry', telemetry)\n\n    const { pagesDir, appDir } = findPagesDir(opts.dir)\n\n    const { setupDevBundler } =\n      require('./router-utils/setup-dev-bundler') as typeof import('./router-utils/setup-dev-bundler')\n\n    const resetFetch = () => {\n      globalThis.fetch = originalFetch\n      ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = false\n    }\n\n    const setupDevBundlerSpan = opts.startServerSpan\n      ? opts.startServerSpan.traceChild('setup-dev-bundler')\n      : trace('setup-dev-bundler')\n    developmentBundler = await setupDevBundlerSpan.traceAsyncFn(() =>\n      setupDevBundler({\n        // Passed here but the initialization of this object happens below, doing the initialization before the setupDev call breaks.\n        renderServer,\n        appDir,\n        pagesDir,\n        telemetry,\n        fsChecker,\n        dir: opts.dir,\n        nextConfig: config,\n        isCustomServer: opts.customServer,\n        turbo: !!process.env.TURBOPACK,\n        port: opts.port,\n        onDevServerCleanup: opts.onDevServerCleanup,\n        resetFetch,\n      })\n    )\n\n    devBundlerService = new DevBundlerService(\n      developmentBundler,\n      // The request handler is assigned below, this allows us to create a lazy\n      // reference to it.\n      (req, res) => {\n        return requestHandlers[opts.dir](req, res)\n      }\n    )\n  }\n\n  renderServer.instance =\n    require('./render-server') as typeof import('./render-server')\n\n  const requestHandlerImpl: WorkerRequestHandler = async (req, res) => {\n    addRequestMeta(req, 'relativeProjectDir', relativeProjectDir)\n\n    // internal headers should not be honored by the request handler\n    if (!process.env.NEXT_PRIVATE_TEST_HEADERS) {\n      filterInternalHeaders(req.headers)\n    }\n\n    if (\n      !opts.minimalMode &&\n      config.i18n &&\n      config.i18n.localeDetection !== false\n    ) {\n      const urlParts = (req.url || '').split('?', 1)\n      let urlNoQuery = urlParts[0] || ''\n\n      if (config.basePath) {\n        urlNoQuery = removePathPrefix(urlNoQuery, config.basePath)\n      }\n\n      const pathnameInfo = getNextPathnameInfo(urlNoQuery, {\n        nextConfig: config,\n      })\n\n      const domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname({ hostname: urlNoQuery }, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      const { getLocaleRedirect } =\n        require('../../shared/lib/i18n/get-locale-redirect') as typeof import('../../shared/lib/i18n/get-locale-redirect')\n\n      const parsedUrl = parseUrlUtil((req.url || '')?.replace(/^\\/+/, '/'))\n\n      const redirect = getLocaleRedirect({\n        defaultLocale,\n        domainLocale,\n        headers: req.headers,\n        nextConfig: config,\n        pathLocale: pathnameInfo.locale,\n        urlParsed: {\n          ...parsedUrl,\n          pathname: pathnameInfo.locale\n            ? `/${pathnameInfo.locale}${urlNoQuery}`\n            : urlNoQuery,\n        },\n      })\n\n      if (redirect) {\n        res.setHeader('Location', redirect)\n        res.statusCode = RedirectStatusCode.TemporaryRedirect\n        res.end(redirect)\n        return\n      }\n    }\n\n    if (compress) {\n      // @ts-expect-error not express req/res\n      compress(req, res, () => {})\n    }\n    req.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n    res.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n\n    const invokedOutputs = new Set<string>()\n\n    async function invokeRender(\n      parsedUrl: NextUrlWithParsedQuery,\n      invokePath: string,\n      handleIndex: number,\n      additionalRequestMeta?: RequestMeta\n    ) {\n      // invokeRender expects /api routes to not be locale prefixed\n      // so normalize here before continuing\n      if (\n        config.i18n &&\n        removePathPrefix(invokePath, config.basePath).startsWith(\n          `/${getRequestMeta(req, 'locale')}/api`\n        )\n      ) {\n        invokePath = fsChecker.handleLocale(\n          removePathPrefix(invokePath, config.basePath)\n        ).pathname\n      }\n\n      if (\n        req.headers['x-nextjs-data'] &&\n        fsChecker.getMiddlewareMatchers()?.length &&\n        removePathPrefix(invokePath, config.basePath) === '/404'\n      ) {\n        res.setHeader('x-nextjs-matched-path', parsedUrl.pathname || '')\n        res.statusCode = 404\n        res.setHeader('content-type', 'application/json')\n        res.end('{}')\n        return null\n      }\n\n      if (!handlers) {\n        throw new Error('Failed to initialize render server')\n      }\n\n      addRequestMeta(req, 'invokePath', invokePath)\n      addRequestMeta(req, 'invokeQuery', parsedUrl.query)\n      addRequestMeta(req, 'middlewareInvoke', false)\n\n      for (const key in additionalRequestMeta || {}) {\n        addRequestMeta(\n          req,\n          key as keyof RequestMeta,\n          additionalRequestMeta![key as keyof RequestMeta]\n        )\n      }\n\n      debug('invokeRender', req.url, req.headers)\n\n      try {\n        const initResult =\n          await renderServer?.instance?.initialize(renderServerOpts)\n        try {\n          await initResult?.requestHandler(req, res)\n        } catch (err) {\n          if (err instanceof NoFallbackError) {\n            // eslint-disable-next-line\n            await handleRequest(handleIndex + 1)\n            return\n          }\n          throw err\n        }\n        return\n      } catch (e) {\n        // If the client aborts before we can receive a response object (when\n        // the headers are flushed), then we can early exit without further\n        // processing.\n        if (isAbortError(e)) {\n          return\n        }\n        throw e\n      }\n    }\n\n    const handleRequest = async (handleIndex: number) => {\n      if (handleIndex > 5) {\n        throw new Error(`Attempted to handle request too many times ${req.url}`)\n      }\n\n      // handle hot-reloader first\n      if (developmentBundler) {\n        if (blockCrossSite(req, res, config.allowedDevOrigins, opts.hostname)) {\n          return\n        }\n\n        const origUrl = req.url || '/'\n\n        // both the basePath and assetPrefix need to be stripped from the URL\n        // so that the development bundler can find the correct file\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        } else if (\n          config.assetPrefix &&\n          pathHasPrefix(origUrl, config.assetPrefix)\n        ) {\n          req.url = removePathPrefix(origUrl, config.assetPrefix)\n        }\n\n        const parsedUrl = url.parse(req.url || '/')\n\n        const hotReloaderResult = await developmentBundler.hotReloader.run(\n          req,\n          res,\n          parsedUrl\n        )\n\n        if (hotReloaderResult.finished) {\n          return hotReloaderResult\n        }\n\n        req.url = origUrl\n      }\n\n      const {\n        finished,\n        parsedUrl,\n        statusCode,\n        resHeaders,\n        bodyStream,\n        matchedOutput,\n      } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: false,\n        signal: signalFromNodeResponse(res),\n        invokedOutputs,\n      })\n\n      if (res.closed || res.finished) {\n        return\n      }\n\n      if (developmentBundler && matchedOutput?.type === 'devVirtualFsItem') {\n        const origUrl = req.url || '/'\n\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        } else if (\n          config.assetPrefix &&\n          pathHasPrefix(origUrl, config.assetPrefix)\n        ) {\n          req.url = removePathPrefix(origUrl, config.assetPrefix)\n        }\n\n        if (resHeaders) {\n          for (const key of Object.keys(resHeaders)) {\n            res.setHeader(key, resHeaders[key])\n          }\n        }\n        const result = await developmentBundler.requestHandler(req, res)\n\n        if (result.finished) {\n          return\n        }\n        // TODO: throw invariant if we resolved to this but it wasn't handled?\n        req.url = origUrl\n      }\n\n      debug('requestHandler!', req.url, {\n        matchedOutput,\n        statusCode,\n        resHeaders,\n        bodyStream: !!bodyStream,\n        parsedUrl: {\n          pathname: parsedUrl.pathname,\n          query: parsedUrl.query,\n        },\n        finished,\n      })\n\n      // apply any response headers from routing\n      for (const key of Object.keys(resHeaders || {})) {\n        res.setHeader(key, resHeaders[key])\n      }\n\n      // handle redirect\n      if (!bodyStream && statusCode && statusCode > 300 && statusCode < 400) {\n        const destination = url.format(parsedUrl)\n        res.statusCode = statusCode\n        res.setHeader('location', destination)\n\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n          res.setHeader('Refresh', `0;url=${destination}`)\n        }\n        return res.end(destination)\n      }\n\n      // handle middleware body response\n      if (bodyStream) {\n        res.statusCode = statusCode || 200\n        return await pipeToNodeResponse(bodyStream, res)\n      }\n\n      if (finished && parsedUrl.protocol) {\n        return await proxyRequest(\n          req,\n          res,\n          parsedUrl,\n          undefined,\n          getRequestMeta(req, 'clonableBody')?.cloneBodyStream(),\n          config.experimental.proxyTimeout\n        )\n      }\n\n      if (matchedOutput?.fsPath && matchedOutput.itemPath) {\n        if (\n          opts.dev &&\n          (fsChecker.appFiles.has(matchedOutput.itemPath) ||\n            fsChecker.pageFiles.has(matchedOutput.itemPath))\n        ) {\n          res.statusCode = 500\n          const message = `A conflicting public file and page file was found for path ${matchedOutput.itemPath} https://nextjs.org/docs/messages/conflicting-public-file-page`\n          await invokeRender(parsedUrl, '/_error', handleIndex, {\n            invokeStatus: 500,\n            invokeError: new Error(message),\n          })\n          Log.error(message)\n          return\n        }\n\n        if (\n          !res.getHeader('cache-control') &&\n          matchedOutput.type === 'nextStaticFolder'\n        ) {\n          if (opts.dev && !isNextFont(parsedUrl.pathname)) {\n            res.setHeader('Cache-Control', 'no-store, must-revalidate')\n          } else {\n            res.setHeader(\n              'Cache-Control',\n              'public, max-age=31536000, immutable'\n            )\n          }\n        }\n        if (!(req.method === 'GET' || req.method === 'HEAD')) {\n          res.setHeader('Allow', ['GET', 'HEAD'])\n          res.statusCode = 405\n          return await invokeRender(\n            url.parse('/405', true),\n            '/405',\n            handleIndex,\n            {\n              invokeStatus: 405,\n            }\n          )\n        }\n\n        try {\n          return await serveStatic(req, res, matchedOutput.itemPath, {\n            root: matchedOutput.itemsRoot,\n            // Ensures that etags are not generated for static files when disabled.\n            etag: config.generateEtags,\n          })\n        } catch (err: any) {\n          /**\n           * Hardcoded every possible error status code that could be thrown by \"serveStatic\" method\n           * This is done by searching \"this.error\" inside \"send\" module's source code:\n           * https://github.com/pillarjs/send/blob/master/index.js\n           * https://github.com/pillarjs/send/blob/develop/index.js\n           */\n          const POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC = new Set([\n            // send module will throw 500 when header is already sent or fs.stat error happens\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L392\n            // Note: we will use Next.js built-in 500 page to handle 500 errors\n            // 500,\n\n            // send module will throw 404 when file is missing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L421\n            // Note: we will use Next.js built-in 404 page to handle 404 errors\n            // 404,\n\n            // send module will throw 403 when redirecting to a directory without enabling directory listing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L484\n            // Note: Next.js throws a different error (without status code) for directory listing\n            // 403,\n\n            // send module will throw 400 when fails to normalize the path\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L520\n            400,\n\n            // send module will throw 412 with conditional GET request\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L632\n            412,\n\n            // send module will throw 416 when range is not satisfiable\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L669\n            416,\n          ])\n\n          let validErrorStatus = POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC.has(\n            err.statusCode\n          )\n\n          // normalize non-allowed status codes\n          if (!validErrorStatus) {\n            ;(err as any).statusCode = 400\n          }\n\n          if (typeof err.statusCode === 'number') {\n            const invokePath = `/${err.statusCode}`\n            const invokeStatus = err.statusCode\n            res.statusCode = err.statusCode\n            return await invokeRender(\n              url.parse(invokePath, true),\n              invokePath,\n              handleIndex,\n              {\n                invokeStatus,\n              }\n            )\n          }\n          throw err\n        }\n      }\n\n      if (matchedOutput) {\n        invokedOutputs.add(matchedOutput.itemPath)\n\n        return await invokeRender(\n          parsedUrl,\n          parsedUrl.pathname || '/',\n          handleIndex,\n          {\n            invokeOutput: matchedOutput.itemPath,\n          }\n        )\n      }\n\n      if (opts.dev && isChromeDevtoolsWorkspaceUrl(parsedUrl)) {\n        await handleChromeDevtoolsWorkspaceRequest(res, opts, config)\n        return\n      }\n\n      // 404 case\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n\n      let realRequestPathname = parsedUrl.pathname ?? ''\n      if (realRequestPathname) {\n        if (config.basePath) {\n          realRequestPathname = removePathPrefix(\n            realRequestPathname,\n            config.basePath\n          )\n        }\n        if (config.assetPrefix) {\n          realRequestPathname = removePathPrefix(\n            realRequestPathname,\n            config.assetPrefix\n          )\n        }\n        if (config.i18n) {\n          realRequestPathname = removePathPrefix(\n            realRequestPathname,\n            '/' + (getRequestMeta(req, 'locale') ?? '')\n          )\n        }\n      }\n      // For not found static assets, return plain text 404 instead of\n      // full HTML 404 pages to save bandwidth.\n      if (realRequestPathname.startsWith('/_next/static/')) {\n        res.statusCode = 404\n        res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n        res.end('Not Found')\n        return null\n      }\n\n      // Short-circuit favicon.ico serving so that the 404 page doesn't get built as favicon is requested by the browser when loading any route.\n      if (opts.dev && !matchedOutput && parsedUrl.pathname === '/favicon.ico') {\n        res.statusCode = 404\n        res.end('')\n        return null\n      }\n\n      const appNotFound = opts.dev\n        ? developmentBundler?.serverFields.hasAppNotFound\n        : await fsChecker.getItem(UNDERSCORE_NOT_FOUND_ROUTE)\n\n      res.statusCode = 404\n\n      if (appNotFound) {\n        return await invokeRender(\n          parsedUrl,\n          UNDERSCORE_NOT_FOUND_ROUTE,\n          handleIndex,\n          {\n            invokeStatus: 404,\n          }\n        )\n      }\n\n      await invokeRender(parsedUrl, '/404', handleIndex, {\n        invokeStatus: 404,\n      })\n    }\n\n    try {\n      await handleRequest(0)\n    } catch (err) {\n      try {\n        let invokePath = '/500'\n        let invokeStatus = '500'\n\n        if (err instanceof DecodeError) {\n          invokePath = '/400'\n          invokeStatus = '400'\n        } else {\n          console.error(err)\n        }\n        res.statusCode = Number(invokeStatus)\n        return await invokeRender(url.parse(invokePath, true), invokePath, 0, {\n          invokeStatus: res.statusCode,\n        })\n      } catch (err2) {\n        console.error(err2)\n      }\n      res.statusCode = 500\n      res.end('Internal Server Error')\n    }\n  }\n\n  let requestHandler: WorkerRequestHandler = requestHandlerImpl\n  if (config.experimental.testProxy) {\n    // Intercept fetch and other testmode apis.\n    const { wrapRequestHandlerWorker, interceptTestApis } =\n      // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n      require('next/dist/experimental/testmode/server') as typeof import('../../experimental/testmode/server')\n    requestHandler = wrapRequestHandlerWorker(requestHandler)\n    interceptTestApis()\n    // We treat the intercepted fetch as \"original\" fetch that should be reset to during HMR.\n    originalFetch = globalThis.fetch\n  }\n  requestHandlers[opts.dir] = requestHandler\n\n  const renderServerOpts: Parameters<RenderServer['initialize']>[0] = {\n    port: opts.port,\n    dir: opts.dir,\n    hostname: opts.hostname,\n    minimalMode: opts.minimalMode,\n    dev: !!opts.dev,\n    server: opts.server,\n    serverFields: {\n      ...(developmentBundler?.serverFields || {}),\n      setIsrStatus: devBundlerService?.setIsrStatus.bind(devBundlerService),\n    } satisfies ServerFields,\n    experimentalTestProxy: !!config.experimental.testProxy,\n    experimentalHttpsServer: !!opts.experimentalHttpsServer,\n    bundlerService: devBundlerService,\n    startServerSpan: opts.startServerSpan,\n    quiet: opts.quiet,\n    onDevServerCleanup: opts.onDevServerCleanup,\n  }\n  renderServerOpts.serverFields.routerServerHandler = requestHandlerImpl\n\n  // pre-initialize workers\n  const handlers = await renderServer.instance.initialize(renderServerOpts)\n\n  // this must come after initialize of render server since it's\n  // using initialized methods\n  if (!routerServerGlobal[RouterServerContextSymbol]) {\n    routerServerGlobal[RouterServerContextSymbol] = {}\n  }\n  const relativeProjectDir = path.relative(process.cwd(), opts.dir)\n\n  routerServerGlobal[RouterServerContextSymbol][relativeProjectDir] = {\n    nextConfig: config,\n    hostname: handlers.server.hostname,\n    revalidate: handlers.server.revalidate.bind(handlers.server),\n    render404: handlers.server.render404.bind(handlers.server),\n    experimentalTestProxy: renderServerOpts.experimentalTestProxy,\n    logErrorWithOriginalStack: opts.dev\n      ? handlers.server.logErrorWithOriginalStack.bind(handlers.server)\n      : (err: unknown) => !opts.quiet && Log.error(err),\n    setIsrStatus: devBundlerService?.setIsrStatus.bind(devBundlerService),\n  }\n\n  const logError = async (\n    type: 'uncaughtException' | 'unhandledRejection',\n    err: Error | undefined\n  ) => {\n    if (isPostpone(err)) {\n      // React postpones that are unhandled might end up logged here but they're\n      // not really errors. They're just part of rendering.\n      return\n    }\n    if (type === 'unhandledRejection') {\n      Log.error('unhandledRejection: ', err)\n    } else if (type === 'uncaughtException') {\n      Log.error('uncaughtException: ', err)\n    }\n  }\n\n  process.on('uncaughtException', logError.bind(null, 'uncaughtException'))\n  process.on('unhandledRejection', logError.bind(null, 'unhandledRejection'))\n\n  const resolveRoutes = getResolveRoutes(\n    fsChecker,\n    config,\n    opts,\n    renderServer.instance,\n    renderServerOpts,\n    developmentBundler?.ensureMiddleware\n  )\n\n  const upgradeHandler: WorkerUpgradeHandler = async (req, socket, head) => {\n    try {\n      req.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n      socket.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n\n      if (opts.dev && developmentBundler && req.url) {\n        if (\n          blockCrossSite(req, socket, config.allowedDevOrigins, opts.hostname)\n        ) {\n          return\n        }\n        const { basePath, assetPrefix } = config\n\n        let hmrPrefix = basePath\n\n        // assetPrefix overrides basePath for HMR path\n        if (assetPrefix) {\n          hmrPrefix = normalizedAssetPrefix(assetPrefix)\n\n          if (URL.canParse(hmrPrefix)) {\n            // remove trailing slash from pathname\n            // return empty string if pathname is '/'\n            // to avoid conflicts with '/_next' below\n            hmrPrefix = new URL(hmrPrefix).pathname.replace(/\\/$/, '')\n          }\n        }\n\n        const isHMRRequest = req.url.startsWith(\n          ensureLeadingSlash(`${hmrPrefix}/_next/webpack-hmr`)\n        )\n\n        // only handle HMR requests if the basePath in the request\n        // matches the basePath for the handler responding to the request\n        if (isHMRRequest) {\n          return developmentBundler.hotReloader.onHMR(\n            req,\n            socket,\n            head,\n            (client) => {\n              client.send(\n                JSON.stringify({\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST,\n                  data: devBundlerService?.appIsrManifest || {},\n                } satisfies AppIsrManifestAction)\n              )\n            }\n          )\n        }\n      }\n\n      const res = new MockedResponse({\n        resWriter: () => {\n          throw new Error(\n            'Invariant: did not expect response writer to be written to for upgrade request'\n          )\n        },\n      })\n      const { matchedOutput, parsedUrl } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: true,\n        signal: signalFromNodeResponse(socket),\n      })\n\n      // TODO: allow upgrade requests to pages/app paths?\n      // this was not previously supported\n      if (matchedOutput) {\n        return socket.end()\n      }\n\n      if (parsedUrl.protocol) {\n        return await proxyRequest(req, socket, parsedUrl, head)\n      }\n\n      // If there's no matched output, we don't handle the request as user's\n      // custom WS server may be listening on the same path.\n    } catch (err) {\n      console.error('Error handling upgrade request', err)\n      socket.end()\n    }\n  }\n\n  return {\n    requestHandler,\n    upgradeHandler,\n    server: handlers.server,\n    closeUpgraded() {\n      developmentBundler?.hotReloader?.close()\n    },\n  }\n}\n"], "names": ["initialize", "debug", "setupDebug", "isNextFont", "pathname", "test", "requestHandlers", "opts", "process", "env", "NODE_ENV", "dev", "config", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_SERVER", "dir", "silent", "compress", "setupCompression", "fs<PERSON><PERSON><PERSON>", "setupFsCheck", "minimalMode", "renderServer", "developmentBundler", "devBundlerService", "originalFetch", "globalThis", "fetch", "Telemetry", "require", "telemetry", "distDir", "path", "join", "traceGlobals", "set", "pagesDir", "appDir", "findPagesDir", "setupDevBundler", "resetFetch", "NEXT_PATCH_SYMBOL", "setupDevBundlerSpan", "startServerSpan", "<PERSON><PERSON><PERSON><PERSON>", "trace", "traceAsyncFn", "nextConfig", "isCustomServer", "customServer", "turbo", "TURBOPACK", "port", "onDevServerCleanup", "DevBundlerService", "req", "res", "instance", "requestHandlerImpl", "addRequestMeta", "relativeProjectDir", "NEXT_PRIVATE_TEST_HEADERS", "filterInternalHeaders", "headers", "i18n", "localeDetection", "urlParts", "url", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basePath", "removePathPrefix", "pathnameInfo", "getNextPathnameInfo", "domainLocale", "detectDomainLocale", "domains", "getHostname", "hostname", "defaultLocale", "getLocaleRedirect", "parsedUrl", "parseUrlUtil", "replace", "redirect", "pathLocale", "locale", "urlParsed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "end", "on", "_err", "invokedOutputs", "Set", "invokeRender", "invoke<PERSON><PERSON>", "handleIndex", "additionalRequestMeta", "startsWith", "getRequestMeta", "handleLocale", "getMiddlewareMatchers", "length", "handlers", "Error", "query", "key", "initResult", "renderServerOpts", "requestHandler", "err", "NoFallbackError", "handleRequest", "e", "isAbortError", "blockCrossSite", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "origUrl", "pathHasPrefix", "assetPrefix", "parse", "hotReloaderResult", "hotReloader", "run", "finished", "resHeaders", "bodyStream", "matchedOutput", "resolveRoutes", "isUpgradeReq", "signal", "signalFromNodeResponse", "closed", "type", "Object", "keys", "result", "destination", "format", "PermanentRedirect", "pipeToNodeResponse", "protocol", "proxyRequest", "undefined", "cloneBodyStream", "experimental", "proxyTimeout", "fsPath", "itemPath", "appFiles", "has", "pageFiles", "message", "invoke<PERSON>tatus", "invokeError", "Log", "error", "<PERSON><PERSON><PERSON><PERSON>", "method", "serveStatic", "root", "itemsRoot", "etag", "generateEtags", "POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC", "validErrorStatus", "add", "invokeOutput", "isChromeDevtoolsWorkspaceUrl", "handleChromeDevtoolsWorkspaceRequest", "realRequestPathname", "appNotFound", "serverFields", "hasAppNotFound", "getItem", "UNDERSCORE_NOT_FOUND_ROUTE", "DecodeError", "console", "Number", "err2", "testProxy", "wrapRequestHandlerWorker", "interceptTestApis", "server", "setIsrStatus", "bind", "experimentalTestProxy", "experimentalHttpsServer", "bundlerService", "quiet", "routerServerHandler", "routerServerGlobal", "RouterServerContextSymbol", "relative", "cwd", "revalidate", "render404", "logErrorWithOriginalStack", "logError", "isPostpone", "getResolveRoutes", "ensureMiddleware", "upgradeHandler", "socket", "head", "hmrPrefix", "normalizedAssetPrefix", "URL", "canParse", "isHMRRequest", "ensureLeadingSlash", "onHMR", "client", "send", "JSON", "stringify", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "data", "appIsrManifest", "MockedResponse", "resWriter", "closeUpgraded", "close"], "mappings": "AAAA,oDAAoD;;;;;+BAgF9BA;;;eAAAA;;;QA1Ef;QACA;4DAES;6DACC;+DACM;6BACK;8DACL;6DACF;uBACO;8BACC;4BACA;8BACA;8BACoB;+BAChB;6BACc;+BACjB;kCACG;oEACJ;6BACU;4BACZ;0BACc;2BAMlC;oCAC4B;mCACD;uBACD;oCACE;qCACC;6BACR;oCACO;6BACJ;kCAIxB;uCAC+B;4BACJ;wBAEI;gCACP;wBACF;yCACG;qCAIzB;yCAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AACzB,MAAMC,aAAa,CAACC,WAClBA,YAAY,4CAA4CC,IAAI,CAACD;AAc/D,MAAME,kBAAwD,CAAC;AAExD,eAAeN,WAAWO,IAahC;IACC,IAAI,CAACC,QAAQC,GAAG,CAACC,QAAQ,EAAE;QACzB,0BAA0B;QAC1BF,QAAQC,GAAG,CAACC,QAAQ,GAAGH,KAAKI,GAAG,GAAG,gBAAgB;IACpD;IAEA,MAAMC,SAAS,MAAMC,IAAAA,eAAU,EAC7BN,KAAKI,GAAG,GAAGG,mCAAwB,GAAGC,kCAAuB,EAC7DR,KAAKS,GAAG,EACR;QAAEC,QAAQ;IAAM;IAGlB,IAAIC;IAEJ,IAAIN,CAAAA,0BAAAA,OAAQM,QAAQ,MAAK,OAAO;QAC9BA,WAAWC,IAAAA,oBAAgB;IAC7B;IAEA,MAAMC,YAAY,MAAMC,IAAAA,wBAAY,EAAC;QACnCV,KAAKJ,KAAKI,GAAG;QACbK,KAAKT,KAAKS,GAAG;QACbJ;QACAU,aAAaf,KAAKe,WAAW;IAC/B;IAEA,MAAMC,eAAyC,CAAC;IAEhD,IAAIC;IAEJ,IAAIC;IAEJ,IAAIC,gBAAgBC,WAAWC,KAAK;IAEpC,IAAIrB,KAAKI,GAAG,EAAE;QACZ,MAAM,EAAEkB,SAAS,EAAE,GACjBC,QAAQ;QAEV,MAAMC,YAAY,IAAIF,UAAU;YAC9BG,SAASC,aAAI,CAACC,IAAI,CAAC3B,KAAKS,GAAG,EAAEJ,OAAOoB,OAAO;QAC7C;QACAG,oBAAY,CAACC,GAAG,CAAC,aAAaL;QAE9B,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAChC,KAAKS,GAAG;QAElD,MAAM,EAAEwB,eAAe,EAAE,GACvBV,QAAQ;QAEV,MAAMW,aAAa;YACjBd,WAAWC,KAAK,GAAGF;YACjBC,UAAsC,CAACe,6BAAiB,CAAC,GAAG;QAChE;QAEA,MAAMC,sBAAsBpC,KAAKqC,eAAe,GAC5CrC,KAAKqC,eAAe,CAACC,UAAU,CAAC,uBAChCC,IAAAA,YAAK,EAAC;QACVtB,qBAAqB,MAAMmB,oBAAoBI,YAAY,CAAC,IAC1DP,gBAAgB;gBACd,6HAA6H;gBAC7HjB;gBACAe;gBACAD;gBACAN;gBACAX;gBACAJ,KAAKT,KAAKS,GAAG;gBACbgC,YAAYpC;gBACZqC,gBAAgB1C,KAAK2C,YAAY;gBACjCC,OAAO,CAAC,CAAC3C,QAAQC,GAAG,CAAC2C,SAAS;gBAC9BC,MAAM9C,KAAK8C,IAAI;gBACfC,oBAAoB/C,KAAK+C,kBAAkB;gBAC3Cb;YACF;QAGFhB,oBAAoB,IAAI8B,oCAAiB,CACvC/B,oBACA,yEAAyE;QACzE,mBAAmB;QACnB,CAACgC,KAAKC;YACJ,OAAOnD,eAAe,CAACC,KAAKS,GAAG,CAAC,CAACwC,KAAKC;QACxC;IAEJ;IAEAlC,aAAamC,QAAQ,GACnB5B,QAAQ;IAEV,MAAM6B,qBAA2C,OAAOH,KAAKC;QAC3DG,IAAAA,2BAAc,EAACJ,KAAK,sBAAsBK;QAE1C,gEAAgE;QAChE,IAAI,CAACrD,QAAQC,GAAG,CAACqD,yBAAyB,EAAE;YAC1CC,IAAAA,6BAAqB,EAACP,IAAIQ,OAAO;QACnC;QAEA,IACE,CAACzD,KAAKe,WAAW,IACjBV,OAAOqD,IAAI,IACXrD,OAAOqD,IAAI,CAACC,eAAe,KAAK,OAChC;gBAuBgCV;YAtBhC,MAAMW,WAAW,AAACX,CAAAA,IAAIY,GAAG,IAAI,EAAC,EAAGC,KAAK,CAAC,KAAK;YAC5C,IAAIC,aAAaH,QAAQ,CAAC,EAAE,IAAI;YAEhC,IAAIvD,OAAO2D,QAAQ,EAAE;gBACnBD,aAAaE,IAAAA,kCAAgB,EAACF,YAAY1D,OAAO2D,QAAQ;YAC3D;YAEA,MAAME,eAAeC,IAAAA,wCAAmB,EAACJ,YAAY;gBACnDtB,YAAYpC;YACd;YAEA,MAAM+D,eAAeC,IAAAA,sCAAkB,EACrChE,OAAOqD,IAAI,CAACY,OAAO,EACnBC,IAAAA,wBAAW,EAAC;gBAAEC,UAAUT;YAAW,GAAGd,IAAIQ,OAAO;YAGnD,MAAMgB,gBACJL,CAAAA,gCAAAA,aAAcK,aAAa,KAAIpE,OAAOqD,IAAI,CAACe,aAAa;YAE1D,MAAM,EAAEC,iBAAiB,EAAE,GACzBnD,QAAQ;YAEV,MAAMoD,YAAYC,IAAAA,kBAAY,GAAE3B,QAAAA,IAAIY,GAAG,IAAI,uBAAZ,AAACZ,MAAgB4B,OAAO,CAAC,QAAQ;YAEhE,MAAMC,WAAWJ,kBAAkB;gBACjCD;gBACAL;gBACAX,SAASR,IAAIQ,OAAO;gBACpBhB,YAAYpC;gBACZ0E,YAAYb,aAAac,MAAM;gBAC/BC,WAAW;oBACT,GAAGN,SAAS;oBACZ9E,UAAUqE,aAAac,MAAM,GACzB,CAAC,CAAC,EAAEd,aAAac,MAAM,GAAGjB,YAAY,GACtCA;gBACN;YACF;YAEA,IAAIe,UAAU;gBACZ5B,IAAIgC,SAAS,CAAC,YAAYJ;gBAC1B5B,IAAIiC,UAAU,GAAGC,sCAAkB,CAACC,iBAAiB;gBACrDnC,IAAIoC,GAAG,CAACR;gBACR;YACF;QACF;QAEA,IAAInE,UAAU;YACZ,uCAAuC;YACvCA,SAASsC,KAAKC,KAAK,KAAO;QAC5B;QACAD,IAAIsC,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QACAtC,IAAIqC,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QAEA,MAAMC,iBAAiB,IAAIC;QAE3B,eAAeC,aACbhB,SAAiC,EACjCiB,UAAkB,EAClBC,WAAmB,EACnBC,qBAAmC;gBAiBjCjF;YAfF,6DAA6D;YAC7D,sCAAsC;YACtC,IACER,OAAOqD,IAAI,IACXO,IAAAA,kCAAgB,EAAC2B,YAAYvF,OAAO2D,QAAQ,EAAE+B,UAAU,CACtD,CAAC,CAAC,EAAEC,IAAAA,2BAAc,EAAC/C,KAAK,UAAU,IAAI,CAAC,GAEzC;gBACA2C,aAAa/E,UAAUoF,YAAY,CACjChC,IAAAA,kCAAgB,EAAC2B,YAAYvF,OAAO2D,QAAQ,GAC5CnE,QAAQ;YACZ;YAEA,IACEoD,IAAIQ,OAAO,CAAC,gBAAgB,MAC5B5C,mCAAAA,UAAUqF,qBAAqB,uBAA/BrF,iCAAmCsF,MAAM,KACzClC,IAAAA,kCAAgB,EAAC2B,YAAYvF,OAAO2D,QAAQ,MAAM,QAClD;gBACAd,IAAIgC,SAAS,CAAC,yBAAyBP,UAAU9E,QAAQ,IAAI;gBAC7DqD,IAAIiC,UAAU,GAAG;gBACjBjC,IAAIgC,SAAS,CAAC,gBAAgB;gBAC9BhC,IAAIoC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,IAAI,CAACc,UAAU;gBACb,MAAM,qBAA+C,CAA/C,IAAIC,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEAhD,IAAAA,2BAAc,EAACJ,KAAK,cAAc2C;YAClCvC,IAAAA,2BAAc,EAACJ,KAAK,eAAe0B,UAAU2B,KAAK;YAClDjD,IAAAA,2BAAc,EAACJ,KAAK,oBAAoB;YAExC,IAAK,MAAMsD,OAAOT,yBAAyB,CAAC,EAAG;gBAC7CzC,IAAAA,2BAAc,EACZJ,KACAsD,KACAT,qBAAsB,CAACS,IAAyB;YAEpD;YAEA7G,MAAM,gBAAgBuD,IAAIY,GAAG,EAAEZ,IAAIQ,OAAO;YAE1C,IAAI;oBAEMzC;gBADR,MAAMwF,aACJ,OAAMxF,iCAAAA,yBAAAA,aAAcmC,QAAQ,qBAAtBnC,uBAAwBvB,UAAU,CAACgH;gBAC3C,IAAI;oBACF,OAAMD,8BAAAA,WAAYE,cAAc,CAACzD,KAAKC;gBACxC,EAAE,OAAOyD,KAAK;oBACZ,IAAIA,eAAeC,wCAAe,EAAE;wBAClC,2BAA2B;wBAC3B,MAAMC,cAAchB,cAAc;wBAClC;oBACF;oBACA,MAAMc;gBACR;gBACA;YACF,EAAE,OAAOG,GAAG;gBACV,qEAAqE;gBACrE,mEAAmE;gBACnE,cAAc;gBACd,IAAIC,IAAAA,0BAAY,EAACD,IAAI;oBACnB;gBACF;gBACA,MAAMA;YACR;QACF;QAEA,MAAMD,gBAAgB,OAAOhB;YAC3B,IAAIA,cAAc,GAAG;gBACnB,MAAM,qBAAkE,CAAlE,IAAIQ,MAAM,CAAC,2CAA2C,EAAEpD,IAAIY,GAAG,EAAE,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;YAEA,4BAA4B;YAC5B,IAAI5C,oBAAoB;gBACtB,IAAI+F,IAAAA,8BAAc,EAAC/D,KAAKC,KAAK7C,OAAO4G,iBAAiB,EAAEjH,KAAKwE,QAAQ,GAAG;oBACrE;gBACF;gBAEA,MAAM0C,UAAUjE,IAAIY,GAAG,IAAI;gBAE3B,qEAAqE;gBACrE,4DAA4D;gBAC5D,IAAIxD,OAAO2D,QAAQ,IAAImD,IAAAA,4BAAa,EAACD,SAAS7G,OAAO2D,QAAQ,GAAG;oBAC9Df,IAAIY,GAAG,GAAGI,IAAAA,kCAAgB,EAACiD,SAAS7G,OAAO2D,QAAQ;gBACrD,OAAO,IACL3D,OAAO+G,WAAW,IAClBD,IAAAA,4BAAa,EAACD,SAAS7G,OAAO+G,WAAW,GACzC;oBACAnE,IAAIY,GAAG,GAAGI,IAAAA,kCAAgB,EAACiD,SAAS7G,OAAO+G,WAAW;gBACxD;gBAEA,MAAMzC,YAAYd,YAAG,CAACwD,KAAK,CAACpE,IAAIY,GAAG,IAAI;gBAEvC,MAAMyD,oBAAoB,MAAMrG,mBAAmBsG,WAAW,CAACC,GAAG,CAChEvE,KACAC,KACAyB;gBAGF,IAAI2C,kBAAkBG,QAAQ,EAAE;oBAC9B,OAAOH;gBACT;gBAEArE,IAAIY,GAAG,GAAGqD;YACZ;YAEA,MAAM,EACJO,QAAQ,EACR9C,SAAS,EACTQ,UAAU,EACVuC,UAAU,EACVC,UAAU,EACVC,aAAa,EACd,GAAG,MAAMC,cAAc;gBACtB5E;gBACAC;gBACA4E,cAAc;gBACdC,QAAQC,IAAAA,mCAAsB,EAAC9E;gBAC/BuC;YACF;YAEA,IAAIvC,IAAI+E,MAAM,IAAI/E,IAAIuE,QAAQ,EAAE;gBAC9B;YACF;YAEA,IAAIxG,sBAAsB2G,CAAAA,iCAAAA,cAAeM,IAAI,MAAK,oBAAoB;gBACpE,MAAMhB,UAAUjE,IAAIY,GAAG,IAAI;gBAE3B,IAAIxD,OAAO2D,QAAQ,IAAImD,IAAAA,4BAAa,EAACD,SAAS7G,OAAO2D,QAAQ,GAAG;oBAC9Df,IAAIY,GAAG,GAAGI,IAAAA,kCAAgB,EAACiD,SAAS7G,OAAO2D,QAAQ;gBACrD,OAAO,IACL3D,OAAO+G,WAAW,IAClBD,IAAAA,4BAAa,EAACD,SAAS7G,OAAO+G,WAAW,GACzC;oBACAnE,IAAIY,GAAG,GAAGI,IAAAA,kCAAgB,EAACiD,SAAS7G,OAAO+G,WAAW;gBACxD;gBAEA,IAAIM,YAAY;oBACd,KAAK,MAAMnB,OAAO4B,OAAOC,IAAI,CAACV,YAAa;wBACzCxE,IAAIgC,SAAS,CAACqB,KAAKmB,UAAU,CAACnB,IAAI;oBACpC;gBACF;gBACA,MAAM8B,SAAS,MAAMpH,mBAAmByF,cAAc,CAACzD,KAAKC;gBAE5D,IAAImF,OAAOZ,QAAQ,EAAE;oBACnB;gBACF;gBACA,sEAAsE;gBACtExE,IAAIY,GAAG,GAAGqD;YACZ;YAEAxH,MAAM,mBAAmBuD,IAAIY,GAAG,EAAE;gBAChC+D;gBACAzC;gBACAuC;gBACAC,YAAY,CAAC,CAACA;gBACdhD,WAAW;oBACT9E,UAAU8E,UAAU9E,QAAQ;oBAC5ByG,OAAO3B,UAAU2B,KAAK;gBACxB;gBACAmB;YACF;YAEA,0CAA0C;YAC1C,KAAK,MAAMlB,OAAO4B,OAAOC,IAAI,CAACV,cAAc,CAAC,GAAI;gBAC/CxE,IAAIgC,SAAS,CAACqB,KAAKmB,UAAU,CAACnB,IAAI;YACpC;YAEA,kBAAkB;YAClB,IAAI,CAACoB,cAAcxC,cAAcA,aAAa,OAAOA,aAAa,KAAK;gBACrE,MAAMmD,cAAczE,YAAG,CAAC0E,MAAM,CAAC5D;gBAC/BzB,IAAIiC,UAAU,GAAGA;gBACjBjC,IAAIgC,SAAS,CAAC,YAAYoD;gBAE1B,IAAInD,eAAeC,sCAAkB,CAACoD,iBAAiB,EAAE;oBACvDtF,IAAIgC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEoD,aAAa;gBACjD;gBACA,OAAOpF,IAAIoC,GAAG,CAACgD;YACjB;YAEA,kCAAkC;YAClC,IAAIX,YAAY;gBACdzE,IAAIiC,UAAU,GAAGA,cAAc;gBAC/B,OAAO,MAAMsD,IAAAA,gCAAkB,EAACd,YAAYzE;YAC9C;YAEA,IAAIuE,YAAY9C,UAAU+D,QAAQ,EAAE;oBAMhC1C;gBALF,OAAO,MAAM2C,IAAAA,0BAAY,EACvB1F,KACAC,KACAyB,WACAiE,YACA5C,kBAAAA,IAAAA,2BAAc,EAAC/C,KAAK,oCAApB+C,gBAAqC6C,eAAe,IACpDxI,OAAOyI,YAAY,CAACC,YAAY;YAEpC;YAEA,IAAInB,CAAAA,iCAAAA,cAAeoB,MAAM,KAAIpB,cAAcqB,QAAQ,EAAE;gBACnD,IACEjJ,KAAKI,GAAG,IACPS,CAAAA,UAAUqI,QAAQ,CAACC,GAAG,CAACvB,cAAcqB,QAAQ,KAC5CpI,UAAUuI,SAAS,CAACD,GAAG,CAACvB,cAAcqB,QAAQ,CAAA,GAChD;oBACA/F,IAAIiC,UAAU,GAAG;oBACjB,MAAMkE,UAAU,CAAC,2DAA2D,EAAEzB,cAAcqB,QAAQ,CAAC,8DAA8D,CAAC;oBACpK,MAAMtD,aAAahB,WAAW,WAAWkB,aAAa;wBACpDyD,cAAc;wBACdC,aAAa,qBAAkB,CAAlB,IAAIlD,MAAMgD,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAChC;oBACAG,KAAIC,KAAK,CAACJ;oBACV;gBACF;gBAEA,IACE,CAACnG,IAAIwG,SAAS,CAAC,oBACf9B,cAAcM,IAAI,KAAK,oBACvB;oBACA,IAAIlI,KAAKI,GAAG,IAAI,CAACR,WAAW+E,UAAU9E,QAAQ,GAAG;wBAC/CqD,IAAIgC,SAAS,CAAC,iBAAiB;oBACjC,OAAO;wBACLhC,IAAIgC,SAAS,CACX,iBACA;oBAEJ;gBACF;gBACA,IAAI,CAAEjC,CAAAA,IAAI0G,MAAM,KAAK,SAAS1G,IAAI0G,MAAM,KAAK,MAAK,GAAI;oBACpDzG,IAAIgC,SAAS,CAAC,SAAS;wBAAC;wBAAO;qBAAO;oBACtChC,IAAIiC,UAAU,GAAG;oBACjB,OAAO,MAAMQ,aACX9B,YAAG,CAACwD,KAAK,CAAC,QAAQ,OAClB,QACAxB,aACA;wBACEyD,cAAc;oBAChB;gBAEJ;gBAEA,IAAI;oBACF,OAAO,MAAMM,IAAAA,wBAAW,EAAC3G,KAAKC,KAAK0E,cAAcqB,QAAQ,EAAE;wBACzDY,MAAMjC,cAAckC,SAAS;wBAC7B,uEAAuE;wBACvEC,MAAM1J,OAAO2J,aAAa;oBAC5B;gBACF,EAAE,OAAOrD,KAAU;oBACjB;;;;;WAKC,GACD,MAAMsD,wCAAwC,IAAIvE,IAAI;wBACpD,kFAAkF;wBAClF,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,kDAAkD;wBAClD,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,gGAAgG;wBAChG,+FAA+F;wBAC/F,qFAAqF;wBACrF,OAAO;wBAEP,8DAA8D;wBAC9D,+FAA+F;wBAC/F;wBAEA,0DAA0D;wBAC1D,+FAA+F;wBAC/F;wBAEA,2DAA2D;wBAC3D,+FAA+F;wBAC/F;qBACD;oBAED,IAAIwE,mBAAmBD,sCAAsCd,GAAG,CAC9DxC,IAAIxB,UAAU;oBAGhB,qCAAqC;oBACrC,IAAI,CAAC+E,kBAAkB;;wBACnBvD,IAAYxB,UAAU,GAAG;oBAC7B;oBAEA,IAAI,OAAOwB,IAAIxB,UAAU,KAAK,UAAU;wBACtC,MAAMS,aAAa,CAAC,CAAC,EAAEe,IAAIxB,UAAU,EAAE;wBACvC,MAAMmE,eAAe3C,IAAIxB,UAAU;wBACnCjC,IAAIiC,UAAU,GAAGwB,IAAIxB,UAAU;wBAC/B,OAAO,MAAMQ,aACX9B,YAAG,CAACwD,KAAK,CAACzB,YAAY,OACtBA,YACAC,aACA;4BACEyD;wBACF;oBAEJ;oBACA,MAAM3C;gBACR;YACF;YAEA,IAAIiB,eAAe;gBACjBnC,eAAe0E,GAAG,CAACvC,cAAcqB,QAAQ;gBAEzC,OAAO,MAAMtD,aACXhB,WACAA,UAAU9E,QAAQ,IAAI,KACtBgG,aACA;oBACEuE,cAAcxC,cAAcqB,QAAQ;gBACtC;YAEJ;YAEA,IAAIjJ,KAAKI,GAAG,IAAIiK,IAAAA,qDAA4B,EAAC1F,YAAY;gBACvD,MAAM2F,IAAAA,6DAAoC,EAACpH,KAAKlD,MAAMK;gBACtD;YACF;YAEA,WAAW;YACX6C,IAAIgC,SAAS,CACX,iBACA;YAGF,IAAIqF,sBAAsB5F,UAAU9E,QAAQ,IAAI;YAChD,IAAI0K,qBAAqB;gBACvB,IAAIlK,OAAO2D,QAAQ,EAAE;oBACnBuG,sBAAsBtG,IAAAA,kCAAgB,EACpCsG,qBACAlK,OAAO2D,QAAQ;gBAEnB;gBACA,IAAI3D,OAAO+G,WAAW,EAAE;oBACtBmD,sBAAsBtG,IAAAA,kCAAgB,EACpCsG,qBACAlK,OAAO+G,WAAW;gBAEtB;gBACA,IAAI/G,OAAOqD,IAAI,EAAE;oBACf6G,sBAAsBtG,IAAAA,kCAAgB,EACpCsG,qBACA,MAAOvE,CAAAA,IAAAA,2BAAc,EAAC/C,KAAK,aAAa,EAAC;gBAE7C;YACF;YACA,gEAAgE;YAChE,yCAAyC;YACzC,IAAIsH,oBAAoBxE,UAAU,CAAC,mBAAmB;gBACpD7C,IAAIiC,UAAU,GAAG;gBACjBjC,IAAIgC,SAAS,CAAC,gBAAgB;gBAC9BhC,IAAIoC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,0IAA0I;YAC1I,IAAItF,KAAKI,GAAG,IAAI,CAACwH,iBAAiBjD,UAAU9E,QAAQ,KAAK,gBAAgB;gBACvEqD,IAAIiC,UAAU,GAAG;gBACjBjC,IAAIoC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,MAAMkF,cAAcxK,KAAKI,GAAG,GACxBa,sCAAAA,mBAAoBwJ,YAAY,CAACC,cAAc,GAC/C,MAAM7J,UAAU8J,OAAO,CAACC,qCAA0B;YAEtD1H,IAAIiC,UAAU,GAAG;YAEjB,IAAIqF,aAAa;gBACf,OAAO,MAAM7E,aACXhB,WACAiG,qCAA0B,EAC1B/E,aACA;oBACEyD,cAAc;gBAChB;YAEJ;YAEA,MAAM3D,aAAahB,WAAW,QAAQkB,aAAa;gBACjDyD,cAAc;YAChB;QACF;QAEA,IAAI;YACF,MAAMzC,cAAc;QACtB,EAAE,OAAOF,KAAK;YACZ,IAAI;gBACF,IAAIf,aAAa;gBACjB,IAAI0D,eAAe;gBAEnB,IAAI3C,eAAekE,kBAAW,EAAE;oBAC9BjF,aAAa;oBACb0D,eAAe;gBACjB,OAAO;oBACLwB,QAAQrB,KAAK,CAAC9C;gBAChB;gBACAzD,IAAIiC,UAAU,GAAG4F,OAAOzB;gBACxB,OAAO,MAAM3D,aAAa9B,YAAG,CAACwD,KAAK,CAACzB,YAAY,OAAOA,YAAY,GAAG;oBACpE0D,cAAcpG,IAAIiC,UAAU;gBAC9B;YACF,EAAE,OAAO6F,MAAM;gBACbF,QAAQrB,KAAK,CAACuB;YAChB;YACA9H,IAAIiC,UAAU,GAAG;YACjBjC,IAAIoC,GAAG,CAAC;QACV;IACF;IAEA,IAAIoB,iBAAuCtD;IAC3C,IAAI/C,OAAOyI,YAAY,CAACmC,SAAS,EAAE;QACjC,2CAA2C;QAC3C,MAAM,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAE,GACnD,sHAAsH;QACtH5J,QAAQ;QACVmF,iBAAiBwE,yBAAyBxE;QAC1CyE;QACA,yFAAyF;QACzFhK,gBAAgBC,WAAWC,KAAK;IAClC;IACAtB,eAAe,CAACC,KAAKS,GAAG,CAAC,GAAGiG;IAE5B,MAAMD,mBAA8D;QAClE3D,MAAM9C,KAAK8C,IAAI;QACfrC,KAAKT,KAAKS,GAAG;QACb+D,UAAUxE,KAAKwE,QAAQ;QACvBzD,aAAaf,KAAKe,WAAW;QAC7BX,KAAK,CAAC,CAACJ,KAAKI,GAAG;QACfgL,QAAQpL,KAAKoL,MAAM;QACnBX,cAAc;YACZ,GAAIxJ,CAAAA,sCAAAA,mBAAoBwJ,YAAY,KAAI,CAAC,CAAC;YAC1CY,YAAY,EAAEnK,qCAAAA,kBAAmBmK,YAAY,CAACC,IAAI,CAACpK;QACrD;QACAqK,uBAAuB,CAAC,CAAClL,OAAOyI,YAAY,CAACmC,SAAS;QACtDO,yBAAyB,CAAC,CAACxL,KAAKwL,uBAAuB;QACvDC,gBAAgBvK;QAChBmB,iBAAiBrC,KAAKqC,eAAe;QACrCqJ,OAAO1L,KAAK0L,KAAK;QACjB3I,oBAAoB/C,KAAK+C,kBAAkB;IAC7C;IACA0D,iBAAiBgE,YAAY,CAACkB,mBAAmB,GAAGvI;IAEpD,yBAAyB;IACzB,MAAMgD,WAAW,MAAMpF,aAAamC,QAAQ,CAAC1D,UAAU,CAACgH;IAExD,8DAA8D;IAC9D,4BAA4B;IAC5B,IAAI,CAACmF,uCAAkB,CAACC,8CAAyB,CAAC,EAAE;QAClDD,uCAAkB,CAACC,8CAAyB,CAAC,GAAG,CAAC;IACnD;IACA,MAAMvI,qBAAqB5B,aAAI,CAACoK,QAAQ,CAAC7L,QAAQ8L,GAAG,IAAI/L,KAAKS,GAAG;IAEhEmL,uCAAkB,CAACC,8CAAyB,CAAC,CAACvI,mBAAmB,GAAG;QAClEb,YAAYpC;QACZmE,UAAU4B,SAASgF,MAAM,CAAC5G,QAAQ;QAClCwH,YAAY5F,SAASgF,MAAM,CAACY,UAAU,CAACV,IAAI,CAAClF,SAASgF,MAAM;QAC3Da,WAAW7F,SAASgF,MAAM,CAACa,SAAS,CAACX,IAAI,CAAClF,SAASgF,MAAM;QACzDG,uBAAuB9E,iBAAiB8E,qBAAqB;QAC7DW,2BAA2BlM,KAAKI,GAAG,GAC/BgG,SAASgF,MAAM,CAACc,yBAAyB,CAACZ,IAAI,CAAClF,SAASgF,MAAM,IAC9D,CAACzE,MAAiB,CAAC3G,KAAK0L,KAAK,IAAIlC,KAAIC,KAAK,CAAC9C;QAC/C0E,YAAY,EAAEnK,qCAAAA,kBAAmBmK,YAAY,CAACC,IAAI,CAACpK;IACrD;IAEA,MAAMiL,WAAW,OACfjE,MACAvB;QAEA,IAAIyF,IAAAA,sBAAU,EAACzF,MAAM;YACnB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,IAAIuB,SAAS,sBAAsB;YACjCsB,KAAIC,KAAK,CAAC,wBAAwB9C;QACpC,OAAO,IAAIuB,SAAS,qBAAqB;YACvCsB,KAAIC,KAAK,CAAC,uBAAuB9C;QACnC;IACF;IAEA1G,QAAQsF,EAAE,CAAC,qBAAqB4G,SAASb,IAAI,CAAC,MAAM;IACpDrL,QAAQsF,EAAE,CAAC,sBAAsB4G,SAASb,IAAI,CAAC,MAAM;IAErD,MAAMzD,gBAAgBwE,IAAAA,+BAAgB,EACpCxL,WACAR,QACAL,MACAgB,aAAamC,QAAQ,EACrBsD,kBACAxF,sCAAAA,mBAAoBqL,gBAAgB;IAGtC,MAAMC,iBAAuC,OAAOtJ,KAAKuJ,QAAQC;QAC/D,IAAI;YACFxJ,IAAIsC,EAAE,CAAC,SAAS,CAACC;YACf,2BAA2B;YAC3B,uBAAuB;YACzB;YACAgH,OAAOjH,EAAE,CAAC,SAAS,CAACC;YAClB,2BAA2B;YAC3B,uBAAuB;YACzB;YAEA,IAAIxF,KAAKI,GAAG,IAAIa,sBAAsBgC,IAAIY,GAAG,EAAE;gBAC7C,IACEmD,IAAAA,8BAAc,EAAC/D,KAAKuJ,QAAQnM,OAAO4G,iBAAiB,EAAEjH,KAAKwE,QAAQ,GACnE;oBACA;gBACF;gBACA,MAAM,EAAER,QAAQ,EAAEoD,WAAW,EAAE,GAAG/G;gBAElC,IAAIqM,YAAY1I;gBAEhB,8CAA8C;gBAC9C,IAAIoD,aAAa;oBACfsF,YAAYC,IAAAA,4CAAqB,EAACvF;oBAElC,IAAIwF,IAAIC,QAAQ,CAACH,YAAY;wBAC3B,sCAAsC;wBACtC,yCAAyC;wBACzC,yCAAyC;wBACzCA,YAAY,IAAIE,IAAIF,WAAW7M,QAAQ,CAACgF,OAAO,CAAC,OAAO;oBACzD;gBACF;gBAEA,MAAMiI,eAAe7J,IAAIY,GAAG,CAACkC,UAAU,CACrCgH,IAAAA,sCAAkB,EAAC,GAAGL,UAAU,kBAAkB,CAAC;gBAGrD,0DAA0D;gBAC1D,iEAAiE;gBACjE,IAAII,cAAc;oBAChB,OAAO7L,mBAAmBsG,WAAW,CAACyF,KAAK,CACzC/J,KACAuJ,QACAC,MACA,CAACQ;wBACCA,OAAOC,IAAI,CACTC,KAAKC,SAAS,CAAC;4BACbC,QAAQC,6CAA2B,CAACC,YAAY;4BAChDC,MAAMtM,CAAAA,qCAAAA,kBAAmBuM,cAAc,KAAI,CAAC;wBAC9C;oBAEJ;gBAEJ;YACF;YAEA,MAAMvK,MAAM,IAAIwK,2BAAc,CAAC;gBAC7BC,WAAW;oBACT,MAAM,qBAEL,CAFK,IAAItH,MACR,mFADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAM,EAAEuB,aAAa,EAAEjD,SAAS,EAAE,GAAG,MAAMkD,cAAc;gBACvD5E;gBACAC;gBACA4E,cAAc;gBACdC,QAAQC,IAAAA,mCAAsB,EAACwE;YACjC;YAEA,mDAAmD;YACnD,oCAAoC;YACpC,IAAI5E,eAAe;gBACjB,OAAO4E,OAAOlH,GAAG;YACnB;YAEA,IAAIX,UAAU+D,QAAQ,EAAE;gBACtB,OAAO,MAAMC,IAAAA,0BAAY,EAAC1F,KAAKuJ,QAAQ7H,WAAW8H;YACpD;QAEA,sEAAsE;QACtE,sDAAsD;QACxD,EAAE,OAAO9F,KAAK;YACZmE,QAAQrB,KAAK,CAAC,kCAAkC9C;YAChD6F,OAAOlH,GAAG;QACZ;IACF;IAEA,OAAO;QACLoB;QACA6F;QACAnB,QAAQhF,SAASgF,MAAM;QACvBwC;gBACE3M;YAAAA,uCAAAA,kCAAAA,mBAAoBsG,WAAW,qBAA/BtG,gCAAiC4M,KAAK;QACxC;IACF;AACF", "ignoreList": [0]}