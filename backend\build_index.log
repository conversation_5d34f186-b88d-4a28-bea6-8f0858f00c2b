2025-08-29 11:28:40,450 - INFO - Starting document indexing process...
2025-08-29 11:28:40,450 - INFO - Configuration: Config(embed_model='sentence-transformers/all-MiniLM-L6-v2', chunk_size=500, chunk_overlap=100, ocr_lang='vie+eng', min_chars=30, data_glob='data/**/*', show_processing=True, max_workers=4, batch_size=32, max_file_size_mb=100, use_parallel_processing=True)
2025-08-29 11:28:40,452 - INFO - Found 8 files to process
2025-08-29 11:28:40,452 - INFO - Using sequential processing
2025-08-29 11:28:40,453 - WARNING - Excel engines not available, skipping Excel files
2025-08-29 11:28:40,453 - WARNING - Invalid JSON at line 1 in data\sft.jsonl: Expecting value: line 1 column 14 (char 13)
2025-08-29 11:28:40,454 - WARNING - Invalid <PERSON> at line 2 in data\sft.jsonl: Extra data: line 1 column 102 (char 101)
2025-08-29 11:28:40,454 - WARNING - Invalid JSON at line 3 in data\sft.jsonl: Extra data: line 1 column 49 (char 48)
2025-08-29 11:28:40,454 - WARNING - Invalid JSON at line 5 in data\sft.jsonl: Expecting value: line 1 column 1 (char 0)
2025-08-29 11:28:40,454 - WARNING - Invalid JSON at line 6 in data\sft.jsonl: Expecting value: line 1 column 14 (char 13)
2025-08-29 11:28:40,454 - WARNING - Invalid JSON at line 7 in data\sft.jsonl: Extra data: line 1 column 58 (char 57)
2025-08-29 11:28:40,454 - WARNING - Invalid JSON at line 9 in data\sft.jsonl: Expecting value: line 1 column 1 (char 0)
2025-08-29 11:28:41,084 - WARNING - Excel engines not available, skipping Excel files
2025-08-29 11:28:41,084 - INFO - Successfully loaded 6 documents
2025-08-29 11:28:41,084 - INFO - Starting text chunking...
2025-08-29 11:28:41,085 - INFO - Created 60 chunks
2025-08-29 11:28:41,085 - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-08-29 11:28:41,087 - INFO - Use pytorch device_name: cpu
2025-08-29 11:28:41,087 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-08-29 11:28:45,613 - INFO - Generating embeddings...
2025-08-29 11:28:47,648 - INFO - Building FAISS index...
2025-08-29 11:28:47,649 - INFO - Saving index and chunks...
2025-08-29 11:28:47,652 - INFO - Indexing process completed successfully!
2025-08-29 11:32:49,988 - INFO - Starting document indexing process...
2025-08-29 11:32:49,989 - INFO - Configuration: Config(embed_model='sentence-transformers/all-MiniLM-L6-v2', chunk_size=500, chunk_overlap=100, ocr_lang='vie+eng', min_chars=30, data_glob='data/**/*', show_processing=True, max_workers=4, batch_size=32, max_file_size_mb=100, use_parallel_processing=True)
2025-08-29 11:32:49,989 - INFO - Found 8 files to process
2025-08-29 11:32:49,989 - INFO - Using sequential processing
2025-08-29 11:32:49,990 - WARNING - Excel engines not available, skipping Excel files
2025-08-29 11:32:49,990 - WARNING - Invalid JSON at line 1 in data\sft.jsonl: Expecting value: line 1 column 14 (char 13)
2025-08-29 11:32:49,991 - WARNING - Invalid JSON at line 2 in data\sft.jsonl: Extra data: line 1 column 102 (char 101)
2025-08-29 11:32:49,991 - WARNING - Invalid JSON at line 3 in data\sft.jsonl: Extra data: line 1 column 49 (char 48)
2025-08-29 11:32:49,991 - WARNING - Invalid JSON at line 5 in data\sft.jsonl: Expecting value: line 1 column 1 (char 0)
2025-08-29 11:32:49,991 - WARNING - Invalid JSON at line 6 in data\sft.jsonl: Expecting value: line 1 column 14 (char 13)
2025-08-29 11:32:49,991 - WARNING - Invalid JSON at line 7 in data\sft.jsonl: Extra data: line 1 column 58 (char 57)
2025-08-29 11:32:49,991 - WARNING - Invalid JSON at line 9 in data\sft.jsonl: Expecting value: line 1 column 1 (char 0)
2025-08-29 11:32:50,538 - WARNING - Excel engines not available, skipping Excel files
2025-08-29 11:32:50,539 - INFO - Successfully loaded 6 documents
2025-08-29 11:32:50,539 - INFO - Starting text chunking...
2025-08-29 11:32:50,540 - INFO - Created 60 chunks
2025-08-29 11:32:50,540 - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-08-29 11:32:50,541 - INFO - Use pytorch device_name: cpu
2025-08-29 11:32:50,541 - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-08-29 11:32:54,769 - INFO - Generating embeddings...
2025-08-29 11:32:56,587 - INFO - Building FAISS index...
2025-08-29 11:32:56,588 - INFO - Saving index and chunks...
2025-08-29 11:32:56,591 - INFO - Indexing process completed successfully!
2025-08-29 11:32:56,592 - INFO - Indexing process completed successfully!
