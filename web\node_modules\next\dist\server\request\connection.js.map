{"version": 3, "sources": ["../../../src/server/request/connection.ts"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeHangingPromise,\n  makeDevtoolsIOAwarePromise,\n} from '../dynamic-rendering-utils'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * This function allows you to indicate that you require an actual user Request before continuing.\n *\n * During prerendering it will never resolve and during rendering it resolves immediately.\n */\nexport function connection(): Promise<void> {\n  const callingExpression = 'connection'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"connection\" inside \"after(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but \"after(...)\" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic, we override all other logic and always just\n      // return a resolving promise without tracking.\n      return Promise.resolve(undefined)\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`connection\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"connection\" inside \"use cache\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual request, but caches must be able to be produced before a request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, connection)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'private-cache': {\n          // It might not be intuitive to throw for private caches as well, but\n          // we don't consider runtime prefetches as \"actual requests\" (in the\n          // navigation sense), despite allowing them to read cookies.\n          const error = new Error(\n            `Route ${workStore.route} used \"connection\" inside \"use cache: private\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual navigation request, but caches must be able to be produced before a navigation request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, connection)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"connection\" inside a function cached with \"unstable_cache(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n          // We return a promise that never resolves to allow the prerender to\n          // stall at this point.\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            workStore.route,\n            '`connection()`'\n          )\n        case 'prerender-ppr':\n          // We use React's postpone API to interrupt rendering here to create a\n          // dynamic hole\n          return postponeWithTracking(\n            workStore.route,\n            'connection',\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // We throw an error here to interrupt prerendering to mark the route\n          // as dynamic\n          return throwToInterruptStaticGeneration(\n            'connection',\n            workStore,\n            workUnitStore\n          )\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            return makeDevtoolsIOAwarePromise(undefined)\n          } else {\n            return Promise.resolve(undefined)\n          }\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n"], "names": ["connection", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "Promise", "resolve", "undefined", "dynamicShouldError", "StaticGenBailoutError", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "makeHangingPromise", "renderSignal", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "process", "env", "NODE_ENV", "makeDevtoolsIOAwarePromise", "throwForMissingRequestStore"], "mappings": ";;;;+BAsBgBA;;;eAAAA;;;0CAtBiB;8CAI1B;kCAKA;yCAC+B;uCAI/B;uBACyC;AAOzC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,IAAAA,sCAA+B,KAChC;YACA,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,6UAA6U,CAAC,GADnW,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,sEAAsE;YACtE,+CAA+C;YAC/C,OAAOC,QAAQC,OAAO,CAACC;QACzB;QAEA,IAAIZ,UAAUa,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEd,UAAUQ,KAAK,CAAC,oNAAoN,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,OAAQA,cAAcY,IAAI;gBACxB,KAAK;oBAAS;wBACZ,MAAMC,QAAQ,qBAEb,CAFa,IAAIT,MAChB,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,kVAAkV,CAAC,GADhW,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMU,iBAAiB,CAACD,OAAOlB;wBAC/BE,UAAUkB,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBAAiB;wBACpB,qEAAqE;wBACrE,oEAAoE;wBACpE,4DAA4D;wBAC5D,MAAMA,QAAQ,qBAEb,CAFa,IAAIT,MAChB,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,iXAAiX,CAAC,GAD/X,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMU,iBAAiB,CAACD,OAAOlB;wBAC/BE,UAAUkB,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIT,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0XAA0X,CAAC,GADhZ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,oEAAoE;oBACpE,uBAAuB;oBACvB,OAAOW,IAAAA,yCAAkB,EACvBhB,cAAciB,YAAY,EAC1BpB,UAAUQ,KAAK,EACf;gBAEJ,KAAK;oBACH,sEAAsE;oBACtE,eAAe;oBACf,OAAOa,IAAAA,sCAAoB,EACzBrB,UAAUQ,KAAK,EACf,cACAL,cAAcmB,eAAe;gBAEjC,KAAK;oBACH,qEAAqE;oBACrE,aAAa;oBACb,OAAOC,IAAAA,kDAAgC,EACrC,cACAvB,WACAG;gBAEJ,KAAK;oBACHqB,IAAAA,iDAA+B,EAACrB;oBAChC,IAAIsB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,OAAOC,IAAAA,iDAA0B,EAAChB;oBACpC,OAAO;wBACL,OAAOF,QAAQC,OAAO,CAACC;oBACzB;gBACF;oBACET;YACJ;QACF;IACF;IAEA,yEAAyE;IACzE0B,IAAAA,yDAA2B,EAAC9B;AAC9B", "ignoreList": [0]}