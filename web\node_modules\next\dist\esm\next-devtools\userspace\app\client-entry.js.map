{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/client-entry.tsx"], "sourcesContent": ["import React from 'react'\nimport DefaultGlobalError from '../../../client/components/builtin/global-error'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\n\n// If an error is thrown while rendering an RSC stream, this will catch it in\n// dev and show the error overlay.\nexport function RootLevelDevOverlayElement({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <AppDevOverlayErrorBoundary globalError={[DefaultGlobalError, null]}>\n      {children}\n    </AppDevOverlayErrorBoundary>\n  )\n}\n"], "names": ["React", "DefaultGlobalError", "AppDevOverlayErrorBoundary", "RootLevelDevOverlayElement", "children", "globalError"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,wBAAwB,kDAAiD;AAChF,SAASC,0BAA0B,QAAQ,mCAAkC;AAE7E,6EAA6E;AAC7E,kCAAkC;AAClC,OAAO,SAASC,2BAA2B,KAI1C;IAJ0C,IAAA,EACzCC,QAAQ,EAGT,GAJ0C;IAKzC,qBACE,KAACF;QAA2BG,aAAa;YAACJ;YAAoB;SAAK;kBAChEG;;AAGP", "ignoreList": [0]}