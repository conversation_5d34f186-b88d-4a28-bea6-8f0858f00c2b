{"version": 3, "sources": ["../../../src/client/react-client-callbacks/error-boundary-callbacks.ts"], "sourcesContent": ["// This file is only used in app router due to the specific error state handling.\n\nimport type { ErrorInfo } from 'react'\nimport { isNextRouterError } from '../components/is-next-router-error'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { reportGlobalError } from './report-global-error'\nimport { ErrorBoundaryHandler } from '../components/error-boundary'\nimport DefaultErrorBoundary from '../components/builtin/global-error'\n\nconst devToolErrorMod: typeof import('../../next-devtools/userspace/app/errors') =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../../next-devtools/userspace/app/errors') as typeof import('../../next-devtools/userspace/app/errors'))\n    : {\n        decorateDevError: (error: unknown) => error as Error,\n        handleClientError: () => {},\n        originConsoleError: console.error.bind(console),\n      }\n\nexport function onCaughtError(\n  thrownValue: unknown,\n  errorInfo: ErrorInfo & { errorBoundary?: React.Component }\n) {\n  const errorBoundaryComponent = errorInfo.errorBoundary?.constructor\n\n  let isImplicitErrorBoundary\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { AppDevOverlayErrorBoundary } =\n      require('../../next-devtools/userspace/app/app-dev-overlay-error-boundary') as typeof import('../../next-devtools/userspace/app/app-dev-overlay-error-boundary')\n\n    isImplicitErrorBoundary =\n      errorBoundaryComponent === AppDevOverlayErrorBoundary\n  }\n\n  isImplicitErrorBoundary =\n    isImplicitErrorBoundary ||\n    (errorBoundaryComponent === ErrorBoundaryHandler &&\n      (errorInfo.errorBoundary! as InstanceType<typeof ErrorBoundaryHandler>)\n        .props.errorComponent === DefaultErrorBoundary)\n\n  // Skip the segment explorer triggered error\n  if (process.env.NODE_ENV !== 'production') {\n    const { SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE } =\n      require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n    if (\n      thrownValue instanceof Error &&\n      thrownValue.message === SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE\n    ) {\n      return\n    }\n  }\n\n  if (isImplicitErrorBoundary) {\n    // We don't consider errors caught unless they're caught by an explicit error\n    // boundary. The built-in ones are considered implicit.\n    // This mimics how the same app would behave without Next.js.\n    return onUncaughtError(thrownValue)\n  }\n\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(thrownValue) || isNextRouterError(thrownValue)) return\n\n  if (process.env.NODE_ENV !== 'production') {\n    const errorBoundaryName =\n      // read react component displayName\n      (errorBoundaryComponent as any)?.displayName ||\n      errorBoundaryComponent?.name ||\n      'Unknown'\n\n    const componentThatErroredFrame = errorInfo?.componentStack?.split('\\n')[1]\n\n    // Match chrome or safari stack trace\n    const matches =\n      // regex to match the function name in the stack trace\n      // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n      // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n      componentThatErroredFrame?.match(/\\s+at (\\w+)\\s+|(\\w+)@/) ?? []\n    const componentThatErroredName = matches[1] || matches[2] || 'Unknown'\n\n    // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n    const errorBoundaryMessage = `It was handled by the <${errorBoundaryName}> error boundary.`\n    const componentErrorMessage = componentThatErroredName\n      ? `The above error occurred in the <${componentThatErroredName}> component.`\n      : `The above error occurred in one of your components.`\n\n    const errorLocation = `${componentErrorMessage} ${errorBoundaryMessage}`\n    const error = devToolErrorMod.decorateDevError(thrownValue)\n\n    // Log and report the error with location but without modifying the error stack\n    devToolErrorMod.originConsoleError('%o\\n\\n%s', thrownValue, errorLocation)\n\n    devToolErrorMod.handleClientError(error)\n  } else {\n    devToolErrorMod.originConsoleError(thrownValue)\n  }\n}\n\nexport function onUncaughtError(thrownValue: unknown) {\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(thrownValue) || isNextRouterError(thrownValue)) return\n\n  if (process.env.NODE_ENV !== 'production') {\n    const error = devToolErrorMod.decorateDevError(thrownValue)\n\n    // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n    reportGlobalError(error)\n  } else {\n    reportGlobalError(thrownValue)\n  }\n}\n"], "names": ["isNextRouterError", "isBailoutToCSRError", "reportGlobalError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultErrorBoundary", "devToolErrorMod", "process", "env", "NODE_ENV", "require", "decorateDevError", "error", "handleClientError", "originConsoleError", "console", "bind", "onCaughtError", "thrownValue", "errorInfo", "errorBoundaryComponent", "errorBoundary", "constructor", "isImplicitErrorBoundary", "AppDevOverlayErrorBoundary", "props", "errorComponent", "SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE", "Error", "message", "onUncaughtError", "errorBoundaryName", "displayName", "name", "componentThatErroredFrame", "componentStack", "split", "matches", "match", "componentThatErroredName", "errorBoundaryMessage", "componentErrorMessage", "errorLocation"], "mappings": "AAAA,iFAAiF;AAGjF,SAASA,iBAAiB,QAAQ,qCAAoC;AACtE,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,oBAAoB,QAAQ,+BAA8B;AACnE,OAAOC,0BAA0B,qCAAoC;AAErE,MAAMC,kBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACpBC,QAAQ,8CACT;IACEC,kBAAkB,CAACC,QAAmBA;IACtCC,mBAAmB,KAAO;IAC1BC,oBAAoBC,QAAQH,KAAK,CAACI,IAAI,CAACD;AACzC;AAEN,OAAO,SAASE,cACdC,WAAoB,EACpBC,SAA0D;QAE3BA;IAA/B,MAAMC,0BAAyBD,2BAAAA,UAAUE,aAAa,qBAAvBF,yBAAyBG,WAAW;IAEnE,IAAIC;IAEJ,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEe,0BAA0B,EAAE,GAClCd,QAAQ;QAEVa,0BACEH,2BAA2BI;IAC/B;IAEAD,0BACEA,2BACCH,2BAA2BhB,wBAC1B,AAACe,UAAUE,aAAa,CACrBI,KAAK,CAACC,cAAc,KAAKrB;IAEhC,4CAA4C;IAC5C,IAAIE,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEkB,wCAAwC,EAAE,GAChDjB,QAAQ;QACV,IACEQ,uBAAuBU,SACvBV,YAAYW,OAAO,KAAKF,0CACxB;YACA;QACF;IACF;IAEA,IAAIJ,yBAAyB;QAC3B,6EAA6E;QAC7E,uDAAuD;QACvD,6DAA6D;QAC7D,OAAOO,gBAAgBZ;IACzB;IAEA,6EAA6E;IAC7E,IAAIhB,oBAAoBgB,gBAAgBjB,kBAAkBiB,cAAc;IAExE,IAAIX,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YAOPU;QANlC,MAAMY,oBAEJ,CADA,mCAAmC;QAClCX,0CAAD,AAACA,uBAAgCY,WAAW,MAC5CZ,0CAAAA,uBAAwBa,IAAI,KAC5B;QAEF,MAAMC,4BAA4Bf,8BAAAA,4BAAAA,UAAWgB,cAAc,qBAAzBhB,0BAA2BiB,KAAK,CAAC,KAAK,CAAC,EAAE;YAIzE,sDAAsD;QACtD,qGAAqG;QACrG,gGAAgG;QAChGF;QALF,qCAAqC;QACrC,MAAMG,UAIJH,CAAAA,mCAAAA,6CAAAA,0BAA2BI,KAAK,CAAC,oCAAjCJ,mCAA6D,EAAE;QACjE,MAAMK,2BAA2BF,OAAO,CAAC,EAAE,IAAIA,OAAO,CAAC,EAAE,IAAI;QAE7D,iIAAiI;QACjI,MAAMG,uBAAuB,AAAC,4BAAyBT,oBAAkB;QACzE,MAAMU,wBAAwBF,2BAC1B,AAAC,sCAAmCA,2BAAyB,iBAC5D;QAEL,MAAMG,gBAAgB,AAAGD,wBAAsB,MAAGD;QAClD,MAAM5B,QAAQN,gBAAgBK,gBAAgB,CAACO;QAE/C,+EAA+E;QAC/EZ,gBAAgBQ,kBAAkB,CAAC,YAAYI,aAAawB;QAE5DpC,gBAAgBO,iBAAiB,CAACD;IACpC,OAAO;QACLN,gBAAgBQ,kBAAkB,CAACI;IACrC;AACF;AAEA,OAAO,SAASY,gBAAgBZ,WAAoB;IAClD,6EAA6E;IAC7E,IAAIhB,oBAAoBgB,gBAAgBjB,kBAAkBiB,cAAc;IAExE,IAAIX,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMG,QAAQN,gBAAgBK,gBAAgB,CAACO;QAE/C,oFAAoF;QACpFf,kBAAkBS;IACpB,OAAO;QACLT,kBAAkBe;IACpB;AACF", "ignoreList": [0]}