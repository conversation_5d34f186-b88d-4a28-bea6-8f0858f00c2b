{"version": 3, "sources": ["../../../../../src/client/dev/hot-reloader/app/hot-reloader-app.tsx"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nimport type { ReactNode } from 'react'\nimport { useEffect, startTransition, useRef } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../../../../shared/lib/format-webpack-messages'\nimport { useRouter } from '../../../components/navigation'\nimport {\n  REACT_REFRESH_FULL_RELOAD,\n  REACT_REFRESH_FULL_RELOAD_FROM_ERROR,\n  reportInvalidHmrMessage,\n} from '../shared'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { ReplaySsrOnlyErrors } from '../../../../next-devtools/userspace/app/errors/replay-ssr-only-errors'\nimport { AppDevOverlayErrorBoundary } from '../../../../next-devtools/userspace/app/app-dev-overlay-error-boundary'\nimport { useErrorHandler } from '../../../../next-devtools/userspace/app/errors/use-error-handler'\nimport { RuntimeErrorHandler } from '../../runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from './use-websocket'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { useUntrackedPathname } from '../../../components/navigation-untracked'\nimport reportHmrLatency from '../../report-hmr-latency'\nimport { TurbopackHmr } from '../turbopack-hot-reloader-common'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../../components/app-router-headers'\nimport type { GlobalErrorState } from '../../../components/app-router-instance'\nimport { useForwardConsoleLog } from '../../../../next-devtools/userspace/app/errors/use-forward-console-log'\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack(sendMessage: (message: string) => void) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [], webpackStartMsSinceEpoch!, Date.now())\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack(sendMessage)\n      return\n    }\n\n    dispatcher.onRefresh()\n    resolvePendingHotUpdateWebpack()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the server for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      const hmrUpdate = turbopackHmr!.onBuilt()\n      if (hmrUpdate != null) {\n        reportHmrLatency(\n          sendMessage,\n          [...hmrUpdate.updatedModules],\n          hmrUpdate.startMsSinceEpoch,\n          hmrUpdate.endMsSinceEpoch,\n          // suppress the `client-hmr-latency` event if the update was a no-op:\n          hmrUpdate.hasUpdates\n        )\n      }\n      dispatcher.onBuildOk()\n    } else {\n      tryApplyUpdatesWebpack(sendMessage)\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      if (process.env.__NEXT_DEV_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            dispatcher.onStaticIndicator(true)\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      dispatcher.buildingIndicatorShow()\n\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        setPendingHotUpdateWebpack()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      dispatcher.buildingIndicatorHide()\n\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n      if ('devToolsConfig' in obj)\n        dispatcher.onDevToolsConfig(obj.devToolsConfig)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n          hash: obj.hash,\n        })\n      )\n\n      // Store the latest hash in a session cookie so that it's sent back to the\n      // server with any subsequent requests.\n      document.cookie = `${NEXT_HMR_REFRESH_HASH_COOKIE}=${obj.hash};path=/`\n\n      if (\n        RuntimeErrorHandler.hadRuntimeError ||\n        document.documentElement.id === '__next_error__'\n      ) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      turbopackHmr?.onReloadPage()\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      turbopackHmr?.onPageAddRemove()\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG: {\n      dispatcher.onDevToolsConfig(obj.data)\n      return\n    }\n    default: {\n      obj satisfies never\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n  globalError,\n}: {\n  assetPrefix: string\n  children: ReactNode\n  globalError: GlobalErrorState\n}) {\n  useErrorHandler(dispatcher.onUnhandledError, dispatcher.onUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  useForwardConsoleLog(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            dispatcher.onStaticIndicator(true)\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: unknown) {\n        reportInvalidHmrMessage(event, err)\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n  return (\n    <AppDevOverlayErrorBoundary globalError={globalError}>\n      <ReplaySsrOnlyErrors onBlockingError={dispatcher.openErrorOverlay} />\n      {children}\n    </AppDevOverlayErrorBoundary>\n  )\n}\n"], "names": ["HotReload", "waitForWebpackRuntimeHotUpdate", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "webpackStartMsSinceEpoch", "turbopackHmr", "process", "env", "TURBOPACK", "TurbopackHmr", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "RuntimeError<PERSON>andler", "dependency<PERSON><PERSON>n", "undefined", "window", "location", "reload", "tryApplyUpdatesWebpack", "dispatcher", "onBuildOk", "reportHmrLatency", "handleApplyUpdates", "updatedModules", "console", "warn", "REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "onRefresh", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "onBeforeRefresh", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "formatWebpackMessages", "warnings", "onBuildError", "i", "length", "error", "stripAnsi", "handleHotUpdate", "hmrUpdate", "onBuilt", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "__NEXT_DEV_INDICATOR", "current", "data", "onStaticIndicator", "BUILDING", "buildingIndicatorShow", "onBuilding", "log", "BUILT", "SYNC", "buildingIndicatorHide", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "onDevIndicator", "devIndicator", "onDevToolsConfig", "devToolsConfig", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "onTurbopackMessage", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "document", "cookie", "NEXT_HMR_REFRESH_HASH_COOKIE", "documentElement", "id", "startTransition", "hmrRefresh", "RELOAD_PAGE", "onReloadPage", "ADDED_PAGE", "REMOVED_PAGE", "onPageAddRemove", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "DEVTOOLS_CONFIG", "assetPrefix", "children", "globalError", "useErrorHandler", "onUnhandledError", "onUnhandledRejection", "webSocketRef", "useWebsocket", "useWebsocketPing", "useSendMessage", "useForwardConsoleLog", "useTurbopack", "useRouter", "pathname", "useUntrackedPathname", "useRef", "useEffect", "appIsrManifest", "reason", "DOMException", "websocket", "reportInvalidHmrMessage", "addEventListener", "removeEventListener", "AppDevOverlayErrorBoundary", "ReplaySsrOnlyErrors", "onBlockingError", "openErrorOverlay"], "mappings": "AAAA,6CAA6C;;;;;;;;;;;;;;;;IAyc7C,OAiGC;eAjGuBA;;IApZRC,8BAA8B;eAA9BA;;;;;uBAlDmC;oEAC7B;gFACY;4BACR;wBAKnB;8BACoB;qCACS;4CACO;iCACX;qCACI;8BAM7B;kCACqC;qCAKP;2EACR;4CACA;kCACgB;sCAER;AAErC,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,2BAA0C;AAC9C,MAAMC,eAAoCC,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAIC,wCAAY,KAChB;AAEJ,IAAIC,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEO,SAASjB;IACd,OAAOe;AACT;AAEA,kDAAkD;AAClD,SAASK,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCpB,4BAA4BoB;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIX,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOZ,8BAA8BsB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACAJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAACC,wCAAmB,CAACD,eAAe;QACtDE,iBAAiBb,MAAMA,IAAIa,eAAe,GAAGC;IAC/C;IAGF,IAAIxC,WAAW;IACfA,YAAY;IACZyC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,uBAAuBjB,WAAsC;IACpE,IAAI,CAACb,uBAAuB,CAACE,mBAAmB;QAC9CN;QACAmC,wBAAU,CAACC,SAAS;QACpBC,IAAAA,yBAAgB,EAACpB,aAAa,EAAE,EAAE1B,0BAA2BH,KAAKC,GAAG;QACrE;IACF;IAEA,SAASiD,mBACPtB,GAAQ,EACRuB,cAA0C;QAE1C,IAAIvB,OAAOY,wCAAmB,CAACD,eAAe,IAAIY,kBAAkB,MAAM;YACxE,IAAIvB,KAAK;gBACPwB,QAAQC,IAAI,CAACC,iCAAyB;YACxC,OAAO,IAAId,wCAAmB,CAACD,eAAe,EAAE;gBAC9Ca,QAAQC,IAAI,CAACE,4CAAoC;YACnD;YACA5B,kBAAkBC,KAAKC;YACvB;QACF;QAEAkB,wBAAU,CAACC,SAAS;QAEpB,IAAIhC,qBAAqB;YACvB,+DAA+D;YAC/D8B,uBAAuBjB;YACvB;QACF;QAEAkB,wBAAU,CAACS,SAAS;QACpB5C;QACAqC,IAAAA,yBAAgB,EACdpB,aACAsB,gBACAhD,0BACAH,KAAKC,GAAG;QAGV,IAAII,QAAQC,GAAG,CAACmD,gBAAgB,EAAE;YAChCnC,kBAAkB;gBAChB,IAAIoC,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3DxC,OAAOC,GAAG,CACPwC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACV;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGJ,wBAAU,CAACe,eAAe;QAC1B,2DAA2D;QAC3D,OAAO3C,OAAOC,GAAG,CAAC2C,KAAK;IACzB,GACCF,IAAI,CACH,CAACV;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACvB;QACCsB,mBAAmBtB,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAASoC,eACPC,GAAqB,EACrBpC,WAAsC,EACtCqC,uBAA6D,EAC7DC,MAAoC,EACpCC,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCF,QAAQA;YACRG,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B3B,wBAAU,CAAC4B,YAAY,CAACH,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAIK,IAAI,GAAGA,IAAIJ,UAAUD,MAAM,CAACM,MAAM,EAAED,IAAK;YAChDxB,QAAQ0B,KAAK,CAACC,IAAAA,kBAAS,EAACP,UAAUD,MAAM,CAACK,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIvE,QAAQC,GAAG,CAACmD,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACa,UAAUD,MAAM,CAAC,EAAE;gBACtCb,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASqB;QACP,IAAI3E,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB,MAAM0E,YAAY7E,aAAc8E,OAAO;YACvC,IAAID,aAAa,MAAM;gBACrBhC,IAAAA,yBAAgB,EACdpB,aACA;uBAAIoD,UAAU9B,cAAc;iBAAC,EAC7B8B,UAAUE,iBAAiB,EAC3BF,UAAUG,eAAe,EACzB,qEAAqE;gBACrEH,UAAUI,UAAU;YAExB;YACAtC,wBAAU,CAACC,SAAS;QACtB,OAAO;YACLF,uBAAuBjB;QACzB;IACF;IAEA,OAAQoC,IAAIqB,MAAM;QAChB,KAAKC,6CAA2B,CAACC,YAAY;YAAE;gBAC7C,IAAInF,QAAQC,GAAG,CAACmF,oBAAoB,EAAE;oBACpC,IAAIrB,mBAAmB;wBACrBA,kBAAkBsB,OAAO,GAAGzB,IAAI0B,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAI,AAACtB,YAAYqB,OAAO,IAAezB,IAAI0B,IAAI,EAAE;4BAC/C5C,wBAAU,CAAC6C,iBAAiB,CAAC;wBAC/B,OAAO;4BACL7C,wBAAU,CAAC6C,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,KAAKL,6CAA2B,CAACM,QAAQ;YAAE;gBACzC9C,wBAAU,CAAC+C,qBAAqB;gBAEhC,IAAIzF,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBH,aAAc2F,UAAU;gBAC1B,OAAO;oBACL5F,2BAA2BH,KAAKC,GAAG;oBACnCY;oBACAuC,QAAQ4C,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAKT,6CAA2B,CAACU,KAAK;QACtC,KAAKV,6CAA2B,CAACW,IAAI;YAAE;gBACrCnD,wBAAU,CAACoD,qBAAqB;gBAEhC,IAAIlC,IAAIlD,IAAI,EAAE;oBACZD,oBAAoBmD,IAAIlD,IAAI;gBAC9B;gBAEA,MAAM,EAAEwD,MAAM,EAAEG,QAAQ,EAAE,GAAGT;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKlB,wBAAU,CAACqD,aAAa,CAACnC,IAAIoC,WAAW;gBAClE,IAAI,WAAWpC,OAAOA,IAAIqC,KAAK,EAAEvD,wBAAU,CAACwD,WAAW,CAACtC,IAAIqC,KAAK;gBACjE,IAAI,kBAAkBrC,KAAKlB,wBAAU,CAACyD,cAAc,CAACvC,IAAIwC,YAAY;gBACrE,IAAI,oBAAoBxC,KACtBlB,wBAAU,CAAC2D,gBAAgB,CAACzC,IAAI0C,cAAc;gBAEhD,MAAMC,YAAYC,QAAQtC,UAAUA,OAAOM,MAAM;gBACjD,kEAAkE;gBAClE,IAAI+B,WAAW;oBACb/E,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPwE,YAAYvC,OAAOM,MAAM;wBACzBkC,UAAUnH;oBACZ;oBAGF0E,aAAaC;oBACb;gBACF;gBAEA,MAAMyC,cAAcH,QAAQnC,YAAYA,SAASG,MAAM;gBACvD,IAAImC,aAAa;oBACfnF,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP2E,cAAcvC,SAASG,MAAM;wBAC7BkC,UAAUnH;oBACZ;oBAGF,iCAAiC;oBACjC,MAAMsH,oBAAoBzC,IAAAA,8BAAqB,EAAC;wBAC9CC,UAAUA;wBACVH,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAIK,IAAI,GAAGA,IAAIsC,kBAAkBxC,QAAQ,CAACG,MAAM,EAAED,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXxB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAAC0B,IAAAA,kBAAS,EAACmC,kBAAkBxC,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA/C,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPyE,UAAUnH;gBACZ;gBAGF,IAAIqE,IAAIqB,MAAM,KAAKC,6CAA2B,CAACU,KAAK,EAAE;oBACpDjB;gBACF;gBACA;YACF;QACA,KAAKO,6CAA2B,CAAC4B,mBAAmB;YAAE;gBACpDjD,wBAAwB;oBACtBkD,MAAM7B,6CAA2B,CAAC4B,mBAAmB;oBACrDxB,MAAM;wBACJ0B,WAAWpD,IAAI0B,IAAI,CAAC0B,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,KAAK9B,6CAA2B,CAAC+B,iBAAiB;YAAE;gBAClDlH,aAAcmH,kBAAkB,CAACtD;gBACjClB,wBAAU,CAACe,eAAe;gBAC1BI,wBAAwB;oBACtBkD,MAAM7B,6CAA2B,CAAC+B,iBAAiB;oBACnD3B,MAAM1B,IAAI0B,IAAI;gBAChB;gBACA,IAAInD,wCAAmB,CAACD,eAAe,EAAE;oBACvCa,QAAQC,IAAI,CAACE,4CAAoC;oBACjD5B,kBAAkB,MAAME;gBAC1B;gBACAkB,wBAAU,CAACS,SAAS;gBACpB;YACF;QACA,uDAAuD;QACvD,KAAK+B,6CAA2B,CAACiC,wBAAwB;YAAE;gBACzDpH,gCAAAA,aAAcqH,wBAAwB;gBACtC5F,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPyE,UAAUnH;oBACVmB,MAAMkD,IAAIlD,IAAI;gBAChB;gBAGF,0EAA0E;gBAC1E,uCAAuC;gBACvC2G,SAASC,MAAM,GAAG,AAAGC,8CAA4B,GAAC,MAAG3D,IAAIlD,IAAI,GAAC;gBAE9D,IACEyB,wCAAmB,CAACD,eAAe,IACnCmF,SAASG,eAAe,CAACC,EAAE,KAAK,kBAChC;oBACA,IAAI5H,WAAW;oBACfA,YAAY;oBACZ,OAAOyC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBAEAkF,IAAAA,sBAAe,EAAC;oBACd5D,OAAO6D,UAAU;oBACjBjF,wBAAU,CAACS,SAAS;gBACtB;gBAEA,IAAInD,QAAQC,GAAG,CAACmD,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAK4B,6CAA2B,CAAC0C,WAAW;YAAE;gBAC5C7H,gCAAAA,aAAc8H,YAAY;gBAC1BrG,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPyE,UAAUnH;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOyC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAK0C,6CAA2B,CAAC4C,UAAU;QAC3C,KAAK5C,6CAA2B,CAAC6C,YAAY;YAAE;gBAC7ChI,gCAAAA,aAAciI,eAAe;gBAC7B,qFAAqF;gBACrF,OAAOlE,OAAO6D,UAAU;YAC1B;QACA,KAAKzC,6CAA2B,CAAC+C,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGtE;gBACtB,IAAIsE,WAAW;oBACb,MAAM,EAAEpG,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAKoG,KAAK,CAACD;oBACtC,MAAMzD,QAAQ,qBAAkB,CAAlB,IAAI2D,MAAMtG,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/B2C,MAAM/C,KAAK,GAAGA;oBACduC,aAAa;wBAACQ;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKS,6CAA2B,CAACmD,yBAAyB;YAAE;gBAC1D;YACF;QACA,KAAKnD,6CAA2B,CAACoD,eAAe;YAAE;gBAChD5F,wBAAU,CAAC2D,gBAAgB,CAACzC,IAAI0B,IAAI;gBACpC;YACF;QACA;YAAS;gBACP1B;YACF;IACF;AACF;AAEe,SAASxE,UAAU,KAQjC;IARiC,IAAA,EAChCmJ,WAAW,EACXC,QAAQ,EACRC,WAAW,EAKZ,GARiC;IAShCC,IAAAA,gCAAe,EAAChG,wBAAU,CAACiG,gBAAgB,EAAEjG,wBAAU,CAACkG,oBAAoB;IAE5E,MAAMC,eAAeC,IAAAA,0BAAY,EAACP;IAElCQ,IAAAA,8BAAgB,EAACF;IACjB,MAAMrH,cAAcwH,IAAAA,4BAAc,EAACH;IACnCI,IAAAA,0CAAoB,EAACJ;IACrB,MAAMhF,0BAA0BqF,IAAAA,0BAAY,EAAC1H,aAAa,CAACD,MACzDD,kBAAkBC,KAAKC;IAGzB,MAAMsC,SAASqF,IAAAA,qBAAS;IAExB,8EAA8E;IAC9E,mEAAmE;IACnE,MAAMC,WAAWC,IAAAA,yCAAoB;IACrC,MAAMtF,oBAAoBuF,IAAAA,aAAM,EAAiC,CAAC;IAClE,MAAMtF,cAAcsF,IAAAA,aAAM,EAACF;IAE3B,IAAIpJ,QAAQC,GAAG,CAACmF,oBAAoB,EAAE;QACpC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;QACtDmE,IAAAA,gBAAS,EAAC;YACRvF,YAAYqB,OAAO,GAAG+D;YAEtB,MAAMI,iBAAiBzF,kBAAkBsB,OAAO;YAEhD,IAAImE,gBAAgB;gBAClB,IAAIJ,YAAYA,YAAYI,gBAAgB;oBAC1C,IAAI;wBACF9G,wBAAU,CAAC6C,iBAAiB,CAAC;oBAC/B,EAAE,OAAOkE,QAAQ;wBACf,IAAI3H,UAAU;wBAEd,IAAI2H,kBAAkBC,cAAc;gCAExBD;4BADV,sEAAsE;4BACtE3H,UAAU2H,CAAAA,gBAAAA,OAAO/H,KAAK,YAAZ+H,gBAAgBA,OAAO3H,OAAO;wBAC1C,OAAO,IAAI2H,kBAAkBrB,OAAO;gCACaqB;4BAA/C3H,UAAU,YAAY2H,OAAO3H,OAAO,GAAG,OAAQ2H,CAAAA,CAAAA,iBAAAA,OAAO/H,KAAK,YAAZ+H,iBAAgB,EAAC;wBAClE,OAAO;4BACL3H,UAAU,2BAA2B2H;wBACvC;wBAEA1G,QAAQC,IAAI,CAAC,WAAWlB;oBAC1B;gBACF,OAAO;oBACLY,wBAAU,CAAC6C,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAAC6D;SAAS;IACf;IAEAG,IAAAA,gBAAS,EAAC;QACR,MAAMI,YAAYd,aAAaxD,OAAO;QACtC,IAAI,CAACsE,WAAW;QAEhB,MAAMxI,UAAU,CAACc;YACf,IAAI;gBACF,MAAM2B,MAAM7B,KAAKoG,KAAK,CAAClG,MAAMqD,IAAI;gBACjC3B,eACEC,KACApC,aACAqC,yBACAC,QACAC,mBACAC;YAEJ,EAAE,OAAOzC,KAAc;gBACrBqI,IAAAA,+BAAuB,EAAC3H,OAAOV;YACjC;QACF;QAEAoI,UAAUE,gBAAgB,CAAC,WAAW1I;QACtC,OAAO,IAAMwI,UAAUG,mBAAmB,CAAC,WAAW3I;IACxD,GAAG;QACDK;QACAsC;QACA+E;QACAhF;QACAE;KACD;IACD,qBACE,sBAACgG,sDAA0B;QAACtB,aAAaA;;0BACvC,qBAACuB,wCAAmB;gBAACC,iBAAiBvH,wBAAU,CAACwH,gBAAgB;;YAChE1B;;;AAGP", "ignoreList": [0]}