{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/prepare-destination.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { Key } from 'next/dist/compiled/path-to-regexp'\nimport type { NextParsedUrlQuery } from '../../../../server/request-meta'\nimport type { RouteHas } from '../../../../lib/load-custom-routes'\nimport type { BaseNextRequest } from '../../../../server/base-http'\n\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { parseUrl } from './parse-url'\nimport {\n  INTERCEPTION_ROUTE_MARKERS,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\nimport { getCookieParser } from '../../../../server/api-utils/get-cookie-parser'\nimport type { Params } from '../../../../server/request/params'\nimport { safePathToRegexp, safeCompile } from './route-match-utils'\n\n/**\n * Ensure only a-zA-Z are used for param names for proper interpolating\n * with path-to-regexp\n */\nfunction getSafeParamName(paramName: string) {\n  let newParamName = ''\n\n  for (let i = 0; i < paramName.length; i++) {\n    const charCode = paramName.charCodeAt(i)\n\n    if (\n      (charCode > 64 && charCode < 91) || // A-Z\n      (charCode > 96 && charCode < 123) // a-z\n    ) {\n      newParamName += paramName[i]\n    }\n  }\n  return newParamName\n}\n\nfunction escapeSegment(str: string, segmentName: string) {\n  return str.replace(\n    new RegExp(`:${escapeStringRegexp(segmentName)}`, 'g'),\n    `__ESC_COLON_${segmentName}`\n  )\n}\n\nfunction unescapeSegments(str: string) {\n  return str.replace(/__ESC_COLON_/gi, ':')\n}\n\nexport function matchHas(\n  req: BaseNextRequest | IncomingMessage,\n  query: Params,\n  has: RouteHas[] = [],\n  missing: RouteHas[] = []\n): false | Params {\n  const params: Params = {}\n\n  const hasMatch = (hasItem: RouteHas) => {\n    let value\n    let key = hasItem.key\n\n    switch (hasItem.type) {\n      case 'header': {\n        key = key!.toLowerCase()\n        value = req.headers[key] as string\n        break\n      }\n      case 'cookie': {\n        if ('cookies' in req) {\n          value = req.cookies[hasItem.key]\n        } else {\n          const cookies = getCookieParser(req.headers)()\n          value = cookies[hasItem.key]\n        }\n\n        break\n      }\n      case 'query': {\n        value = query[key!]\n        break\n      }\n      case 'host': {\n        const { host } = req?.headers || {}\n        // remove port from host if present\n        const hostname = host?.split(':', 1)[0].toLowerCase()\n        value = hostname\n        break\n      }\n      default: {\n        break\n      }\n    }\n\n    if (!hasItem.value && value) {\n      params[getSafeParamName(key!)] = value\n      return true\n    } else if (value) {\n      const matcher = new RegExp(`^${hasItem.value}$`)\n      const matches = Array.isArray(value)\n        ? value.slice(-1)[0].match(matcher)\n        : value.match(matcher)\n\n      if (matches) {\n        if (Array.isArray(matches)) {\n          if (matches.groups) {\n            Object.keys(matches.groups).forEach((groupKey) => {\n              params[groupKey] = matches.groups![groupKey]\n            })\n          } else if (hasItem.type === 'host' && matches[0]) {\n            params.host = matches[0]\n          }\n        }\n        return true\n      }\n    }\n    return false\n  }\n\n  const allMatch =\n    has.every((item) => hasMatch(item)) &&\n    !missing.some((item) => hasMatch(item))\n\n  if (allMatch) {\n    return params\n  }\n  return false\n}\n\nexport function compileNonPath(value: string, params: Params): string {\n  if (!value.includes(':')) {\n    return value\n  }\n\n  for (const key of Object.keys(params)) {\n    if (value.includes(`:${key}`)) {\n      value = value\n        .replace(\n          new RegExp(`:${key}\\\\*`, 'g'),\n          `:${key}--ESCAPED_PARAM_ASTERISKS`\n        )\n        .replace(\n          new RegExp(`:${key}\\\\?`, 'g'),\n          `:${key}--ESCAPED_PARAM_QUESTION`\n        )\n        .replace(new RegExp(`:${key}\\\\+`, 'g'), `:${key}--ESCAPED_PARAM_PLUS`)\n        .replace(\n          new RegExp(`:${key}(?!\\\\w)`, 'g'),\n          `--ESCAPED_PARAM_COLON${key}`\n        )\n    }\n  }\n  value = value\n    .replace(/(:|\\*|\\?|\\+|\\(|\\)|\\{|\\})/g, '\\\\$1')\n    .replace(/--ESCAPED_PARAM_PLUS/g, '+')\n    .replace(/--ESCAPED_PARAM_COLON/g, ':')\n    .replace(/--ESCAPED_PARAM_QUESTION/g, '?')\n    .replace(/--ESCAPED_PARAM_ASTERISKS/g, '*')\n\n  // the value needs to start with a forward-slash to be compiled\n  // correctly\n  return safeCompile(`/${value}`, { validate: false })(params).slice(1)\n}\n\nexport function parseDestination(args: {\n  destination: string\n  params: Readonly<Params>\n  query: Readonly<NextParsedUrlQuery>\n}) {\n  let escaped = args.destination\n  for (const param of Object.keys({ ...args.params, ...args.query })) {\n    if (!param) continue\n\n    escaped = escapeSegment(escaped, param)\n  }\n\n  const parsed = parseUrl(escaped)\n\n  let pathname = parsed.pathname\n  if (pathname) {\n    pathname = unescapeSegments(pathname)\n  }\n\n  let href = parsed.href\n  if (href) {\n    href = unescapeSegments(href)\n  }\n\n  let hostname = parsed.hostname\n  if (hostname) {\n    hostname = unescapeSegments(hostname)\n  }\n\n  let hash = parsed.hash\n  if (hash) {\n    hash = unescapeSegments(hash)\n  }\n\n  let search = parsed.search\n  if (search) {\n    search = unescapeSegments(search)\n  }\n\n  return {\n    ...parsed,\n    pathname,\n    hostname,\n    href,\n    hash,\n    search,\n  }\n}\n\nexport function prepareDestination(args: {\n  appendParamsToQuery: boolean\n  destination: string\n  params: Params\n  query: NextParsedUrlQuery\n}) {\n  const parsedDestination = parseDestination(args)\n\n  const {\n    hostname: destHostname,\n    query: destQuery,\n    search: destSearch,\n  } = parsedDestination\n\n  // The following code assumes that the pathname here includes the hash if it's\n  // present.\n  let destPath = parsedDestination.pathname\n  if (parsedDestination.hash) {\n    destPath = `${destPath}${parsedDestination.hash}`\n  }\n\n  const destParams: (string | number)[] = []\n\n  const destPathParamKeys: Key[] = []\n  safePathToRegexp(destPath, destPathParamKeys)\n  for (const key of destPathParamKeys) {\n    destParams.push(key.name)\n  }\n\n  if (destHostname) {\n    const destHostnameParamKeys: Key[] = []\n    safePathToRegexp(destHostname, destHostnameParamKeys)\n    for (const key of destHostnameParamKeys) {\n      destParams.push(key.name)\n    }\n  }\n\n  const destPathCompiler = safeCompile(\n    destPath,\n    // we don't validate while compiling the destination since we should\n    // have already validated before we got to this point and validating\n    // breaks compiling destinations with named pattern params from the source\n    // e.g. /something:hello(.*) -> /another/:hello is broken with validation\n    // since compile validation is meant for reversing and not for inserting\n    // params from a separate path-regex into another\n    { validate: false }\n  )\n\n  let destHostnameCompiler\n  if (destHostname) {\n    destHostnameCompiler = safeCompile(destHostname, { validate: false })\n  }\n\n  // update any params in query values\n  for (const [key, strOrArray] of Object.entries(destQuery)) {\n    // the value needs to start with a forward-slash to be compiled\n    // correctly\n    if (Array.isArray(strOrArray)) {\n      destQuery[key] = strOrArray.map((value) =>\n        compileNonPath(unescapeSegments(value), args.params)\n      )\n    } else if (typeof strOrArray === 'string') {\n      destQuery[key] = compileNonPath(unescapeSegments(strOrArray), args.params)\n    }\n  }\n\n  // add path params to query if it's not a redirect and not\n  // already defined in destination query or path\n  let paramKeys = Object.keys(args.params).filter(\n    (name) => name !== 'nextInternalLocale'\n  )\n\n  if (\n    args.appendParamsToQuery &&\n    !paramKeys.some((key) => destParams.includes(key))\n  ) {\n    for (const key of paramKeys) {\n      if (!(key in destQuery)) {\n        destQuery[key] = args.params[key]\n      }\n    }\n  }\n\n  let newUrl\n\n  // The compiler also that the interception route marker is an unnamed param, hence '0',\n  // so we need to add it to the params object.\n  if (isInterceptionRouteAppPath(destPath)) {\n    for (const segment of destPath.split('/')) {\n      const marker = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n        segment.startsWith(m)\n      )\n      if (marker) {\n        if (marker === '(..)(..)') {\n          args.params['0'] = '(..)'\n          args.params['1'] = '(..)'\n        } else {\n          args.params['0'] = marker\n        }\n        break\n      }\n    }\n  }\n\n  try {\n    newUrl = destPathCompiler(args.params)\n\n    const [pathname, hash] = newUrl.split('#', 2)\n    if (destHostnameCompiler) {\n      parsedDestination.hostname = destHostnameCompiler(args.params)\n    }\n    parsedDestination.pathname = pathname\n    parsedDestination.hash = `${hash ? '#' : ''}${hash || ''}`\n    parsedDestination.search = destSearch\n      ? compileNonPath(destSearch, args.params)\n      : ''\n  } catch (err: any) {\n    if (err.message.match(/Expected .*? to not repeat, but got an array/)) {\n      throw new Error(\n        `To use a multi-match in the destination you must add \\`*\\` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match`\n      )\n    }\n    throw err\n  }\n\n  // Query merge order lowest priority to highest\n  // 1. initial URL query values\n  // 2. path segment values\n  // 3. destination specified query values\n  parsedDestination.query = {\n    ...args.query,\n    ...parsedDestination.query,\n  }\n\n  return {\n    newUrl,\n    destQuery,\n    parsedDestination,\n  }\n}\n"], "names": ["compileNonPath", "matchHas", "parseDestination", "prepareDestination", "getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "escapeSegment", "str", "segmentName", "replace", "RegExp", "escapeStringRegexp", "unescapeSegments", "req", "query", "has", "missing", "params", "hasMatch", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "host", "hostname", "split", "matcher", "matches", "Array", "isArray", "slice", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "allMatch", "every", "item", "some", "includes", "safeCompile", "validate", "args", "escaped", "destination", "param", "parsed", "parseUrl", "pathname", "href", "hash", "search", "parsedDestination", "destHostname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destSearch", "destPath", "destParams", "destPathPara<PERSON><PERSON><PERSON>s", "safePathToRegexp", "push", "name", "destHostnameParamKeys", "destPathCompiler", "destHostnameCompiler", "strOrArray", "entries", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "appendParamsToQuery", "newUrl", "isInterceptionRouteAppPath", "segment", "marker", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "err", "message", "Error"], "mappings": ";;;;;;;;;;;;;;;;;IA8HgBA,cAAc;eAAdA;;IA/EAC,QAAQ;eAARA;;IAkHAC,gBAAgB;eAAhBA;;IAiDAC,kBAAkB;eAAlBA;;;8BA5MmB;0BACV;oCAIlB;iCACyB;iCAEc;AAE9C;;;CAGC,GACD,SAASC,iBAAiBC,SAAiB;IACzC,IAAIC,eAAe;IAEnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;QACzC,MAAME,WAAWJ,UAAUK,UAAU,CAACH;QAEtC,IACE,AAACE,WAAW,MAAMA,WAAW,MAAO,MAAM;QACzCA,WAAW,MAAMA,WAAW,IAAK,MAAM;UACxC;YACAH,gBAAgBD,SAAS,CAACE,EAAE;QAC9B;IACF;IACA,OAAOD;AACT;AAEA,SAASK,cAAcC,GAAW,EAAEC,WAAmB;IACrD,OAAOD,IAAIE,OAAO,CAChB,IAAIC,OAAO,AAAC,MAAGC,IAAAA,gCAAkB,EAACH,cAAgB,MAClD,AAAC,iBAAcA;AAEnB;AAEA,SAASI,iBAAiBL,GAAW;IACnC,OAAOA,IAAIE,OAAO,CAAC,kBAAkB;AACvC;AAEO,SAASb,SACdiB,GAAsC,EACtCC,KAAa,EACbC,GAAoB,EACpBC,OAAwB;IADxBD,IAAAA,gBAAAA,MAAkB,EAAE;IACpBC,IAAAA,oBAAAA,UAAsB,EAAE;IAExB,MAAMC,SAAiB,CAAC;IAExB,MAAMC,WAAW,CAACC;QAChB,IAAIC;QACJ,IAAIC,MAAMF,QAAQE,GAAG;QAErB,OAAQF,QAAQG,IAAI;YAClB,KAAK;gBAAU;oBACbD,MAAMA,IAAKE,WAAW;oBACtBH,QAAQP,IAAIW,OAAO,CAACH,IAAI;oBACxB;gBACF;YACA,KAAK;gBAAU;oBACb,IAAI,aAAaR,KAAK;wBACpBO,QAAQP,IAAIY,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAClC,OAAO;wBACL,MAAMI,UAAUC,IAAAA,gCAAe,EAACb,IAAIW,OAAO;wBAC3CJ,QAAQK,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAC9B;oBAEA;gBACF;YACA,KAAK;gBAAS;oBACZD,QAAQN,KAAK,CAACO,IAAK;oBACnB;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,EAAEM,IAAI,EAAE,GAAGd,CAAAA,uBAAAA,IAAKW,OAAO,KAAI,CAAC;oBAClC,mCAAmC;oBACnC,MAAMI,WAAWD,wBAAAA,KAAME,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACN,WAAW;oBACnDH,QAAQQ;oBACR;gBACF;YACA;gBAAS;oBACP;gBACF;QACF;QAEA,IAAI,CAACT,QAAQC,KAAK,IAAIA,OAAO;YAC3BH,MAAM,CAAClB,iBAAiBsB,KAAM,GAAGD;YACjC,OAAO;QACT,OAAO,IAAIA,OAAO;YAChB,MAAMU,UAAU,IAAIpB,OAAO,AAAC,MAAGS,QAAQC,KAAK,GAAC;YAC7C,MAAMW,UAAUC,MAAMC,OAAO,CAACb,SAC1BA,MAAMc,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAACL,WACzBV,MAAMe,KAAK,CAACL;YAEhB,IAAIC,SAAS;gBACX,IAAIC,MAAMC,OAAO,CAACF,UAAU;oBAC1B,IAAIA,QAAQK,MAAM,EAAE;wBAClBC,OAAOC,IAAI,CAACP,QAAQK,MAAM,EAAEG,OAAO,CAAC,CAACC;4BACnCvB,MAAM,CAACuB,SAAS,GAAGT,QAAQK,MAAM,AAAC,CAACI,SAAS;wBAC9C;oBACF,OAAO,IAAIrB,QAAQG,IAAI,KAAK,UAAUS,OAAO,CAAC,EAAE,EAAE;wBAChDd,OAAOU,IAAI,GAAGI,OAAO,CAAC,EAAE;oBAC1B;gBACF;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAMU,WACJ1B,IAAI2B,KAAK,CAAC,CAACC,OAASzB,SAASyB,UAC7B,CAAC3B,QAAQ4B,IAAI,CAAC,CAACD,OAASzB,SAASyB;IAEnC,IAAIF,UAAU;QACZ,OAAOxB;IACT;IACA,OAAO;AACT;AAEO,SAAStB,eAAeyB,KAAa,EAAEH,MAAc;IAC1D,IAAI,CAACG,MAAMyB,QAAQ,CAAC,MAAM;QACxB,OAAOzB;IACT;IAEA,KAAK,MAAMC,OAAOgB,OAAOC,IAAI,CAACrB,QAAS;QACrC,IAAIG,MAAMyB,QAAQ,CAAC,AAAC,MAAGxB,MAAQ;YAC7BD,QAAQA,MACLX,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,6BAETZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,4BAETZ,OAAO,CAAC,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MAAM,AAAC,MAAGA,MAAI,wBAC/CZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,WAAU,MAC7B,AAAC,0BAAuBA;QAE9B;IACF;IACAD,QAAQA,MACLX,OAAO,CAAC,6BAA6B,QACrCA,OAAO,CAAC,yBAAyB,KACjCA,OAAO,CAAC,0BAA0B,KAClCA,OAAO,CAAC,6BAA6B,KACrCA,OAAO,CAAC,8BAA8B;IAEzC,+DAA+D;IAC/D,YAAY;IACZ,OAAOqC,IAAAA,4BAAW,EAAC,AAAC,MAAG1B,OAAS;QAAE2B,UAAU;IAAM,GAAG9B,QAAQiB,KAAK,CAAC;AACrE;AAEO,SAASrC,iBAAiBmD,IAIhC;IACC,IAAIC,UAAUD,KAAKE,WAAW;IAC9B,KAAK,MAAMC,SAASd,OAAOC,IAAI,CAAC;QAAE,GAAGU,KAAK/B,MAAM;QAAE,GAAG+B,KAAKlC,KAAK;IAAC,GAAI;QAClE,IAAI,CAACqC,OAAO;QAEZF,UAAU3C,cAAc2C,SAASE;IACnC;IAEA,MAAMC,SAASC,IAAAA,kBAAQ,EAACJ;IAExB,IAAIK,WAAWF,OAAOE,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAW1C,iBAAiB0C;IAC9B;IAEA,IAAIC,OAAOH,OAAOG,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO3C,iBAAiB2C;IAC1B;IAEA,IAAI3B,WAAWwB,OAAOxB,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAWhB,iBAAiBgB;IAC9B;IAEA,IAAI4B,OAAOJ,OAAOI,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO5C,iBAAiB4C;IAC1B;IAEA,IAAIC,SAASL,OAAOK,MAAM;IAC1B,IAAIA,QAAQ;QACVA,SAAS7C,iBAAiB6C;IAC5B;IAEA,OAAO;QACL,GAAGL,MAAM;QACTE;QACA1B;QACA2B;QACAC;QACAC;IACF;AACF;AAEO,SAAS3D,mBAAmBkD,IAKlC;IACC,MAAMU,oBAAoB7D,iBAAiBmD;IAE3C,MAAM,EACJpB,UAAU+B,YAAY,EACtB7C,OAAO8C,SAAS,EAChBH,QAAQI,UAAU,EACnB,GAAGH;IAEJ,8EAA8E;IAC9E,WAAW;IACX,IAAII,WAAWJ,kBAAkBJ,QAAQ;IACzC,IAAII,kBAAkBF,IAAI,EAAE;QAC1BM,WAAW,AAAC,KAAEA,WAAWJ,kBAAkBF,IAAI;IACjD;IAEA,MAAMO,aAAkC,EAAE;IAE1C,MAAMC,oBAA2B,EAAE;IACnCC,IAAAA,iCAAgB,EAACH,UAAUE;IAC3B,KAAK,MAAM3C,OAAO2C,kBAAmB;QACnCD,WAAWG,IAAI,CAAC7C,IAAI8C,IAAI;IAC1B;IAEA,IAAIR,cAAc;QAChB,MAAMS,wBAA+B,EAAE;QACvCH,IAAAA,iCAAgB,EAACN,cAAcS;QAC/B,KAAK,MAAM/C,OAAO+C,sBAAuB;YACvCL,WAAWG,IAAI,CAAC7C,IAAI8C,IAAI;QAC1B;IACF;IAEA,MAAME,mBAAmBvB,IAAAA,4BAAW,EAClCgB,UACA,oEAAoE;IACpE,oEAAoE;IACpE,0EAA0E;IAC1E,yEAAyE;IACzE,wEAAwE;IACxE,iDAAiD;IACjD;QAAEf,UAAU;IAAM;IAGpB,IAAIuB;IACJ,IAAIX,cAAc;QAChBW,uBAAuBxB,IAAAA,4BAAW,EAACa,cAAc;YAAEZ,UAAU;QAAM;IACrE;IAEA,oCAAoC;IACpC,KAAK,MAAM,CAAC1B,KAAKkD,WAAW,IAAIlC,OAAOmC,OAAO,CAACZ,WAAY;QACzD,+DAA+D;QAC/D,YAAY;QACZ,IAAI5B,MAAMC,OAAO,CAACsC,aAAa;YAC7BX,SAAS,CAACvC,IAAI,GAAGkD,WAAWE,GAAG,CAAC,CAACrD,QAC/BzB,eAAeiB,iBAAiBQ,QAAQ4B,KAAK/B,MAAM;QAEvD,OAAO,IAAI,OAAOsD,eAAe,UAAU;YACzCX,SAAS,CAACvC,IAAI,GAAG1B,eAAeiB,iBAAiB2D,aAAavB,KAAK/B,MAAM;QAC3E;IACF;IAEA,0DAA0D;IAC1D,+CAA+C;IAC/C,IAAIyD,YAAYrC,OAAOC,IAAI,CAACU,KAAK/B,MAAM,EAAE0D,MAAM,CAC7C,CAACR,OAASA,SAAS;IAGrB,IACEnB,KAAK4B,mBAAmB,IACxB,CAACF,UAAU9B,IAAI,CAAC,CAACvB,MAAQ0C,WAAWlB,QAAQ,CAACxB,OAC7C;QACA,KAAK,MAAMA,OAAOqD,UAAW;YAC3B,IAAI,CAAErD,CAAAA,OAAOuC,SAAQ,GAAI;gBACvBA,SAAS,CAACvC,IAAI,GAAG2B,KAAK/B,MAAM,CAACI,IAAI;YACnC;QACF;IACF;IAEA,IAAIwD;IAEJ,uFAAuF;IACvF,6CAA6C;IAC7C,IAAIC,IAAAA,8CAA0B,EAAChB,WAAW;QACxC,KAAK,MAAMiB,WAAWjB,SAASjC,KAAK,CAAC,KAAM;YACzC,MAAMmD,SAASC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IAC9CJ,QAAQK,UAAU,CAACD;YAErB,IAAIH,QAAQ;gBACV,IAAIA,WAAW,YAAY;oBACzBhC,KAAK/B,MAAM,CAAC,IAAI,GAAG;oBACnB+B,KAAK/B,MAAM,CAAC,IAAI,GAAG;gBACrB,OAAO;oBACL+B,KAAK/B,MAAM,CAAC,IAAI,GAAG+D;gBACrB;gBACA;YACF;QACF;IACF;IAEA,IAAI;QACFH,SAASR,iBAAiBrB,KAAK/B,MAAM;QAErC,MAAM,CAACqC,UAAUE,KAAK,GAAGqB,OAAOhD,KAAK,CAAC,KAAK;QAC3C,IAAIyC,sBAAsB;YACxBZ,kBAAkB9B,QAAQ,GAAG0C,qBAAqBtB,KAAK/B,MAAM;QAC/D;QACAyC,kBAAkBJ,QAAQ,GAAGA;QAC7BI,kBAAkBF,IAAI,GAAG,AAAC,KAAEA,CAAAA,OAAO,MAAM,EAAC,IAAIA,CAAAA,QAAQ,EAAC;QACvDE,kBAAkBD,MAAM,GAAGI,aACvBlE,eAAekE,YAAYb,KAAK/B,MAAM,IACtC;IACN,EAAE,OAAOoE,KAAU;QACjB,IAAIA,IAAIC,OAAO,CAACnD,KAAK,CAAC,iDAAiD;YACrE,MAAM,qBAEL,CAFK,IAAIoD,MACP,4KADG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMF;IACR;IAEA,+CAA+C;IAC/C,8BAA8B;IAC9B,yBAAyB;IACzB,wCAAwC;IACxC3B,kBAAkB5C,KAAK,GAAG;QACxB,GAAGkC,KAAKlC,KAAK;QACb,GAAG4C,kBAAkB5C,KAAK;IAC5B;IAEA,OAAO;QACL+D;QACAjB;QACAF;IACF;AACF", "ignoreList": [0]}