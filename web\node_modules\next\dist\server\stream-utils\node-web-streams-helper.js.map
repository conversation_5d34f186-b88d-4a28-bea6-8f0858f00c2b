{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import type { ReactDOMServerReadableStream } from 'react-dom/server'\nimport { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encoded-tags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\nimport { insertBuildIdComment } from '../../shared/lib/segment-cache/output-export-prefetch-encoding'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // If we have no streams, return an empty stream. This behavior is\n  // intentional as we're now providing the `RenderResult.EMPTY` value.\n  if (streams.length === 0) {\n    return new ReadableStream<T>({\n      start(controller) {\n        controller.close()\n      },\n    })\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nfunction createPrefetchCommentStream(\n  isBuildTimePrerendering: boolean,\n  buildId: string\n): TransformStream<Uint8Array, Uint8Array> {\n  // Insert an extra comment at the beginning of the HTML document. This must\n  // come after the DOCTYPE, which is inserted by React.\n  //\n  // The first chunk sent by React will contain the doctype. After that, we can\n  // pass through the rest of the chunks as-is.\n  let didTransformFirstChunk = false\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (isBuildTimePrerendering && !didTransformFirstChunk) {\n        didTransformFirstChunk = true\n        const decoder = new TextDecoder('utf-8', { fatal: true })\n        const chunkStr = decoder.decode(chunk, {\n          stream: true,\n        })\n        const updatedChunkStr = insertBuildIdComment(chunkStr, buildId)\n        controller.enqueue(encoder.encode(updatedChunkStr))\n        return\n      }\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: {\n    renderToReadableStream: typeof import('react-dom/server').renderToReadableStream\n  }\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactDOMServerReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createMetadataTransformStream(\n  insert: () => Promise<string> | string\n): TransformStream<Uint8Array, Uint8Array> {\n  let chunkIndex = -1\n  let isMarkRemoved = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      let iconMarkIndex = -1\n      let closedHeadIndex = -1\n      chunkIndex++\n\n      if (isMarkRemoved) {\n        controller.enqueue(chunk)\n        return\n      }\n      let iconMarkLength = 0\n      // Only search for the closed head tag once\n      if (iconMarkIndex === -1) {\n        iconMarkIndex = indexOfUint8Array(chunk, ENCODED_TAGS.META.ICON_MARK)\n        if (iconMarkIndex === -1) {\n          controller.enqueue(chunk)\n          return\n        } else {\n          // When we found the `<meta name=\"«nxt-icon»\"` tag prefix, we will remove it from the chunk.\n          // Its close tag could either be `/>` or `>`, checking the next char to ensure we cover both cases.\n          iconMarkLength = ENCODED_TAGS.META.ICON_MARK.length\n          // Check if next char is /, this is for xml mode.\n          if (chunk[iconMarkIndex + iconMarkLength] === 47) {\n            iconMarkLength += 2\n          } else {\n            // The last char is `>`\n            iconMarkLength++\n          }\n        }\n      }\n\n      // Check if icon mark is inside <head> tag in the first chunk.\n      if (chunkIndex === 0) {\n        closedHeadIndex = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        if (iconMarkIndex !== -1) {\n          // The mark icon is located in the 1st chunk before the head tag.\n          // We do not need to insert the script tag in this case because it's in the head.\n          // Just remove the icon mark from the chunk.\n          if (iconMarkIndex < closedHeadIndex) {\n            const replaced = new Uint8Array(chunk.length - iconMarkLength)\n\n            // Remove the icon mark from the chunk.\n            replaced.set(chunk.subarray(0, iconMarkIndex))\n            replaced.set(\n              chunk.subarray(iconMarkIndex + iconMarkLength),\n              iconMarkIndex\n            )\n            chunk = replaced\n          } else {\n            // The icon mark is after the head tag, replace and insert the script tag at that position.\n            const insertion = await insert()\n            const encodedInsertion = encoder.encode(insertion)\n            const insertionLength = encodedInsertion.length\n            const replaced = new Uint8Array(\n              chunk.length - iconMarkLength + insertionLength\n            )\n            replaced.set(chunk.subarray(0, iconMarkIndex))\n            replaced.set(encodedInsertion, iconMarkIndex)\n            replaced.set(\n              chunk.subarray(iconMarkIndex + iconMarkLength),\n              iconMarkIndex + insertionLength\n            )\n            chunk = replaced\n          }\n          isMarkRemoved = true\n        }\n        // If there's no icon mark located, it will be handled later when if present in the following chunks.\n      } else {\n        // When it's appeared in the following chunks, we'll need to\n        // remove the mark and then insert the script tag at that position.\n        const insertion = await insert()\n        const encodedInsertion = encoder.encode(insertion)\n        const insertionLength = encodedInsertion.length\n        // Replace the icon mark with the hoist script or empty string.\n        const replaced = new Uint8Array(\n          chunk.length - iconMarkLength + insertionLength\n        )\n        // Set the first part of the chunk, before the icon mark.\n        replaced.set(chunk.subarray(0, iconMarkIndex))\n        // Set the insertion after the icon mark.\n        replaced.set(encodedInsertion, iconMarkIndex)\n\n        // Set the rest of the chunk after the icon mark.\n        replaced.set(\n          chunk.subarray(iconMarkIndex + iconMarkLength),\n          iconMarkIndex + insertionLength\n        )\n        chunk = replaced\n        isMarkRemoved = true\n      }\n      controller.enqueue(chunk)\n    },\n  })\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\nfunction createFlightDataInjectionTransformStream(\n  stream: ReadableStream<Uint8Array>,\n  delayDataUntilFirstHtmlChunk: boolean\n): TransformStream<Uint8Array, Uint8Array> {\n  let htmlStreamFinished = false\n\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  function startOrContinuePulling(\n    controller: TransformStreamDefaultController\n  ) {\n    if (!pull) {\n      pull = startPulling(controller)\n    }\n    return pull\n  }\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    const reader = stream.getReader()\n\n    if (delayDataUntilFirstHtmlChunk) {\n      // NOTE: streaming flush\n      // We are buffering here for the inlined data stream because the\n      // \"shell\" stream might be chunkenized again by the underlying stream\n      // implementation, e.g. with a specific high-water mark. To ensure it's\n      // the safe timing to pipe the data stream, this extra tick is\n      // necessary.\n\n      // We don't start reading until we've left the current Task to ensure\n      // that it's inserted after flushing the shell. Note that this implementation\n      // might get stale if impl details of Fizz change in the future.\n      await atLeastOneTask()\n    }\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        // We want to prioritize HTML over RSC data.\n        // The SSR render is based on the same RSC stream, so when we get a new RSC chunk,\n        // we're likely to produce an HTML chunk as well, so give it a chance to flush first.\n        if (!delayDataUntilFirstHtmlChunk && !htmlStreamFinished) {\n          await atLeastOneTask()\n        }\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    start(controller) {\n      if (!delayDataUntilFirstHtmlChunk) {\n        startOrContinuePulling(controller)\n      }\n    },\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (delayDataUntilFirstHtmlChunk) {\n        startOrContinuePulling(controller)\n      }\n    },\n    flush(controller) {\n      htmlStreamFinished = true\n      if (donePulling) {\n        return\n      }\n      return startOrContinuePulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  isBuildTimePrerendering: boolean\n  buildId: string\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactDOMServerReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    isBuildTimePrerendering,\n    buildId,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML we need to wait for it to resolve before continuing.\n  if (isStaticGeneration) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Add build id comment to start of the HTML document (in export mode)\n    createPrefetchCommentStream(isBuildTimePrerendering, buildId),\n\n    // Transform metadata\n    createMetadataTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream\n      ? createFlightDataInjectionTransformStream(inlinedDataStream, true)\n      : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  isBuildTimePrerendering: boolean\n  buildId: string\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    isBuildTimePrerendering,\n    buildId,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Add build id comment to start of the HTML document (in export mode)\n      .pipeThrough(\n        createPrefetchCommentStream(isBuildTimePrerendering, buildId)\n      )\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(\n        createFlightDataInjectionTransformStream(inlinedDataStream, true)\n      )\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  delayDataUntilFirstHtmlChunk: boolean\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    delayDataUntilFirstHtmlChunk,\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Transform metadata\n      .pipeThrough(createMetadataTransformStream(getServerInsertedMetadata))\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(\n        createFlightDataInjectionTransformStream(\n          inlinedDataStream,\n          delayDataUntilFirstHtmlChunk\n        )\n      )\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["chainStreams", "continueDynamicHTMLResume", "continueDynamicPrerender", "continueFizzStream", "continueStaticP<PERSON><PERSON>", "createBufferedTransformStream", "createDocumentClosingStream", "createRootLayoutValidatorStream", "renderToInitialFizzStream", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "voidCatch", "encoder", "TextEncoder", "streams", "length", "ReadableStream", "start", "controller", "close", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "str", "enqueue", "encode", "chunk", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Detached<PERSON>romise", "scheduleImmediate", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "createPrefetchCommentStream", "isBuildTimePrerendering", "buildId", "didTransformFirstChunk", "chunkStr", "updatedChunkStr", "insertBuildIdComment", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createMetadataTransformStream", "insert", "chunkIndex", "isMarkRemoved", "iconMarkIndex", "closedHeadIndex", "iconMarkLength", "indexOfUint8Array", "ENCODED_TAGS", "META", "ICON_MARK", "CLOSED", "HEAD", "replaced", "subarray", "insertion", "encodedInsertion", "<PERSON><PERSON><PERSON><PERSON>", "createHeadInsertionTransformStream", "inserted", "hasBytes", "index", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createFlightDataInjectionTransformStream", "delayDataUntilFirstHtmlChunk", "htmlStreamFinished", "pull", "donePulling", "startOrContinuePulling", "startPulling", "atLeastOneTask", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "isEquivalentUint8Arrays", "BODY", "HTML", "removeFromUint8Array", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "MISSING_ROOT_TAGS_ERROR", "chainTransformers", "transformers", "transformer", "pipeThrough", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "prerenderStream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAyBgBA,YAAY;eAAZA;;IA2xBMC,yBAAyB;eAAzBA;;IAjEAC,wBAAwB;eAAxBA;;IA3DAC,kBAAkB;eAAlBA;;IAsFAC,uBAAuB;eAAvBA;;IAjpBNC,6BAA6B;eAA7BA;;IAotBAC,2BAA2B;eAA3BA;;IAvOAC,+BAA+B;eAA/BA;;IAxZAC,yBAAyB;eAAzBA;;IApIAC,gBAAgB;eAAhBA;;IATAC,gBAAgB;eAAhBA;;IAkBMC,cAAc;eAAdA;;IAkBAC,cAAc;eAAdA;;;wBAxGI;2BACI;iCACE;2BACkB;6BACrB;mCAKtB;4BACiC;8CACH;AAErC,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAEA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASf,aACd,GAAGgB,OAA4B;IAE/B,kEAAkE;IAClE,qEAAqE;IACrE,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAO,IAAIC,eAAkB;YAC3BC,OAAMC,UAAU;gBACdA,WAAWC,KAAK;YAClB;QACF;IACF;IAEA,yEAAyE;IACzE,IAAIL,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEM,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUT,OAAO,CAAC,EAAE,CAACU,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIZ,QAAQC,MAAM,GAAG,GAAGW,IAAK;QAClC,MAAMC,aAAab,OAAO,CAACY,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaf,OAAO,CAACY,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAACnB;IAEd,OAAOS;AACT;AAEO,SAASZ,iBAAiBuB,GAAW;IAC1C,OAAO,IAAIf,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWc,OAAO,CAACpB,QAAQqB,MAAM,CAACF;YAClCb,WAAWC,KAAK;QAClB;IACF;AACF;AAEO,SAASZ,iBAAiB2B,KAAa;IAC5C,OAAO,IAAIlB,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWc,OAAO,CAACE;YACnBhB,WAAWC,KAAK;QAClB;IACF;AACF;AAEO,eAAeV,eACpB0B,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEO,eAAe5B,eACpByB,MAAkC,EAClCU,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMf,SAASC,OAAQ;QAChC,IAAIU,0BAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACjB,OAAO;YAAEC,QAAQ;QAAK;IACjD;IAEAc,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAAS9C;IAId,IAAIiD,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAACrC;QACb,yDAAyD;QACzD,IAAIoC,SAAS;QAEb,MAAME,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACF,MAAMxB,QAAQ,IAAIyB,WAAWN;gBAC7B,IAAIO,cAAc;gBAElB,IAAK,IAAIlC,IAAI,GAAGA,IAAI0B,eAAerC,MAAM,EAAEW,IAAK;oBAC9C,MAAMmC,gBAAgBT,cAAc,CAAC1B,EAAE;oBACvCQ,MAAM4B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5EX,eAAerC,MAAM,GAAG;gBACxBsC,mBAAmB;gBACnBnC,WAAWc,OAAO,CAACE;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRoB,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI3C,gBAAgB;QACzB4C,WAAUhC,KAAK,EAAEhB,UAAU;YACzB,kDAAkD;YAClDkC,eAAeV,IAAI,CAACR;YACpBmB,oBAAoBnB,MAAM6B,UAAU;YAEpC,sCAAsC;YACtCR,MAAMrC;QACR;QACAqC;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQ/B,OAAO;QACxB;IACF;AACF;AAEA,SAAS4C,4BACPC,uBAAgC,EAChCC,OAAe;IAEf,2EAA2E;IAC3E,sDAAsD;IACtD,EAAE;IACF,6EAA6E;IAC7E,6CAA6C;IAC7C,IAAIC,yBAAyB;IAC7B,OAAO,IAAIhD,gBAAgB;QACzB4C,WAAUhC,KAAK,EAAEhB,UAAU;YACzB,IAAIkD,2BAA2B,CAACE,wBAAwB;gBACtDA,yBAAyB;gBACzB,MAAMxB,UAAU,IAAIC,YAAY,SAAS;oBAAEC,OAAO;gBAAK;gBACvD,MAAMuB,WAAWzB,QAAQK,MAAM,CAACjB,OAAO;oBACrCC,QAAQ;gBACV;gBACA,MAAMqC,kBAAkBC,IAAAA,kDAAoB,EAACF,UAAUF;gBACvDnD,WAAWc,OAAO,CAACpB,QAAQqB,MAAM,CAACuC;gBAClC;YACF;YACAtD,WAAWc,OAAO,CAACE;QACrB;IACF;AACF;AAEO,SAAS5B,0BAA0B,EACxCoE,cAAc,EACdC,OAAO,EACPC,aAAa,EAOd;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,wBAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,8BACPC,MAAsC;IAEtC,IAAIC,aAAa,CAAC;IAClB,IAAIC,gBAAgB;IAEpB,OAAO,IAAI9D,gBAAgB;QACzB,MAAM4C,WAAUhC,KAAK,EAAEhB,UAAU;YAC/B,IAAImE,gBAAgB,CAAC;YACrB,IAAIC,kBAAkB,CAAC;YACvBH;YAEA,IAAIC,eAAe;gBACjBlE,WAAWc,OAAO,CAACE;gBACnB;YACF;YACA,IAAIqD,iBAAiB;YACrB,2CAA2C;YAC3C,IAAIF,kBAAkB,CAAC,GAAG;gBACxBA,gBAAgBG,IAAAA,oCAAiB,EAACtD,OAAOuD,yBAAY,CAACC,IAAI,CAACC,SAAS;gBACpE,IAAIN,kBAAkB,CAAC,GAAG;oBACxBnE,WAAWc,OAAO,CAACE;oBACnB;gBACF,OAAO;oBACL,4FAA4F;oBAC5F,mGAAmG;oBACnGqD,iBAAiBE,yBAAY,CAACC,IAAI,CAACC,SAAS,CAAC5E,MAAM;oBACnD,iDAAiD;oBACjD,IAAImB,KAAK,CAACmD,gBAAgBE,eAAe,KAAK,IAAI;wBAChDA,kBAAkB;oBACpB,OAAO;wBACL,uBAAuB;wBACvBA;oBACF;gBACF;YACF;YAEA,8DAA8D;YAC9D,IAAIJ,eAAe,GAAG;gBACpBG,kBAAkBE,IAAAA,oCAAiB,EAACtD,OAAOuD,yBAAY,CAACG,MAAM,CAACC,IAAI;gBACnE,IAAIR,kBAAkB,CAAC,GAAG;oBACxB,iEAAiE;oBACjE,iFAAiF;oBACjF,4CAA4C;oBAC5C,IAAIA,gBAAgBC,iBAAiB;wBACnC,MAAMQ,WAAW,IAAInC,WAAWzB,MAAMnB,MAAM,GAAGwE;wBAE/C,uCAAuC;wBACvCO,SAAShC,GAAG,CAAC5B,MAAM6D,QAAQ,CAAC,GAAGV;wBAC/BS,SAAShC,GAAG,CACV5B,MAAM6D,QAAQ,CAACV,gBAAgBE,iBAC/BF;wBAEFnD,QAAQ4D;oBACV,OAAO;wBACL,2FAA2F;wBAC3F,MAAME,YAAY,MAAMd;wBACxB,MAAMe,mBAAmBrF,QAAQqB,MAAM,CAAC+D;wBACxC,MAAME,kBAAkBD,iBAAiBlF,MAAM;wBAC/C,MAAM+E,WAAW,IAAInC,WACnBzB,MAAMnB,MAAM,GAAGwE,iBAAiBW;wBAElCJ,SAAShC,GAAG,CAAC5B,MAAM6D,QAAQ,CAAC,GAAGV;wBAC/BS,SAAShC,GAAG,CAACmC,kBAAkBZ;wBAC/BS,SAAShC,GAAG,CACV5B,MAAM6D,QAAQ,CAACV,gBAAgBE,iBAC/BF,gBAAgBa;wBAElBhE,QAAQ4D;oBACV;oBACAV,gBAAgB;gBAClB;YACA,qGAAqG;YACvG,OAAO;gBACL,4DAA4D;gBAC5D,mEAAmE;gBACnE,MAAMY,YAAY,MAAMd;gBACxB,MAAMe,mBAAmBrF,QAAQqB,MAAM,CAAC+D;gBACxC,MAAME,kBAAkBD,iBAAiBlF,MAAM;gBAC/C,+DAA+D;gBAC/D,MAAM+E,WAAW,IAAInC,WACnBzB,MAAMnB,MAAM,GAAGwE,iBAAiBW;gBAElC,yDAAyD;gBACzDJ,SAAShC,GAAG,CAAC5B,MAAM6D,QAAQ,CAAC,GAAGV;gBAC/B,yCAAyC;gBACzCS,SAAShC,GAAG,CAACmC,kBAAkBZ;gBAE/B,iDAAiD;gBACjDS,SAAShC,GAAG,CACV5B,MAAM6D,QAAQ,CAACV,gBAAgBE,iBAC/BF,gBAAgBa;gBAElBhE,QAAQ4D;gBACRV,gBAAgB;YAClB;YACAlE,WAAWc,OAAO,CAACE;QACrB;IACF;AACF;AAEA,SAASiE,mCACPjB,MAA6B;IAE7B,IAAIkB,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAI/E,gBAAgB;QACzB,MAAM4C,WAAUhC,KAAK,EAAEhB,UAAU;YAC/BmF,WAAW;YAEX,MAAML,YAAY,MAAMd;YACxB,IAAIkB,UAAU;gBACZ,IAAIJ,WAAW;oBACb,MAAMC,mBAAmBrF,QAAQqB,MAAM,CAAC+D;oBACxC9E,WAAWc,OAAO,CAACiE;gBACrB;gBACA/E,WAAWc,OAAO,CAACE;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAMoE,QAAQd,IAAAA,oCAAiB,EAACtD,OAAOuD,yBAAY,CAACG,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIS,UAAU,CAAC,GAAG;oBAChB,IAAIN,WAAW;wBACb,MAAMC,mBAAmBrF,QAAQqB,MAAM,CAAC+D;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMO,sBAAsB,IAAI5C,WAC9BzB,MAAMnB,MAAM,GAAGkF,iBAAiBlF,MAAM;wBAExC,0DAA0D;wBAC1DwF,oBAAoBzC,GAAG,CAAC5B,MAAMsE,KAAK,CAAC,GAAGF;wBACvC,qCAAqC;wBACrCC,oBAAoBzC,GAAG,CAACmC,kBAAkBK;wBAC1C,+BAA+B;wBAC/BC,oBAAoBzC,GAAG,CACrB5B,MAAMsE,KAAK,CAACF,QACZA,QAAQL,iBAAiBlF,MAAM;wBAEjCG,WAAWc,OAAO,CAACuE;oBACrB,OAAO;wBACLrF,WAAWc,OAAO,CAACE;oBACrB;oBACAkE,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIJ,WAAW;wBACb9E,WAAWc,OAAO,CAACpB,QAAQqB,MAAM,CAAC+D;oBACpC;oBACA9E,WAAWc,OAAO,CAACE;oBACnBkE,WAAW;gBACb;YACF;QACF;QACA,MAAM7C,OAAMrC,UAAU;YACpB,gEAAgE;YAChE,IAAImF,UAAU;gBACZ,MAAML,YAAY,MAAMd;gBACxB,IAAIc,WAAW;oBACb9E,WAAWc,OAAO,CAACpB,QAAQqB,MAAM,CAAC+D;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAIrD;IAEJ,MAAMC,QAAQ,CAACrC;QACb,MAAMsC,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACFxC,WAAWc,OAAO,CAACpB,QAAQqB,MAAM,CAACyE;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRpD,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI3C,gBAAgB;QACzB4C,WAAUhC,KAAK,EAAEhB,UAAU;YACzBA,WAAWc,OAAO,CAACE;YAEnB,wCAAwC;YACxC,IAAIyE,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVpD,MAAMrC;QACR;QACAqC,OAAMrC,UAAU;YACd,IAAIoC,SAAS,OAAOA,QAAQ/B,OAAO;YACnC,IAAIoF,SAAS;YAEb,aAAa;YACbzF,WAAWc,OAAO,CAACpB,QAAQqB,MAAM,CAACyE;QACpC;IACF;AACF;AAEA,SAASE,yCACPzE,MAAkC,EAClC0E,4BAAqC;IAErC,IAAIC,qBAAqB;IAEzB,IAAIC,OAA6B;IACjC,IAAIC,cAAc;IAElB,SAASC,uBACP/F,UAA4C;QAE5C,IAAI,CAAC6F,MAAM;YACTA,OAAOG,aAAahG;QACtB;QACA,OAAO6F;IACT;IAEA,eAAeG,aAAahG,UAA4C;QACtE,MAAMkB,SAASD,OAAOE,SAAS;QAE/B,IAAIwE,8BAA8B;YAChC,wBAAwB;YACxB,gEAAgE;YAChE,qEAAqE;YACrE,uEAAuE;YACvE,8DAA8D;YAC9D,aAAa;YAEb,qEAAqE;YACrE,6EAA6E;YAC7E,gEAAgE;YAChE,MAAMM,IAAAA,yBAAc;QACtB;QAEA,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE5E,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRyE,cAAc;oBACd;gBACF;gBAEA,4CAA4C;gBAC5C,kFAAkF;gBAClF,qFAAqF;gBACrF,IAAI,CAACH,gCAAgC,CAACC,oBAAoB;oBACxD,MAAMK,IAAAA,yBAAc;gBACtB;gBACAjG,WAAWc,OAAO,CAACQ;YACrB;QACF,EAAE,OAAO4E,KAAK;YACZlG,WAAWmG,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI9F,gBAAgB;QACzBL,OAAMC,UAAU;YACd,IAAI,CAAC2F,8BAA8B;gBACjCI,uBAAuB/F;YACzB;QACF;QACAgD,WAAUhC,KAAK,EAAEhB,UAAU;YACzBA,WAAWc,OAAO,CAACE;YAEnB,6DAA6D;YAC7D,IAAI2E,8BAA8B;gBAChCI,uBAAuB/F;YACzB;QACF;QACAqC,OAAMrC,UAAU;YACd4F,qBAAqB;YACrB,IAAIE,aAAa;gBACf;YACF;YACA,OAAOC,uBAAuB/F;QAChC;IACF;AACF;AAEA,MAAMoG,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIlG,gBAAgB;QACzB4C,WAAUhC,KAAK,EAAEhB,UAAU;YACzB,IAAIsG,aAAa;gBACf,OAAOtG,WAAWc,OAAO,CAACE;YAC5B;YAEA,MAAMoE,QAAQd,IAAAA,oCAAiB,EAACtD,OAAOuD,yBAAY,CAACG,MAAM,CAAC6B,aAAa;YACxE,IAAInB,QAAQ,CAAC,GAAG;gBACdkB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAItF,MAAMnB,MAAM,KAAK0E,yBAAY,CAACG,MAAM,CAAC6B,aAAa,CAAC1G,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAM2G,SAASxF,MAAMsE,KAAK,CAAC,GAAGF;gBAC9BpF,WAAWc,OAAO,CAAC0F;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAIxF,MAAMnB,MAAM,GAAG0E,yBAAY,CAACG,MAAM,CAAC6B,aAAa,CAAC1G,MAAM,GAAGuF,OAAO;oBACnE,uCAAuC;oBACvC,MAAMqB,QAAQzF,MAAMsE,KAAK,CACvBF,QAAQb,yBAAY,CAACG,MAAM,CAAC6B,aAAa,CAAC1G,MAAM;oBAElDG,WAAWc,OAAO,CAAC2F;gBACrB;YACF,OAAO;gBACLzG,WAAWc,OAAO,CAACE;YACrB;QACF;QACAqB,OAAMrC,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWc,OAAO,CAACyD,yBAAY,CAACG,MAAM,CAAC6B,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAItG,gBAAgB;QACzB4C,WAAUhC,KAAK,EAAEhB,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACE2G,IAAAA,0CAAuB,EAAC3F,OAAOuD,yBAAY,CAACG,MAAM,CAAC6B,aAAa,KAChEI,IAAAA,0CAAuB,EAAC3F,OAAOuD,yBAAY,CAACG,MAAM,CAACkC,IAAI,KACvDD,IAAAA,0CAAuB,EAAC3F,OAAOuD,yBAAY,CAACG,MAAM,CAACmC,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtF7F,QAAQ8F,IAAAA,uCAAoB,EAAC9F,OAAOuD,yBAAY,CAACG,MAAM,CAACkC,IAAI;YAC5D5F,QAAQ8F,IAAAA,uCAAoB,EAAC9F,OAAOuD,yBAAY,CAACG,MAAM,CAACmC,IAAI;YAE5D7G,WAAWc,OAAO,CAACE;QACrB;IACF;AACF;AAOO,SAAS7B;IAId,IAAI4H,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI5G,gBAAgB;QACzB,MAAM4C,WAAUhC,KAAK,EAAEhB,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC+G,aACDzC,IAAAA,oCAAiB,EAACtD,OAAOuD,yBAAY,CAAC0C,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACD1C,IAAAA,oCAAiB,EAACtD,OAAOuD,yBAAY,CAAC0C,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEAhH,WAAWc,OAAO,CAACE;QACrB;QACAqB,OAAMrC,UAAU;YACd,MAAMkH,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAY1F,IAAI,CAAC;YACjC,IAAI,CAACwF,WAAWE,YAAY1F,IAAI,CAAC;YAEjC,IAAI,CAAC0F,YAAYrH,MAAM,EAAE;YAEzBG,WAAWc,OAAO,CAChBpB,QAAQqB,MAAM,CACZ,CAAC;;+CAEoC,EAAEmG,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAYrH,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,EAAEyH,mCAAuB,CAAC;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAASC,kBACPrH,QAA2B,EAC3BsH,YAAyD;IAEzD,IAAIvG,SAASf;IACb,KAAK,MAAMuH,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElBxG,SAASA,OAAOyG,WAAW,CAACD;IAC9B;IACA,OAAOxG;AACT;AAgBO,eAAelC,mBACpB4I,YAA0C,EAC1C,EACEnC,MAAM,EACNoC,iBAAiB,EACjBC,kBAAkB,EAClB3E,uBAAuB,EACvBC,OAAO,EACP2E,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBzC,SAASA,OAAO0C,KAAK,CAAC9B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,uFAAuF;IACvF,IAAIyB,oBAAoB;QACtB,MAAMF,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrD1I;QAEA,sEAAsE;QACtEgE,4BAA4BC,yBAAyBC;QAErD,qBAAqB;QACrBY,8BAA8BgE;QAE9B,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAepI,MAAM,GAAG,IAC9C0F,2BAA2B0C,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBACIlC,yCAAyCkC,mBAAmB,QAC5D;QAEJ,yDAAyD;QACzDI,qBAAqB7I,oCAAoC;QAEzD,kDAAkD;QAClDkH;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/EpB,mCAAmC6C;KACpC;AACH;AAOO,eAAehJ,yBACpBsJ,eAA2C,EAC3C,EACEN,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEK,eACE,qDAAqD;KACpDV,WAAW,CAACzI,iCACZyI,WAAW,CAAChB,0CACb,gCAAgC;KAC/BgB,WAAW,CAACzC,mCAAmC6C,uBAChD,qBAAqB;KACpBJ,WAAW,CAAC3D,8BAA8BgE;AAEjD;AAUO,eAAe/I,wBACpBoJ,eAA2C,EAC3C,EACER,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACzB7E,uBAAuB,EACvBC,OAAO,EACwB;IAEjC,OACEiF,eACE,qDAAqD;KACpDV,WAAW,CAACzI,gCACb,sEAAsE;KACrEyI,WAAW,CACVzE,4BAA4BC,yBAAyBC,SAEvD,gCAAgC;KAC/BuE,WAAW,CAACzC,mCAAmC6C,uBAChD,qBAAqB;KACpBJ,WAAW,CAAC3D,8BAA8BgE,2BAC3C,+EAA+E;KAC9EL,WAAW,CACVhC,yCAAyCkC,mBAAmB,MAE9D,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AASO,eAAexH,0BACpB8I,YAAwC,EACxC,EACEhC,4BAA4B,EAC5BiC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,YACE,qDAAqD;KACpDD,WAAW,CAACzI,gCACb,gCAAgC;KAC/ByI,WAAW,CAACzC,mCAAmC6C,uBAChD,qBAAqB;KACpBJ,WAAW,CAAC3D,8BAA8BgE,2BAC3C,+EAA+E;KAC9EL,WAAW,CACVhC,yCACEkC,mBACAjC,8BAGJ,kDAAkD;KACjD+B,WAAW,CAACrB;AAEnB;AAEO,SAASnH;IACd,OAAOI,iBAAiB8G;AAC1B", "ignoreList": [0]}