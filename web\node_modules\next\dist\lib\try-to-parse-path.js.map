{"version": 3, "sources": ["../../src/lib/try-to-parse-path.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\nimport { parse, tokensToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { parse as parseURL } from 'url'\nimport isError from './is-error'\nimport { normalizeTokensForRegexp } from './route-pattern-normalizer'\n\ninterface ParseResult {\n  error?: any\n  parsedPath: string\n  regexStr?: string\n  route: string\n  tokens?: Token[]\n}\n\n/**\n * If there is an error show our error link but still show original error or\n * a formatted one if we can\n */\nfunction reportError({ route, parsedPath }: ParseResult, err: any) {\n  let errMatches\n  if (isError(err) && (errMatches = err.message.match(/at (\\d{0,})/))) {\n    const position = parseInt(errMatches[1], 10)\n    console.error(\n      `\\nError parsing \\`${route}\\` ` +\n        `https://nextjs.org/docs/messages/invalid-route-source\\n` +\n        `Reason: ${err.message}\\n\\n` +\n        `  ${parsedPath}\\n` +\n        `  ${new Array(position).fill(' ').join('')}^\\n`\n    )\n  } else {\n    console.error(\n      `\\nError parsing ${route} https://nextjs.org/docs/messages/invalid-route-source`,\n      err\n    )\n  }\n}\n\n/**\n * Safe wrapper around tokensToRegexp that handles path-to-regexp 6.3.0+ validation errors.\n */\nfunction safeTokensToRegexp(tokens: Token[]): RegExp {\n  try {\n    return tokensToRegexp(tokens)\n  } catch (error) {\n    if (isError(error)) {\n      // Try to normalize tokens with repeating modifiers but no prefix/suffix\n      const normalizedTokens = normalizeTokensForRegexp(tokens)\n      return tokensToRegexp(normalizedTokens)\n    }\n    throw error\n  }\n}\n\n/**\n * Attempts to parse a given route with `path-to-regexp` and returns an object\n * with the result. Whenever an error happens on parse, it will print an error\n * attempting to find the error position and showing a link to the docs. When\n * `handleUrl` is set to `true` it will also attempt to parse the route\n * and use the resulting pathname to parse with `path-to-regexp`.\n */\nexport function tryToParsePath(\n  route: string,\n  options?: {\n    handleUrl?: boolean\n  }\n): ParseResult {\n  const result: ParseResult = { route, parsedPath: route }\n  try {\n    if (options?.handleUrl) {\n      const parsed = parseURL(route, true)\n      result.parsedPath = `${parsed.pathname!}${parsed.hash || ''}`\n    }\n\n    result.tokens = parse(result.parsedPath)\n\n    // Use safe wrapper instead of proactive detection\n    if (result.tokens) {\n      result.regexStr = safeTokensToRegexp(result.tokens).source\n    }\n  } catch (err) {\n    reportError(result, err)\n    result.error = err\n  }\n\n  return result\n}\n"], "names": ["tryToParsePath", "reportError", "route", "parsed<PERSON><PERSON>", "err", "err<PERSON><PERSON><PERSON>", "isError", "message", "match", "position", "parseInt", "console", "error", "Array", "fill", "join", "safeTokensToRegexp", "tokens", "tokensToRegexp", "normalizedTokens", "normalizeTokensForRegexp", "options", "result", "handleUrl", "parsed", "parseURL", "pathname", "hash", "parse", "regexStr", "source"], "mappings": ";;;;+BA4DgBA;;;eAAAA;;;8BA3DsB;qBACJ;gEACd;wCACqB;;;;;;AAUzC;;;CAGC,GACD,SAASC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAe,EAAEC,GAAQ;IAC/D,IAAIC;IACJ,IAAIC,IAAAA,gBAAO,EAACF,QAASC,CAAAA,aAAaD,IAAIG,OAAO,CAACC,KAAK,CAAC,cAAa,GAAI;QACnE,MAAMC,WAAWC,SAASL,UAAU,CAAC,EAAE,EAAE;QACzCM,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEV,MAAM,GAAG,CAAC,GAC7B,CAAC,uDAAuD,CAAC,GACzD,CAAC,QAAQ,EAAEE,IAAIG,OAAO,CAAC,IAAI,CAAC,GAC5B,CAAC,EAAE,EAAEJ,WAAW,EAAE,CAAC,GACnB,CAAC,EAAE,EAAE,IAAIU,MAAMJ,UAAUK,IAAI,CAAC,KAAKC,IAAI,CAAC,IAAI,GAAG,CAAC;IAEtD,OAAO;QACLJ,QAAQC,KAAK,CACX,CAAC,gBAAgB,EAAEV,MAAM,sDAAsD,CAAC,EAChFE;IAEJ;AACF;AAEA;;CAEC,GACD,SAASY,mBAAmBC,MAAe;IACzC,IAAI;QACF,OAAOC,IAAAA,4BAAc,EAACD;IACxB,EAAE,OAAOL,OAAO;QACd,IAAIN,IAAAA,gBAAO,EAACM,QAAQ;YAClB,wEAAwE;YACxE,MAAMO,mBAAmBC,IAAAA,gDAAwB,EAACH;YAClD,OAAOC,IAAAA,4BAAc,EAACC;QACxB;QACA,MAAMP;IACR;AACF;AASO,SAASZ,eACdE,KAAa,EACbmB,OAEC;IAED,MAAMC,SAAsB;QAAEpB;QAAOC,YAAYD;IAAM;IACvD,IAAI;QACF,IAAImB,2BAAAA,QAASE,SAAS,EAAE;YACtB,MAAMC,SAASC,IAAAA,UAAQ,EAACvB,OAAO;YAC/BoB,OAAOnB,UAAU,GAAG,GAAGqB,OAAOE,QAAQ,GAAIF,OAAOG,IAAI,IAAI,IAAI;QAC/D;QAEAL,OAAOL,MAAM,GAAGW,IAAAA,mBAAK,EAACN,OAAOnB,UAAU;QAEvC,kDAAkD;QAClD,IAAImB,OAAOL,MAAM,EAAE;YACjBK,OAAOO,QAAQ,GAAGb,mBAAmBM,OAAOL,MAAM,EAAEa,MAAM;QAC5D;IACF,EAAE,OAAO1B,KAAK;QACZH,YAAYqB,QAAQlB;QACpBkB,OAAOV,KAAK,GAAGR;IACjB;IAEA,OAAOkB;AACT", "ignoreList": [0]}