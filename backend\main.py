import os, time, logging
from data_loader import safe_read_text, read_docx_enhanced, read_excel_robust, read_csv, read_json, load_jsonl_enhanced, ocr_image_enhanced, ocr_pdf_enhanced
from text_splitter import get_splitter, split_and_dedupe
from embedding import get_model, encode_chunks
from indexer import build_faiss_index, save_index

# Config
EMBED_MODEL = os.getenv("EMBED_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "500"))
CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "100"))
MIN_CHARS = int(os.getenv("MIN_CHARS", "30"))
DATA_GLOB = os.getenv("DATA_GLOB", "data/**/*")
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "32"))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_docs():
    import glob
    from pathlib import Path
    docs, successes, skipped, failures = [], [], [], []
    for path in glob.glob(DATA_GLOB, recursive=True):
        p = Path(path)
        if not p.is_file():
            continue
        lower = p.suffix.lower()
        try:
            if lower in [".txt", ".md"]:
                text = safe_read_text(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower == ".docx":
                text = read_docx_enhanced(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower == ".xlsx":
                text = read_excel_robust(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower == ".csv":
                text = read_csv(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower == ".json":
                text = read_json(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower == ".jsonl":
                jsonl_docs = load_jsonl_enhanced(str(p))
                if jsonl_docs:
                    docs.extend(jsonl_docs)
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower in [".png", ".jpg", ".jpeg", ".webp"]:
                text = ocr_image_enhanced(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            elif lower == ".pdf":
                text = ocr_pdf_enhanced(str(p))
                if text and len(text.strip()) >= MIN_CHARS:
                    docs.append({"text": text, "meta": {"source": str(p)}})
                    successes.append(str(p))
                else:
                    skipped.append((str(p), "empty or too short"))
            else:
                skipped.append((str(p), "unsupported file type"))
        except Exception as e:
            failures.append((str(p), str(e)))
    return docs, successes, skipped, failures

def main():
    start = time.time()
    logger.info("Loading documents...")
    docs, successes, skipped, failures = load_docs()
    logger.info(f"Loaded {len(docs)} documents")

    print(f"\n📄 Đã đọc thành công ({len(successes)}):")
    for s in successes:
        print(f"  ✅ {s}")
    print(f"\n⚠️  Đã skip ({len(skipped)}):")
    for s, reason in skipped:
        print(f"  ⚠️  {s} - {reason}")
    print(f"\n❌ Lỗi ({len(failures)}):")
    for s, error in failures:
        print(f"  ❌ {s} - {error}")

    if not docs:
        logger.error("No documents to process.")
        return

    logger.info("Splitting and deduping text...")
    splitter = get_splitter(CHUNK_SIZE, CHUNK_OVERLAP)
    all_chunks = []
    for d in docs:
        chunks = split_and_dedupe(d["text"], splitter, MIN_CHARS)
        for c in chunks:
            all_chunks.append({"text": c, "meta": d["meta"]})
    logger.info(f"Created {len(all_chunks)} chunks")

    logger.info("Loading embedding model...")
    model = get_model(EMBED_MODEL)
    embeddings = encode_chunks(model, all_chunks, BATCH_SIZE)
    logger.info("Building FAISS index...")
    index = build_faiss_index(embeddings)
    metadata = {
        "total_documents": len(docs),
        "total_chunks": len(all_chunks),
        "embedding_model": EMBED_MODEL,
        "chunk_size": CHUNK_SIZE,
        "chunk_overlap": CHUNK_OVERLAP,
        "index_created": time.strftime("%Y-%m-%d %H:%M:%S"),
        "processing_time_seconds": time.time() - start,
    }
    save_index(index, all_chunks, metadata)
    print("\n🎉 Indexing completed!")

if __name__ == "__main__":
    main()
