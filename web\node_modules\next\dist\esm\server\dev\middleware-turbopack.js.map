{"version": 3, "sources": ["../../../src/server/dev/middleware-turbopack.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport {\n  getOriginalCodeFrame,\n  ignoreListAnonymousStackFramesIfSandwiched,\n  type IgnorableStackFrame,\n  type OriginalStackFrameResponse,\n  type OriginalStackFramesRequest,\n  type OriginalStackFramesResponse,\n  type StackFrame,\n} from '../../next-devtools/server/shared'\nimport { middlewareResponse } from '../../next-devtools/server/middleware-response'\nimport path from 'path'\nimport { openFileInEditor } from '../../next-devtools/server/launch-editor'\nimport {\n  SourceMapConsumer,\n  type NullableMappedPosition,\n} from 'next/dist/compiled/source-map08'\nimport type { Project, TurbopackStackFrame } from '../../build/swc/types'\nimport {\n  type ModernSourceMapPayload,\n  devirtualizeReactServerURL,\n  findApplicableSourceMapPayload,\n} from '../lib/source-maps'\nimport { findSourceMap, type SourceMap } from 'node:module'\nimport { fileURLToPath, pathToFileURL } from 'node:url'\nimport { inspect } from 'node:util'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist') ||\n    modulePath.startsWith('node:')\n  )\n}\n\nconst currentSourcesByFile: Map<string, Promise<string | null>> = new Map()\n/**\n * @returns 1-based lines and 1-based columns\n */\nasync function batchedTraceSource(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const file = frame.file\n    ? // TODO(veil): Why are the frames sent encoded?\n      decodeURIComponent(frame.file)\n    : undefined\n\n  if (!file) return\n\n  // For node internals they cannot traced the actual source code with project.traceSource,\n  // we need an early return to indicate it's ignored to avoid the unknown scheme error from `project.traceSource`.\n  if (file.startsWith('node:')) {\n    return {\n      frame: {\n        file,\n        line1: frame.line ?? null,\n        column1: frame.column ?? null,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: true,\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  const currentDirectoryFileUrl = pathToFileURL(process.cwd()).href\n\n  const sourceFrame = await project.traceSource(frame, currentDirectoryFileUrl)\n  if (!sourceFrame) {\n    return {\n      frame: {\n        file,\n        line1: frame.line ?? null,\n        column1: frame.column ?? null,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: shouldIgnorePath(file),\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  let source = null\n  const originalFile = sourceFrame.originalFile\n\n  // Don't look up source for node_modules or internals. These can often be large bundled files.\n  const ignored =\n    shouldIgnorePath(originalFile ?? sourceFrame.file) ||\n    // isInternal means resource starts with turbopack:///[turbopack]\n    !!sourceFrame.isInternal\n  if (originalFile && !ignored) {\n    let sourcePromise = currentSourcesByFile.get(originalFile)\n    if (!sourcePromise) {\n      sourcePromise = project.getSourceForAsset(originalFile)\n      currentSourcesByFile.set(originalFile, sourcePromise)\n      setTimeout(() => {\n        // Cache file reads for 100ms, as frames will often reference the same\n        // files and can be large.\n        currentSourcesByFile.delete(originalFile!)\n      }, 100)\n    }\n    source = await sourcePromise\n  }\n\n  // TODO: get ignoredList from turbopack source map\n  const ignorableFrame: IgnorableStackFrame = {\n    file: sourceFrame.file,\n    line1: sourceFrame.line ?? null,\n    column1: sourceFrame.column ?? null,\n    methodName:\n      // We ignore the sourcemapped name since it won't be the correct name.\n      // The callsite will point to the column of the variable name instead of the\n      // name of the enclosing function.\n      // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n      frame.methodName ?? '<unknown>',\n    ignored,\n    arguments: [],\n  }\n\n  return {\n    frame: ignorableFrame,\n    source,\n  }\n}\n\nfunction parseFile(fileParam: string | null): string | undefined {\n  if (!fileParam) {\n    return undefined\n  }\n\n  return devirtualizeReactServerURL(fileParam)\n}\n\nfunction createStackFrames(\n  body: OriginalStackFramesRequest\n): TurbopackStackFrame[] {\n  const { frames, isServer } = body\n\n  return frames\n    .map((frame): TurbopackStackFrame | undefined => {\n      const file = parseFile(frame.file)\n\n      if (!file) {\n        return undefined\n      }\n\n      return {\n        file,\n        methodName: frame.methodName ?? '<unknown>',\n        line: frame.line1 ?? undefined,\n        column: frame.column1 ?? undefined,\n        isServer,\n      }\n    })\n    .filter((f): f is TurbopackStackFrame => f !== undefined)\n}\n\nfunction createStackFrame(\n  searchParams: URLSearchParams\n): TurbopackStackFrame | undefined {\n  const file = parseFile(searchParams.get('file'))\n\n  if (!file) {\n    return undefined\n  }\n\n  return {\n    file,\n    methodName: searchParams.get('methodName') ?? '<unknown>',\n    line: parseInt(searchParams.get('line1') ?? '0', 10) || undefined,\n    column: parseInt(searchParams.get('column1') ?? '0', 10) || undefined,\n    isServer: searchParams.get('isServer') === 'true',\n  }\n}\n\n/**\n * @returns 1-based lines and 1-based columns\n */\nasync function nativeTraceSource(\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const sourceURL = frame.file\n  let sourceMapPayload: ModernSourceMapPayload | undefined\n  try {\n    sourceMapPayload = findSourceMap(sourceURL)?.payload\n  } catch (cause) {\n    throw new Error(\n      `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n      { cause }\n    )\n  }\n\n  if (sourceMapPayload !== undefined) {\n    let consumer: SourceMapConsumer\n    try {\n      consumer = await new SourceMapConsumer(sourceMapPayload)\n    } catch (cause) {\n      throw new Error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n        { cause }\n      )\n    }\n    let traced: {\n      originalPosition: NullableMappedPosition\n      sourceContent: string | null\n    } | null\n    try {\n      const originalPosition = consumer.originalPositionFor({\n        line: frame.line ?? 1,\n        // 0-based columns out requires 0-based columns in.\n        column: (frame.column ?? 1) - 1,\n      })\n\n      if (originalPosition.source === null) {\n        traced = null\n      } else {\n        const sourceContent: string | null =\n          consumer.sourceContentFor(\n            originalPosition.source,\n            /* returnNullOnMissing */ true\n          ) ?? null\n\n        traced = { originalPosition, sourceContent }\n      }\n    } finally {\n      consumer.destroy()\n    }\n\n    if (traced !== null) {\n      const { originalPosition, sourceContent } = traced\n      const applicableSourceMap = findApplicableSourceMapPayload(\n        (frame.line ?? 1) - 1,\n        (frame.column ?? 1) - 1,\n        sourceMapPayload\n      )\n\n      // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n      let ignored = false\n      if (applicableSourceMap === undefined) {\n        console.error(\n          'No applicable source map found in sections for frame',\n          frame\n        )\n      } else {\n        // TODO: O(n^2). Consider moving `ignoreList` into a Set\n        const sourceIndex = applicableSourceMap.sources.indexOf(\n          originalPosition.source!\n        )\n        ignored =\n          applicableSourceMap.ignoreList?.includes(sourceIndex) ??\n          // When sourcemap is not available, fallback to checking `frame.file`.\n          // e.g. In pages router, nextjs server code is not bundled into the page.\n          shouldIgnorePath(frame.file)\n      }\n\n      const originalStackFrame: IgnorableStackFrame = {\n        methodName:\n          // We ignore the sourcemapped name since it won't be the correct name.\n          // The callsite will point to the column of the variable name instead of the\n          // name of the enclosing function.\n          // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n          frame.methodName\n            ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n            ?.replace('__webpack_exports__.', '') || '<unknown>',\n        file: originalPosition.source,\n        line1: originalPosition.line,\n        column1:\n          originalPosition.column === null ? null : originalPosition.column + 1,\n        // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n        arguments: [],\n        ignored,\n      }\n\n      return {\n        frame: originalStackFrame,\n        source: sourceContent,\n      }\n    }\n  }\n\n  return undefined\n}\n\nasync function createOriginalStackFrame(\n  project: Project,\n  projectPath: string,\n  frame: TurbopackStackFrame\n): Promise<OriginalStackFrameResponse | null> {\n  const traced =\n    (await nativeTraceSource(frame)) ??\n    // TODO(veil): When would the bundler know more than native?\n    // If it's faster, try the bundler first and fall back to native later.\n    (await batchedTraceSource(project, frame))\n  if (!traced) {\n    return null\n  }\n\n  let normalizedStackFrameLocation = traced.frame.file\n  if (\n    normalizedStackFrameLocation !== null &&\n    normalizedStackFrameLocation.startsWith('file://')\n  ) {\n    normalizedStackFrameLocation = path.relative(\n      projectPath,\n      fileURLToPath(normalizedStackFrameLocation)\n    )\n  }\n\n  return {\n    originalStackFrame: {\n      arguments: traced.frame.arguments,\n      file: normalizedStackFrameLocation,\n      line1: traced.frame.line1,\n      column1: traced.frame.column1,\n      ignored: traced.frame.ignored,\n      methodName: traced.frame.methodName,\n    },\n    originalCodeFrame: getOriginalCodeFrame(traced.frame, traced.source),\n  }\n}\n\nexport function getOverlayMiddleware({\n  project,\n  projectPath,\n  isSrcDir,\n}: {\n  project: Project\n  projectPath: string\n  isSrcDir: boolean\n}) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname === '/__nextjs_original-stack-frames') {\n      if (req.method !== 'POST') {\n        return middlewareResponse.badRequest(res)\n      }\n\n      const body = await new Promise<string>((resolve, reject) => {\n        let data = ''\n        req.on('data', (chunk) => {\n          data += chunk\n        })\n        req.on('end', () => resolve(data))\n        req.on('error', reject)\n      })\n\n      const request = JSON.parse(body) as OriginalStackFramesRequest\n      const result = await getOriginalStackFrames({\n        project,\n        projectPath,\n        frames: request.frames,\n        isServer: request.isServer,\n        isEdgeServer: request.isEdgeServer,\n        isAppDirectory: request.isAppDirectory,\n      })\n\n      ignoreListAnonymousStackFramesIfSandwiched(result)\n\n      return middlewareResponse.json(res, result)\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const isAppRelativePath = searchParams.get('isAppRelativePath') === '1'\n\n      let openEditorResult\n      if (isAppRelativePath) {\n        const relativeFilePath = searchParams.get('file') || ''\n        const appPath = path.join(\n          'app',\n          isSrcDir ? 'src' : '',\n          relativeFilePath\n        )\n        openEditorResult = await openFileInEditor(appPath, 1, 1, projectPath)\n      } else {\n        const frame = createStackFrame(searchParams)\n        if (!frame) return middlewareResponse.badRequest(res)\n        openEditorResult = await openFileInEditor(\n          frame.file,\n          frame.line ?? 1,\n          frame.column ?? 1,\n          projectPath\n        )\n      }\n\n      if (openEditorResult.error) {\n        return middlewareResponse.internalServerError(\n          res,\n          openEditorResult.error\n        )\n      }\n      if (!openEditorResult.found) {\n        return middlewareResponse.notFound(res)\n      }\n      return middlewareResponse.noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    let filename = searchParams.get('filename')\n\n    if (!filename) {\n      return middlewareResponse.badRequest(res)\n    }\n\n    let nativeSourceMap: SourceMap | undefined\n    try {\n      nativeSourceMap = findSourceMap(filename)\n    } catch (cause) {\n      return middlewareResponse.internalServerError(\n        res,\n        new Error(\n          `${filename}: Invalid source map. Only conformant source maps can be used to find the original code.`,\n          { cause }\n        )\n      )\n    }\n\n    if (nativeSourceMap !== undefined) {\n      const sourceMapPayload = nativeSourceMap.payload\n      return middlewareResponse.json(res, sourceMapPayload)\n    }\n\n    try {\n      // Turbopack chunk filenames might be URL-encoded.\n      filename = decodeURI(filename)\n    } catch {\n      return middlewareResponse.badRequest(res)\n    }\n\n    if (path.isAbsolute(filename)) {\n      filename = pathToFileURL(filename).href\n    }\n\n    try {\n      const sourceMapString = await project.getSourceMap(filename)\n\n      if (sourceMapString) {\n        return middlewareResponse.jsonString(res, sourceMapString)\n      }\n    } catch (cause) {\n      return middlewareResponse.internalServerError(\n        res,\n        new Error(\n          `Failed to get source map for '${filename}'. This is a bug in Next.js`,\n          {\n            cause,\n          }\n        )\n      )\n    }\n\n    middlewareResponse.noContent(res)\n  }\n}\n\nexport async function getOriginalStackFrames({\n  project,\n  projectPath,\n  frames,\n  isServer,\n  isEdgeServer,\n  isAppDirectory,\n}: {\n  project: Project\n  projectPath: string\n  frames: readonly StackFrame[]\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n}): Promise<OriginalStackFramesResponse> {\n  const stackFrames = createStackFrames({\n    frames,\n    isServer,\n    isEdgeServer,\n    isAppDirectory,\n  })\n\n  return Promise.all(\n    stackFrames.map(async (frame) => {\n      try {\n        const stackFrame = await createOriginalStackFrame(\n          project,\n          projectPath,\n          frame\n        )\n        if (stackFrame === null) {\n          return {\n            status: 'rejected',\n            reason: 'Failed to create original stack frame',\n          }\n        }\n        return { status: 'fulfilled', value: stackFrame }\n      } catch (error) {\n        return {\n          status: 'rejected',\n          reason: inspect(error, { colors: false }),\n        }\n      }\n    })\n  )\n}\n"], "names": ["getOriginalCodeFrame", "ignoreListAnonymousStackFramesIfSandwiched", "middlewareResponse", "path", "openFileInEditor", "SourceMapConsumer", "devirtualizeReactServerURL", "findApplicableSourceMapPayload", "findSourceMap", "fileURLToPath", "pathToFileURL", "inspect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "startsWith", "currentSourcesByFile", "Map", "batchedTraceSource", "project", "frame", "file", "decodeURIComponent", "undefined", "line1", "line", "column1", "column", "methodName", "ignored", "arguments", "source", "currentDirectoryFileUrl", "process", "cwd", "href", "sourceFrame", "traceSource", "originalFile", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "ignorableFrame", "parseFile", "fileParam", "createStackFrames", "body", "frames", "isServer", "map", "filter", "f", "createStackFrame", "searchParams", "parseInt", "nativeTraceSource", "sourceURL", "sourceMapPayload", "payload", "cause", "Error", "consumer", "traced", "originalPosition", "originalPositionFor", "sourceContent", "sourceContentFor", "destroy", "applicableSourceMap", "console", "error", "sourceIndex", "sources", "indexOf", "ignoreList", "originalStackFrame", "replace", "createOriginalStackFrame", "projectPath", "normalizedStackFrameLocation", "relative", "originalCodeFrame", "getOverlayMiddleware", "isSrcDir", "req", "res", "next", "pathname", "URL", "url", "method", "badRequest", "Promise", "resolve", "reject", "data", "on", "chunk", "request", "JSON", "parse", "result", "getOriginalStackFrames", "isEdgeServer", "isAppDirectory", "json", "isAppRelativePath", "openEditorResult", "relativeFilePath", "appPath", "join", "internalServerError", "found", "notFound", "noContent", "getSourceMapMiddleware", "filename", "nativeSourceMap", "decodeURI", "isAbsolute", "sourceMapString", "getSourceMap", "jsonString", "stackFrames", "all", "stackFrame", "status", "reason", "value", "colors"], "mappings": "AACA,SACEA,oBAAoB,EACpBC,0CAA0C,QAMrC,oCAAmC;AAC1C,SAASC,kBAAkB,QAAQ,iDAAgD;AACnF,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,2CAA0C;AAC3E,SACEC,iBAAiB,QAEZ,kCAAiC;AAExC,SAEEC,0BAA0B,EAC1BC,8BAA8B,QACzB,qBAAoB;AAC3B,SAASC,aAAa,QAAwB,cAAa;AAC3D,SAASC,aAAa,EAAEC,aAAa,QAAQ,WAAU;AACvD,SAASC,OAAO,QAAQ,YAAW;AAEnC,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC,gBACpBD,WAAWE,UAAU,CAAC;AAE1B;AAEA,MAAMC,uBAA4D,IAAIC;AACtE;;CAEC,GACD,eAAeC,mBACbC,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAEnBC,mBAAmBF,MAAMC,IAAI,IAC7BE;IAEJ,IAAI,CAACF,MAAM;IAEX,yFAAyF;IACzF,iHAAiH;IACjH,IAAIA,KAAKN,UAAU,CAAC,UAAU;QAC5B,OAAO;YACLK,OAAO;gBACLC;gBACAG,OAAOJ,MAAMK,IAAI,IAAI;gBACrBC,SAASN,MAAMO,MAAM,IAAI;gBACzBC,YAAYR,MAAMQ,UAAU,IAAI;gBAChCC,SAAS;gBACTC,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,MAAMC,0BAA0BtB,cAAcuB,QAAQC,GAAG,IAAIC,IAAI;IAEjE,MAAMC,cAAc,MAAMjB,QAAQkB,WAAW,CAACjB,OAAOY;IACrD,IAAI,CAACI,aAAa;QAChB,OAAO;YACLhB,OAAO;gBACLC;gBACAG,OAAOJ,MAAMK,IAAI,IAAI;gBACrBC,SAASN,MAAMO,MAAM,IAAI;gBACzBC,YAAYR,MAAMQ,UAAU,IAAI;gBAChCC,SAASjB,iBAAiBS;gBAC1BS,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,IAAIA,SAAS;IACb,MAAMO,eAAeF,YAAYE,YAAY;IAE7C,8FAA8F;IAC9F,MAAMT,UACJjB,iBAAiB0B,gBAAgBF,YAAYf,IAAI,KACjD,iEAAiE;IACjE,CAAC,CAACe,YAAYG,UAAU;IAC1B,IAAID,gBAAgB,CAACT,SAAS;QAC5B,IAAIW,gBAAgBxB,qBAAqByB,GAAG,CAACH;QAC7C,IAAI,CAACE,eAAe;YAClBA,gBAAgBrB,QAAQuB,iBAAiB,CAACJ;YAC1CtB,qBAAqB2B,GAAG,CAACL,cAAcE;YACvCI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1B5B,qBAAqB6B,MAAM,CAACP;YAC9B,GAAG;QACL;QACAP,SAAS,MAAMS;IACjB;IAEA,kDAAkD;IAClD,MAAMM,iBAAsC;QAC1CzB,MAAMe,YAAYf,IAAI;QACtBG,OAAOY,YAAYX,IAAI,IAAI;QAC3BC,SAASU,YAAYT,MAAM,IAAI;QAC/BC,YACE,sEAAsE;QACtE,4EAA4E;QAC5E,kCAAkC;QAClC,oGAAoG;QACpGR,MAAMQ,UAAU,IAAI;QACtBC;QACAC,WAAW,EAAE;IACf;IAEA,OAAO;QACLV,OAAO0B;QACPf;IACF;AACF;AAEA,SAASgB,UAAUC,SAAwB;IACzC,IAAI,CAACA,WAAW;QACd,OAAOzB;IACT;IAEA,OAAOjB,2BAA2B0C;AACpC;AAEA,SAASC,kBACPC,IAAgC;IAEhC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF;IAE7B,OAAOC,OACJE,GAAG,CAAC,CAACjC;QACJ,MAAMC,OAAO0B,UAAU3B,MAAMC,IAAI;QAEjC,IAAI,CAACA,MAAM;YACT,OAAOE;QACT;QAEA,OAAO;YACLF;YACAO,YAAYR,MAAMQ,UAAU,IAAI;YAChCH,MAAML,MAAMI,KAAK,IAAID;YACrBI,QAAQP,MAAMM,OAAO,IAAIH;YACzB6B;QACF;IACF,GACCE,MAAM,CAAC,CAACC,IAAgCA,MAAMhC;AACnD;AAEA,SAASiC,iBACPC,YAA6B;IAE7B,MAAMpC,OAAO0B,UAAUU,aAAahB,GAAG,CAAC;IAExC,IAAI,CAACpB,MAAM;QACT,OAAOE;IACT;IAEA,OAAO;QACLF;QACAO,YAAY6B,aAAahB,GAAG,CAAC,iBAAiB;QAC9ChB,MAAMiC,SAASD,aAAahB,GAAG,CAAC,YAAY,KAAK,OAAOlB;QACxDI,QAAQ+B,SAASD,aAAahB,GAAG,CAAC,cAAc,KAAK,OAAOlB;QAC5D6B,UAAUK,aAAahB,GAAG,CAAC,gBAAgB;IAC7C;AACF;AAEA;;CAEC,GACD,eAAekB,kBACbvC,KAA0B;IAE1B,MAAMwC,YAAYxC,MAAMC,IAAI;IAC5B,IAAIwC;IACJ,IAAI;YACiBrD;QAAnBqD,oBAAmBrD,iBAAAA,cAAcoD,+BAAdpD,eAA0BsD,OAAO;IACtD,EAAE,OAAOC,OAAO;QACd,MAAM,qBAGL,CAHK,IAAIC,MACR,GAAGJ,UAAU,wFAAwF,CAAC,EACtG;YAAEG;QAAM,IAFJ,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIF,qBAAqBtC,WAAW;QAClC,IAAI0C;QACJ,IAAI;YACFA,WAAW,MAAM,IAAI5D,kBAAkBwD;QACzC,EAAE,OAAOE,OAAO;YACd,MAAM,qBAGL,CAHK,IAAIC,MACR,GAAGJ,UAAU,wFAAwF,CAAC,EACtG;gBAAEG;YAAM,IAFJ,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,IAAIG;QAIJ,IAAI;YACF,MAAMC,mBAAmBF,SAASG,mBAAmB,CAAC;gBACpD3C,MAAML,MAAMK,IAAI,IAAI;gBACpB,mDAAmD;gBACnDE,QAAQ,AAACP,CAAAA,MAAMO,MAAM,IAAI,CAAA,IAAK;YAChC;YAEA,IAAIwC,iBAAiBpC,MAAM,KAAK,MAAM;gBACpCmC,SAAS;YACX,OAAO;gBACL,MAAMG,gBACJJ,SAASK,gBAAgB,CACvBH,iBAAiBpC,MAAM,EACvB,uBAAuB,GAAG,SACvB;gBAEPmC,SAAS;oBAAEC;oBAAkBE;gBAAc;YAC7C;QACF,SAAU;YACRJ,SAASM,OAAO;QAClB;QAEA,IAAIL,WAAW,MAAM;gBA6Bf,sEAAsE;YACtE,4EAA4E;YAC5E,kCAAkC;YAClC,oGAAoG;YACpG9C,2BAAAA;YAhCJ,MAAM,EAAE+C,gBAAgB,EAAEE,aAAa,EAAE,GAAGH;YAC5C,MAAMM,sBAAsBjE,+BAC1B,AAACa,CAAAA,MAAMK,IAAI,IAAI,CAAA,IAAK,GACpB,AAACL,CAAAA,MAAMO,MAAM,IAAI,CAAA,IAAK,GACtBkC;YAGF,0GAA0G;YAC1G,IAAIhC,UAAU;YACd,IAAI2C,wBAAwBjD,WAAW;gBACrCkD,QAAQC,KAAK,CACX,wDACAtD;YAEJ,OAAO;oBAMHoD;gBALF,wDAAwD;gBACxD,MAAMG,cAAcH,oBAAoBI,OAAO,CAACC,OAAO,CACrDV,iBAAiBpC,MAAM;gBAEzBF,UACE2C,EAAAA,kCAAAA,oBAAoBM,UAAU,qBAA9BN,gCAAgC1D,QAAQ,CAAC6D,iBACzC,sEAAsE;gBACtE,yEAAyE;gBACzE/D,iBAAiBQ,MAAMC,IAAI;YAC/B;YAEA,MAAM0D,qBAA0C;gBAC9CnD,YAKER,EAAAA,oBAAAA,MAAMQ,UAAU,sBAAhBR,4BAAAA,kBACI4D,OAAO,CAAC,8BAA8B,+BAD1C5D,0BAEI4D,OAAO,CAAC,wBAAwB,QAAO;gBAC7C3D,MAAM8C,iBAAiBpC,MAAM;gBAC7BP,OAAO2C,iBAAiB1C,IAAI;gBAC5BC,SACEyC,iBAAiBxC,MAAM,KAAK,OAAO,OAAOwC,iBAAiBxC,MAAM,GAAG;gBACtE,6EAA6E;gBAC7EG,WAAW,EAAE;gBACbD;YACF;YAEA,OAAO;gBACLT,OAAO2D;gBACPhD,QAAQsC;YACV;QACF;IACF;IAEA,OAAO9C;AACT;AAEA,eAAe0D,yBACb9D,OAAgB,EAChB+D,WAAmB,EACnB9D,KAA0B;IAE1B,MAAM8C,SACJ,AAAC,MAAMP,kBAAkBvC,UACzB,4DAA4D;IAC5D,uEAAuE;IACtE,MAAMF,mBAAmBC,SAASC;IACrC,IAAI,CAAC8C,QAAQ;QACX,OAAO;IACT;IAEA,IAAIiB,+BAA+BjB,OAAO9C,KAAK,CAACC,IAAI;IACpD,IACE8D,iCAAiC,QACjCA,6BAA6BpE,UAAU,CAAC,YACxC;QACAoE,+BAA+BhF,KAAKiF,QAAQ,CAC1CF,aACAzE,cAAc0E;IAElB;IAEA,OAAO;QACLJ,oBAAoB;YAClBjD,WAAWoC,OAAO9C,KAAK,CAACU,SAAS;YACjCT,MAAM8D;YACN3D,OAAO0C,OAAO9C,KAAK,CAACI,KAAK;YACzBE,SAASwC,OAAO9C,KAAK,CAACM,OAAO;YAC7BG,SAASqC,OAAO9C,KAAK,CAACS,OAAO;YAC7BD,YAAYsC,OAAO9C,KAAK,CAACQ,UAAU;QACrC;QACAyD,mBAAmBrF,qBAAqBkE,OAAO9C,KAAK,EAAE8C,OAAOnC,MAAM;IACrE;AACF;AAEA,OAAO,SAASuD,qBAAqB,EACnCnE,OAAO,EACP+D,WAAW,EACXK,QAAQ,EAKT;IACC,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAElC,YAAY,EAAE,GAAG,IAAImC,IAAIJ,IAAIK,GAAG,EAAG;QAErD,IAAIF,aAAa,mCAAmC;YAClD,IAAIH,IAAIM,MAAM,KAAK,QAAQ;gBACzB,OAAO5F,mBAAmB6F,UAAU,CAACN;YACvC;YAEA,MAAMvC,OAAO,MAAM,IAAI8C,QAAgB,CAACC,SAASC;gBAC/C,IAAIC,OAAO;gBACXX,IAAIY,EAAE,CAAC,QAAQ,CAACC;oBACdF,QAAQE;gBACV;gBACAb,IAAIY,EAAE,CAAC,OAAO,IAAMH,QAAQE;gBAC5BX,IAAIY,EAAE,CAAC,SAASF;YAClB;YAEA,MAAMI,UAAUC,KAAKC,KAAK,CAACtD;YAC3B,MAAMuD,SAAS,MAAMC,uBAAuB;gBAC1CvF;gBACA+D;gBACA/B,QAAQmD,QAAQnD,MAAM;gBACtBC,UAAUkD,QAAQlD,QAAQ;gBAC1BuD,cAAcL,QAAQK,YAAY;gBAClCC,gBAAgBN,QAAQM,cAAc;YACxC;YAEA3G,2CAA2CwG;YAE3C,OAAOvG,mBAAmB2G,IAAI,CAACpB,KAAKgB;QACtC,OAAO,IAAId,aAAa,2BAA2B;YACjD,MAAMmB,oBAAoBrD,aAAahB,GAAG,CAAC,yBAAyB;YAEpE,IAAIsE;YACJ,IAAID,mBAAmB;gBACrB,MAAME,mBAAmBvD,aAAahB,GAAG,CAAC,WAAW;gBACrD,MAAMwE,UAAU9G,KAAK+G,IAAI,CACvB,OACA3B,WAAW,QAAQ,IACnByB;gBAEFD,mBAAmB,MAAM3G,iBAAiB6G,SAAS,GAAG,GAAG/B;YAC3D,OAAO;gBACL,MAAM9D,QAAQoC,iBAAiBC;gBAC/B,IAAI,CAACrC,OAAO,OAAOlB,mBAAmB6F,UAAU,CAACN;gBACjDsB,mBAAmB,MAAM3G,iBACvBgB,MAAMC,IAAI,EACVD,MAAMK,IAAI,IAAI,GACdL,MAAMO,MAAM,IAAI,GAChBuD;YAEJ;YAEA,IAAI6B,iBAAiBrC,KAAK,EAAE;gBAC1B,OAAOxE,mBAAmBiH,mBAAmB,CAC3C1B,KACAsB,iBAAiBrC,KAAK;YAE1B;YACA,IAAI,CAACqC,iBAAiBK,KAAK,EAAE;gBAC3B,OAAOlH,mBAAmBmH,QAAQ,CAAC5B;YACrC;YACA,OAAOvF,mBAAmBoH,SAAS,CAAC7B;QACtC;QAEA,OAAOC;IACT;AACF;AAEA,OAAO,SAAS6B,uBAAuBpG,OAAgB;IACrD,OAAO,eACLqE,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAElC,YAAY,EAAE,GAAG,IAAImC,IAAIJ,IAAIK,GAAG,EAAG;QAErD,IAAIF,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,IAAI8B,WAAW/D,aAAahB,GAAG,CAAC;QAEhC,IAAI,CAAC+E,UAAU;YACb,OAAOtH,mBAAmB6F,UAAU,CAACN;QACvC;QAEA,IAAIgC;QACJ,IAAI;YACFA,kBAAkBjH,cAAcgH;QAClC,EAAE,OAAOzD,OAAO;YACd,OAAO7D,mBAAmBiH,mBAAmB,CAC3C1B,KACA,qBAGC,CAHD,IAAIzB,MACF,GAAGwD,SAAS,wFAAwF,CAAC,EACrG;gBAAEzD;YAAM,IAFV,qBAAA;uBAAA;4BAAA;8BAAA;YAGA;QAEJ;QAEA,IAAI0D,oBAAoBlG,WAAW;YACjC,MAAMsC,mBAAmB4D,gBAAgB3D,OAAO;YAChD,OAAO5D,mBAAmB2G,IAAI,CAACpB,KAAK5B;QACtC;QAEA,IAAI;YACF,kDAAkD;YAClD2D,WAAWE,UAAUF;QACvB,EAAE,OAAM;YACN,OAAOtH,mBAAmB6F,UAAU,CAACN;QACvC;QAEA,IAAItF,KAAKwH,UAAU,CAACH,WAAW;YAC7BA,WAAW9G,cAAc8G,UAAUrF,IAAI;QACzC;QAEA,IAAI;YACF,MAAMyF,kBAAkB,MAAMzG,QAAQ0G,YAAY,CAACL;YAEnD,IAAII,iBAAiB;gBACnB,OAAO1H,mBAAmB4H,UAAU,CAACrC,KAAKmC;YAC5C;QACF,EAAE,OAAO7D,OAAO;YACd,OAAO7D,mBAAmBiH,mBAAmB,CAC3C1B,KACA,qBAKC,CALD,IAAIzB,MACF,CAAC,8BAA8B,EAAEwD,SAAS,2BAA2B,CAAC,EACtE;gBACEzD;YACF,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKA;QAEJ;QAEA7D,mBAAmBoH,SAAS,CAAC7B;IAC/B;AACF;AAEA,OAAO,eAAeiB,uBAAuB,EAC3CvF,OAAO,EACP+D,WAAW,EACX/B,MAAM,EACNC,QAAQ,EACRuD,YAAY,EACZC,cAAc,EAQf;IACC,MAAMmB,cAAc9E,kBAAkB;QACpCE;QACAC;QACAuD;QACAC;IACF;IAEA,OAAOZ,QAAQgC,GAAG,CAChBD,YAAY1E,GAAG,CAAC,OAAOjC;QACrB,IAAI;YACF,MAAM6G,aAAa,MAAMhD,yBACvB9D,SACA+D,aACA9D;YAEF,IAAI6G,eAAe,MAAM;gBACvB,OAAO;oBACLC,QAAQ;oBACRC,QAAQ;gBACV;YACF;YACA,OAAO;gBAAED,QAAQ;gBAAaE,OAAOH;YAAW;QAClD,EAAE,OAAOvD,OAAO;YACd,OAAO;gBACLwD,QAAQ;gBACRC,QAAQxH,QAAQ+D,OAAO;oBAAE2D,QAAQ;gBAAM;YACzC;QACF;IACF;AAEJ", "ignoreList": [0]}