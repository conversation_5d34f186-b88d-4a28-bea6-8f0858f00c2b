{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "sourcesContent": ["import type { RouteMetadata } from '../../../export/routes/types'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from '.'\nimport type { CacheFs } from '../../../shared/lib/utils'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchValue,\n  type IncrementalCacheValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\n\nimport type { LRUCache } from '../lru-cache'\nimport path from '../../../shared/lib/isomorphic/path'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_DATA_SUFFIX,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n} from '../../../lib/constants'\nimport { isStale, tagsManifest } from './tags-manifest.external'\nimport { MultiFileWriter } from '../../../lib/multi-file-writer'\nimport { getMemoryCache } from './memory-cache.external'\n\ntype FileSystemCacheContext = Omit<\n  CacheHandlerContext,\n  'fs' | 'serverDistDir'\n> & {\n  fs: CacheFs\n  serverDistDir: string\n}\n\nexport default class FileSystemCache implements CacheHandler {\n  private fs: FileSystemCacheContext['fs']\n  private flushToDisk?: FileSystemCacheContext['flushToDisk']\n  private serverDistDir: FileSystemCacheContext['serverDistDir']\n  private revalidatedTags: string[]\n  private static debug: boolean = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n  private static memoryCache: LRUCache<CacheHandlerValue> | undefined\n\n  constructor(ctx: FileSystemCacheContext) {\n    this.fs = ctx.fs\n    this.flushToDisk = ctx.flushToDisk\n    this.serverDistDir = ctx.serverDistDir\n    this.revalidatedTags = ctx.revalidatedTags\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!FileSystemCache.memoryCache) {\n        if (FileSystemCache.debug) {\n          console.log('using memory store for fetch cache')\n        }\n\n        FileSystemCache.memoryCache = getMemoryCache(ctx.maxMemoryCacheSize)\n      } else if (FileSystemCache.debug) {\n        console.log('memory store already initialized')\n      }\n    } else if (FileSystemCache.debug) {\n      console.log('not using memory store for fetch cache')\n    }\n  }\n\n  public resetRequestCache(): void {}\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n\n    if (FileSystemCache.debug) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (tags.length === 0) {\n      return\n    }\n\n    for (const tag of tags) {\n      if (!tagsManifest.has(tag)) {\n        tagsManifest.set(tag, Date.now())\n      }\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { kind } = ctx\n\n    let data = FileSystemCache.memoryCache?.get(key)\n\n    if (FileSystemCache.debug) {\n      if (kind === IncrementalCacheKind.FETCH) {\n        console.log('get', key, ctx.tags, kind, !!data)\n      } else {\n        console.log('get', key, kind, !!data)\n      }\n    }\n\n    // let's check the disk for seed data\n    if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n      try {\n        if (kind === IncrementalCacheKind.APP_ROUTE) {\n          const filePath = this.getFilePath(\n            `${key}.body`,\n            IncrementalCacheKind.APP_ROUTE\n          )\n          const fileData = await this.fs.readFile(filePath)\n          const { mtime } = await this.fs.stat(filePath)\n\n          const meta = JSON.parse(\n            await this.fs.readFile(\n              filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n              'utf8'\n            )\n          )\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_ROUTE,\n              body: fileData,\n              headers: meta.headers,\n              status: meta.status,\n            },\n          }\n        } else {\n          const filePath = this.getFilePath(\n            kind === IncrementalCacheKind.FETCH ? key : `${key}.html`,\n            kind\n          )\n\n          const fileData = await this.fs.readFile(filePath, 'utf8')\n          const { mtime } = await this.fs.stat(filePath)\n\n          if (kind === IncrementalCacheKind.FETCH) {\n            const { tags, fetchIdx, fetchUrl } = ctx\n\n            if (!this.flushToDisk) return null\n\n            const lastModified = mtime.getTime()\n            const parsedData: CachedFetchValue = JSON.parse(fileData)\n            data = {\n              lastModified,\n              value: parsedData,\n            }\n\n            if (data.value?.kind === CachedRouteKind.FETCH) {\n              const storedTags = data.value?.tags\n\n              // update stored tags if a new one is being added\n              // TODO: remove this when we can send the tags\n              // via header on GET same as SET\n              if (!tags?.every((tag) => storedTags?.includes(tag))) {\n                if (FileSystemCache.debug) {\n                  console.log('tags vs storedTags mismatch', tags, storedTags)\n                }\n                await this.set(key, data.value, {\n                  fetchCache: true,\n                  tags,\n                  fetchIdx,\n                  fetchUrl,\n                })\n              }\n            }\n          } else if (kind === IncrementalCacheKind.APP_PAGE) {\n            // We try to load the metadata file, but if it fails, we don't\n            // error. We also don't load it if this is a fallback.\n            let meta: RouteMetadata | undefined\n            try {\n              meta = JSON.parse(\n                await this.fs.readFile(\n                  filePath.replace(/\\.html$/, NEXT_META_SUFFIX),\n                  'utf8'\n                )\n              )\n            } catch {}\n\n            let maybeSegmentData: Map<string, Buffer> | undefined\n            if (meta?.segmentPaths) {\n              // Collect all the segment data for this page.\n              // TODO: To optimize file system reads, we should consider creating\n              // separate cache entries for each segment, rather than storing them\n              // all on the page's entry. Though the behavior is\n              // identical regardless.\n              const segmentData: Map<string, Buffer> = new Map()\n              maybeSegmentData = segmentData\n              const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX\n              await Promise.all(\n                meta.segmentPaths.map(async (segmentPath: string) => {\n                  const segmentDataFilePath = this.getFilePath(\n                    segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX,\n                    IncrementalCacheKind.APP_PAGE\n                  )\n                  try {\n                    segmentData.set(\n                      segmentPath,\n                      await this.fs.readFile(segmentDataFilePath)\n                    )\n                  } catch {\n                    // This shouldn't happen, but if for some reason we fail to\n                    // load a segment from the filesystem, treat it the same as if\n                    // the segment is dynamic and does not have a prefetch.\n                  }\n                })\n              )\n            }\n\n            let rscData: Buffer | undefined\n            if (!ctx.isFallback) {\n              rscData = await this.fs.readFile(\n                this.getFilePath(\n                  `${key}${ctx.isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`,\n                  IncrementalCacheKind.APP_PAGE\n                )\n              )\n            }\n\n            data = {\n              lastModified: mtime.getTime(),\n              value: {\n                kind: CachedRouteKind.APP_PAGE,\n                html: fileData,\n                rscData,\n                postponed: meta?.postponed,\n                headers: meta?.headers,\n                status: meta?.status,\n                segmentData: maybeSegmentData,\n              },\n            }\n          } else if (kind === IncrementalCacheKind.PAGES) {\n            let meta: RouteMetadata | undefined\n            let pageData: string | object = {}\n\n            if (!ctx.isFallback) {\n              pageData = JSON.parse(\n                await this.fs.readFile(\n                  this.getFilePath(\n                    `${key}${NEXT_DATA_SUFFIX}`,\n                    IncrementalCacheKind.PAGES\n                  ),\n                  'utf8'\n                )\n              )\n            }\n\n            data = {\n              lastModified: mtime.getTime(),\n              value: {\n                kind: CachedRouteKind.PAGES,\n                html: fileData,\n                pageData,\n                headers: meta?.headers,\n                status: meta?.status,\n              },\n            }\n          } else {\n            throw new Error(\n              `Invariant: Unexpected route kind ${kind} in file system cache.`\n            )\n          }\n        }\n\n        if (data) {\n          FileSystemCache.memoryCache?.set(key, data)\n        }\n      } catch {\n        return null\n      }\n    }\n\n    if (\n      data?.value?.kind === CachedRouteKind.APP_PAGE ||\n      data?.value?.kind === CachedRouteKind.APP_ROUTE ||\n      data?.value?.kind === CachedRouteKind.PAGES\n    ) {\n      let cacheTags: undefined | string[]\n      const tagsHeader = data.value.headers?.[NEXT_CACHE_TAGS_HEADER]\n\n      if (typeof tagsHeader === 'string') {\n        cacheTags = tagsHeader.split(',')\n      }\n\n      if (cacheTags?.length) {\n        // we trigger a blocking validation if an ISR page\n        // had a tag revalidated, if we want to be a background\n        // revalidation instead we return data.lastModified = -1\n        if (isStale(cacheTags, data?.lastModified || Date.now())) {\n          return null\n        }\n      }\n    } else if (data?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags =\n        ctx.kind === IncrementalCacheKind.FETCH\n          ? [...(ctx.tags || []), ...(ctx.softTags || [])]\n          : []\n\n      const wasRevalidated = combinedTags.some((tag) => {\n        if (this.revalidatedTags.includes(tag)) {\n          return true\n        }\n\n        return isStale([tag], data?.lastModified || Date.now())\n      })\n      // When revalidate tag is called we don't return\n      // stale data so it's updated right away\n      if (wasRevalidated) {\n        data = undefined\n      }\n    }\n\n    return data ?? null\n  }\n\n  public async set(\n    key: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ) {\n    FileSystemCache.memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (FileSystemCache.debug) {\n      console.log('set', key)\n    }\n\n    if (!this.flushToDisk || !data) return\n\n    // Create a new writer that will prepare to write all the files to disk\n    // after their containing directory is created.\n    const writer = new MultiFileWriter(this.fs)\n\n    if (data.kind === CachedRouteKind.APP_ROUTE) {\n      const filePath = this.getFilePath(\n        `${key}.body`,\n        IncrementalCacheKind.APP_ROUTE\n      )\n\n      writer.append(filePath, data.body)\n\n      const meta: RouteMetadata = {\n        headers: data.headers,\n        status: data.status,\n        postponed: undefined,\n        segmentPaths: undefined,\n      }\n\n      writer.append(\n        filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n        JSON.stringify(meta, null, 2)\n      )\n    } else if (\n      data.kind === CachedRouteKind.PAGES ||\n      data.kind === CachedRouteKind.APP_PAGE\n    ) {\n      const isAppPath = data.kind === CachedRouteKind.APP_PAGE\n      const htmlPath = this.getFilePath(\n        `${key}.html`,\n        isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES\n      )\n\n      writer.append(htmlPath, data.html)\n\n      // Fallbacks don't generate a data file.\n      if (!ctx.fetchCache && !ctx.isFallback) {\n        writer.append(\n          this.getFilePath(\n            `${key}${\n              isAppPath\n                ? ctx.isRoutePPREnabled\n                  ? RSC_PREFETCH_SUFFIX\n                  : RSC_SUFFIX\n                : NEXT_DATA_SUFFIX\n            }`,\n            isAppPath\n              ? IncrementalCacheKind.APP_PAGE\n              : IncrementalCacheKind.PAGES\n          ),\n          isAppPath ? data.rscData! : JSON.stringify(data.pageData)\n        )\n      }\n\n      if (data?.kind === CachedRouteKind.APP_PAGE) {\n        let segmentPaths: string[] | undefined\n        if (data.segmentData) {\n          segmentPaths = []\n          const segmentsDir = htmlPath.replace(\n            /\\.html$/,\n            RSC_SEGMENTS_DIR_SUFFIX\n          )\n\n          for (const [segmentPath, buffer] of data.segmentData) {\n            segmentPaths.push(segmentPath)\n            const segmentDataFilePath =\n              segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX\n            writer.append(segmentDataFilePath, buffer)\n          }\n        }\n\n        const meta: RouteMetadata = {\n          headers: data.headers,\n          status: data.status,\n          postponed: data.postponed,\n          segmentPaths,\n        }\n\n        writer.append(\n          htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX),\n          JSON.stringify(meta)\n        )\n      }\n    } else if (data.kind === CachedRouteKind.FETCH) {\n      const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH)\n      writer.append(\n        filePath,\n        JSON.stringify({\n          ...data,\n          tags: ctx.fetchCache ? ctx.tags : [],\n        })\n      )\n    }\n\n    // Wait for all FS operations to complete.\n    await writer.wait()\n  }\n\n  private getFilePath(pathname: string, kind: IncrementalCacheKind): string {\n    switch (kind) {\n      case IncrementalCacheKind.FETCH:\n        // we store in .next/cache/fetch-cache so it can be persisted\n        // across deploys\n        return path.join(\n          this.serverDistDir,\n          '..',\n          'cache',\n          'fetch-cache',\n          pathname\n        )\n      case IncrementalCacheKind.PAGES:\n        return path.join(this.serverDistDir, 'pages', pathname)\n      case IncrementalCacheKind.IMAGE:\n      case IncrementalCacheKind.APP_PAGE:\n      case IncrementalCacheKind.APP_ROUTE:\n        return path.join(this.serverDistDir, 'app', pathname)\n      default:\n        throw new Error(`Unexpected file path kind: ${kind}`)\n    }\n  }\n}\n"], "names": ["FileSystemCache", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "revalidatedTags", "maxMemoryCacheSize", "memoryCache", "console", "log", "getMemoryCache", "resetRequestCache", "revalidateTag", "args", "tags", "length", "tag", "tagsManifest", "has", "set", "Date", "now", "get", "data", "key", "kind", "IncrementalCacheKind", "FETCH", "NEXT_RUNTIME", "APP_ROUTE", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "JSON", "parse", "replace", "NEXT_META_SUFFIX", "lastModified", "getTime", "value", "CachedRouteKind", "body", "headers", "status", "fetchIdx", "fetchUrl", "parsedData", "storedTags", "every", "includes", "fetchCache", "APP_PAGE", "maybeSegmentData", "segmentPaths", "segmentData", "Map", "segmentsDir", "RSC_SEGMENTS_DIR_SUFFIX", "Promise", "all", "map", "segmentPath", "segmentDataFilePath", "RSC_SEGMENT_SUFFIX", "rscData", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "html", "postponed", "PAGES", "pageData", "NEXT_DATA_SUFFIX", "Error", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_CACHE_TAGS_HEADER", "split", "isStale", "combinedTags", "softTags", "wasRevalidated", "some", "undefined", "writer", "MultiFileWriter", "append", "stringify", "isAppPath", "htmlPath", "buffer", "push", "wait", "pathname", "path", "join", "IMAGE"], "mappings": ";;;;+BAmCA;;;eAAqBA;;;+BAzBd;6DAGU;2BASV;sCAC+B;iCACN;qCACD;;;;;;AAUhB,MAAMA;qBAKJC,QAAiB,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;IAGtEC,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,eAAe,GAAGJ,IAAII,eAAe;QAE1C,IAAIJ,IAAIK,kBAAkB,EAAE;YAC1B,IAAI,CAACX,gBAAgBY,WAAW,EAAE;gBAChC,IAAIZ,gBAAgBC,KAAK,EAAE;oBACzBY,QAAQC,GAAG,CAAC;gBACd;gBAEAd,gBAAgBY,WAAW,GAAGG,IAAAA,mCAAc,EAACT,IAAIK,kBAAkB;YACrE,OAAO,IAAIX,gBAAgBC,KAAK,EAAE;gBAChCY,QAAQC,GAAG,CAAC;YACd;QACF,OAAO,IAAId,gBAAgBC,KAAK,EAAE;YAChCY,QAAQC,GAAG,CAAC;QACd;IACF;IAEOE,oBAA0B,CAAC;IAElC,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAInB,gBAAgBC,KAAK,EAAE;YACzBY,QAAQC,GAAG,CAAC,iBAAiBK;QAC/B;QAEA,IAAIA,KAAKC,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,KAAK,MAAMC,OAAOF,KAAM;YACtB,IAAI,CAACG,kCAAY,CAACC,GAAG,CAACF,MAAM;gBAC1BC,kCAAY,CAACE,GAAG,CAACH,KAAKI,KAAKC,GAAG;YAChC;QACF;IACF;IAEA,MAAaC,IAAI,GAAGT,IAAqC,EAAE;YAI9ClB,8BAuLT4B,aACAA,cACAA,cAiBSA;QA7MX,MAAM,CAACC,KAAKvB,IAAI,GAAGY;QACnB,MAAM,EAAEY,IAAI,EAAE,GAAGxB;QAEjB,IAAIsB,QAAO5B,+BAAAA,gBAAgBY,WAAW,qBAA3BZ,6BAA6B2B,GAAG,CAACE;QAE5C,IAAI7B,gBAAgBC,KAAK,EAAE;YACzB,IAAI6B,SAASC,mCAAoB,CAACC,KAAK,EAAE;gBACvCnB,QAAQC,GAAG,CAAC,OAAOe,KAAKvB,IAAIa,IAAI,EAAEW,MAAM,CAAC,CAACF;YAC5C,OAAO;gBACLf,QAAQC,GAAG,CAAC,OAAOe,KAAKC,MAAM,CAAC,CAACF;YAClC;QACF;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQ1B,QAAQC,GAAG,CAAC8B,YAAY,KAAK,QAAQ;YAChD,IAAI;gBACF,IAAIH,SAASC,mCAAoB,CAACG,SAAS,EAAE;oBAC3C,MAAMC,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGP,IAAI,KAAK,CAAC,EACbE,mCAAoB,CAACG,SAAS;oBAEhC,MAAMG,WAAW,MAAM,IAAI,CAAC9B,EAAE,CAAC+B,QAAQ,CAACH;oBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAChC,EAAE,CAACiC,IAAI,CAACL;oBAErC,MAAMM,OAAOC,KAAKC,KAAK,CACrB,MAAM,IAAI,CAACpC,EAAE,CAAC+B,QAAQ,CACpBH,SAASS,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;oBAIJjB,OAAO;wBACLkB,cAAcP,MAAMQ,OAAO;wBAC3BC,OAAO;4BACLlB,MAAMmB,8BAAe,CAACf,SAAS;4BAC/BgB,MAAMb;4BACNc,SAASV,KAAKU,OAAO;4BACrBC,QAAQX,KAAKW,MAAM;wBACrB;oBACF;gBACF,OAAO;oBACL,MAAMjB,WAAW,IAAI,CAACC,WAAW,CAC/BN,SAASC,mCAAoB,CAACC,KAAK,GAAGH,MAAM,GAAGA,IAAI,KAAK,CAAC,EACzDC;oBAGF,MAAMO,WAAW,MAAM,IAAI,CAAC9B,EAAE,CAAC+B,QAAQ,CAACH,UAAU;oBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAChC,EAAE,CAACiC,IAAI,CAACL;oBAErC,IAAIL,SAASC,mCAAoB,CAACC,KAAK,EAAE;4BAYnCJ;wBAXJ,MAAM,EAAET,IAAI,EAAEkC,QAAQ,EAAEC,QAAQ,EAAE,GAAGhD;wBAErC,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,OAAO;wBAE9B,MAAMsC,eAAeP,MAAMQ,OAAO;wBAClC,MAAMQ,aAA+Bb,KAAKC,KAAK,CAACN;wBAChDT,OAAO;4BACLkB;4BACAE,OAAOO;wBACT;wBAEA,IAAI3B,EAAAA,eAAAA,KAAKoB,KAAK,qBAAVpB,aAAYE,IAAI,MAAKmB,8BAAe,CAACjB,KAAK,EAAE;gCAC3BJ;4BAAnB,MAAM4B,cAAa5B,eAAAA,KAAKoB,KAAK,qBAAVpB,aAAYT,IAAI;4BAEnC,iDAAiD;4BACjD,8CAA8C;4BAC9C,gCAAgC;4BAChC,IAAI,EAACA,wBAAAA,KAAMsC,KAAK,CAAC,CAACpC,MAAQmC,8BAAAA,WAAYE,QAAQ,CAACrC,QAAO;gCACpD,IAAIrB,gBAAgBC,KAAK,EAAE;oCACzBY,QAAQC,GAAG,CAAC,+BAA+BK,MAAMqC;gCACnD;gCACA,MAAM,IAAI,CAAChC,GAAG,CAACK,KAAKD,KAAKoB,KAAK,EAAE;oCAC9BW,YAAY;oCACZxC;oCACAkC;oCACAC;gCACF;4BACF;wBACF;oBACF,OAAO,IAAIxB,SAASC,mCAAoB,CAAC6B,QAAQ,EAAE;wBACjD,8DAA8D;wBAC9D,sDAAsD;wBACtD,IAAInB;wBACJ,IAAI;4BACFA,OAAOC,KAAKC,KAAK,CACf,MAAM,IAAI,CAACpC,EAAE,CAAC+B,QAAQ,CACpBH,SAASS,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;wBAGN,EAAE,OAAM,CAAC;wBAET,IAAIgB;wBACJ,IAAIpB,wBAAAA,KAAMqB,YAAY,EAAE;4BACtB,8CAA8C;4BAC9C,mEAAmE;4BACnE,oEAAoE;4BACpE,kDAAkD;4BAClD,wBAAwB;4BACxB,MAAMC,cAAmC,IAAIC;4BAC7CH,mBAAmBE;4BACnB,MAAME,cAAcpC,MAAMqC,kCAAuB;4BACjD,MAAMC,QAAQC,GAAG,CACf3B,KAAKqB,YAAY,CAACO,GAAG,CAAC,OAAOC;gCAC3B,MAAMC,sBAAsB,IAAI,CAACnC,WAAW,CAC1C6B,cAAcK,cAAcE,6BAAkB,EAC9CzC,mCAAoB,CAAC6B,QAAQ;gCAE/B,IAAI;oCACFG,YAAYvC,GAAG,CACb8C,aACA,MAAM,IAAI,CAAC/D,EAAE,CAAC+B,QAAQ,CAACiC;gCAE3B,EAAE,OAAM;gCACN,2DAA2D;gCAC3D,8DAA8D;gCAC9D,uDAAuD;gCACzD;4BACF;wBAEJ;wBAEA,IAAIE;wBACJ,IAAI,CAACnE,IAAIoE,UAAU,EAAE;4BACnBD,UAAU,MAAM,IAAI,CAAClE,EAAE,CAAC+B,QAAQ,CAC9B,IAAI,CAACF,WAAW,CACd,GAAGP,MAAMvB,IAAIqE,iBAAiB,GAAGC,8BAAmB,GAAGC,qBAAU,EAAE,EACnE9C,mCAAoB,CAAC6B,QAAQ;wBAGnC;wBAEAhC,OAAO;4BACLkB,cAAcP,MAAMQ,OAAO;4BAC3BC,OAAO;gCACLlB,MAAMmB,8BAAe,CAACW,QAAQ;gCAC9BkB,MAAMzC;gCACNoC;gCACAM,SAAS,EAAEtC,wBAAAA,KAAMsC,SAAS;gCAC1B5B,OAAO,EAAEV,wBAAAA,KAAMU,OAAO;gCACtBC,MAAM,EAAEX,wBAAAA,KAAMW,MAAM;gCACpBW,aAAaF;4BACf;wBACF;oBACF,OAAO,IAAI/B,SAASC,mCAAoB,CAACiD,KAAK,EAAE;wBAC9C,IAAIvC;wBACJ,IAAIwC,WAA4B,CAAC;wBAEjC,IAAI,CAAC3E,IAAIoE,UAAU,EAAE;4BACnBO,WAAWvC,KAAKC,KAAK,CACnB,MAAM,IAAI,CAACpC,EAAE,CAAC+B,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,GAAGP,MAAMqD,2BAAgB,EAAE,EAC3BnD,mCAAoB,CAACiD,KAAK,GAE5B;wBAGN;wBAEApD,OAAO;4BACLkB,cAAcP,MAAMQ,OAAO;4BAC3BC,OAAO;gCACLlB,MAAMmB,8BAAe,CAAC+B,KAAK;gCAC3BF,MAAMzC;gCACN4C;gCACA9B,OAAO,EAAEV,wBAAAA,KAAMU,OAAO;gCACtBC,MAAM,EAAEX,wBAAAA,KAAMW,MAAM;4BACtB;wBACF;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAI+B,MACR,CAAC,iCAAiC,EAAErD,KAAK,sBAAsB,CAAC,GAD5D,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,IAAIF,MAAM;wBACR5B;qBAAAA,gCAAAA,gBAAgBY,WAAW,qBAA3BZ,8BAA6BwB,GAAG,CAACK,KAAKD;gBACxC;YACF,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IACEA,CAAAA,yBAAAA,cAAAA,KAAMoB,KAAK,qBAAXpB,YAAaE,IAAI,MAAKmB,8BAAe,CAACW,QAAQ,IAC9ChC,CAAAA,yBAAAA,eAAAA,KAAMoB,KAAK,qBAAXpB,aAAaE,IAAI,MAAKmB,8BAAe,CAACf,SAAS,IAC/CN,CAAAA,yBAAAA,eAAAA,KAAMoB,KAAK,qBAAXpB,aAAaE,IAAI,MAAKmB,8BAAe,CAAC+B,KAAK,EAC3C;gBAEmBpD;YADnB,IAAIwD;YACJ,MAAMC,cAAazD,sBAAAA,KAAKoB,KAAK,CAACG,OAAO,qBAAlBvB,mBAAoB,CAAC0D,iCAAsB,CAAC;YAE/D,IAAI,OAAOD,eAAe,UAAU;gBAClCD,YAAYC,WAAWE,KAAK,CAAC;YAC/B;YAEA,IAAIH,6BAAAA,UAAWhE,MAAM,EAAE;gBACrB,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAIoE,IAAAA,6BAAO,EAACJ,WAAWxD,CAAAA,wBAAAA,KAAMkB,YAAY,KAAIrB,KAAKC,GAAG,KAAK;oBACxD,OAAO;gBACT;YACF;QACF,OAAO,IAAIE,CAAAA,yBAAAA,eAAAA,KAAMoB,KAAK,qBAAXpB,aAAaE,IAAI,MAAKmB,8BAAe,CAACjB,KAAK,EAAE;YACtD,MAAMyD,eACJnF,IAAIwB,IAAI,KAAKC,mCAAoB,CAACC,KAAK,GACnC;mBAAK1B,IAAIa,IAAI,IAAI,EAAE;mBAAOb,IAAIoF,QAAQ,IAAI,EAAE;aAAE,GAC9C,EAAE;YAER,MAAMC,iBAAiBF,aAAaG,IAAI,CAAC,CAACvE;gBACxC,IAAI,IAAI,CAACX,eAAe,CAACgD,QAAQ,CAACrC,MAAM;oBACtC,OAAO;gBACT;gBAEA,OAAOmE,IAAAA,6BAAO,EAAC;oBAACnE;iBAAI,EAAEO,CAAAA,wBAAAA,KAAMkB,YAAY,KAAIrB,KAAKC,GAAG;YACtD;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAIiE,gBAAgB;gBAClB/D,OAAOiE;YACT;QACF;QAEA,OAAOjE,QAAQ;IACjB;IAEA,MAAaJ,IACXK,GAAW,EACXD,IAAkC,EAClCtB,GAAyE,EACzE;YACAN;SAAAA,+BAAAA,gBAAgBY,WAAW,qBAA3BZ,6BAA6BwB,GAAG,CAACK,KAAK;YACpCmB,OAAOpB;YACPkB,cAAcrB,KAAKC,GAAG;QACxB;QAEA,IAAI1B,gBAAgBC,KAAK,EAAE;YACzBY,QAAQC,GAAG,CAAC,OAAOe;QACrB;QAEA,IAAI,CAAC,IAAI,CAACrB,WAAW,IAAI,CAACoB,MAAM;QAEhC,uEAAuE;QACvE,+CAA+C;QAC/C,MAAMkE,SAAS,IAAIC,gCAAe,CAAC,IAAI,CAACxF,EAAE;QAE1C,IAAIqB,KAAKE,IAAI,KAAKmB,8BAAe,CAACf,SAAS,EAAE;YAC3C,MAAMC,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGP,IAAI,KAAK,CAAC,EACbE,mCAAoB,CAACG,SAAS;YAGhC4D,OAAOE,MAAM,CAAC7D,UAAUP,KAAKsB,IAAI;YAEjC,MAAMT,OAAsB;gBAC1BU,SAASvB,KAAKuB,OAAO;gBACrBC,QAAQxB,KAAKwB,MAAM;gBACnB2B,WAAWc;gBACX/B,cAAc+B;YAChB;YAEAC,OAAOE,MAAM,CACX7D,SAASS,OAAO,CAAC,WAAWC,2BAAgB,GAC5CH,KAAKuD,SAAS,CAACxD,MAAM,MAAM;QAE/B,OAAO,IACLb,KAAKE,IAAI,KAAKmB,8BAAe,CAAC+B,KAAK,IACnCpD,KAAKE,IAAI,KAAKmB,8BAAe,CAACW,QAAQ,EACtC;YACA,MAAMsC,YAAYtE,KAAKE,IAAI,KAAKmB,8BAAe,CAACW,QAAQ;YACxD,MAAMuC,WAAW,IAAI,CAAC/D,WAAW,CAC/B,GAAGP,IAAI,KAAK,CAAC,EACbqE,YAAYnE,mCAAoB,CAAC6B,QAAQ,GAAG7B,mCAAoB,CAACiD,KAAK;YAGxEc,OAAOE,MAAM,CAACG,UAAUvE,KAAKkD,IAAI;YAEjC,wCAAwC;YACxC,IAAI,CAACxE,IAAIqD,UAAU,IAAI,CAACrD,IAAIoE,UAAU,EAAE;gBACtCoB,OAAOE,MAAM,CACX,IAAI,CAAC5D,WAAW,CACd,GAAGP,MACDqE,YACI5F,IAAIqE,iBAAiB,GACnBC,8BAAmB,GACnBC,qBAAU,GACZK,2BAAgB,EACpB,EACFgB,YACInE,mCAAoB,CAAC6B,QAAQ,GAC7B7B,mCAAoB,CAACiD,KAAK,GAEhCkB,YAAYtE,KAAK6C,OAAO,GAAI/B,KAAKuD,SAAS,CAACrE,KAAKqD,QAAQ;YAE5D;YAEA,IAAIrD,CAAAA,wBAAAA,KAAME,IAAI,MAAKmB,8BAAe,CAACW,QAAQ,EAAE;gBAC3C,IAAIE;gBACJ,IAAIlC,KAAKmC,WAAW,EAAE;oBACpBD,eAAe,EAAE;oBACjB,MAAMG,cAAckC,SAASvD,OAAO,CAClC,WACAsB,kCAAuB;oBAGzB,KAAK,MAAM,CAACI,aAAa8B,OAAO,IAAIxE,KAAKmC,WAAW,CAAE;wBACpDD,aAAauC,IAAI,CAAC/B;wBAClB,MAAMC,sBACJN,cAAcK,cAAcE,6BAAkB;wBAChDsB,OAAOE,MAAM,CAACzB,qBAAqB6B;oBACrC;gBACF;gBAEA,MAAM3D,OAAsB;oBAC1BU,SAASvB,KAAKuB,OAAO;oBACrBC,QAAQxB,KAAKwB,MAAM;oBACnB2B,WAAWnD,KAAKmD,SAAS;oBACzBjB;gBACF;gBAEAgC,OAAOE,MAAM,CACXG,SAASvD,OAAO,CAAC,WAAWC,2BAAgB,GAC5CH,KAAKuD,SAAS,CAACxD;YAEnB;QACF,OAAO,IAAIb,KAAKE,IAAI,KAAKmB,8BAAe,CAACjB,KAAK,EAAE;YAC9C,MAAMG,WAAW,IAAI,CAACC,WAAW,CAACP,KAAKE,mCAAoB,CAACC,KAAK;YACjE8D,OAAOE,MAAM,CACX7D,UACAO,KAAKuD,SAAS,CAAC;gBACb,GAAGrE,IAAI;gBACPT,MAAMb,IAAIqD,UAAU,GAAGrD,IAAIa,IAAI,GAAG,EAAE;YACtC;QAEJ;QAEA,0CAA0C;QAC1C,MAAM2E,OAAOQ,IAAI;IACnB;IAEQlE,YAAYmE,QAAgB,EAAEzE,IAA0B,EAAU;QACxE,OAAQA;YACN,KAAKC,mCAAoB,CAACC,KAAK;gBAC7B,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOwE,aAAI,CAACC,IAAI,CACd,IAAI,CAAChG,aAAa,EAClB,MACA,SACA,eACA8F;YAEJ,KAAKxE,mCAAoB,CAACiD,KAAK;gBAC7B,OAAOwB,aAAI,CAACC,IAAI,CAAC,IAAI,CAAChG,aAAa,EAAE,SAAS8F;YAChD,KAAKxE,mCAAoB,CAAC2E,KAAK;YAC/B,KAAK3E,mCAAoB,CAAC6B,QAAQ;YAClC,KAAK7B,mCAAoB,CAACG,SAAS;gBACjC,OAAOsE,aAAI,CAACC,IAAI,CAAC,IAAI,CAAChG,aAAa,EAAE,OAAO8F;YAC9C;gBACE,MAAM,qBAA+C,CAA/C,IAAIpB,MAAM,CAAC,2BAA2B,EAAErD,MAAM,GAA9C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;QACxD;IACF;AACF", "ignoreList": [0]}