{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "sourcesContent": ["import { createHash } from 'crypto'\nimport { promises } from 'fs'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport { mediaType } from 'next/dist/compiled/@hapi/accept'\nimport contentDisposition from 'next/dist/compiled/content-disposition'\nimport imageSizeOf from 'next/dist/compiled/image-size'\nimport { detector } from 'next/dist/compiled/image-detector/detector.js'\nimport isAnimated from 'next/dist/compiled/is-animated'\nimport { join } from 'path'\nimport nodeUrl, { type UrlWithParsedQuery } from 'url'\n\nimport { getImageBlurSvg } from '../shared/lib/image-blur-svg'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { hasLocalMatch } from '../shared/lib/match-local-pattern'\nimport { hasRemoteMatch } from '../shared/lib/match-remote-pattern'\nimport type { NextConfigComplete } from './config-shared'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport type { NextUrlWithParsedQuery } from './request-meta'\nimport {\n  CachedRouteKind,\n  type CachedImageValue,\n  type IncrementalCacheEntry,\n  type IncrementalCacheValue,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { sendEtagResponse } from './send-payload'\nimport { getContentType, getExtension } from './serve-static'\nimport * as Log from '../build/output/log'\nimport isError from '../lib/is-error'\nimport { parseUrl } from '../lib/url'\nimport type { CacheControl } from './lib/cache-control'\nimport { InvariantError } from '../shared/lib/invariant-error'\n\ntype XCacheHeader = 'MISS' | 'HIT' | 'STALE'\n\nconst AVIF = 'image/avif'\nconst WEBP = 'image/webp'\nconst PNG = 'image/png'\nconst JPEG = 'image/jpeg'\nconst JXL = 'image/jxl'\nconst JP2 = 'image/jp2'\nconst HEIC = 'image/heic'\nconst GIF = 'image/gif'\nconst SVG = 'image/svg+xml'\nconst ICO = 'image/x-icon'\nconst ICNS = 'image/x-icns'\nconst TIFF = 'image/tiff'\nconst BMP = 'image/bmp'\nconst PDF = 'application/pdf'\nconst CACHE_VERSION = 4\nconst ANIMATABLE_TYPES = [WEBP, PNG, GIF]\nconst BYPASS_TYPES = [SVG, ICO, ICNS, BMP, JXL, HEIC]\nconst BLUR_IMG_SIZE = 8 // should match `next-image-loader`\nconst BLUR_QUALITY = 70 // should match `next-image-loader`\n\nlet _sharp: typeof import('sharp')\n\nexport function getSharp(concurrency: number | null | undefined) {\n  if (_sharp) {\n    return _sharp\n  }\n  try {\n    _sharp = require('sharp') as typeof import('sharp')\n    if (_sharp && _sharp.concurrency() > 1) {\n      // Reducing concurrency should reduce the memory usage too.\n      // We more aggressively reduce in dev but also reduce in prod.\n      // https://sharp.pixelplumbing.com/api-utility#concurrency\n      const divisor = process.env.NODE_ENV === 'development' ? 4 : 2\n      _sharp.concurrency(\n        concurrency ?? Math.floor(Math.max(_sharp.concurrency() / divisor, 1))\n      )\n    }\n  } catch (e: unknown) {\n    if (isError(e) && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        'Module `sharp` not found. Please run `npm install --cpu=wasm32 sharp` to install it.'\n      )\n    }\n    throw e\n  }\n  return _sharp\n}\n\nexport interface ImageParamsResult {\n  href: string\n  isAbsolute: boolean\n  isStatic: boolean\n  width: number\n  quality: number\n  mimeType: string\n  sizes: number[]\n  minimumCacheTTL: number\n}\n\ninterface ImageUpstream {\n  buffer: Buffer\n  contentType: string | null | undefined\n  cacheControl: string | null | undefined\n  etag: string\n}\n\nfunction getSupportedMimeType(options: string[], accept = ''): string {\n  const mimeType = mediaType(accept, options)\n  return accept.includes(mimeType) ? mimeType : ''\n}\n\nexport function getHash(items: (string | number | Buffer)[]) {\n  const hash = createHash('sha256')\n  for (let item of items) {\n    if (typeof item === 'number') hash.update(String(item))\n    else {\n      hash.update(item)\n    }\n  }\n  // See https://en.wikipedia.org/wiki/Base64#URL_applications\n  return hash.digest('base64url')\n}\n\nexport function extractEtag(\n  etag: string | null | undefined,\n  imageBuffer: Buffer\n) {\n  if (etag) {\n    // upstream etag needs to be base64url encoded due to weak etag signature\n    // as we store this in the cache-entry file name.\n    return Buffer.from(etag).toString('base64url')\n  }\n  return getImageEtag(imageBuffer)\n}\n\nexport function getImageEtag(image: Buffer) {\n  return getHash([image])\n}\n\nasync function writeToCacheDir(\n  dir: string,\n  extension: string,\n  maxAge: number,\n  expireAt: number,\n  buffer: Buffer,\n  etag: string,\n  upstreamEtag: string\n) {\n  const filename = join(\n    dir,\n    `${maxAge}.${expireAt}.${etag}.${upstreamEtag}.${extension}`\n  )\n\n  await promises.rm(dir, { recursive: true, force: true }).catch(() => {})\n\n  await promises.mkdir(dir, { recursive: true })\n  await promises.writeFile(filename, buffer)\n}\n\n/**\n * Inspects the first few bytes of a buffer to determine if\n * it matches the \"magic number\" of known file signatures.\n * https://en.wikipedia.org/wiki/List_of_file_signatures\n */\nexport async function detectContentType(\n  buffer: Buffer,\n  skipMetadata: boolean | null | undefined,\n  concurrency?: number | null | undefined\n): Promise<string | null> {\n  if (buffer.byteLength === 0) {\n    return null\n  }\n  if ([0xff, 0xd8, 0xff].every((b, i) => buffer[i] === b)) {\n    return JPEG\n  }\n  if (\n    [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a].every(\n      (b, i) => buffer[i] === b\n    )\n  ) {\n    return PNG\n  }\n  if ([0x47, 0x49, 0x46, 0x38].every((b, i) => buffer[i] === b)) {\n    return GIF\n  }\n  if (\n    [0x52, 0x49, 0x46, 0x46, 0, 0, 0, 0, 0x57, 0x45, 0x42, 0x50].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return WEBP\n  }\n  if ([0x3c, 0x3f, 0x78, 0x6d, 0x6c].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if ([0x3c, 0x73, 0x76, 0x67].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if (\n    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return AVIF\n  }\n  if ([0x00, 0x00, 0x01, 0x00].every((b, i) => buffer[i] === b)) {\n    return ICO\n  }\n  if ([0x69, 0x63, 0x6e, 0x73].every((b, i) => buffer[i] === b)) {\n    return ICNS\n  }\n  if ([0x49, 0x49, 0x2a, 0x00].every((b, i) => buffer[i] === b)) {\n    return TIFF\n  }\n  if ([0x42, 0x4d].every((b, i) => buffer[i] === b)) {\n    return BMP\n  }\n  if ([0xff, 0x0a].every((b, i) => buffer[i] === b)) {\n    return JXL\n  }\n  if (\n    [\n      0x00, 0x00, 0x00, 0x0c, 0x4a, 0x58, 0x4c, 0x20, 0x0d, 0x0a, 0x87, 0x0a,\n    ].every((b, i) => buffer[i] === b)\n  ) {\n    return JXL\n  }\n  if (\n    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return HEIC\n  }\n  if ([0x25, 0x50, 0x44, 0x46, 0x2d].every((b, i) => buffer[i] === b)) {\n    return PDF\n  }\n  if (\n    [\n      0x00, 0x00, 0x00, 0x0c, 0x6a, 0x50, 0x20, 0x20, 0x0d, 0x0a, 0x87, 0x0a,\n    ].every((b, i) => buffer[i] === b)\n  ) {\n    return JP2\n  }\n\n  let format:\n    | import('sharp').Metadata['format']\n    | ReturnType<typeof detector>\n    | undefined\n  format = detector(buffer)\n\n  if (!format && !skipMetadata) {\n    const sharp = getSharp(concurrency)\n    const meta = await sharp(buffer)\n      .metadata()\n      .catch((_) => null)\n    format = meta?.format\n  }\n\n  switch (format) {\n    case 'avif':\n      return AVIF\n    case 'webp':\n      return WEBP\n    case 'png':\n      return PNG\n    case 'jpeg':\n    case 'jpg':\n      return JPEG\n    case 'gif':\n      return GIF\n    case 'svg':\n      return SVG\n    case 'jxl':\n    case 'jxl-stream':\n      return JXL\n    case 'jp2':\n      return JP2\n    case 'tiff':\n    case 'tif':\n      return TIFF\n    case 'pdf':\n      return PDF\n    case 'bmp':\n      return BMP\n    case 'ico':\n      return ICO\n    case 'icns':\n      return ICNS\n    case 'dcraw':\n    case 'dz':\n    case 'exr':\n    case 'fits':\n    case 'heif':\n    case 'input':\n    case 'magick':\n    case 'openslide':\n    case 'ppm':\n    case 'rad':\n    case 'raw':\n    case 'v':\n    case 'cur':\n    case 'dds':\n    case 'j2c':\n    case 'ktx':\n    case 'pnm':\n    case 'psd':\n    case 'tga':\n    case undefined:\n    default:\n      return null\n  }\n}\n\nexport class ImageOptimizerCache {\n  private cacheDir: string\n  private nextConfig: NextConfigComplete\n\n  static validateParams(\n    req: IncomingMessage,\n    query: UrlWithParsedQuery['query'],\n    nextConfig: NextConfigComplete,\n    isDev: boolean\n  ): ImageParamsResult | { errorMessage: string } {\n    const imageData = nextConfig.images\n    const {\n      deviceSizes = [],\n      imageSizes = [],\n      domains = [],\n      minimumCacheTTL = 60,\n      formats = ['image/webp'],\n    } = imageData\n    const remotePatterns = nextConfig.images?.remotePatterns || []\n    const localPatterns = nextConfig.images?.localPatterns\n    const qualities = nextConfig.images?.qualities\n    const { url, w, q } = query\n    let href: string\n\n    if (domains.length > 0) {\n      Log.warnOnce(\n        'The \"images.domains\" configuration is deprecated. Please use \"images.remotePatterns\" configuration instead.'\n      )\n    }\n\n    if (!url) {\n      return { errorMessage: '\"url\" parameter is required' }\n    } else if (Array.isArray(url)) {\n      return { errorMessage: '\"url\" parameter cannot be an array' }\n    }\n\n    if (url.length > 3072) {\n      return { errorMessage: '\"url\" parameter is too long' }\n    }\n\n    if (url.startsWith('//')) {\n      return {\n        errorMessage: '\"url\" parameter cannot be a protocol-relative URL (//)',\n      }\n    }\n\n    let isAbsolute: boolean\n\n    if (url.startsWith('/')) {\n      href = url\n      isAbsolute = false\n      if (\n        /\\/_next\\/image($|\\/)/.test(\n          decodeURIComponent(parseUrl(url)?.pathname ?? '')\n        )\n      ) {\n        return {\n          errorMessage: '\"url\" parameter cannot be recursive',\n        }\n      }\n      if (!hasLocalMatch(localPatterns, url)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    } else {\n      let hrefParsed: URL\n\n      try {\n        hrefParsed = new URL(url)\n        href = hrefParsed.toString()\n        isAbsolute = true\n      } catch (_error) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!['http:', 'https:'].includes(hrefParsed.protocol)) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!hasRemoteMatch(domains, remotePatterns, hrefParsed)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    }\n\n    if (!w) {\n      return { errorMessage: '\"w\" parameter (width) is required' }\n    } else if (Array.isArray(w)) {\n      return { errorMessage: '\"w\" parameter (width) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(w)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    if (!q) {\n      return { errorMessage: '\"q\" parameter (quality) is required' }\n    } else if (Array.isArray(q)) {\n      return { errorMessage: '\"q\" parameter (quality) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(q)) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    const width = parseInt(w, 10)\n\n    if (width <= 0 || isNaN(width)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    const sizes = [...(deviceSizes || []), ...(imageSizes || [])]\n\n    if (isDev) {\n      sizes.push(BLUR_IMG_SIZE)\n    }\n\n    const isValidSize =\n      sizes.includes(width) || (isDev && width <= BLUR_IMG_SIZE)\n\n    if (!isValidSize) {\n      return {\n        errorMessage: `\"w\" parameter (width) of ${width} is not allowed`,\n      }\n    }\n\n    const quality = parseInt(q, 10)\n\n    if (isNaN(quality) || quality < 1 || quality > 100) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    if (qualities) {\n      if (isDev) {\n        qualities.push(BLUR_QUALITY)\n      }\n\n      if (!qualities.includes(quality)) {\n        return {\n          errorMessage: `\"q\" parameter (quality) of ${q} is not allowed`,\n        }\n      }\n    }\n\n    const mimeType = getSupportedMimeType(formats || [], req.headers['accept'])\n\n    const isStatic = url.startsWith(\n      `${nextConfig.basePath || ''}/_next/static/media`\n    )\n\n    return {\n      href,\n      sizes,\n      isAbsolute,\n      isStatic,\n      width,\n      quality,\n      mimeType,\n      minimumCacheTTL,\n    }\n  }\n\n  static getCacheKey({\n    href,\n    width,\n    quality,\n    mimeType,\n  }: {\n    href: string\n    width: number\n    quality: number\n    mimeType: string\n  }): string {\n    return getHash([CACHE_VERSION, href, width, quality, mimeType])\n  }\n\n  constructor({\n    distDir,\n    nextConfig,\n  }: {\n    distDir: string\n    nextConfig: NextConfigComplete\n  }) {\n    this.cacheDir = join(distDir, 'cache', 'images')\n    this.nextConfig = nextConfig\n  }\n\n  async get(cacheKey: string): Promise<IncrementalResponseCacheEntry | null> {\n    try {\n      const cacheDir = join(this.cacheDir, cacheKey)\n      const files = await promises.readdir(cacheDir)\n      const now = Date.now()\n\n      for (const file of files) {\n        const [maxAgeSt, expireAtSt, etag, upstreamEtag, extension] =\n          file.split('.', 5)\n        const buffer = await promises.readFile(join(cacheDir, file))\n        const expireAt = Number(expireAtSt)\n        const maxAge = Number(maxAgeSt)\n\n        return {\n          value: {\n            kind: CachedRouteKind.IMAGE,\n            etag,\n            buffer,\n            extension,\n            upstreamEtag,\n          },\n          revalidateAfter:\n            Math.max(maxAge, this.nextConfig.images.minimumCacheTTL) * 1000 +\n            Date.now(),\n          cacheControl: { revalidate: maxAge, expire: undefined },\n          isStale: now > expireAt,\n        }\n      }\n    } catch (_) {\n      // failed to read from cache dir, treat as cache miss\n    }\n    return null\n  }\n  async set(\n    cacheKey: string,\n    value: IncrementalCacheValue | null,\n    {\n      cacheControl,\n    }: {\n      cacheControl?: CacheControl\n    }\n  ) {\n    if (!this.nextConfig.experimental.isrFlushToDisk) {\n      return\n    }\n\n    if (value?.kind !== CachedRouteKind.IMAGE) {\n      throw new Error('invariant attempted to set non-image to image-cache')\n    }\n\n    const revalidate = cacheControl?.revalidate\n\n    if (typeof revalidate !== 'number') {\n      throw new InvariantError('revalidate must be a number for image-cache')\n    }\n\n    const expireAt =\n      Math.max(revalidate, this.nextConfig.images.minimumCacheTTL) * 1000 +\n      Date.now()\n\n    try {\n      await writeToCacheDir(\n        join(this.cacheDir, cacheKey),\n        value.extension,\n        revalidate,\n        expireAt,\n        value.buffer,\n        value.etag,\n        value.upstreamEtag\n      )\n    } catch (err) {\n      Log.error(`Failed to write image to cache ${cacheKey}`, err)\n    }\n  }\n}\nexport class ImageError extends Error {\n  statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n\n    // ensure an error status is used > 400\n    if (statusCode >= 400) {\n      this.statusCode = statusCode\n    } else {\n      this.statusCode = 500\n    }\n  }\n}\n\nfunction parseCacheControl(\n  str: string | null | undefined\n): Map<string, string> {\n  const map = new Map<string, string>()\n  if (!str) {\n    return map\n  }\n  for (let directive of str.split(',')) {\n    let [key, value] = directive.trim().split('=', 2)\n    key = key.toLowerCase()\n    if (value) {\n      value = value.toLowerCase()\n    }\n    map.set(key, value)\n  }\n  return map\n}\n\nexport function getMaxAge(str: string | null | undefined): number {\n  const map = parseCacheControl(str)\n  if (map) {\n    let age = map.get('s-maxage') || map.get('max-age') || ''\n    if (age.startsWith('\"') && age.endsWith('\"')) {\n      age = age.slice(1, -1)\n    }\n    const n = parseInt(age, 10)\n    if (!isNaN(n)) {\n      return n\n    }\n  }\n  return 0\n}\nexport function getPreviouslyCachedImageOrNull(\n  upstreamImage: ImageUpstream,\n  previousCacheEntry: IncrementalCacheEntry | null | undefined\n): CachedImageValue | null {\n  if (\n    previousCacheEntry?.value?.kind === 'IMAGE' &&\n    // Images that are SVGs, animated or failed the optimization previously end up using upstreamEtag as their etag as well,\n    // in these cases we want to trigger a new \"optimization\" attempt.\n    previousCacheEntry.value.upstreamEtag !== previousCacheEntry.value.etag &&\n    // and the upstream etag is the same as the previous cache entry's\n    upstreamImage.etag === previousCacheEntry.value.upstreamEtag\n  ) {\n    return previousCacheEntry.value\n  }\n  return null\n}\n\nexport async function optimizeImage({\n  buffer,\n  contentType,\n  quality,\n  width,\n  height,\n  concurrency,\n  limitInputPixels,\n  sequentialRead,\n  timeoutInSeconds,\n}: {\n  buffer: Buffer\n  contentType: string\n  quality: number\n  width: number\n  height?: number\n  concurrency?: number | null\n  limitInputPixels?: number\n  sequentialRead?: boolean | null\n  timeoutInSeconds?: number\n}): Promise<Buffer> {\n  const sharp = getSharp(concurrency)\n  const transformer = sharp(buffer, {\n    limitInputPixels,\n    sequentialRead: sequentialRead ?? undefined,\n  })\n    .timeout({\n      seconds: timeoutInSeconds ?? 7,\n    })\n    .rotate()\n\n  if (height) {\n    transformer.resize(width, height)\n  } else {\n    transformer.resize(width, undefined, {\n      withoutEnlargement: true,\n    })\n  }\n\n  if (contentType === AVIF) {\n    transformer.avif({\n      quality: Math.max(quality - 20, 1),\n      effort: 3,\n    })\n  } else if (contentType === WEBP) {\n    transformer.webp({ quality })\n  } else if (contentType === PNG) {\n    transformer.png({ quality })\n  } else if (contentType === JPEG) {\n    transformer.jpeg({ quality, mozjpeg: true })\n  }\n\n  const optimizedBuffer = await transformer.toBuffer()\n\n  return optimizedBuffer\n}\n\nexport async function fetchExternalImage(href: string): Promise<ImageUpstream> {\n  const res = await fetch(href, {\n    signal: AbortSignal.timeout(7_000),\n  }).catch((err) => err as Error)\n\n  if (res instanceof Error) {\n    const err = res as Error\n    if (err.name === 'TimeoutError') {\n      Log.error('upstream image response timed out for', href)\n      throw new ImageError(\n        504,\n        '\"url\" parameter is valid but upstream response timed out'\n      )\n    }\n    throw err\n  }\n\n  if (!res.ok) {\n    Log.error('upstream image response failed for', href, res.status)\n    throw new ImageError(\n      res.status,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n\n  const buffer = Buffer.from(await res.arrayBuffer())\n  const contentType = res.headers.get('Content-Type')\n  const cacheControl = res.headers.get('Cache-Control')\n  const etag = extractEtag(res.headers.get('ETag'), buffer)\n  return { buffer, contentType, cacheControl, etag }\n}\n\nexport async function fetchInternalImage(\n  href: string,\n  _req: IncomingMessage,\n  _res: ServerResponse,\n  handleRequest: (\n    newReq: IncomingMessage,\n    newRes: ServerResponse,\n    newParsedUrl?: NextUrlWithParsedQuery\n  ) => Promise<void>\n): Promise<ImageUpstream> {\n  try {\n    const mocked = createRequestResponseMocks({\n      url: href,\n      method: _req.method || 'GET',\n      socket: _req.socket,\n    })\n\n    await handleRequest(mocked.req, mocked.res, nodeUrl.parse(href, true))\n    await mocked.res.hasStreamed\n\n    if (!mocked.res.statusCode) {\n      Log.error('image response failed for', href, mocked.res.statusCode)\n      throw new ImageError(\n        mocked.res.statusCode,\n        '\"url\" parameter is valid but internal response is invalid'\n      )\n    }\n\n    const buffer = Buffer.concat(mocked.res.buffers)\n    const contentType = mocked.res.getHeader('Content-Type')\n    const cacheControl = mocked.res.getHeader('Cache-Control')\n    const etag = extractEtag(mocked.res.getHeader('ETag'), buffer)\n\n    return { buffer, contentType, cacheControl, etag }\n  } catch (err) {\n    Log.error('upstream image response failed for', href, err)\n    throw new ImageError(\n      500,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n}\n\nexport async function imageOptimizer(\n  imageUpstream: ImageUpstream,\n  paramsResult: Pick<\n    ImageParamsResult,\n    'href' | 'width' | 'quality' | 'mimeType'\n  >,\n  nextConfig: {\n    experimental: Pick<\n      NextConfigComplete['experimental'],\n      | 'imgOptConcurrency'\n      | 'imgOptMaxInputPixels'\n      | 'imgOptSequentialRead'\n      | 'imgOptSkipMetadata'\n      | 'imgOptTimeoutInSeconds'\n    >\n    images: Pick<\n      NextConfigComplete['images'],\n      'dangerouslyAllowSVG' | 'minimumCacheTTL'\n    >\n  },\n  opts: {\n    isDev?: boolean\n    silent?: boolean\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  }\n): Promise<{\n  buffer: Buffer\n  contentType: string\n  maxAge: number\n  etag: string\n  upstreamEtag: string\n  error?: unknown\n}> {\n  const { href, quality, width, mimeType } = paramsResult\n  const { buffer: upstreamBuffer, etag: upstreamEtag } = imageUpstream\n  const maxAge = Math.max(\n    nextConfig.images.minimumCacheTTL,\n    getMaxAge(imageUpstream.cacheControl)\n  )\n\n  const upstreamType = await detectContentType(\n    upstreamBuffer,\n    nextConfig.experimental.imgOptSkipMetadata,\n    nextConfig.experimental.imgOptConcurrency\n  )\n\n  if (\n    !upstreamType ||\n    !upstreamType.startsWith('image/') ||\n    upstreamType.includes(',')\n  ) {\n    if (!opts.silent) {\n      Log.error(\n        \"The requested resource isn't a valid image for\",\n        href,\n        'received',\n        upstreamType\n      )\n    }\n    throw new ImageError(400, \"The requested resource isn't a valid image.\")\n  }\n  if (\n    upstreamType.startsWith('image/svg') &&\n    !nextConfig.images.dangerouslyAllowSVG\n  ) {\n    if (!opts.silent) {\n      Log.error(\n        `The requested resource \"${href}\" has type \"${upstreamType}\" but dangerouslyAllowSVG is disabled. Consider adding the \"unoptimized\" property to the <Image>.`\n      )\n    }\n    throw new ImageError(\n      400,\n      '\"url\" parameter is valid but image type is not allowed'\n    )\n  }\n  if (ANIMATABLE_TYPES.includes(upstreamType) && isAnimated(upstreamBuffer)) {\n    if (!opts.silent) {\n      Log.warnOnce(\n        `The requested resource \"${href}\" is an animated image so it will not be optimized. Consider adding the \"unoptimized\" property to the <Image>.`\n      )\n    }\n    return {\n      buffer: upstreamBuffer,\n      contentType: upstreamType,\n      maxAge,\n      etag: upstreamEtag,\n      upstreamEtag,\n    }\n  }\n  if (BYPASS_TYPES.includes(upstreamType)) {\n    return {\n      buffer: upstreamBuffer,\n      contentType: upstreamType,\n      maxAge,\n      etag: upstreamEtag,\n      upstreamEtag,\n    }\n  }\n\n  let contentType: string\n\n  if (mimeType) {\n    contentType = mimeType\n  } else if (\n    getExtension(upstreamType) &&\n    upstreamType !== WEBP &&\n    upstreamType !== AVIF\n  ) {\n    contentType = upstreamType\n  } else {\n    contentType = JPEG\n  }\n  const previouslyCachedImage = getPreviouslyCachedImageOrNull(\n    imageUpstream,\n    opts.previousCacheEntry\n  )\n  if (previouslyCachedImage) {\n    return {\n      buffer: previouslyCachedImage.buffer,\n      contentType,\n      maxAge: opts?.previousCacheEntry?.cacheControl?.revalidate || maxAge,\n      etag: previouslyCachedImage.etag,\n      upstreamEtag: previouslyCachedImage.upstreamEtag,\n    }\n  }\n\n  try {\n    let optimizedBuffer = await optimizeImage({\n      buffer: upstreamBuffer,\n      contentType,\n      quality,\n      width,\n      concurrency: nextConfig.experimental.imgOptConcurrency,\n      limitInputPixels: nextConfig.experimental.imgOptMaxInputPixels,\n      sequentialRead: nextConfig.experimental.imgOptSequentialRead,\n      timeoutInSeconds: nextConfig.experimental.imgOptTimeoutInSeconds,\n    })\n    if (opts.isDev && width <= BLUR_IMG_SIZE && quality === BLUR_QUALITY) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      const meta = await getImageSize(optimizedBuffer)\n      const blurOpts = {\n        blurWidth: meta.width,\n        blurHeight: meta.height,\n        blurDataURL: `data:${contentType};base64,${optimizedBuffer.toString(\n          'base64'\n        )}`,\n      }\n      optimizedBuffer = Buffer.from(unescape(getImageBlurSvg(blurOpts)))\n      contentType = 'image/svg+xml'\n    }\n    return {\n      buffer: optimizedBuffer,\n      contentType,\n      maxAge,\n      etag: getImageEtag(optimizedBuffer),\n      upstreamEtag,\n    }\n  } catch (error) {\n    if (upstreamType) {\n      // If we fail to optimize, fallback to the original image\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge: nextConfig.images.minimumCacheTTL,\n        etag: upstreamEtag,\n        upstreamEtag,\n        error,\n      }\n    } else {\n      throw new ImageError(\n        400,\n        'Unable to optimize image and unable to fallback to upstream image'\n      )\n    }\n  }\n}\n\nfunction getFileNameWithExtension(\n  url: string,\n  contentType: string | null\n): string {\n  const [urlWithoutQueryParams] = url.split('?', 1)\n  const fileNameWithExtension = urlWithoutQueryParams.split('/').pop()\n  if (!contentType || !fileNameWithExtension) {\n    return 'image.bin'\n  }\n\n  const [fileName] = fileNameWithExtension.split('.', 1)\n  const extension = getExtension(contentType)\n  return `${fileName}.${extension}`\n}\n\nfunction setResponseHeaders(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  etag: string,\n  contentType: string | null,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  res.setHeader('Vary', 'Accept')\n  res.setHeader(\n    'Cache-Control',\n    isStatic\n      ? 'public, max-age=315360000, immutable'\n      : `public, max-age=${isDev ? 0 : maxAge}, must-revalidate`\n  )\n  if (sendEtagResponse(req, res, etag)) {\n    // already called res.end() so we're finished\n    return { finished: true }\n  }\n  if (contentType) {\n    res.setHeader('Content-Type', contentType)\n  }\n\n  const fileName = getFileNameWithExtension(url, contentType)\n  res.setHeader(\n    'Content-Disposition',\n    contentDisposition(fileName, { type: imagesConfig.contentDispositionType })\n  )\n\n  res.setHeader('Content-Security-Policy', imagesConfig.contentSecurityPolicy)\n  res.setHeader('X-Nextjs-Cache', xCache)\n\n  return { finished: false }\n}\n\nexport function sendResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  extension: string,\n  buffer: Buffer,\n  etag: string,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  const contentType = getContentType(extension)\n  const result = setResponseHeaders(\n    req,\n    res,\n    url,\n    etag,\n    contentType,\n    isStatic,\n    xCache,\n    imagesConfig,\n    maxAge,\n    isDev\n  )\n  if (!result.finished) {\n    res.setHeader('Content-Length', Buffer.byteLength(buffer))\n    res.end(buffer)\n  }\n}\n\nexport async function getImageSize(buffer: Buffer): Promise<{\n  width?: number\n  height?: number\n}> {\n  const { width, height } = imageSizeOf(buffer)\n  return { width, height }\n}\n"], "names": ["createHash", "promises", "mediaType", "contentDisposition", "imageSizeOf", "detector", "isAnimated", "join", "nodeUrl", "getImageBlurSvg", "hasLocalMatch", "hasRemoteMatch", "createRequestResponseMocks", "CachedRouteKind", "sendEtagResponse", "getContentType", "getExtension", "Log", "isError", "parseUrl", "InvariantError", "AVIF", "WEBP", "PNG", "JPEG", "JXL", "JP2", "HEIC", "GIF", "SVG", "ICO", "ICNS", "TIFF", "BMP", "PDF", "CACHE_VERSION", "ANIMATABLE_TYPES", "BYPASS_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "_sharp", "getSharp", "concurrency", "require", "divisor", "process", "env", "NODE_ENV", "Math", "floor", "max", "e", "code", "Error", "getSupportedMimeType", "options", "accept", "mimeType", "includes", "getHash", "items", "hash", "item", "update", "String", "digest", "extractEtag", "etag", "imageBuffer", "<PERSON><PERSON><PERSON>", "from", "toString", "getImageEtag", "image", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "upstreamEtag", "filename", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "detectContentType", "skipMetadata", "byteLength", "every", "b", "i", "format", "sharp", "meta", "metadata", "_", "undefined", "ImageOptimizerCache", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "length", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "test", "decodeURIComponent", "pathname", "hrefParsed", "URL", "_error", "protocol", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "IMAGE", "revalidateAfter", "cacheControl", "revalidate", "expire", "isStale", "set", "experimental", "isrFlushToDisk", "err", "error", "ImageError", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "getMaxAge", "age", "endsWith", "slice", "n", "getPreviouslyCachedImageOrNull", "upstreamImage", "previousCacheEntry", "optimizeImage", "contentType", "height", "limitInputPixels", "sequentialRead", "timeoutInSeconds", "transformer", "timeout", "seconds", "rotate", "resize", "withoutEnlargement", "avif", "effort", "webp", "png", "jpeg", "mozjpeg", "optimizedBuffer", "<PERSON><PERSON><PERSON><PERSON>", "fetchExternalImage", "res", "fetch", "signal", "AbortSignal", "name", "ok", "status", "arrayBuffer", "fetchInternalImage", "_req", "_res", "handleRequest", "mocked", "method", "socket", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageOptimizer", "imageUpstream", "paramsResult", "opts", "upstreamBuffer", "upstreamType", "imgOptSkipMetadata", "imgOptConcurrency", "silent", "dangerouslyAllowSVG", "previouslyCachedImage", "imgOptMaxInputPixels", "imgOptSequentialRead", "imgOptTimeoutInSeconds", "getImageSize", "blurOpts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "finished", "type", "contentDispositionType", "contentSecurityPolicy", "sendResponse", "result", "end"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,QAAQ,QAAQ,KAAI;AAE7B,SAASC,SAAS,QAAQ,kCAAiC;AAC3D,OAAOC,wBAAwB,yCAAwC;AACvE,OAAOC,iBAAiB,gCAA+B;AACvD,SAASC,QAAQ,QAAQ,gDAA+C;AACxE,OAAOC,gBAAgB,iCAAgC;AACvD,SAASC,IAAI,QAAQ,OAAM;AAC3B,OAAOC,aAA0C,MAAK;AAEtD,SAASC,eAAe,QAAQ,+BAA8B;AAE9D,SAASC,aAAa,QAAQ,oCAAmC;AACjE,SAASC,cAAc,QAAQ,qCAAoC;AAEnE,SAASC,0BAA0B,QAAQ,qBAAoB;AAE/D,SACEC,eAAe,QAKV,mBAAkB;AACzB,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,cAAc,EAAEC,YAAY,QAAQ,iBAAgB;AAC7D,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,aAAa,kBAAiB;AACrC,SAASC,QAAQ,QAAQ,aAAY;AAErC,SAASC,cAAc,QAAQ,gCAA+B;AAI9D,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACd;IAAMC;IAAKK;CAAI;AACzC,MAAMS,eAAe;IAACR;IAAKC;IAAKC;IAAME;IAAKR;IAAKE;CAAK;AACrD,MAAMW,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,OAAO,SAASC,SAASC,WAAsC;IAC7D,IAAIF,QAAQ;QACV,OAAOA;IACT;IACA,IAAI;QACFA,SAASG,QAAQ;QACjB,IAAIH,UAAUA,OAAOE,WAAW,KAAK,GAAG;YACtC,2DAA2D;YAC3D,8DAA8D;YAC9D,0DAA0D;YAC1D,MAAME,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,IAAI;YAC7DP,OAAOE,WAAW,CAChBA,eAAeM,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACV,OAAOE,WAAW,KAAKE,SAAS;QAEvE;IACF,EAAE,OAAOO,GAAY;QACnB,IAAIjC,QAAQiC,MAAMA,EAAEC,IAAI,KAAK,oBAAoB;YAC/C,MAAM,qBAEL,CAFK,IAAIC,MACR,yFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMF;IACR;IACA,OAAOX;AACT;AAoBA,SAASc,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWvD,UAAUsD,QAAQD;IACnC,OAAOC,OAAOE,QAAQ,CAACD,YAAYA,WAAW;AAChD;AAEA,OAAO,SAASE,QAAQC,KAAmC;IACzD,MAAMC,OAAO7D,WAAW;IACxB,KAAK,IAAI8D,QAAQF,MAAO;QACtB,IAAI,OAAOE,SAAS,UAAUD,KAAKE,MAAM,CAACC,OAAOF;aAC5C;YACHD,KAAKE,MAAM,CAACD;QACd;IACF;IACA,4DAA4D;IAC5D,OAAOD,KAAKI,MAAM,CAAC;AACrB;AAEA,OAAO,SAASC,YACdC,IAA+B,EAC/BC,WAAmB;IAEnB,IAAID,MAAM;QACR,yEAAyE;QACzE,iDAAiD;QACjD,OAAOE,OAAOC,IAAI,CAACH,MAAMI,QAAQ,CAAC;IACpC;IACA,OAAOC,aAAaJ;AACtB;AAEA,OAAO,SAASI,aAAaC,KAAa;IACxC,OAAOd,QAAQ;QAACc;KAAM;AACxB;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdZ,IAAY,EACZa,YAAoB;IAEpB,MAAMC,WAAW1E,KACfoE,KACA,GAAGE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEX,KAAK,CAAC,EAAEa,aAAa,CAAC,EAAEJ,WAAW;IAG9D,MAAM3E,SAASiF,EAAE,CAACP,KAAK;QAAEQ,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMpF,SAASqF,KAAK,CAACX,KAAK;QAAEQ,WAAW;IAAK;IAC5C,MAAMlF,SAASsF,SAAS,CAACN,UAAUF;AACrC;AAEA;;;;CAIC,GACD,OAAO,eAAeS,kBACpBT,MAAc,EACdU,YAAwC,EACxC/C,WAAuC;IAEvC,IAAIqC,OAAOW,UAAU,KAAK,GAAG;QAC3B,OAAO;IACT;IACA,IAAI;QAAC;QAAM;QAAM;KAAK,CAACC,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QACvD,OAAOpE;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACmE,KAAK,CACpD,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAE1B;QACA,OAAOrE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACoE,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QAC7D,OAAOhE;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAAC+D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKb,MAAM,CAACc,EAAE,KAAKD,IAEhC;QACA,OAAOtE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACqE,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QACnE,OAAO/D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QAC7D,OAAO/D;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKb,MAAM,CAACc,EAAE,KAAKD,IAEhC;QACA,OAAOvE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACsE,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QAC7D,OAAO9D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC6D,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QAC7D,OAAO7D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC4D,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QAC7D,OAAO5D;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAAC2D,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QACjD,OAAO3D;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAAC0D,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QACjD,OAAOnE;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACkE,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAChC;QACA,OAAOnE;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACkE,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKb,MAAM,CAACc,EAAE,KAAKD,IAEhC;QACA,OAAOjE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACgE,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAAI;QACnE,OAAO1D;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMd,MAAM,CAACc,EAAE,KAAKD,IAChC;QACA,OAAOlE;IACT;IAEA,IAAIoE;IAIJA,SAASzF,SAAS0E;IAElB,IAAI,CAACe,UAAU,CAACL,cAAc;QAC5B,MAAMM,QAAQtD,SAASC;QACvB,MAAMsD,OAAO,MAAMD,MAAMhB,QACtBkB,QAAQ,GACRZ,KAAK,CAAC,CAACa,IAAM;QAChBJ,SAASE,wBAAAA,KAAMF,MAAM;IACvB;IAEA,OAAQA;QACN,KAAK;YACH,OAAOzE;QACT,KAAK;YACH,OAAOC;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;YACH,OAAOC;QACT,KAAK;YACH,OAAOI;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;YACH,OAAOJ;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;YACH,OAAOM;QACT,KAAK;YACH,OAAOE;QACT,KAAK;YACH,OAAOD;QACT,KAAK;YACH,OAAOH;QACT,KAAK;YACH,OAAOC;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAKoE;QACL;YACE,OAAO;IACX;AACF;AAEA,OAAO,MAAMC;IAIX,OAAOC,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQU,MAAM,GAAG,GAAG;YACtBvG,IAAIwG,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACL,KAAK;YACR,OAAO;gBAAEM,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACR,MAAM;YAC7B,OAAO;gBAAEM,cAAc;YAAqC;QAC9D;QAEA,IAAIN,IAAII,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEE,cAAc;YAA8B;QACvD;QAEA,IAAIN,IAAIS,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIV,IAAIS,UAAU,CAAC,MAAM;gBAKA1G;YAJvBoG,OAAOH;YACPU,aAAa;YACb,IACE,uBAAuBC,IAAI,CACzBC,mBAAmB7G,EAAAA,YAAAA,SAASiG,yBAATjG,UAAe8G,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLP,cAAc;gBAChB;YACF;YACA,IAAI,CAAChH,cAAcwG,eAAeE,MAAM;gBACtC,OAAO;oBAAEM,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIQ;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIf;gBACrBG,OAAOW,WAAW3D,QAAQ;gBAC1BuD,aAAa;YACf,EAAE,OAAOM,QAAQ;gBACf,OAAO;oBAAEV,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAAChE,QAAQ,CAACwE,WAAWG,QAAQ,GAAG;gBACtD,OAAO;oBAAEX,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC/G,eAAemG,SAASG,gBAAgBiB,aAAa;gBACxD,OAAO;oBAAER,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA2C;QACpE,OAAO,IAAI,CAAC,WAAWK,IAAI,CAACV,IAAI;YAC9B,OAAO;gBACLK,cAAc;YAChB;QACF;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA6C;QACtE,OAAO,IAAI,CAAC,WAAWK,IAAI,CAACT,IAAI;YAC9B,OAAO;gBACLI,cACE;YACJ;QACF;QAEA,MAAMY,QAAQC,SAASlB,GAAG;QAE1B,IAAIiB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLZ,cAAc;YAChB;QACF;QAEA,MAAMe,QAAQ;eAAK7B,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACTgC,MAAMC,IAAI,CAACpG;QACb;QAEA,MAAMqG,cACJF,MAAM/E,QAAQ,CAAC4E,UAAW7B,SAAS6B,SAAShG;QAE9C,IAAI,CAACqG,aAAa;YAChB,OAAO;gBACLjB,cAAc,CAAC,yBAAyB,EAAEY,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASjB,GAAG;QAE5B,IAAIkB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLlB,cACE;YACJ;QACF;QAEA,IAAIP,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAUuB,IAAI,CAACnG;YACjB;YAEA,IAAI,CAAC4E,UAAUzD,QAAQ,CAACkF,UAAU;gBAChC,OAAO;oBACLlB,cAAc,CAAC,2BAA2B,EAAEJ,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAM7D,WAAWH,qBAAqB0D,WAAW,EAAE,EAAEV,IAAIuC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW1B,IAAIS,UAAU,CAC7B,GAAGrB,WAAWuC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLxB;YACAkB;YACAX;YACAgB;YACAR;YACAM;YACAnF;YACAsD;QACF;IACF;IAEA,OAAOiC,YAAY,EACjBzB,IAAI,EACJe,KAAK,EACLM,OAAO,EACPnF,QAAQ,EAMT,EAAU;QACT,OAAOE,QAAQ;YAACxB;YAAeoF;YAAMe;YAAOM;YAASnF;SAAS;IAChE;IAEAwF,YAAY,EACVC,OAAO,EACP1C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC2C,QAAQ,GAAG5I,KAAK2I,SAAS,SAAS;QACvC,IAAI,CAAC1C,UAAU,GAAGA;IACpB;IAEA,MAAM4C,IAAIC,QAAgB,EAAiD;QACzE,IAAI;YACF,MAAMF,WAAW5I,KAAK,IAAI,CAAC4I,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMrJ,SAASsJ,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYzF,MAAMa,cAAcJ,UAAU,GACzD8E,KAAKG,KAAK,CAAC,KAAK;gBAClB,MAAM9E,SAAS,MAAM9E,SAAS6J,QAAQ,CAACvJ,KAAK4I,UAAUO;gBACtD,MAAM5E,WAAWiF,OAAOH;gBACxB,MAAM/E,SAASkF,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAMpJ,gBAAgBqJ,KAAK;wBAC3B/F;wBACAY;wBACAH;wBACAI;oBACF;oBACAmF,iBACEnH,KAAKE,GAAG,CAAC2B,QAAQ,IAAI,CAAC2B,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D0C,KAAKD,GAAG;oBACVY,cAAc;wBAAEC,YAAYxF;wBAAQyF,QAAQnE;oBAAU;oBACtDoE,SAASf,MAAM1E;gBACjB;YACF;QACF,EAAE,OAAOoB,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMsE,IACJnB,QAAgB,EAChBW,KAAmC,EACnC,EACEI,YAAY,EAGb,EACD;QACA,IAAI,CAAC,IAAI,CAAC5D,UAAU,CAACiE,YAAY,CAACC,cAAc,EAAE;YAChD;QACF;QAEA,IAAIV,CAAAA,yBAAAA,MAAOC,IAAI,MAAKpJ,gBAAgBqJ,KAAK,EAAE;YACzC,MAAM,qBAAgE,CAAhE,IAAI7G,MAAM,wDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,MAAMgH,aAAaD,gCAAAA,aAAcC,UAAU;QAE3C,IAAI,OAAOA,eAAe,UAAU;YAClC,MAAM,qBAAiE,CAAjE,IAAIjJ,eAAe,gDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAgE;QACxE;QAEA,MAAM0D,WACJ9B,KAAKE,GAAG,CAACmH,YAAY,IAAI,CAAC7D,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D0C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAM9E,gBACJnE,KAAK,IAAI,CAAC4I,QAAQ,EAAEE,WACpBW,MAAMpF,SAAS,EACfyF,YACAvF,UACAkF,MAAMjF,MAAM,EACZiF,MAAM7F,IAAI,EACV6F,MAAMhF,YAAY;QAEtB,EAAE,OAAO2F,KAAK;YACZ1J,IAAI2J,KAAK,CAAC,CAAC,+BAA+B,EAAEvB,UAAU,EAAEsB;QAC1D;IACF;AACF;AACA,OAAO,MAAME,mBAAmBxH;IAG9B4F,YAAY6B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIpB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACwB,KAAKrB,MAAM,GAAGoB,UAAUE,IAAI,GAAGzB,KAAK,CAAC,KAAK;QAC/CwB,MAAMA,IAAIE,WAAW;QACrB,IAAIvB,OAAO;YACTA,QAAQA,MAAMuB,WAAW;QAC3B;QACAL,IAAIV,GAAG,CAACa,KAAKrB;IACf;IACA,OAAOkB;AACT;AAEA,OAAO,SAASM,UAAUP,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIO,MAAMP,IAAI9B,GAAG,CAAC,eAAe8B,IAAI9B,GAAG,CAAC,cAAc;QACvD,IAAIqC,IAAI5D,UAAU,CAAC,QAAQ4D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIrD,SAASkD,KAAK;QACxB,IAAI,CAACjD,MAAMoD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AACA,OAAO,SAASC,+BACdC,aAA4B,EAC5BC,kBAA4D;QAG1DA;IADF,IACEA,CAAAA,uCAAAA,4BAAAA,mBAAoB/B,KAAK,qBAAzB+B,0BAA2B9B,IAAI,MAAK,WACpC,wHAAwH;IACxH,kEAAkE;IAClE8B,mBAAmB/B,KAAK,CAAChF,YAAY,KAAK+G,mBAAmB/B,KAAK,CAAC7F,IAAI,IACvE,kEAAkE;IAClE2H,cAAc3H,IAAI,KAAK4H,mBAAmB/B,KAAK,CAAChF,YAAY,EAC5D;QACA,OAAO+G,mBAAmB/B,KAAK;IACjC;IACA,OAAO;AACT;AAEA,OAAO,eAAegC,cAAc,EAClCjH,MAAM,EACNkH,WAAW,EACXrD,OAAO,EACPN,KAAK,EACL4D,MAAM,EACNxJ,WAAW,EACXyJ,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAWjB;IACC,MAAMtG,QAAQtD,SAASC;IACvB,MAAM4J,cAAcvG,MAAMhB,QAAQ;QAChCoH;QACAC,gBAAgBA,kBAAkBjG;IACpC,GACGoG,OAAO,CAAC;QACPC,SAASH,oBAAoB;IAC/B,GACCI,MAAM;IAET,IAAIP,QAAQ;QACVI,YAAYI,MAAM,CAACpE,OAAO4D;IAC5B,OAAO;QACLI,YAAYI,MAAM,CAACpE,OAAOnC,WAAW;YACnCwG,oBAAoB;QACtB;IACF;IAEA,IAAIV,gBAAgB5K,MAAM;QACxBiL,YAAYM,IAAI,CAAC;YACfhE,SAAS5F,KAAKE,GAAG,CAAC0F,UAAU,IAAI;YAChCiE,QAAQ;QACV;IACF,OAAO,IAAIZ,gBAAgB3K,MAAM;QAC/BgL,YAAYQ,IAAI,CAAC;YAAElE;QAAQ;IAC7B,OAAO,IAAIqD,gBAAgB1K,KAAK;QAC9B+K,YAAYS,GAAG,CAAC;YAAEnE;QAAQ;IAC5B,OAAO,IAAIqD,gBAAgBzK,MAAM;QAC/B8K,YAAYU,IAAI,CAAC;YAAEpE;YAASqE,SAAS;QAAK;IAC5C;IAEA,MAAMC,kBAAkB,MAAMZ,YAAYa,QAAQ;IAElD,OAAOD;AACT;AAEA,OAAO,eAAeE,mBAAmB7F,IAAY;IACnD,MAAM8F,MAAM,MAAMC,MAAM/F,MAAM;QAC5BgG,QAAQC,YAAYjB,OAAO,CAAC;IAC9B,GAAGlH,KAAK,CAAC,CAACsF,MAAQA;IAElB,IAAI0C,eAAehK,OAAO;QACxB,MAAMsH,MAAM0C;QACZ,IAAI1C,IAAI8C,IAAI,KAAK,gBAAgB;YAC/BxM,IAAI2J,KAAK,CAAC,yCAAyCrD;YACnD,MAAM,qBAGL,CAHK,IAAIsD,WACR,KACA,6DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,MAAMF;IACR;IAEA,IAAI,CAAC0C,IAAIK,EAAE,EAAE;QACXzM,IAAI2J,KAAK,CAAC,sCAAsCrD,MAAM8F,IAAIM,MAAM;QAChE,MAAM,qBAGL,CAHK,IAAI9C,WACRwC,IAAIM,MAAM,EACV,8DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,MAAM5I,SAASV,OAAOC,IAAI,CAAC,MAAM+I,IAAIO,WAAW;IAChD,MAAM3B,cAAcoB,IAAIxE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMgB,eAAeiD,IAAIxE,OAAO,CAACO,GAAG,CAAC;IACrC,MAAMjF,OAAOD,YAAYmJ,IAAIxE,OAAO,CAACO,GAAG,CAAC,SAASrE;IAClD,OAAO;QAAEA;QAAQkH;QAAa7B;QAAcjG;IAAK;AACnD;AAEA,OAAO,eAAe0J,mBACpBtG,IAAY,EACZuG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASrN,2BAA2B;YACxCwG,KAAKG;YACL2G,QAAQJ,KAAKI,MAAM,IAAI;YACvBC,QAAQL,KAAKK,MAAM;QACrB;QAEA,MAAMH,cAAcC,OAAO3H,GAAG,EAAE2H,OAAOZ,GAAG,EAAE7M,QAAQ4N,KAAK,CAAC7G,MAAM;QAChE,MAAM0G,OAAOZ,GAAG,CAACgB,WAAW;QAE5B,IAAI,CAACJ,OAAOZ,GAAG,CAACvC,UAAU,EAAE;YAC1B7J,IAAI2J,KAAK,CAAC,6BAA6BrD,MAAM0G,OAAOZ,GAAG,CAACvC,UAAU;YAClE,MAAM,qBAGL,CAHK,IAAID,WACRoD,OAAOZ,GAAG,CAACvC,UAAU,EACrB,8DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,MAAM/F,SAASV,OAAOiK,MAAM,CAACL,OAAOZ,GAAG,CAACkB,OAAO;QAC/C,MAAMtC,cAAcgC,OAAOZ,GAAG,CAACmB,SAAS,CAAC;QACzC,MAAMpE,eAAe6D,OAAOZ,GAAG,CAACmB,SAAS,CAAC;QAC1C,MAAMrK,OAAOD,YAAY+J,OAAOZ,GAAG,CAACmB,SAAS,CAAC,SAASzJ;QAEvD,OAAO;YAAEA;YAAQkH;YAAa7B;YAAcjG;QAAK;IACnD,EAAE,OAAOwG,KAAK;QACZ1J,IAAI2J,KAAK,CAAC,sCAAsCrD,MAAMoD;QACtD,MAAM,qBAGL,CAHK,IAAIE,WACR,KACA,8DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;AACF;AAEA,OAAO,eAAe4D,eACpBC,aAA4B,EAC5BC,YAGC,EACDnI,UAaC,EACDoI,IAIC;IASD,MAAM,EAAErH,IAAI,EAAEqB,OAAO,EAAEN,KAAK,EAAE7E,QAAQ,EAAE,GAAGkL;IAC3C,MAAM,EAAE5J,QAAQ8J,cAAc,EAAE1K,MAAMa,YAAY,EAAE,GAAG0J;IACvD,MAAM7J,SAAS7B,KAAKE,GAAG,CACrBsD,WAAWG,MAAM,CAACI,eAAe,EACjCyE,UAAUkD,cAActE,YAAY;IAGtC,MAAM0E,eAAe,MAAMtJ,kBACzBqJ,gBACArI,WAAWiE,YAAY,CAACsE,kBAAkB,EAC1CvI,WAAWiE,YAAY,CAACuE,iBAAiB;IAG3C,IACE,CAACF,gBACD,CAACA,aAAajH,UAAU,CAAC,aACzBiH,aAAapL,QAAQ,CAAC,MACtB;QACA,IAAI,CAACkL,KAAKK,MAAM,EAAE;YAChBhO,IAAI2J,KAAK,CACP,kDACArD,MACA,YACAuH;QAEJ;QACA,MAAM,qBAAkE,CAAlE,IAAIjE,WAAW,KAAK,gDAApB,qBAAA;mBAAA;wBAAA;0BAAA;QAAiE;IACzE;IACA,IACEiE,aAAajH,UAAU,CAAC,gBACxB,CAACrB,WAAWG,MAAM,CAACuI,mBAAmB,EACtC;QACA,IAAI,CAACN,KAAKK,MAAM,EAAE;YAChBhO,IAAI2J,KAAK,CACP,CAAC,wBAAwB,EAAErD,KAAK,YAAY,EAAEuH,aAAa,iGAAiG,CAAC;QAEjK;QACA,MAAM,qBAGL,CAHK,IAAIjE,WACR,KACA,2DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IACA,IAAIzI,iBAAiBsB,QAAQ,CAACoL,iBAAiBxO,WAAWuO,iBAAiB;QACzE,IAAI,CAACD,KAAKK,MAAM,EAAE;YAChBhO,IAAIwG,QAAQ,CACV,CAAC,wBAAwB,EAAEF,KAAK,8GAA8G,CAAC;QAEnJ;QACA,OAAO;YACLxC,QAAQ8J;YACR5C,aAAa6C;YACbjK;YACAV,MAAMa;YACNA;QACF;IACF;IACA,IAAI3C,aAAaqB,QAAQ,CAACoL,eAAe;QACvC,OAAO;YACL/J,QAAQ8J;YACR5C,aAAa6C;YACbjK;YACAV,MAAMa;YACNA;QACF;IACF;IAEA,IAAIiH;IAEJ,IAAIxI,UAAU;QACZwI,cAAcxI;IAChB,OAAO,IACLzC,aAAa8N,iBACbA,iBAAiBxN,QACjBwN,iBAAiBzN,MACjB;QACA4K,cAAc6C;IAChB,OAAO;QACL7C,cAAczK;IAChB;IACA,MAAM2N,wBAAwBtD,+BAC5B6C,eACAE,KAAK7C,kBAAkB;IAEzB,IAAIoD,uBAAuB;YAIfP,uCAAAA;QAHV,OAAO;YACL7J,QAAQoK,sBAAsBpK,MAAM;YACpCkH;YACApH,QAAQ+J,CAAAA,yBAAAA,2BAAAA,KAAM7C,kBAAkB,sBAAxB6C,wCAAAA,yBAA0BxE,YAAY,qBAAtCwE,sCAAwCvE,UAAU,KAAIxF;YAC9DV,MAAMgL,sBAAsBhL,IAAI;YAChCa,cAAcmK,sBAAsBnK,YAAY;QAClD;IACF;IAEA,IAAI;QACF,IAAIkI,kBAAkB,MAAMlB,cAAc;YACxCjH,QAAQ8J;YACR5C;YACArD;YACAN;YACA5F,aAAa8D,WAAWiE,YAAY,CAACuE,iBAAiB;YACtD7C,kBAAkB3F,WAAWiE,YAAY,CAAC2E,oBAAoB;YAC9DhD,gBAAgB5F,WAAWiE,YAAY,CAAC4E,oBAAoB;YAC5DhD,kBAAkB7F,WAAWiE,YAAY,CAAC6E,sBAAsB;QAClE;QACA,IAAIV,KAAKnI,KAAK,IAAI6B,SAAShG,iBAAiBsG,YAAYrG,cAAc;YACpE,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF,MAAMyD,OAAO,MAAMuJ,aAAarC;YAChC,MAAMsC,WAAW;gBACfC,WAAWzJ,KAAKsC,KAAK;gBACrBoH,YAAY1J,KAAKkG,MAAM;gBACvByD,aAAa,CAAC,KAAK,EAAE1D,YAAY,QAAQ,EAAEiB,gBAAgB3I,QAAQ,CACjE,WACC;YACL;YACA2I,kBAAkB7I,OAAOC,IAAI,CAACsL,SAASnP,gBAAgB+O;YACvDvD,cAAc;QAChB;QACA,OAAO;YACLlH,QAAQmI;YACRjB;YACApH;YACAV,MAAMK,aAAa0I;YACnBlI;QACF;IACF,EAAE,OAAO4F,OAAO;QACd,IAAIkE,cAAc;YAChB,yDAAyD;YACzD,OAAO;gBACL/J,QAAQ8J;gBACR5C,aAAa6C;gBACbjK,QAAQ2B,WAAWG,MAAM,CAACI,eAAe;gBACzC5C,MAAMa;gBACNA;gBACA4F;YACF;QACF,OAAO;YACL,MAAM,qBAGL,CAHK,IAAIC,WACR,KACA,sEAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF;AACF;AAEA,SAASgF,yBACPzI,GAAW,EACX6E,WAA0B;IAE1B,MAAM,CAAC6D,sBAAsB,GAAG1I,IAAIyC,KAAK,CAAC,KAAK;IAC/C,MAAMkG,wBAAwBD,sBAAsBjG,KAAK,CAAC,KAAKmG,GAAG;IAClE,IAAI,CAAC/D,eAAe,CAAC8D,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsBlG,KAAK,CAAC,KAAK;IACpD,MAAMjF,YAAY5D,aAAaiL;IAC/B,OAAO,GAAGgE,SAAS,CAAC,EAAErL,WAAW;AACnC;AAEA,SAASsL,mBACP5J,GAAoB,EACpB+G,GAAmB,EACnBjG,GAAW,EACXjD,IAAY,EACZ8H,WAA0B,EAC1BnD,QAAiB,EACjBqH,MAAoB,EACpBC,YAAiC,EACjCvL,MAAc,EACd4B,KAAc;IAEd4G,IAAIgD,SAAS,CAAC,QAAQ;IACtBhD,IAAIgD,SAAS,CACX,iBACAvH,WACI,yCACA,CAAC,gBAAgB,EAAErC,QAAQ,IAAI5B,OAAO,iBAAiB,CAAC;IAE9D,IAAI/D,iBAAiBwF,KAAK+G,KAAKlJ,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEmM,UAAU;QAAK;IAC1B;IACA,IAAIrE,aAAa;QACfoB,IAAIgD,SAAS,CAAC,gBAAgBpE;IAChC;IAEA,MAAMgE,WAAWJ,yBAAyBzI,KAAK6E;IAC/CoB,IAAIgD,SAAS,CACX,uBACAlQ,mBAAmB8P,UAAU;QAAEM,MAAMH,aAAaI,sBAAsB;IAAC;IAG3EnD,IAAIgD,SAAS,CAAC,2BAA2BD,aAAaK,qBAAqB;IAC3EpD,IAAIgD,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEG,UAAU;IAAM;AAC3B;AAEA,OAAO,SAASI,aACdpK,GAAoB,EACpB+G,GAAmB,EACnBjG,GAAW,EACXxC,SAAiB,EACjBG,MAAc,EACdZ,IAAY,EACZ2E,QAAiB,EACjBqH,MAAoB,EACpBC,YAAiC,EACjCvL,MAAc,EACd4B,KAAc;IAEd,MAAMwF,cAAclL,eAAe6D;IACnC,MAAM+L,SAAST,mBACb5J,KACA+G,KACAjG,KACAjD,MACA8H,aACAnD,UACAqH,QACAC,cACAvL,QACA4B;IAEF,IAAI,CAACkK,OAAOL,QAAQ,EAAE;QACpBjD,IAAIgD,SAAS,CAAC,kBAAkBhM,OAAOqB,UAAU,CAACX;QAClDsI,IAAIuD,GAAG,CAAC7L;IACV;AACF;AAEA,OAAO,eAAewK,aAAaxK,MAAc;IAI/C,MAAM,EAAEuD,KAAK,EAAE4D,MAAM,EAAE,GAAG9L,YAAY2E;IACtC,OAAO;QAAEuD;QAAO4D;IAAO;AACzB", "ignoreList": [0]}