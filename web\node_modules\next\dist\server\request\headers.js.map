{"version": 3, "sources": ["../../../src/server/request/headers.ts"], "sourcesContent": ["import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const callingExpression = 'headers'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'private-cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"headers\" inside \"use cache: private\". Accessing \"headers\" inside a private cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-runtime':\n          return makeHangingHeaders(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`headers`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender (no cacheComponents)\n          // We are prerendering with PPR. We need track dynamic access here eagerly\n          // to keep continuity with how headers has worked in PPR without cacheComponents.\n          // TODO consider switching the semantic to throw on property access instead\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // Legacy Prerender\n          // We are in a legacy static generation mode while prerendering\n          // We track dynamic access here so we don't need to wrap the headers in\n          // individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedHeadersWithDevWarnings(\n                workUnitStore.headers,\n                workStore?.route\n              )\n            }\n\n            return makeUntrackedExoticHeadersWithDevWarnings(\n              workUnitStore.headers,\n              workStore?.route\n            )\n          } else {\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedHeaders(workUnitStore.headers)\n            }\n\n            return makeUntrackedExoticHeaders(workUnitStore.headers)\n          }\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeHangingHeaders(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingHeaders)\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticHeadersWithDevWarnings`, but just logging the\n// sync access without actually defining the headers properties on the promise.\nfunction makeUntrackedHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingHeaders)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...headers()` or similar iteration')\n          break\n        }\n        case 'append':\n        case 'delete':\n        case 'get':\n        case 'has':\n        case 'set':\n        case 'getSetCookie':\n        case 'forEach':\n        case 'keys':\n        case 'values':\n        case 'entries': {\n          warnForSyncAccess(route, `\\`headers().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the headers object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedHeaders.set(underlyingHeaders, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n"], "names": ["headers", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "Headers", "makeUntrackedExoticHeaders", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "dynamicShouldError", "StaticGenBailoutError", "makeHangingHeaders", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "process", "env", "NODE_ENV", "__NEXT_CACHE_COMPONENTS", "makeUntrackedHeadersWithDevWarnings", "makeUntrackedExoticHeadersWithDevWarnings", "makeUntrackedHeaders", "throwForMissingRequestStore", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Promise", "resolve", "Object", "defineProperties", "append", "value", "bind", "delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "Symbol", "iterator", "makeDevtoolsIOAwarePromise", "expression", "describeNameArg", "arguments", "syncIODev", "apply", "_delete", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "ReflectAdapter", "arg", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "createHeadersAccessError", "prefix"], "mappings": ";;;;+BA6DgBA;;;eAAAA;;;yBA1DT;0CAIA;8CAKA;kCAMA;yCAC+B;uCAI/B;0DACqD;uBACZ;gCACjB;yBACA;AAkCxB,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,IAAAA,sCAA+B,KAChC;YACA,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,yOAAyO,CAAC,GAD/P,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC,uBAAc,CAACC,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,2BAA2BJ;QACpC;QAEA,IAAIP,eAAe;YACjB,OAAQA,cAAcY,IAAI;gBACxB,KAAK;oBAAS;wBACZ,MAAMC,QAAQ,qBAEb,CAFa,IAAIT,MAChB,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0UAA0U,CAAC,GADxV,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMU,iBAAiB,CAACD,OAAOlB;wBAC/BE,UAAUkB,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBAAiB;wBACpB,MAAMA,QAAQ,qBAEb,CAFa,IAAIT,MAChB,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,gVAAgV,CAAC,GAD9V,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMU,iBAAiB,CAACD,OAAOlB;wBAC/BE,UAAUkB,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIT,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF;oBACEL;YACJ;QACF;QAEA,IAAIH,UAAUmB,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEpB,UAAUQ,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,OAAQA,cAAcY,IAAI;gBACxB,KAAK;gBACL,KAAK;oBACH,OAAOM,mBAAmBrB,WAAWG;gBACvC,KAAK;oBACH,MAAMmB,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,GAAGD,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,qCAAqC;oBACrC,0EAA0E;oBAC1E,iFAAiF;oBACjF,2EAA2E;oBAC3E,OAAOE,IAAAA,sCAAoB,EACzBxB,UAAUQ,KAAK,EACfT,mBACAI,cAAcsB,eAAe;gBAEjC,KAAK;oBACH,mBAAmB;oBACnB,+DAA+D;oBAC/D,uEAAuE;oBACvE,uCAAuC;oBACvC,OAAOC,IAAAA,kDAAgC,EACrC3B,mBACAC,WACAG;gBAEJ,KAAK;oBACHwB,IAAAA,iDAA+B,EAACxB;oBAEhC,IAAIyB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,IAAIF,QAAQC,GAAG,CAACE,uBAAuB,EAAE;4BACvC,OAAOC,oCACL7B,cAAcL,OAAO,EACrBE,6BAAAA,UAAWQ,KAAK;wBAEpB;wBAEA,OAAOyB,0CACL9B,cAAcL,OAAO,EACrBE,6BAAAA,UAAWQ,KAAK;oBAEpB,OAAO;wBACL,IAAIoB,QAAQC,GAAG,CAACE,uBAAuB,EAAE;4BACvC,OAAOG,qBAAqB/B,cAAcL,OAAO;wBACnD;wBAEA,OAAOgB,2BAA2BX,cAAcL,OAAO;oBACzD;oBACA;gBACF;oBACEK;YACJ;QACF;IACF;IAEA,yEAAyE;IACzEgC,IAAAA,yDAA2B,EAACpC;AAC9B;AAGA,MAAMqC,gBAAgB,IAAIC;AAE1B,SAAShB,mBACPrB,SAAoB,EACpBsC,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,IAAAA,yCAAkB,EAChCJ,eAAeK,YAAY,EAC3B3C,UAAUQ,KAAK,EACf;IAEF4B,cAAcQ,GAAG,CAACN,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAASP,qBACPxB,iBAAkC;IAElC,MAAM6B,gBAAgBH,cAAcI,GAAG,CAAC9B;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUI,QAAQC,OAAO,CAACpC;IAChC0B,cAAcQ,GAAG,CAAClC,mBAAmB+B;IAErC,OAAOA;AACT;AAEA,SAAS3B,2BACPJ,iBAAkC;IAElC,MAAM6B,gBAAgBH,cAAcI,GAAG,CAAC9B;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUI,QAAQC,OAAO,CAACpC;IAChC0B,cAAcQ,GAAG,CAAClC,mBAAmB+B;IAErCM,OAAOC,gBAAgB,CAACP,SAAS;QAC/BQ,QAAQ;YACNC,OAAOxC,kBAAkBuC,MAAM,CAACE,IAAI,CAACzC;QACvC;QACA0C,QAAQ;YACNF,OAAOxC,kBAAkB0C,MAAM,CAACD,IAAI,CAACzC;QACvC;QACA8B,KAAK;YACHU,OAAOxC,kBAAkB8B,GAAG,CAACW,IAAI,CAACzC;QACpC;QACA2C,KAAK;YACHH,OAAOxC,kBAAkB2C,GAAG,CAACF,IAAI,CAACzC;QACpC;QACAkC,KAAK;YACHM,OAAOxC,kBAAkBkC,GAAG,CAACO,IAAI,CAACzC;QACpC;QACA4C,cAAc;YACZJ,OAAOxC,kBAAkB4C,YAAY,CAACH,IAAI,CAACzC;QAC7C;QACA6C,SAAS;YACPL,OAAOxC,kBAAkB6C,OAAO,CAACJ,IAAI,CAACzC;QACxC;QACA8C,MAAM;YACJN,OAAOxC,kBAAkB8C,IAAI,CAACL,IAAI,CAACzC;QACrC;QACA+C,QAAQ;YACNP,OAAOxC,kBAAkB+C,MAAM,CAACN,IAAI,CAACzC;QACvC;QACAgD,SAAS;YACPR,OAAOxC,kBAAkBgD,OAAO,CAACP,IAAI,CAACzC;QACxC;QACA,CAACiD,OAAOC,QAAQ,CAAC,EAAE;YACjBV,OAAOxC,iBAAiB,CAACiD,OAAOC,QAAQ,CAAC,CAACT,IAAI,CAACzC;QACjD;IACF;IAEA,OAAO+B;AACT;AAEA,SAASR,0CACPvB,iBAAkC,EAClCF,KAAc;IAEd,MAAM+B,gBAAgBH,cAAcI,GAAG,CAAC9B;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUoB,IAAAA,iDAA0B,EAACnD;IAE3C0B,cAAcQ,GAAG,CAAClC,mBAAmB+B;IAErCM,OAAOC,gBAAgB,CAACP,SAAS;QAC/BQ,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAMa,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChFC,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkBuC,MAAM,CAACiB,KAAK,CACnCxD,mBACAsD;YAEJ;QACF;QACAZ,QAAQ;YACNF,OAAO,SAASiB;gBACd,MAAML,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3EC,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB0C,MAAM,CAACc,KAAK,CACnCxD,mBACAsD;YAEJ;QACF;QACAxB,KAAK;YACHU,OAAO,SAASV;gBACd,MAAMsB,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEC,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB8B,GAAG,CAAC0B,KAAK,CAACxD,mBAAmBsD;YACxD;QACF;QACAX,KAAK;YACHH,OAAO,SAASG;gBACd,MAAMS,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEC,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB2C,GAAG,CAACa,KAAK,CAACxD,mBAAmBsD;YACxD;QACF;QACApB,KAAK;YACHM,OAAO,SAASN;gBACd,MAAMkB,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7EC,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkBkC,GAAG,CAACsB,KAAK,CAACxD,mBAAmBsD;YACxD;QACF;QACAV,cAAc;YACZJ,OAAO,SAASI;gBACd,MAAMQ,aAAa;gBACnBG,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB4C,YAAY,CAACY,KAAK,CACzCxD,mBACAsD;YAEJ;QACF;QACAT,SAAS;YACPL,OAAO,SAASK;gBACd,MAAMO,aAAa;gBACnBG,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB6C,OAAO,CAACW,KAAK,CACpCxD,mBACAsD;YAEJ;QACF;QACAR,MAAM;YACJN,OAAO,SAASM;gBACd,MAAMM,aAAa;gBACnBG,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB8C,IAAI,CAACU,KAAK,CAACxD,mBAAmBsD;YACzD;QACF;QACAP,QAAQ;YACNP,OAAO,SAASO;gBACd,MAAMK,aAAa;gBACnBG,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkB+C,MAAM,CAACS,KAAK,CACnCxD,mBACAsD;YAEJ;QACF;QACAN,SAAS;YACPR,OAAO,SAASQ;gBACd,MAAMI,aAAa;gBACnBG,UAAUzD,OAAOsD;gBACjB,OAAOpD,kBAAkBgD,OAAO,CAACQ,KAAK,CACpCxD,mBACAsD;YAEJ;QACF;QACA,CAACL,OAAOC,QAAQ,CAAC,EAAE;YACjBV,OAAO;gBACL,MAAMY,aAAa;gBACnBG,UAAUzD,OAAOsD;gBACjB,OAAOpD,iBAAiB,CAACiD,OAAOC,QAAQ,CAAC,CAACM,KAAK,CAC7CxD,mBACAsD;YAEJ;QACF;IACF;IAEA,OAAOvB;AACT;AAEA,+EAA+E;AAC/E,+EAA+E;AAC/E,SAAST,oCACPtB,iBAAkC,EAClCF,KAAc;IAEd,MAAM+B,gBAAgBH,cAAcI,GAAG,CAAC9B;IACxC,IAAI6B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUoB,IAAAA,iDAA0B,EAACnD;IAE3C,MAAM0D,iBAAiB,IAAIC,MAAM5B,SAAS;QACxCD,KAAI8B,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAKZ,OAAOC,QAAQ;oBAAE;wBACpBa,kBAAkBjE,OAAO;wBACzB;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAW;wBACdiE,kBAAkBjE,OAAO,CAAC,YAAY,EAAE+D,KAAK,EAAE,CAAC;wBAChD;oBACF;gBACA;oBAAS;oBACP,kEAAkE;oBACpE;YACF;YAEA,OAAOG,uBAAc,CAAClC,GAAG,CAAC8B,QAAQC,MAAMC;QAC1C;IACF;IAEApC,cAAcQ,GAAG,CAAClC,mBAAmB0D;IAErC,OAAOA;AACT;AAEA,SAASL,gBAAgBY,GAAY;IACnC,OAAO,OAAOA,QAAQ,WAAW,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GAAG;AAChD;AAEA,SAASV,UAAUzD,KAAyB,EAAEsD,UAAkB;IAC9D,MAAM3D,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIC,eAAe;QACjB,OAAQA,cAAcY,IAAI;YACxB,KAAK;gBACH,IAAIZ,cAAcyE,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;oBACrEC,IAAAA,wDAAsC,EAAC1E;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;IAEA,gCAAgC;IAChCsE,kBAAkBjE,OAAOsD;AAC3B;AAEA,MAAMW,oBAAoBK,IAAAA,qFAA2C,EACnEC;AAGF,SAASA,yBACPvE,KAAyB,EACzBsD,UAAkB;IAElB,MAAMkB,SAASxE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAGyE,OAAO,KAAK,EAAElB,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0]}