{"version": 3, "sources": ["../../../../src/server/lib/router-utils/typegen.ts"], "sourcesContent": ["import type { RouteTypesManifest } from './route-types-utils'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils/is-dynamic'\n\nfunction generateRouteTypes(routesManifest: RouteTypesManifest): string {\n  const appRoutes = Object.keys(routesManifest.appRoutes).sort()\n  const pageRoutes = Object.keys(routesManifest.pageRoutes).sort()\n  const layoutRoutes = Object.keys(routesManifest.layoutRoutes).sort()\n  const redirectRoutes = Object.keys(routesManifest.redirectRoutes).sort()\n  const rewriteRoutes = Object.keys(routesManifest.rewriteRoutes).sort()\n\n  let result = ''\n\n  // Generate AppRoutes union type (pages only)\n  if (appRoutes.length > 0) {\n    result += `type AppRoutes = ${appRoutes.map((route) => JSON.stringify(route)).join(' | ')}\\n`\n  } else {\n    result += 'type AppRoutes = never\\n'\n  }\n\n  // Generate AppRouteHandlerRoutes union type for route handlers\n  const appRouteHandlerRoutes = Object.keys(\n    routesManifest.appRouteHandlerRoutes\n  ).sort()\n\n  const hasAppRouteHandlers = appRouteHandlerRoutes.length > 0\n\n  if (hasAppRouteHandlers) {\n    result += `type AppRouteHandlerRoutes = ${appRouteHandlerRoutes.map((route) => JSON.stringify(route)).join(' | ')}\\n`\n  }\n\n  // Generate PageRoutes union type\n  if (pageRoutes.length > 0) {\n    result += `type PageRoutes = ${pageRoutes.map((route) => JSON.stringify(route)).join(' | ')}\\n`\n  } else {\n    result += 'type PageRoutes = never\\n'\n  }\n\n  // Generate LayoutRoutes union type\n  if (layoutRoutes.length > 0) {\n    result += `type LayoutRoutes = ${layoutRoutes.map((route) => JSON.stringify(route)).join(' | ')}\\n`\n  } else {\n    result += 'type LayoutRoutes = never\\n'\n  }\n\n  // Generate RedirectRoutes union type\n  if (redirectRoutes.length > 0) {\n    result += `type RedirectRoutes = ${redirectRoutes\n      .map((route) => JSON.stringify(route))\n      .join(' | ')}\\n`\n  } else {\n    result += 'type RedirectRoutes = never\\n'\n  }\n\n  // Generate RewriteRoutes union type\n  if (rewriteRoutes.length > 0) {\n    result += `type RewriteRoutes = ${rewriteRoutes\n      .map((route) => JSON.stringify(route))\n      .join(' | ')}\\n`\n  } else {\n    result += 'type RewriteRoutes = never\\n'\n  }\n\n  // Only include AppRouteHandlerRoutes in Routes union if there are actual route handlers\n  const routeUnionParts = [\n    'AppRoutes',\n    'PageRoutes',\n    'LayoutRoutes',\n    'RedirectRoutes',\n    'RewriteRoutes',\n  ]\n  if (hasAppRouteHandlers) {\n    routeUnionParts.push('AppRouteHandlerRoutes')\n  }\n\n  result += `type Routes = ${routeUnionParts.join(' | ')}\\n`\n\n  return result\n}\n\nfunction generateParamTypes(routesManifest: RouteTypesManifest): string {\n  const allRoutes = {\n    ...routesManifest.appRoutes,\n    ...routesManifest.appRouteHandlerRoutes,\n    ...routesManifest.pageRoutes,\n    ...routesManifest.layoutRoutes,\n    ...routesManifest.redirectRoutes,\n    ...routesManifest.rewriteRoutes,\n  }\n\n  let paramTypes = 'interface ParamMap {\\n'\n\n  // Sort routes deterministically for consistent output\n  const sortedRoutes = Object.entries(allRoutes).sort(([a], [b]) =>\n    a.localeCompare(b)\n  )\n\n  for (const [route, routeInfo] of sortedRoutes) {\n    const { groups } = routeInfo\n\n    // For static routes (no dynamic segments), we can produce an empty parameter map.\n    if (!isDynamicRoute(route) || Object.keys(groups ?? {}).length === 0) {\n      paramTypes += `  ${JSON.stringify(route)}: {}\\n`\n      continue\n    }\n\n    let paramType = '{'\n\n    // Process each group based on its properties\n    for (const [key, group] of Object.entries(groups)) {\n      const escapedKey = JSON.stringify(key)\n      if (group.repeat) {\n        // Catch-all parameters\n        if (group.optional) {\n          paramType += ` ${escapedKey}?: string[];`\n        } else {\n          paramType += ` ${escapedKey}: string[];`\n        }\n      } else {\n        // Regular parameters\n        if (group.optional) {\n          paramType += ` ${escapedKey}?: string;`\n        } else {\n          paramType += ` ${escapedKey}: string;`\n        }\n      }\n    }\n\n    paramType += ' }'\n\n    paramTypes += `  ${JSON.stringify(route)}: ${paramType}\\n`\n  }\n\n  paramTypes += '}\\n'\n  return paramTypes\n}\n\nfunction generateLayoutSlotMap(routesManifest: RouteTypesManifest): string {\n  let slotMap = 'interface LayoutSlotMap {\\n'\n\n  // Sort routes deterministically for consistent output\n  const sortedLayoutRoutes = Object.entries(routesManifest.layoutRoutes).sort(\n    ([a], [b]) => a.localeCompare(b)\n  )\n\n  for (const [route, routeInfo] of sortedLayoutRoutes) {\n    if ('slots' in routeInfo) {\n      const slots = routeInfo.slots.sort()\n      if (slots.length > 0) {\n        slotMap += `  ${JSON.stringify(route)}: ${slots.map((slot) => JSON.stringify(slot)).join(' | ')}\\n`\n      } else {\n        slotMap += `  ${JSON.stringify(route)}: never\\n`\n      }\n    } else {\n      slotMap += `  ${JSON.stringify(route)}: never\\n`\n    }\n  }\n\n  slotMap += '}\\n'\n  return slotMap\n}\n\n// Helper function to format routes to route types (matches the plugin logic exactly)\nfunction formatRouteToRouteType(route: string) {\n  const isDynamic = isDynamicRoute(route)\n  if (isDynamic) {\n    route = route\n      .split('/')\n      .map((part) => {\n        if (part.startsWith('[') && part.endsWith(']')) {\n          if (part.startsWith('[...')) {\n            // /[...slug]\n            return `\\${CatchAllSlug<T>}`\n          } else if (part.startsWith('[[...') && part.endsWith(']]')) {\n            // /[[...slug]]\n            return `\\${OptionalCatchAllSlug<T>}`\n          }\n          // /[slug]\n          return `\\${SafeSlug<T>}`\n        }\n        return part\n      })\n      .join('/')\n  }\n\n  return {\n    isDynamic,\n    routeType: route,\n  }\n}\n\n// Helper function to serialize route types (matches the plugin logic exactly)\nfunction serializeRouteTypes(routeTypes: string[]) {\n  // route collection is not deterministic, this makes the output of the file deterministic\n  return routeTypes\n    .sort()\n    .map((route) => `\\n    | \\`${route}\\``)\n    .join('')\n}\n\nexport function generateLinkTypesFile(\n  routesManifest: RouteTypesManifest\n): string {\n  // Generate serialized static and dynamic routes for the internal namespace\n  // Build a unified set of routes across app/pages/redirect/rewrite as well as\n  // app route handlers and Pages Router API routes.\n  const allRoutesSet = new Set<string>([\n    ...Object.keys(routesManifest.appRoutes),\n    ...Object.keys(routesManifest.pageRoutes),\n    ...Object.keys(routesManifest.redirectRoutes),\n    ...Object.keys(routesManifest.rewriteRoutes),\n    // Allow linking to App Route Handlers (e.g. `/logout/route.ts`)\n    ...Object.keys(routesManifest.appRouteHandlerRoutes),\n    // Allow linking to Pages Router API routes (e.g. `/api/*`)\n    ...Array.from(routesManifest.pageApiRoutes),\n  ])\n\n  const staticRouteTypes: string[] = []\n  const dynamicRouteTypes: string[] = []\n\n  // Process each route using the same logic as the plugin\n  for (const route of allRoutesSet) {\n    const { isDynamic, routeType } = formatRouteToRouteType(route)\n    if (isDynamic) {\n      dynamicRouteTypes.push(routeType)\n    } else {\n      staticRouteTypes.push(routeType)\n    }\n  }\n\n  const serializedStaticRouteTypes = serializeRouteTypes(staticRouteTypes)\n  const serializedDynamicRouteTypes = serializeRouteTypes(dynamicRouteTypes)\n\n  // If both StaticRoutes and DynamicRoutes are empty, fallback to type 'string & {}'.\n  const routeTypesFallback =\n    !serializedStaticRouteTypes && !serializedDynamicRouteTypes\n      ? 'string & {}'\n      : ''\n\n  return `// This file is generated automatically by Next.js\n// Do not edit this file manually\n\n// Type definitions for Next.js routes\n\n/**\n * Internal types used by the Next.js router and Link component.\n * These types are not meant to be used directly.\n * @internal\n */\ndeclare namespace __next_route_internal_types__ {\n  type SearchOrHash = \\`?\\${string}\\` | \\`#\\${string}\\`\n  type WithProtocol = \\`\\${string}:\\${string}\\`\n\n  type Suffix = '' | SearchOrHash\n\n  type SafeSlug<S extends string> = S extends \\`\\${string}/\\${string}\\`\n    ? never\n    : S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n    ? never\n    : S\n\n  type CatchAllSlug<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n    ? never\n    : S\n\n  type OptionalCatchAllSlug<S extends string> =\n    S extends \\`\\${string}\\${SearchOrHash}\\` ? never : S\n\n  type StaticRoutes = ${serializedStaticRouteTypes || 'never'}\n  type DynamicRoutes<T extends string = string> = ${\n    serializedDynamicRouteTypes || 'never'\n  }\n\n  type RouteImpl<T> = ${\n    routeTypesFallback ||\n    `\n    ${\n      // This keeps autocompletion working for static routes.\n      '| StaticRoutes'\n    }\n    | SearchOrHash\n    | WithProtocol\n    | \\`\\${StaticRoutes}\\${SearchOrHash}\\`\n    | (T extends \\`\\${DynamicRoutes<infer _>}\\${Suffix}\\` ? T : never)\n    `\n  }\n}\n\ndeclare module 'next' {\n  export { default } from 'next/types.js'\n  export * from 'next/types.js'\n\n  export type Route<T extends string = string> =\n    __next_route_internal_types__.RouteImpl<T>\n}\n\ndeclare module 'next/link' {\n  import type { LinkProps as OriginalLinkProps } from 'next/dist/client/link.js'\n  import type { AnchorHTMLAttributes, DetailedHTMLProps } from 'react'\n  import type { UrlObject } from 'url'\n\n  type LinkRestProps = Omit<\n    Omit<\n      DetailedHTMLProps<\n        AnchorHTMLAttributes<HTMLAnchorElement>,\n        HTMLAnchorElement\n      >,\n      keyof OriginalLinkProps\n    > &\n      OriginalLinkProps,\n    'href'\n  >\n\n  export type LinkProps<RouteInferType> = LinkRestProps & {\n    /**\n     * The path or URL to navigate to. This is the only required prop. It can also be an object.\n     * @see https://nextjs.org/docs/api-reference/next/link\n     */\n    href: __next_route_internal_types__.RouteImpl<RouteInferType> | UrlObject\n  }\n\n  export default function Link<RouteType>(props: LinkProps<RouteType>): JSX.Element\n}\n\ndeclare module 'next/navigation' {\n  export * from 'next/dist/client/components/navigation.js'\n\n  import type { NavigateOptions, AppRouterInstance as OriginalAppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime.js'\n  import type { RedirectType } from 'next/dist/client/components/redirect-error.js'\n  \n  interface AppRouterInstance extends OriginalAppRouterInstance {\n    /**\n     * Navigate to the provided href.\n     * Pushes a new history entry.\n     */\n    push<RouteType>(href: __next_route_internal_types__.RouteImpl<RouteType>, options?: NavigateOptions): void\n    /**\n     * Navigate to the provided href.\n     * Replaces the current history entry.\n     */\n    replace<RouteType>(href: __next_route_internal_types__.RouteImpl<RouteType>, options?: NavigateOptions): void\n    /**\n     * Prefetch the provided href.\n     */\n    prefetch<RouteType>(href: __next_route_internal_types__.RouteImpl<RouteType>): void\n  }\n\n  export function useRouter(): AppRouterInstance;\n  \n  /**\n   * This function allows you to redirect the user to another URL. It can be used in\n   * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n   * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n   * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n   *\n   * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n   * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n   * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n   *\n   * Read more: [Next.js Docs: redirect](https://nextjs.org/docs/app/api-reference/functions/redirect)\n   */\n  export function redirect<RouteType>(\n    /** The URL to redirect to */\n    url: __next_route_internal_types__.RouteImpl<RouteType>,\n    type?: RedirectType\n  ): never;\n  \n  /**\n   * This function allows you to redirect the user to another URL. It can be used in\n   * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n   * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n   * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n   *\n   * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n   * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n   *\n   * Read more: [Next.js Docs: redirect](https://nextjs.org/docs/app/api-reference/functions/redirect)\n   */\n  export function permanentRedirect<RouteType>(\n    /** The URL to redirect to */\n    url: __next_route_internal_types__.RouteImpl<RouteType>,\n    type?: RedirectType\n  ): never;\n}\n\ndeclare module 'next/form' {\n  import type { FormProps as OriginalFormProps } from 'next/dist/client/form.js'\n\n  type FormRestProps = Omit<OriginalFormProps, 'action'>\n\n  export type FormProps<RouteInferType> = {\n    /**\n     * \\`action\\` can be either a \\`string\\` or a function.\n     * - If \\`action\\` is a string, it will be interpreted as a path or URL to navigate to when the form is submitted.\n     *   The path will be prefetched when the form becomes visible.\n     * - If \\`action\\` is a function, it will be called when the form is submitted. See the [React docs](https://react.dev/reference/react-dom/components/form#props) for more.\n     */\n    action: __next_route_internal_types__.RouteImpl<RouteInferType> | ((formData: FormData) => void)\n  } & FormRestProps\n\n  export default function Form<RouteType>(props: FormProps<RouteType>): JSX.Element\n}\n`\n}\n\nexport function generateValidatorFile(\n  routesManifest: RouteTypesManifest\n): string {\n  const generateValidations = (\n    paths: string[],\n    type:\n      | 'AppPageConfig'\n      | 'PagesPageConfig'\n      | 'LayoutConfig'\n      | 'RouteHandlerConfig'\n      | 'ApiRouteConfig',\n    pathToRouteMap?: Map<string, string>\n  ) =>\n    paths\n      .sort()\n      // Only validate TypeScript files - JavaScript files have too many type inference limitations\n      .filter(\n        (filePath) => filePath.endsWith('.ts') || filePath.endsWith('.tsx')\n      )\n      .filter(\n        // Don't include metadata routes or pages\n        // (e.g. /manifest.webmanifest)\n        (filePath) =>\n          type !== 'AppPageConfig' ||\n          filePath.endsWith('page.ts') ||\n          filePath.endsWith('page.tsx')\n      )\n      .map((filePath) => {\n        // Keep the file extension for TypeScript imports to support node16 module resolution\n        const importPath = filePath\n        const route = pathToRouteMap?.get(filePath)\n        const typeWithRoute =\n          route &&\n          (type === 'AppPageConfig' ||\n            type === 'LayoutConfig' ||\n            type === 'RouteHandlerConfig')\n            ? `${type}<${JSON.stringify(route)}>`\n            : type\n        return `// Validate ${filePath}\n{\n  const handler = {} as typeof import(${JSON.stringify(\n    importPath.replace(/\\.tsx?$/, '.js')\n  )})\n  handler satisfies ${typeWithRoute}\n}`\n      })\n      .join('\\n\\n')\n\n  // Use direct mappings from the manifest\n\n  // Generate validations for different route types\n  const appPageValidations = generateValidations(\n    Array.from(routesManifest.appPagePaths).sort(),\n    'AppPageConfig',\n    routesManifest.filePathToRoute\n  )\n  const appRouteHandlerValidations = generateValidations(\n    Array.from(routesManifest.appRouteHandlers).sort(),\n    'RouteHandlerConfig',\n    routesManifest.filePathToRoute\n  )\n  const pagesRouterPageValidations = generateValidations(\n    Array.from(routesManifest.pagesRouterPagePaths).sort(),\n    'PagesPageConfig'\n  )\n  const pagesApiRouteValidations = generateValidations(\n    Array.from(routesManifest.pageApiRoutes).sort(),\n    'ApiRouteConfig'\n  )\n  const layoutValidations = generateValidations(\n    Array.from(routesManifest.layoutPaths).sort(),\n    'LayoutConfig',\n    routesManifest.filePathToRoute\n  )\n\n  const hasAppRouteHandlers =\n    Object.keys(routesManifest.appRouteHandlerRoutes).length > 0\n\n  // Build type definitions based on what's actually used\n  let typeDefinitions = ''\n\n  if (appPageValidations) {\n    typeDefinitions += `type AppPageConfig<Route extends AppRoutes = AppRoutes> = {\n  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)\n  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]\n  generateMetadata?: (\n    props: { params: Promise<ParamMap[Route]> } & any,\n    parent: ResolvingMetadata\n  ) => Promise<any> | any\n  generateViewport?: (\n    props: { params: Promise<ParamMap[Route]> } & any,\n    parent: ResolvingViewport\n  ) => Promise<any> | any\n  metadata?: any\n  viewport?: any\n}\n\n`\n  }\n\n  if (pagesRouterPageValidations) {\n    typeDefinitions += `type PagesPageConfig = {\n  default: React.ComponentType<any> | ((props: any) => React.ReactNode | Promise<React.ReactNode> | never | void)\n  getStaticProps?: (context: any) => Promise<any> | any\n  getStaticPaths?: (context: any) => Promise<any> | any\n  getServerSideProps?: (context: any) => Promise<any> | any\n  getInitialProps?: (context: any) => Promise<any> | any\n  /**\n   * Segment configuration for legacy Pages Router pages.\n   * Validated at build-time by parsePagesSegmentConfig.\n   */\n  config?: {\n    amp?: boolean | 'hybrid' | string // necessary for JS\n    maxDuration?: number\n    runtime?: 'edge' | 'experimental-edge' | 'nodejs' | string // necessary unless config is exported as const\n    regions?: string[]\n  }\n}\n\n`\n  }\n\n  if (layoutValidations) {\n    typeDefinitions += `type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {\n  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)\n  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]\n  generateMetadata?: (\n    props: { params: Promise<ParamMap[Route]> } & any,\n    parent: ResolvingMetadata\n  ) => Promise<any> | any\n  generateViewport?: (\n    props: { params: Promise<ParamMap[Route]> } & any,\n    parent: ResolvingViewport\n  ) => Promise<any> | any\n  metadata?: any\n  viewport?: any\n}\n\n`\n  }\n\n  if (appRouteHandlerValidations) {\n    typeDefinitions += `type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {\n  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void\n}\n\n`\n  }\n\n  if (pagesApiRouteValidations) {\n    typeDefinitions += `type ApiRouteConfig = {\n  default: (req: any, res: any) => Promise<Response | void> | Response | void\n  config?: {\n    api?: {\n      bodyParser?: boolean | { sizeLimit?: string }\n      responseLimit?: string | number | boolean\n      externalResolver?: boolean\n    }\n    runtime?: 'edge' | 'experimental-edge' | 'nodejs' | string // necessary unless config is exported as const\n    maxDuration?: number\n  }\n}\n\n`\n  }\n\n  // Build import statement based on what's actually needed\n  const routeImports = []\n\n  // Only import AppRoutes if there are app pages\n  if (appPageValidations) {\n    routeImports.push('AppRoutes')\n  }\n\n  // Only import LayoutRoutes if there are layouts\n  if (layoutValidations) {\n    routeImports.push('LayoutRoutes')\n  }\n\n  // Only import ParamMap if there are routes that use it\n  if (appPageValidations || layoutValidations || appRouteHandlerValidations) {\n    routeImports.push('ParamMap')\n  }\n\n  if (hasAppRouteHandlers) {\n    routeImports.push('AppRouteHandlerRoutes')\n  }\n\n  const routeImportStatement =\n    routeImports.length > 0\n      ? `import type { ${routeImports.join(', ')} } from \"./routes.js\"`\n      : ''\n\n  const nextRequestImport = hasAppRouteHandlers\n    ? \"import type { NextRequest } from 'next/server.js'\\n\"\n    : ''\n\n  // Only import metadata types if there are App Router pages or layouts that might use them\n  const metadataImport =\n    appPageValidations || layoutValidations\n      ? 'import type { ResolvingMetadata, ResolvingViewport } from \"next/dist/lib/metadata/types/metadata-interface.js\"\\n'\n      : ''\n\n  return `// This file is generated automatically by Next.js\n// Do not edit this file manually\n// This file validates that all pages and layouts export the correct types\n\n${routeImportStatement}\n${metadataImport}${nextRequestImport}\n${typeDefinitions}\n${appPageValidations}\n\n${appRouteHandlerValidations}\n\n${pagesRouterPageValidations}\n\n${pagesApiRouteValidations}\n\n${layoutValidations}\n`\n}\n\nexport function generateRouteTypesFile(\n  routesManifest: RouteTypesManifest\n): string {\n  const routeTypes = generateRouteTypes(routesManifest)\n  const paramTypes = generateParamTypes(routesManifest)\n  const layoutSlotMap = generateLayoutSlotMap(routesManifest)\n\n  const hasAppRouteHandlers =\n    Object.keys(routesManifest.appRouteHandlerRoutes).length > 0\n\n  // Build export statement based on what's actually generated\n  const routeExports = [\n    'AppRoutes',\n    'PageRoutes',\n    'LayoutRoutes',\n    'RedirectRoutes',\n    'RewriteRoutes',\n    'ParamMap',\n  ]\n  if (hasAppRouteHandlers) {\n    routeExports.push('AppRouteHandlerRoutes')\n  }\n\n  const exportStatement = `export type { ${routeExports.join(', ')} }`\n\n  const routeContextInterface = hasAppRouteHandlers\n    ? `\n\n  /**\n   * Context for Next.js App Router route handlers\n   * @example\n   * \\`\\`\\`tsx\n   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {\n   *   const { id } = await context.params\n   *   return Response.json({ id })\n   * }\n   * \\`\\`\\`\n   */\n  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {\n    params: Promise<ParamMap[AppRouteHandlerRoute]>\n  }`\n    : ''\n\n  return `// This file is generated automatically by Next.js\n// Do not edit this file manually\n\n${routeTypes}\n\n${paramTypes}\n\nexport type ParamsOf<Route extends Routes> = ParamMap[Route]\n\n${layoutSlotMap}\n\n${exportStatement}\n\ndeclare global {\n  /**\n   * Props for Next.js App Router page components\n   * @example\n   * \\`\\`\\`tsx\n   * export default function Page(props: PageProps<'/blog/[slug]'>) {\n   *   const { slug } = await props.params\n   *   return <div>Blog post: {slug}</div>\n   * }\n   * \\`\\`\\`\n   */\n  interface PageProps<AppRoute extends AppRoutes> {\n    params: Promise<ParamMap[AppRoute]>\n    searchParams: Promise<Record<string, string | string[] | undefined>>\n  }\n\n  /**\n   * Props for Next.js App Router layout components\n   * @example\n   * \\`\\`\\`tsx\n   * export default function Layout(props: LayoutProps<'/dashboard'>) {\n   *   return <div>{props.children}</div>\n   * }\n   * \\`\\`\\`\n   */\n  type LayoutProps<LayoutRoute extends LayoutRoutes> = {\n    params: Promise<ParamMap[LayoutRoute]>\n    children: React.ReactNode\n  } & {\n    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode\n  }${routeContextInterface}\n}\n`\n}\n"], "names": ["isDynamicRoute", "generateRouteTypes", "routesManifest", "appRoutes", "Object", "keys", "sort", "pageRoutes", "layoutRoutes", "redirectRoutes", "rewriteRoutes", "result", "length", "map", "route", "JSON", "stringify", "join", "appRouteHandlerRoutes", "hasAppRouteHandlers", "routeUnionParts", "push", "generateParamTypes", "allRoutes", "paramTypes", "sortedRoutes", "entries", "a", "b", "localeCompare", "routeInfo", "groups", "paramType", "key", "group", "<PERSON><PERSON><PERSON>", "repeat", "optional", "generateLayoutSlotMap", "slotMap", "sortedLayoutRoutes", "slots", "slot", "formatRouteToRouteType", "isDynamic", "split", "part", "startsWith", "endsWith", "routeType", "serializeRouteTypes", "routeTypes", "generateLinkTypesFile", "allRoutesSet", "Set", "Array", "from", "pageApiRoutes", "staticRouteTypes", "dynamicRouteTypes", "serializedStaticRouteTypes", "serializedDynamicRouteTypes", "routeTypesFallback", "generateValidatorFile", "generateValidations", "paths", "type", "pathToRouteMap", "filter", "filePath", "importPath", "get", "typeWithRoute", "replace", "appPageValidations", "appPagePaths", "filePathToRoute", "appRouteHandlerValidations", "appRouteHandlers", "pagesRouterPageValidations", "pagesRouterPagePaths", "pagesApiRouteValidations", "layoutValidations", "layoutPaths", "typeDefinitions", "routeImports", "routeImportStatement", "nextRequestImport", "metadataImport", "generateRouteTypesFile", "layoutSlotMap", "routeExports", "exportStatement", "routeContextInterface"], "mappings": "AACA,SAASA,cAAc,QAAQ,8CAA6C;AAE5E,SAASC,mBAAmBC,cAAkC;IAC5D,MAAMC,YAAYC,OAAOC,IAAI,CAACH,eAAeC,SAAS,EAAEG,IAAI;IAC5D,MAAMC,aAAaH,OAAOC,IAAI,CAACH,eAAeK,UAAU,EAAED,IAAI;IAC9D,MAAME,eAAeJ,OAAOC,IAAI,CAACH,eAAeM,YAAY,EAAEF,IAAI;IAClE,MAAMG,iBAAiBL,OAAOC,IAAI,CAACH,eAAeO,cAAc,EAAEH,IAAI;IACtE,MAAMI,gBAAgBN,OAAOC,IAAI,CAACH,eAAeQ,aAAa,EAAEJ,IAAI;IAEpE,IAAIK,SAAS;IAEb,6CAA6C;IAC7C,IAAIR,UAAUS,MAAM,GAAG,GAAG;QACxBD,UAAU,CAAC,iBAAiB,EAAER,UAAUU,GAAG,CAAC,CAACC,QAAUC,KAAKC,SAAS,CAACF,QAAQG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC/F,OAAO;QACLN,UAAU;IACZ;IAEA,+DAA+D;IAC/D,MAAMO,wBAAwBd,OAAOC,IAAI,CACvCH,eAAegB,qBAAqB,EACpCZ,IAAI;IAEN,MAAMa,sBAAsBD,sBAAsBN,MAAM,GAAG;IAE3D,IAAIO,qBAAqB;QACvBR,UAAU,CAAC,6BAA6B,EAAEO,sBAAsBL,GAAG,CAAC,CAACC,QAAUC,KAAKC,SAAS,CAACF,QAAQG,IAAI,CAAC,OAAO,EAAE,CAAC;IACvH;IAEA,iCAAiC;IACjC,IAAIV,WAAWK,MAAM,GAAG,GAAG;QACzBD,UAAU,CAAC,kBAAkB,EAAEJ,WAAWM,GAAG,CAAC,CAACC,QAAUC,KAAKC,SAAS,CAACF,QAAQG,IAAI,CAAC,OAAO,EAAE,CAAC;IACjG,OAAO;QACLN,UAAU;IACZ;IAEA,mCAAmC;IACnC,IAAIH,aAAaI,MAAM,GAAG,GAAG;QAC3BD,UAAU,CAAC,oBAAoB,EAAEH,aAAaK,GAAG,CAAC,CAACC,QAAUC,KAAKC,SAAS,CAACF,QAAQG,IAAI,CAAC,OAAO,EAAE,CAAC;IACrG,OAAO;QACLN,UAAU;IACZ;IAEA,qCAAqC;IACrC,IAAIF,eAAeG,MAAM,GAAG,GAAG;QAC7BD,UAAU,CAAC,sBAAsB,EAAEF,eAChCI,GAAG,CAAC,CAACC,QAAUC,KAAKC,SAAS,CAACF,QAC9BG,IAAI,CAAC,OAAO,EAAE,CAAC;IACpB,OAAO;QACLN,UAAU;IACZ;IAEA,oCAAoC;IACpC,IAAID,cAAcE,MAAM,GAAG,GAAG;QAC5BD,UAAU,CAAC,qBAAqB,EAAED,cAC/BG,GAAG,CAAC,CAACC,QAAUC,KAAKC,SAAS,CAACF,QAC9BG,IAAI,CAAC,OAAO,EAAE,CAAC;IACpB,OAAO;QACLN,UAAU;IACZ;IAEA,wFAAwF;IACxF,MAAMS,kBAAkB;QACtB;QACA;QACA;QACA;QACA;KACD;IACD,IAAID,qBAAqB;QACvBC,gBAAgBC,IAAI,CAAC;IACvB;IAEAV,UAAU,CAAC,cAAc,EAAES,gBAAgBH,IAAI,CAAC,OAAO,EAAE,CAAC;IAE1D,OAAON;AACT;AAEA,SAASW,mBAAmBpB,cAAkC;IAC5D,MAAMqB,YAAY;QAChB,GAAGrB,eAAeC,SAAS;QAC3B,GAAGD,eAAegB,qBAAqB;QACvC,GAAGhB,eAAeK,UAAU;QAC5B,GAAGL,eAAeM,YAAY;QAC9B,GAAGN,eAAeO,cAAc;QAChC,GAAGP,eAAeQ,aAAa;IACjC;IAEA,IAAIc,aAAa;IAEjB,sDAAsD;IACtD,MAAMC,eAAerB,OAAOsB,OAAO,CAACH,WAAWjB,IAAI,CAAC,CAAC,CAACqB,EAAE,EAAE,CAACC,EAAE,GAC3DD,EAAEE,aAAa,CAACD;IAGlB,KAAK,MAAM,CAACd,OAAOgB,UAAU,IAAIL,aAAc;QAC7C,MAAM,EAAEM,MAAM,EAAE,GAAGD;QAEnB,kFAAkF;QAClF,IAAI,CAAC9B,eAAec,UAAUV,OAAOC,IAAI,CAAC0B,UAAU,CAAC,GAAGnB,MAAM,KAAK,GAAG;YACpEY,cAAc,CAAC,EAAE,EAAET,KAAKC,SAAS,CAACF,OAAO,MAAM,CAAC;YAChD;QACF;QAEA,IAAIkB,YAAY;QAEhB,6CAA6C;QAC7C,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAI9B,OAAOsB,OAAO,CAACK,QAAS;YACjD,MAAMI,aAAapB,KAAKC,SAAS,CAACiB;YAClC,IAAIC,MAAME,MAAM,EAAE;gBAChB,uBAAuB;gBACvB,IAAIF,MAAMG,QAAQ,EAAE;oBAClBL,aAAa,CAAC,CAAC,EAAEG,WAAW,YAAY,CAAC;gBAC3C,OAAO;oBACLH,aAAa,CAAC,CAAC,EAAEG,WAAW,WAAW,CAAC;gBAC1C;YACF,OAAO;gBACL,qBAAqB;gBACrB,IAAID,MAAMG,QAAQ,EAAE;oBAClBL,aAAa,CAAC,CAAC,EAAEG,WAAW,UAAU,CAAC;gBACzC,OAAO;oBACLH,aAAa,CAAC,CAAC,EAAEG,WAAW,SAAS,CAAC;gBACxC;YACF;QACF;QAEAH,aAAa;QAEbR,cAAc,CAAC,EAAE,EAAET,KAAKC,SAAS,CAACF,OAAO,EAAE,EAAEkB,UAAU,EAAE,CAAC;IAC5D;IAEAR,cAAc;IACd,OAAOA;AACT;AAEA,SAASc,sBAAsBpC,cAAkC;IAC/D,IAAIqC,UAAU;IAEd,sDAAsD;IACtD,MAAMC,qBAAqBpC,OAAOsB,OAAO,CAACxB,eAAeM,YAAY,EAAEF,IAAI,CACzE,CAAC,CAACqB,EAAE,EAAE,CAACC,EAAE,GAAKD,EAAEE,aAAa,CAACD;IAGhC,KAAK,MAAM,CAACd,OAAOgB,UAAU,IAAIU,mBAAoB;QACnD,IAAI,WAAWV,WAAW;YACxB,MAAMW,QAAQX,UAAUW,KAAK,CAACnC,IAAI;YAClC,IAAImC,MAAM7B,MAAM,GAAG,GAAG;gBACpB2B,WAAW,CAAC,EAAE,EAAExB,KAAKC,SAAS,CAACF,OAAO,EAAE,EAAE2B,MAAM5B,GAAG,CAAC,CAAC6B,OAAS3B,KAAKC,SAAS,CAAC0B,OAAOzB,IAAI,CAAC,OAAO,EAAE,CAAC;YACrG,OAAO;gBACLsB,WAAW,CAAC,EAAE,EAAExB,KAAKC,SAAS,CAACF,OAAO,SAAS,CAAC;YAClD;QACF,OAAO;YACLyB,WAAW,CAAC,EAAE,EAAExB,KAAKC,SAAS,CAACF,OAAO,SAAS,CAAC;QAClD;IACF;IAEAyB,WAAW;IACX,OAAOA;AACT;AAEA,qFAAqF;AACrF,SAASI,uBAAuB7B,KAAa;IAC3C,MAAM8B,YAAY5C,eAAec;IACjC,IAAI8B,WAAW;QACb9B,QAAQA,MACL+B,KAAK,CAAC,KACNhC,GAAG,CAAC,CAACiC;YACJ,IAAIA,KAAKC,UAAU,CAAC,QAAQD,KAAKE,QAAQ,CAAC,MAAM;gBAC9C,IAAIF,KAAKC,UAAU,CAAC,SAAS;oBAC3B,aAAa;oBACb,OAAO,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,IAAID,KAAKC,UAAU,CAAC,YAAYD,KAAKE,QAAQ,CAAC,OAAO;oBAC1D,eAAe;oBACf,OAAO,CAAC,2BAA2B,CAAC;gBACtC;gBACA,UAAU;gBACV,OAAO,CAAC,eAAe,CAAC;YAC1B;YACA,OAAOF;QACT,GACC7B,IAAI,CAAC;IACV;IAEA,OAAO;QACL2B;QACAK,WAAWnC;IACb;AACF;AAEA,8EAA8E;AAC9E,SAASoC,oBAAoBC,UAAoB;IAC/C,yFAAyF;IACzF,OAAOA,WACJ7C,IAAI,GACJO,GAAG,CAAC,CAACC,QAAU,CAAC,UAAU,EAAEA,MAAM,EAAE,CAAC,EACrCG,IAAI,CAAC;AACV;AAEA,OAAO,SAASmC,sBACdlD,cAAkC;IAElC,2EAA2E;IAC3E,6EAA6E;IAC7E,kDAAkD;IAClD,MAAMmD,eAAe,IAAIC,IAAY;WAChClD,OAAOC,IAAI,CAACH,eAAeC,SAAS;WACpCC,OAAOC,IAAI,CAACH,eAAeK,UAAU;WACrCH,OAAOC,IAAI,CAACH,eAAeO,cAAc;WACzCL,OAAOC,IAAI,CAACH,eAAeQ,aAAa;QAC3C,gEAAgE;WAC7DN,OAAOC,IAAI,CAACH,eAAegB,qBAAqB;QACnD,2DAA2D;WACxDqC,MAAMC,IAAI,CAACtD,eAAeuD,aAAa;KAC3C;IAED,MAAMC,mBAA6B,EAAE;IACrC,MAAMC,oBAA8B,EAAE;IAEtC,wDAAwD;IACxD,KAAK,MAAM7C,SAASuC,aAAc;QAChC,MAAM,EAAET,SAAS,EAAEK,SAAS,EAAE,GAAGN,uBAAuB7B;QACxD,IAAI8B,WAAW;YACbe,kBAAkBtC,IAAI,CAAC4B;QACzB,OAAO;YACLS,iBAAiBrC,IAAI,CAAC4B;QACxB;IACF;IAEA,MAAMW,6BAA6BV,oBAAoBQ;IACvD,MAAMG,8BAA8BX,oBAAoBS;IAExD,oFAAoF;IACpF,MAAMG,qBACJ,CAACF,8BAA8B,CAACC,8BAC5B,gBACA;IAEN,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAiCY,EAAED,8BAA8B,QAAQ;kDACZ,EAC9CC,+BAA+B,QAChC;;sBAEmB,EAClBC,sBACA,CAAC;IACD,EACE,uDAAuD;IACvD,iBACD;;;;;IAKD,CAAC,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHH,CAAC;AACD;AAEA,OAAO,SAASC,sBACd7D,cAAkC;IAElC,MAAM8D,sBAAsB,CAC1BC,OACAC,MAMAC,iBAEAF,MACG3D,IAAI,EACL,6FAA6F;SAC5F8D,MAAM,CACL,CAACC,WAAaA,SAASrB,QAAQ,CAAC,UAAUqB,SAASrB,QAAQ,CAAC,SAE7DoB,MAAM,CACL,yCAAyC;QACzC,+BAA+B;QAC/B,CAACC,WACCH,SAAS,mBACTG,SAASrB,QAAQ,CAAC,cAClBqB,SAASrB,QAAQ,CAAC,aAErBnC,GAAG,CAAC,CAACwD;YACJ,qFAAqF;YACrF,MAAMC,aAAaD;YACnB,MAAMvD,QAAQqD,kCAAAA,eAAgBI,GAAG,CAACF;YAClC,MAAMG,gBACJ1D,SACCoD,CAAAA,SAAS,mBACRA,SAAS,kBACTA,SAAS,oBAAmB,IAC1B,GAAGA,KAAK,CAAC,EAAEnD,KAAKC,SAAS,CAACF,OAAO,CAAC,CAAC,GACnCoD;YACN,OAAO,CAAC,YAAY,EAAEG,SAAS;;sCAED,EAAEtD,KAAKC,SAAS,CAClDsD,WAAWG,OAAO,CAAC,WAAW,QAC9B;oBACgB,EAAED,cAAc;CACnC,CAAC;QACI,GACCvD,IAAI,CAAC;IAEV,wCAAwC;IAExC,iDAAiD;IACjD,MAAMyD,qBAAqBV,oBACzBT,MAAMC,IAAI,CAACtD,eAAeyE,YAAY,EAAErE,IAAI,IAC5C,iBACAJ,eAAe0E,eAAe;IAEhC,MAAMC,6BAA6Bb,oBACjCT,MAAMC,IAAI,CAACtD,eAAe4E,gBAAgB,EAAExE,IAAI,IAChD,sBACAJ,eAAe0E,eAAe;IAEhC,MAAMG,6BAA6Bf,oBACjCT,MAAMC,IAAI,CAACtD,eAAe8E,oBAAoB,EAAE1E,IAAI,IACpD;IAEF,MAAM2E,2BAA2BjB,oBAC/BT,MAAMC,IAAI,CAACtD,eAAeuD,aAAa,EAAEnD,IAAI,IAC7C;IAEF,MAAM4E,oBAAoBlB,oBACxBT,MAAMC,IAAI,CAACtD,eAAeiF,WAAW,EAAE7E,IAAI,IAC3C,gBACAJ,eAAe0E,eAAe;IAGhC,MAAMzD,sBACJf,OAAOC,IAAI,CAACH,eAAegB,qBAAqB,EAAEN,MAAM,GAAG;IAE7D,uDAAuD;IACvD,IAAIwE,kBAAkB;IAEtB,IAAIV,oBAAoB;QACtBU,mBAAmB,CAAC;;;;;;;;;;;;;;;AAexB,CAAC;IACC;IAEA,IAAIL,4BAA4B;QAC9BK,mBAAmB,CAAC;;;;;;;;;;;;;;;;;;AAkBxB,CAAC;IACC;IAEA,IAAIF,mBAAmB;QACrBE,mBAAmB,CAAC;;;;;;;;;;;;;;;AAexB,CAAC;IACC;IAEA,IAAIP,4BAA4B;QAC9BO,mBAAmB,CAAC;;;;;;;;;;AAUxB,CAAC;IACC;IAEA,IAAIH,0BAA0B;QAC5BG,mBAAmB,CAAC;;;;;;;;;;;;;AAaxB,CAAC;IACC;IAEA,yDAAyD;IACzD,MAAMC,eAAe,EAAE;IAEvB,+CAA+C;IAC/C,IAAIX,oBAAoB;QACtBW,aAAahE,IAAI,CAAC;IACpB;IAEA,gDAAgD;IAChD,IAAI6D,mBAAmB;QACrBG,aAAahE,IAAI,CAAC;IACpB;IAEA,uDAAuD;IACvD,IAAIqD,sBAAsBQ,qBAAqBL,4BAA4B;QACzEQ,aAAahE,IAAI,CAAC;IACpB;IAEA,IAAIF,qBAAqB;QACvBkE,aAAahE,IAAI,CAAC;IACpB;IAEA,MAAMiE,uBACJD,aAAazE,MAAM,GAAG,IAClB,CAAC,cAAc,EAAEyE,aAAapE,IAAI,CAAC,MAAM,qBAAqB,CAAC,GAC/D;IAEN,MAAMsE,oBAAoBpE,sBACtB,wDACA;IAEJ,0FAA0F;IAC1F,MAAMqE,iBACJd,sBAAsBQ,oBAClB,qHACA;IAEN,OAAO,CAAC;;;;AAIV,EAAEI,qBAAqB;AACvB,EAAEE,iBAAiBD,kBAAkB;AACrC,EAAEH,gBAAgB;AAClB,EAAEV,mBAAmB;;AAErB,EAAEG,2BAA2B;;AAE7B,EAAEE,2BAA2B;;AAE7B,EAAEE,yBAAyB;;AAE3B,EAAEC,kBAAkB;AACpB,CAAC;AACD;AAEA,OAAO,SAASO,uBACdvF,cAAkC;IAElC,MAAMiD,aAAalD,mBAAmBC;IACtC,MAAMsB,aAAaF,mBAAmBpB;IACtC,MAAMwF,gBAAgBpD,sBAAsBpC;IAE5C,MAAMiB,sBACJf,OAAOC,IAAI,CAACH,eAAegB,qBAAqB,EAAEN,MAAM,GAAG;IAE7D,4DAA4D;IAC5D,MAAM+E,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IACD,IAAIxE,qBAAqB;QACvBwE,aAAatE,IAAI,CAAC;IACpB;IAEA,MAAMuE,kBAAkB,CAAC,cAAc,EAAED,aAAa1E,IAAI,CAAC,MAAM,EAAE,CAAC;IAEpE,MAAM4E,wBAAwB1E,sBAC1B,CAAC;;;;;;;;;;;;;;GAcJ,CAAC,GACE;IAEJ,OAAO,CAAC;;;AAGV,EAAEgC,WAAW;;AAEb,EAAE3B,WAAW;;;;AAIb,EAAEkE,cAAc;;AAEhB,EAAEE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCf,EAAEC,sBAAsB;;AAE3B,CAAC;AACD", "ignoreList": [0]}