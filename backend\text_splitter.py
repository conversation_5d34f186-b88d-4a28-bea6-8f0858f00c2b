from langchain_text_splitters import RecursiveCharacterTextSplitter

def get_splitter(chunk_size=500, chunk_overlap=100):
    return RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=["\n\n", "\n", ". ", "? ", "! ", " ", ""]
    )

def split_and_dedupe(text, splitter, min_chars=30):
    chunks = splitter.split_text(text)
    seen = set()
    unique_chunks = []
    for c in chunks:
        c = c.strip()
        if len(c) >= min_chars and c not in seen:
            seen.add(c)
            unique_chunks.append(c)
    return unique_chunks
