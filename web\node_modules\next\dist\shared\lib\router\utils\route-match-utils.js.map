{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-match-utils.ts"], "sourcesContent": ["/**\n * Client-safe utilities for route matching that don't import server-side\n * utilities to avoid bundling issues with Turbopack\n */\n\nimport type {\n  Key,\n  TokensToRegexpOptions,\n  ParseOptions,\n  TokensToFunctionOptions,\n} from 'next/dist/compiled/path-to-regexp'\nimport {\n  pathToRegexp,\n  compile,\n  regexpToFunction,\n} from 'next/dist/compiled/path-to-regexp'\nimport {\n  hasAdjacentParameterIssues,\n  normalizeAdjacentParameters,\n  stripParameterSeparators,\n} from '../../../../lib/route-pattern-normalizer'\n\n/**\n * Client-safe wrapper around pathToRegexp that handles path-to-regexp 6.3.0+ validation errors.\n * This includes both \"Can not repeat without prefix/suffix\" and \"Must have text between parameters\" errors.\n */\nexport function safePathToRegexp(\n  route: string | RegExp | Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n): RegExp {\n  if (typeof route !== 'string') {\n    return pathToRegexp(route, keys, options)\n  }\n\n  // Check if normalization is needed and cache the result\n  const needsNormalization = hasAdjacentParameterIssues(route)\n  const routeToUse = needsNormalization\n    ? normalizeAdjacentParameters(route)\n    : route\n\n  try {\n    return pathToRegexp(routeToUse, keys, options)\n  } catch (error) {\n    // Only try normalization if we haven't already normalized\n    if (!needsNormalization) {\n      try {\n        const normalizedRoute = normalizeAdjacentParameters(route)\n        return pathToRegexp(normalizedRoute, keys, options)\n      } catch (retryError) {\n        // If that doesn't work, fall back to original error\n        throw error\n      }\n    }\n    throw error\n  }\n}\n\n/**\n * Client-safe wrapper around compile that handles path-to-regexp 6.3.0+ validation errors.\n * No server-side error reporting to avoid bundling issues.\n */\nexport function safeCompile(\n  route: string,\n  options?: TokensToFunctionOptions & ParseOptions\n) {\n  // Check if normalization is needed and cache the result\n  const needsNormalization = hasAdjacentParameterIssues(route)\n  const routeToUse = needsNormalization\n    ? normalizeAdjacentParameters(route)\n    : route\n\n  try {\n    return compile(routeToUse, options)\n  } catch (error) {\n    // Only try normalization if we haven't already normalized\n    if (!needsNormalization) {\n      try {\n        const normalizedRoute = normalizeAdjacentParameters(route)\n        return compile(normalizedRoute, options)\n      } catch (retryError) {\n        // If that doesn't work, fall back to original error\n        throw error\n      }\n    }\n    throw error\n  }\n}\n\n/**\n * Client-safe wrapper around regexpToFunction that automatically cleans parameters.\n */\nexport function safeRegexpToFunction<\n  T extends Record<string, any> = Record<string, any>,\n>(regexp: RegExp, keys?: Key[]): (pathname: string) => { params: T } | false {\n  const originalMatcher = regexpToFunction<T>(regexp, keys || [])\n\n  return (pathname: string) => {\n    const result = originalMatcher(pathname)\n    if (!result) return false\n\n    // Clean parameters before returning\n    return {\n      ...result,\n      params: stripParameterSeparators(result.params as any) as T,\n    }\n  }\n}\n\n/**\n * Safe wrapper for route matcher functions that automatically cleans parameters.\n * This is client-safe and doesn't import path-to-regexp.\n */\nexport function safeRouteMatcher<T extends Record<string, any>>(\n  matcherFn: (pathname: string) => false | T\n): (pathname: string) => false | T {\n  return (pathname: string) => {\n    const result = matcherFn(pathname)\n    if (!result) return false\n\n    // Clean parameters before returning\n    return stripParameterSeparators(result) as T\n  }\n}\n"], "names": ["safeCompile", "safePathToRegexp", "safeRegexpToFunction", "safeRouteMatcher", "route", "keys", "options", "pathToRegexp", "needsNormalization", "hasAdjacentParameterIssues", "routeToUse", "normalizeAdjacentParameters", "error", "normalizedRoute", "retryError", "compile", "regexp", "originalMatcher", "regexpToFunction", "pathname", "result", "params", "stripParameterSeparators", "matcherFn"], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;IA2DeA,WAAW;eAAXA;;IApCAC,gBAAgB;eAAhBA;;IAkEAC,oBAAoB;eAApBA;;IAqBAC,gBAAgB;eAAhBA;;;8BAlGT;wCAKA;AAMA,SAASF,iBACdG,KAA+C,EAC/CC,IAAY,EACZC,OAA8C;IAE9C,IAAI,OAAOF,UAAU,UAAU;QAC7B,OAAOG,IAAAA,0BAAY,EAACH,OAAOC,MAAMC;IACnC;IAEA,wDAAwD;IACxD,MAAME,qBAAqBC,IAAAA,kDAA0B,EAACL;IACtD,MAAMM,aAAaF,qBACfG,IAAAA,mDAA2B,EAACP,SAC5BA;IAEJ,IAAI;QACF,OAAOG,IAAAA,0BAAY,EAACG,YAAYL,MAAMC;IACxC,EAAE,OAAOM,OAAO;QACd,0DAA0D;QAC1D,IAAI,CAACJ,oBAAoB;YACvB,IAAI;gBACF,MAAMK,kBAAkBF,IAAAA,mDAA2B,EAACP;gBACpD,OAAOG,IAAAA,0BAAY,EAACM,iBAAiBR,MAAMC;YAC7C,EAAE,OAAOQ,YAAY;gBACnB,oDAAoD;gBACpD,MAAMF;YACR;QACF;QACA,MAAMA;IACR;AACF;AAMO,SAASZ,YACdI,KAAa,EACbE,OAAgD;IAEhD,wDAAwD;IACxD,MAAME,qBAAqBC,IAAAA,kDAA0B,EAACL;IACtD,MAAMM,aAAaF,qBACfG,IAAAA,mDAA2B,EAACP,SAC5BA;IAEJ,IAAI;QACF,OAAOW,IAAAA,qBAAO,EAACL,YAAYJ;IAC7B,EAAE,OAAOM,OAAO;QACd,0DAA0D;QAC1D,IAAI,CAACJ,oBAAoB;YACvB,IAAI;gBACF,MAAMK,kBAAkBF,IAAAA,mDAA2B,EAACP;gBACpD,OAAOW,IAAAA,qBAAO,EAACF,iBAAiBP;YAClC,EAAE,OAAOQ,YAAY;gBACnB,oDAAoD;gBACpD,MAAMF;YACR;QACF;QACA,MAAMA;IACR;AACF;AAKO,SAASV,qBAEdc,MAAc,EAAEX,IAAY;IAC5B,MAAMY,kBAAkBC,IAAAA,8BAAgB,EAAIF,QAAQX,QAAQ,EAAE;IAE9D,OAAO,CAACc;QACN,MAAMC,SAASH,gBAAgBE;QAC/B,IAAI,CAACC,QAAQ,OAAO;QAEpB,oCAAoC;QACpC,OAAO;YACL,GAAGA,MAAM;YACTC,QAAQC,IAAAA,gDAAwB,EAACF,OAAOC,MAAM;QAChD;IACF;AACF;AAMO,SAASlB,iBACdoB,SAA0C;IAE1C,OAAO,CAACJ;QACN,MAAMC,SAASG,UAAUJ;QACzB,IAAI,CAACC,QAAQ,OAAO;QAEpB,oCAAoC;QACpC,OAAOE,IAAAA,gDAAwB,EAACF;IAClC;AACF", "ignoreList": [0]}