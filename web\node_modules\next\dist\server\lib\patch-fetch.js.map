{"version": 3, "sources": ["../../../src/server/lib/patch-fetch.ts"], "sourcesContent": ["import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport {\n  getCacheSignal,\n  type RevalidateStore,\n  type WorkUnitAsyncStorage,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n  type ServerComponentsHmrCache,\n  type SetIncrementalFetchCacheContext,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\nimport type { IncrementalCache } from './incremental-cache'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  if (!workStore.shouldTrackFetchMetrics) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\nasync function createCachedPrerenderResponse(\n  res: Response,\n  cacheKey: string,\n  incrementalCacheContext: SetIncrementalFetchCacheContext | undefined,\n  incrementalCache: IncrementalCache,\n  revalidate: number,\n  handleUnlock: () => Promise<void> | void\n): Promise<Response> {\n  // We are prerendering at build time or revalidate time with cacheComponents so we\n  // need to buffer the response so we can guarantee it can be read in a\n  // microtask.\n  const bodyBuffer = await res.arrayBuffer()\n\n  const fetchedData = {\n    headers: Object.fromEntries(res.headers.entries()),\n    body: Buffer.from(bodyBuffer).toString('base64'),\n    status: res.status,\n    url: res.url,\n  }\n\n  // We can skip setting the serverComponentsHmrCache because we aren't in dev\n  // mode.\n\n  if (incrementalCacheContext) {\n    await incrementalCache.set(\n      cacheKey,\n      { kind: CachedRouteKind.FETCH, data: fetchedData, revalidate },\n      incrementalCacheContext\n    )\n  }\n\n  await handleUnlock()\n\n  // We return a new Response to the caller.\n  return new Response(bodyBuffer, {\n    headers: res.headers,\n    status: res.status,\n    statusText: res.statusText,\n  })\n}\n\nasync function createCachedDynamicResponse(\n  workStore: WorkStore,\n  res: Response,\n  cacheKey: string,\n  incrementalCacheContext: SetIncrementalFetchCacheContext | undefined,\n  incrementalCache: IncrementalCache,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  revalidate: number,\n  input: RequestInfo | URL,\n  handleUnlock: () => Promise<void> | void\n): Promise<Response> {\n  // We're cloning the response using this utility because there exists a bug in\n  // the undici library around response cloning. See the following pull request\n  // for more details: https://github.com/vercel/next.js/pull/73274\n  const [cloned1, cloned2] = cloneResponse(res)\n\n  // We are dynamically rendering including dev mode. We want to return the\n  // response to the caller as soon as possible because it might stream over a\n  // very long time.\n  const cacheSetPromise = cloned1\n    .arrayBuffer()\n    .then(async (arrayBuffer) => {\n      const bodyBuffer = Buffer.from(arrayBuffer)\n\n      const fetchedData = {\n        headers: Object.fromEntries(cloned1.headers.entries()),\n        body: bodyBuffer.toString('base64'),\n        status: cloned1.status,\n        url: cloned1.url,\n      }\n\n      serverComponentsHmrCache?.set(cacheKey, fetchedData)\n\n      if (incrementalCacheContext) {\n        await incrementalCache.set(\n          cacheKey,\n          { kind: CachedRouteKind.FETCH, data: fetchedData, revalidate },\n          incrementalCacheContext\n        )\n      }\n    })\n    .catch((error) => console.warn(`Failed to set fetch cache`, input, error))\n    .finally(handleUnlock)\n\n  const pendingRevalidateKey = `cache-set-${cacheKey}`\n  workStore.pendingRevalidates ??= {}\n\n  if (pendingRevalidateKey in workStore.pendingRevalidates) {\n    // there is already a pending revalidate entry that we need to await to\n    // avoid race conditions\n    await workStore.pendingRevalidates[pendingRevalidateKey]\n  }\n\n  workStore.pendingRevalidates[pendingRevalidateKey] = cacheSetPromise.finally(\n    () => {\n      // If the pending revalidate is not present in the store, then we have\n      // nothing to delete.\n      if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n        return\n      }\n\n      delete workStore.pendingRevalidates[pendingRevalidateKey]\n    }\n  )\n\n  return cloned2\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function.\n  const patched = async function fetch(\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ): Promise<Response> {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal = workUnitStore ? getCacheSignal(workUnitStore) : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        const originalFetchRevalidate = getNextField('revalidate')\n        let currentFetchRevalidate = originalFetchRevalidate\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        let revalidateStore: RevalidateStore | undefined\n\n        if (workUnitStore) {\n          switch (workUnitStore.type) {\n            case 'prerender':\n            case 'prerender-runtime':\n            // TODO: Stop accumulating tags in client prerender. (fallthrough)\n            case 'prerender-client':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'cache':\n            case 'private-cache':\n              revalidateStore = workUnitStore\n              break\n            case 'request':\n            case 'unstable-cache':\n              break\n            default:\n              workUnitStore satisfies never\n          }\n        }\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags = workUnitStore?.implicitTags\n\n        let pageFetchCacheMode = workStore.fetchCache\n\n        if (workUnitStore) {\n          switch (workUnitStore.type) {\n            case 'unstable-cache':\n              // Inside unstable-cache we treat it the same as force-no-store on\n              // the page.\n              pageFetchCacheMode = 'force-no-store'\n              break\n            case 'prerender':\n            case 'prerender-client':\n            case 'prerender-runtime':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'request':\n            case 'cache':\n            case 'private-cache':\n              break\n            default:\n              workUnitStore satisfies never\n          }\n        }\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          hasExplicitFetchCacheOptOut ||\n          noFetchConfigAndForceDynamic\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n\n        let autoNoCache = Boolean(\n          (hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore?.revalidate === 0\n        )\n\n        let isImplicitBuildTimeCache = false\n\n        if (!autoNoCache && hasNoExplicitCacheConfig) {\n          // We don't enable automatic no-cache behavior during build-time\n          // prerendering so that we can still leverage the fetch cache between\n          // export workers.\n          if (workStore.isBuildTimePrerendering) {\n            isImplicitBuildTimeCache = true\n          } else {\n            autoNoCache = true\n          }\n        }\n\n        // If we have no cache config, and we're in Dynamic I/O prerendering,\n        // it'll be a dynamic call. We don't have to issue that dynamic call.\n        if (hasNoExplicitCacheConfig && workUnitStore !== undefined) {\n          switch (workUnitStore.type) {\n            case 'prerender':\n            case 'prerender-runtime':\n            // While we don't want to do caching in the client scope we know the\n            // fetch will be dynamic for cacheComponents so we may as well avoid the\n            // call here. (fallthrough)\n            case 'prerender-client':\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                workStore.route,\n                'fetch()'\n              )\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'request':\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n              break\n            default:\n              workUnitStore satisfies never\n          }\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          case 'default-cache':\n          case 'default-no-store':\n          case 'auto':\n          case undefined:\n            // sometimes we won't match the above cases. the reason we don't move\n            // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n            // I suspect this could be unified with fetchCacheMode however in which case we could\n            // simplify the switch case and ensure we have an exhaustive switch handling all modes\n            break\n          default:\n            pageFetchCacheMode satisfies never\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                case 'prerender-runtime':\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                    cacheSignal = null\n                  }\n                  return makeHangingPromise<Response>(\n                    workUnitStore.renderSignal,\n                    workStore.route,\n                    'fetch()'\n                  )\n                case 'prerender-ppr':\n                case 'prerender-legacy':\n                case 'request':\n                case 'cache':\n                case 'private-cache':\n                case 'unstable-cache':\n                  break\n                default:\n                  workUnitStore satisfies never\n              }\n            }\n\n            markCurrentScopeAsDynamic(\n              workStore,\n              workUnitStore,\n              `revalidate: 0 fetch ${input} ${workStore.route}`\n            )\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e.\n          // originalFetchRevalidate.\n          if (revalidateStore && originalFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n        let isHmrRefresh = false\n        let serverComponentsHmrCache: ServerComponentsHmrCache | undefined\n\n        if (workUnitStore) {\n          switch (workUnitStore.type) {\n            case 'request':\n            case 'cache':\n            case 'private-cache':\n              isHmrRefresh = workUnitStore.isHmrRefresh ?? false\n              serverComponentsHmrCache = workUnitStore.serverComponentsHmrCache\n              break\n            case 'prerender':\n            case 'prerender-client':\n            case 'prerender-runtime':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'unstable-cache':\n              break\n            default:\n              workUnitStore satisfies never\n          }\n        }\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate || serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock: () => Promise<void> | void = () => {}\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate || serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                const incrementalCacheConfig:\n                  | SetIncrementalFetchCacheContext\n                  | undefined = isCacheableRevalidate\n                  ? {\n                      fetchCache: true,\n                      fetchUrl,\n                      fetchIdx,\n                      tags,\n                      isImplicitBuildTimeCache,\n                    }\n                  : undefined\n\n                switch (workUnitStore?.type) {\n                  case 'prerender':\n                  case 'prerender-client':\n                  case 'prerender-runtime':\n                    return createCachedPrerenderResponse(\n                      res,\n                      cacheKey,\n                      incrementalCacheConfig,\n                      incrementalCache,\n                      normalizedRevalidate,\n                      handleUnlock\n                    )\n                  case 'prerender-ppr':\n                  case 'prerender-legacy':\n                  case 'request':\n                  case 'cache':\n                  case 'private-cache':\n                  case 'unstable-cache':\n                  case undefined:\n                    return createCachedDynamicResponse(\n                      workStore,\n                      res,\n                      cacheKey,\n                      incrementalCacheConfig,\n                      incrementalCache,\n                      serverComponentsHmrCache,\n                      normalizedRevalidate,\n                      input,\n                      handleUnlock\n                    )\n                  default:\n                    workUnitStore satisfies never\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (isHmrRefresh && serverComponentsHmrCache) {\n            cachedFetchData = serverComponentsHmrCache.get(cacheKey)\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags?.tags,\n                })\n\n            if (hasNoExplicitCacheConfig && workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                case 'prerender-runtime':\n                  // We sometimes use the cache to dedupe fetches that do not\n                  // specify a cache configuration. In these cases we want to\n                  // make sure we still exclude them from prerenders if\n                  // cacheComponents is on so we introduce an artificial task boundary\n                  // here.\n                  await waitAtLeastOneReactRenderTask()\n                  break\n                case 'prerender-ppr':\n                case 'prerender-legacy':\n                case 'request':\n                case 'cache':\n                case 'private-cache':\n                case 'unstable-cache':\n                  break\n                default:\n                  workUnitStore satisfies never\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                case 'prerender-client':\n                case 'prerender-runtime':\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                    cacheSignal = null\n                  }\n                  return makeHangingPromise<Response>(\n                    workUnitStore.renderSignal,\n                    workStore.route,\n                    'fetch()'\n                  )\n                case 'prerender-ppr':\n                case 'prerender-legacy':\n                case 'request':\n                case 'cache':\n                case 'private-cache':\n                case 'unstable-cache':\n                  break\n                default:\n                  workUnitStore satisfies never\n              }\n            }\n            markCurrentScopeAsDynamic(\n              workStore,\n              workUnitStore,\n              `no-store fetch ${input} ${workStore.route}`\n            )\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore) {\n                switch (workUnitStore.type) {\n                  case 'prerender':\n                  case 'prerender-client':\n                  case 'prerender-runtime':\n                    return makeHangingPromise<Response>(\n                      workUnitStore.renderSignal,\n                      workStore.route,\n                      'fetch()'\n                    )\n                  case 'request':\n                  case 'cache':\n                  case 'private-cache':\n                  case 'unstable-cache':\n                  case 'prerender-legacy':\n                  case 'prerender-ppr':\n                    break\n                  default:\n                    workUnitStore satisfies never\n                }\n              }\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with cacheComponents we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with cacheComponents.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  // Assign the function name also as a name property, so that it's preserved\n  // even when mangling is enabled.\n  Object.defineProperty(patched, 'name', { value: 'fetch', writable: false })\n\n  return patched\n}\n\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n"], "names": ["NEXT_PATCH_SYMBOL", "createPatchedFetcher", "patchFetch", "validateRevalidate", "validateTags", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "Symbol", "for", "isFetchPatched", "globalThis", "revalidateVal", "route", "normalizedRevalidate", "undefined", "INFINITE_CACHE", "isNaN", "Error", "err", "message", "includes", "tags", "description", "validTags", "invalidTags", "i", "length", "tag", "push", "reason", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_CACHE_TAG_MAX_ITEMS", "console", "warn", "slice", "join", "log", "trackFetchMetric", "workStore", "ctx", "shouldTrackFetchMetrics", "fetchMetrics", "end", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "idx", "nextFetchId", "createCachedPrerenderResponse", "res", "cache<PERSON>ey", "incrementalCacheContext", "incrementalCache", "revalidate", "handleUnlock", "bodyBuffer", "arrayBuffer", "fetchedData", "headers", "Object", "fromEntries", "entries", "body", "<PERSON><PERSON><PERSON>", "from", "toString", "status", "url", "set", "kind", "CachedRouteKind", "FETCH", "data", "Response", "statusText", "createCachedDynamicResponse", "serverComponentsHmrCache", "input", "cloned1", "cloned2", "cloneResponse", "cacheSetPromise", "then", "catch", "error", "finally", "pendingRevalidateKey", "pendingRevalidates", "originFetch", "workAsyncStorage", "workUnitAsyncStorage", "patched", "fetch", "init", "URL", "Request", "username", "password", "fetchUrl", "href", "method", "toUpperCase", "isInternal", "next", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "getStore", "workUnitStore", "cacheSignal", "getCacheSignal", "beginRead", "result", "getTracer", "trace", "NextNodeServerSpan", "internalFetch", "AppRenderSpan", "SpanKind", "CLIENT", "spanName", "filter", "Boolean", "attributes", "hostname", "port", "getRequestMeta", "isDraftMode", "isRequestInput", "field", "value", "finalRevalidate", "getNextField", "originalFetchRevalidate", "currentFetchRevalidate", "revalidateStore", "type", "Array", "isArray", "collectedTags", "implicitTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "cacheWarning", "isConflictingRevalidate", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "forceDynamic", "_headers", "initHeaders", "get", "Headers", "hasUnCacheableHeader", "isUnCacheableMethod", "toLowerCase", "hasNoExplicitCacheConfig", "autoNoCache", "isImplicitBuildTimeCache", "isBuildTimePrerendering", "endRead", "makeHangingPromise", "renderSignal", "forceStatic", "markCurrentScopeAsDynamic", "isCacheableRevalidate", "isHmrRefresh", "generate<PERSON>ache<PERSON>ey", "fetchIdx", "doOriginalFetch", "isStale", "cacheReasonOverride", "requestInputFields", "reqInput", "reqOptions", "_ogBody", "signal", "otherInput", "clonedInit", "fetchType", "start", "cacheStatus", "CACHE_ONE_YEAR", "incrementalCacheConfig", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "lock", "entry", "isOnDemandRevalidate", "IncrementalCacheKind", "softTags", "waitAtLeastOneReactRenderTask", "isRevalidate", "pendingRevalidate", "response", "defineProperty", "isStaticGeneration", "cache", "hasNextConfig", "revalidatedResult", "pendingResponse", "responses", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "writable", "options", "original", "createDedupeFetch"], "mappings": ";;;;;;;;;;;;;;;;;;IA2CaA,iBAAiB;eAAjBA;;IA+<PERSON><PERSON>,oBAAoB;eAApBA;;IAo6BAC,UAAU;eAAVA;;IA7mCAC,kBAAkB;eAAlBA;;IA8BAC,YAAY;eAAZA;;;2BA1EkC;wBACd;4BAM7B;kCACmC;uCACP;6BAED;8CAK3B;+BAOA;2BACuC;+BAChB;AAG9B,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAU5C,MAAMR,oBAAoBS,OAAOC,GAAG,CAAC;AAE5C,SAASC;IACP,OAAO,AAACC,UAAsC,CAACZ,kBAAkB,KAAK;AACxE;AAEO,SAASG,mBACdU,aAAsB,EACtBC,KAAa;IAEb,IAAI;QACF,IAAIC,uBAA2CC;QAE/C,IAAIH,kBAAkB,OAAO;YAC3BE,uBAAuBE,0BAAc;QACvC,OAAO,IACL,OAAOJ,kBAAkB,YACzB,CAACK,MAAML,kBACPA,gBAAgB,CAAC,GACjB;YACAE,uBAAuBF;QACzB,OAAO,IAAI,OAAOA,kBAAkB,aAAa;YAC/C,MAAM,qBAEL,CAFK,IAAIM,MACR,CAAC,0BAA0B,EAAEN,cAAc,MAAM,EAAEC,MAAM,yCAAyC,CAAC,GAD/F,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAOC;IACT,EAAE,OAAOK,KAAU;QACjB,0EAA0E;QAC1E,IAAIA,eAAeD,SAASC,IAAIC,OAAO,CAACC,QAAQ,CAAC,uBAAuB;YACtE,MAAMF;QACR;QACA,OAAOJ;IACT;AACF;AAEO,SAASZ,aAAamB,IAAW,EAAEC,WAAmB;IAC3D,MAAMC,YAAsB,EAAE;IAC9B,MAAMC,cAGD,EAAE;IAEP,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,KAAKK,MAAM,EAAED,IAAK;QACpC,MAAME,MAAMN,IAAI,CAACI,EAAE;QAEnB,IAAI,OAAOE,QAAQ,UAAU;YAC3BH,YAAYI,IAAI,CAAC;gBAAED;gBAAKE,QAAQ;YAAiC;QACnE,OAAO,IAAIF,IAAID,MAAM,GAAGI,qCAAyB,EAAE;YACjDN,YAAYI,IAAI,CAAC;gBACfD;gBACAE,QAAQ,CAAC,uBAAuB,EAAEC,qCAAyB,EAAE;YAC/D;QACF,OAAO;YACLP,UAAUK,IAAI,CAACD;QACjB;QAEA,IAAIJ,UAAUG,MAAM,GAAGK,oCAAwB,EAAE;YAC/CC,QAAQC,IAAI,CACV,CAAC,oCAAoC,EAAEX,YAAY,eAAe,CAAC,EACnED,KAAKa,KAAK,CAACT,GAAGU,IAAI,CAAC;YAErB;QACF;IACF;IAEA,IAAIX,YAAYE,MAAM,GAAG,GAAG;QAC1BM,QAAQC,IAAI,CAAC,CAAC,gCAAgC,EAAEX,YAAY,EAAE,CAAC;QAE/D,KAAK,MAAM,EAAEK,GAAG,EAAEE,MAAM,EAAE,IAAIL,YAAa;YACzCQ,QAAQI,GAAG,CAAC,CAAC,MAAM,EAAET,IAAI,EAAE,EAAEE,QAAQ;QACvC;IACF;IACA,OAAON;AACT;AAEA,SAASc,iBACPC,SAAoB,EACpBC,GAAqC;IAErC,IAAI,CAACD,UAAUE,uBAAuB,EAAE;QACtC;IACF;IAEAF,UAAUG,YAAY,KAAK,EAAE;IAE7BH,UAAUG,YAAY,CAACb,IAAI,CAAC;QAC1B,GAAGW,GAAG;QACNG,KAAKC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAC7CC,KAAKR,UAAUS,WAAW,IAAI;IAChC;AACF;AAEA,eAAeC,8BACbC,GAAa,EACbC,QAAgB,EAChBC,uBAAoE,EACpEC,gBAAkC,EAClCC,UAAkB,EAClBC,YAAwC;IAExC,kFAAkF;IAClF,sEAAsE;IACtE,aAAa;IACb,MAAMC,aAAa,MAAMN,IAAIO,WAAW;IAExC,MAAMC,cAAc;QAClBC,SAASC,OAAOC,WAAW,CAACX,IAAIS,OAAO,CAACG,OAAO;QAC/CC,MAAMC,OAAOC,IAAI,CAACT,YAAYU,QAAQ,CAAC;QACvCC,QAAQjB,IAAIiB,MAAM;QAClBC,KAAKlB,IAAIkB,GAAG;IACd;IAEA,4EAA4E;IAC5E,QAAQ;IAER,IAAIhB,yBAAyB;QAC3B,MAAMC,iBAAiBgB,GAAG,CACxBlB,UACA;YAAEmB,MAAMC,8BAAe,CAACC,KAAK;YAAEC,MAAMf;YAAaJ;QAAW,GAC7DF;IAEJ;IAEA,MAAMG;IAEN,0CAA0C;IAC1C,OAAO,IAAImB,SAASlB,YAAY;QAC9BG,SAAST,IAAIS,OAAO;QACpBQ,QAAQjB,IAAIiB,MAAM;QAClBQ,YAAYzB,IAAIyB,UAAU;IAC5B;AACF;AAEA,eAAeC,4BACbrC,SAAoB,EACpBW,GAAa,EACbC,QAAgB,EAChBC,uBAAoE,EACpEC,gBAAkC,EAClCwB,wBAA8D,EAC9DvB,UAAkB,EAClBwB,KAAwB,EACxBvB,YAAwC;IAExC,8EAA8E;IAC9E,6EAA6E;IAC7E,iEAAiE;IACjE,MAAM,CAACwB,SAASC,QAAQ,GAAGC,IAAAA,4BAAa,EAAC/B;IAEzC,yEAAyE;IACzE,4EAA4E;IAC5E,kBAAkB;IAClB,MAAMgC,kBAAkBH,QACrBtB,WAAW,GACX0B,IAAI,CAAC,OAAO1B;QACX,MAAMD,aAAaQ,OAAOC,IAAI,CAACR;QAE/B,MAAMC,cAAc;YAClBC,SAASC,OAAOC,WAAW,CAACkB,QAAQpB,OAAO,CAACG,OAAO;YACnDC,MAAMP,WAAWU,QAAQ,CAAC;YAC1BC,QAAQY,QAAQZ,MAAM;YACtBC,KAAKW,QAAQX,GAAG;QAClB;QAEAS,4CAAAA,yBAA0BR,GAAG,CAAClB,UAAUO;QAExC,IAAIN,yBAAyB;YAC3B,MAAMC,iBAAiBgB,GAAG,CACxBlB,UACA;gBAAEmB,MAAMC,8BAAe,CAACC,KAAK;gBAAEC,MAAMf;gBAAaJ;YAAW,GAC7DF;QAEJ;IACF,GACCgC,KAAK,CAAC,CAACC,QAAUpD,QAAQC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE4C,OAAOO,QAClEC,OAAO,CAAC/B;IAEX,MAAMgC,uBAAuB,CAAC,UAAU,EAAEpC,UAAU;IACpDZ,UAAUiD,kBAAkB,KAAK,CAAC;IAElC,IAAID,wBAAwBhD,UAAUiD,kBAAkB,EAAE;QACxD,uEAAuE;QACvE,wBAAwB;QACxB,MAAMjD,UAAUiD,kBAAkB,CAACD,qBAAqB;IAC1D;IAEAhD,UAAUiD,kBAAkB,CAACD,qBAAqB,GAAGL,gBAAgBI,OAAO,CAC1E;YAGO/C;QAFL,sEAAsE;QACtE,qBAAqB;QACrB,IAAI,GAACA,gCAAAA,UAAUiD,kBAAkB,qBAA5BjD,6BAA8B,CAACgD,qBAAqB,GAAE;YACzD;QACF;QAEA,OAAOhD,UAAUiD,kBAAkB,CAACD,qBAAqB;IAC3D;IAGF,OAAOP;AACT;AAOO,SAAShF,qBACdyF,WAAoB,EACpB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAmB;IAE3D,qCAAqC;IACrC,MAAMC,UAAU,eAAeC,MAC7Bf,KAAwB,EACxBgB,IAA6B;YAYdA,cAIKA;QAdpB,IAAI1B;QACJ,IAAI;YACFA,MAAM,IAAI2B,IAAIjB,iBAAiBkB,UAAUlB,MAAMV,GAAG,GAAGU;YACrDV,IAAI6B,QAAQ,GAAG;YACf7B,IAAI8B,QAAQ,GAAG;QACjB,EAAE,OAAM;YACN,kEAAkE;YAClE9B,MAAMrD;QACR;QACA,MAAMoF,WAAW/B,CAAAA,uBAAAA,IAAKgC,IAAI,KAAI;QAC9B,MAAMC,SAASP,CAAAA,yBAAAA,eAAAA,KAAMO,MAAM,qBAAZP,aAAcQ,WAAW,OAAM;QAE9C,yDAAyD;QACzD,oBAAoB;QACpB,MAAMC,aAAa,CAACT,yBAAAA,aAAAA,KAAMU,IAAI,qBAAX,AAACV,WAAoBW,QAAQ,MAAK;QACrD,MAAMC,WAAWrG,QAAQC,GAAG,CAACqG,wBAAwB,KAAK;QAC1D,oDAAoD;QACpD,2EAA2E;QAC3E,mEAAmE;QACnE,6DAA6D;QAC7D,MAAMC,aAAiCL,aACnCxF,YACA6B,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAE5C,MAAMP,YAAYmD,iBAAiBmB,QAAQ;QAC3C,MAAMC,gBAAgBnB,qBAAqBkB,QAAQ;QAEnD,sFAAsF;QACtF,IAAIE,cAAcD,gBAAgBE,IAAAA,4CAAc,EAACF,iBAAiB;QAClE,IAAIC,aAAa;YACfA,YAAYE,SAAS;QACvB;QAEA,MAAMC,SAASC,IAAAA,iBAAS,IAAGC,KAAK,CAC9Bb,aAAac,6BAAkB,CAACC,aAAa,GAAGC,wBAAa,CAAC1B,KAAK,EACnE;YACEa;YACApC,MAAMkD,gBAAQ,CAACC,MAAM;YACrBC,UAAU;gBAAC;gBAASrB;gBAAQF;aAAS,CAACwB,MAAM,CAACC,SAASxF,IAAI,CAAC;YAC3DyF,YAAY;gBACV,YAAY1B;gBACZ,eAAeE;gBACf,eAAe,EAAEjC,uBAAAA,IAAK0D,QAAQ;gBAC9B,iBAAiB1D,CAAAA,uBAAAA,IAAK2D,IAAI,KAAIhH;YAChC;QACF,GACA;gBA6LIiH;YA5LF,wEAAwE;YACxE,IAAIzB,YAAY;gBACd,OAAOd,YAAYX,OAAOgB;YAC5B;YAEA,qDAAqD;YACrD,iEAAiE;YACjE,wBAAwB;YACxB,IAAI,CAACvD,WAAW;gBACd,OAAOkD,YAAYX,OAAOgB;YAC5B;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,IAAIvD,UAAU0F,WAAW,EAAE;gBACzB,OAAOxC,YAAYX,OAAOgB;YAC5B;YAEA,MAAMoC,iBACJpD,SACA,OAAOA,UAAU,YACjB,OAAO,AAACA,MAAkBuB,MAAM,KAAK;YAEvC,MAAM2B,iBAAiB,CAACG;gBACtB,0EAA0E;gBAC1E,MAAMC,QAAStC,wBAAD,AAACA,IAAc,CAACqC,MAAM;gBACpC,OAAOC,SAAUF,CAAAA,iBAAiB,AAACpD,KAAa,CAACqD,MAAM,GAAG,IAAG;YAC/D;YAEA,IAAIE,kBAAsCtH;YAC1C,MAAMuH,eAAe,CAACH;oBACNrC,YACVA,aAEE;gBAHN,OAAO,QAAOA,yBAAAA,aAAAA,KAAMU,IAAI,qBAAVV,UAAY,CAACqC,MAAM,MAAK,cAClCrC,yBAAAA,cAAAA,KAAMU,IAAI,qBAAVV,WAAY,CAACqC,MAAM,GACnBD,kBACE,cAAA,AAACpD,MAAc0B,IAAI,qBAAnB,WAAqB,CAAC2B,MAAM,GAC5BpH;YACR;YACA,0DAA0D;YAC1D,0CAA0C;YAC1C,MAAMwH,0BAA0BD,aAAa;YAC7C,IAAIE,yBAAyBD;YAC7B,MAAMjH,OAAiBnB,aACrBmI,aAAa,WAAW,EAAE,EAC1B,CAAC,MAAM,EAAExD,MAAMZ,QAAQ,IAAI;YAG7B,IAAIuE;YAEJ,IAAI3B,eAAe;gBACjB,OAAQA,cAAc4B,IAAI;oBACxB,KAAK;oBACL,KAAK;oBACL,kEAAkE;oBAClE,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACHD,kBAAkB3B;wBAClB;oBACF,KAAK;oBACL,KAAK;wBACH;oBACF;wBACEA;gBACJ;YACF;YAEA,IAAI2B,iBAAiB;gBACnB,IAAIE,MAAMC,OAAO,CAACtH,OAAO;oBACvB,wDAAwD;oBACxD,MAAMuH,gBACJJ,gBAAgBnH,IAAI,IAAKmH,CAAAA,gBAAgBnH,IAAI,GAAG,EAAE,AAAD;oBACnD,KAAK,MAAMM,OAAON,KAAM;wBACtB,IAAI,CAACuH,cAAcxH,QAAQ,CAACO,MAAM;4BAChCiH,cAAchH,IAAI,CAACD;wBACrB;oBACF;gBACF;YACF;YAEA,MAAMkH,eAAehC,iCAAAA,cAAegC,YAAY;YAEhD,IAAIC,qBAAqBxG,UAAUyG,UAAU;YAE7C,IAAIlC,eAAe;gBACjB,OAAQA,cAAc4B,IAAI;oBACxB,KAAK;wBACH,kEAAkE;wBAClE,YAAY;wBACZK,qBAAqB;wBACrB;oBACF,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH;oBACF;wBACEjC;gBACJ;YACF;YAEA,MAAMmC,iBAAiB,CAAC,CAAC1G,UAAU2G,iBAAiB;YAEpD,IAAIC,0BAA0BnB,eAAe;YAC7C,IAAIoB,cAAc;YAClB,IAAIC;YAEJ,IACE,OAAOF,4BAA4B,YACnC,OAAOX,2BAA2B,aAClC;gBACA,oHAAoH;gBACpH,MAAMc,0BAEJ,AADA,uCAAuC;gBACtCH,4BAA4B,iBAC3BX,2BAA2B,KAC7B,0DAA0D;gBACzDW,4BAA4B,cAC1BX,CAAAA,yBAAyB,KAAKA,2BAA2B,KAAI;gBAElE,IAAIc,yBAAyB;oBAC3BD,eAAe,CAAC,kBAAkB,EAAEF,wBAAwB,mBAAmB,EAAEX,uBAAuB,gCAAgC,CAAC;oBACzIW,0BAA0BpI;oBAC1ByH,yBAAyBzH;gBAC3B;YACF;YAEA,MAAMwI,8BACJ,2CAA2C;YAC3CJ,4BAA4B,cAC5BA,4BAA4B,cAC5B,6FAA6F;YAC7F,gFAAgF;YAChFJ,uBAAuB,oBACvBA,uBAAuB;YAEzB,gFAAgF;YAChF,+EAA+E;YAC/E,sFAAsF;YACtF,wFAAwF;YACxF,wBAAwB;YACxB,MAAMS,+BACJ,CAACT,sBACD,CAACI,2BACD,CAACX,0BACDjG,UAAUkH,YAAY;YAExB,IACE,6FAA6F;YAC7F,gDAAgD;YAChDN,4BAA4B,iBAC5B,OAAOX,2BAA2B,aAClC;gBACAA,yBAAyB;YAC3B,OAAO,IACLe,+BACAC,8BACA;gBACAhB,yBAAyB;YAC3B;YAEA,IACEW,4BAA4B,cAC5BA,4BAA4B,YAC5B;gBACAC,cAAc,CAAC,OAAO,EAAED,yBAAyB;YACnD;YAEAd,kBAAkBnI,mBAChBsI,wBACAjG,UAAU1B,KAAK;YAGjB,MAAM6I,WAAW1B,eAAe;YAChC,MAAM2B,cACJ,QAAOD,4BAAAA,SAAUE,GAAG,MAAK,aACrBF,WACA,IAAIG,QAAQH,YAAY,CAAC;YAE/B,MAAMI,uBACJH,YAAYC,GAAG,CAAC,oBAAoBD,YAAYC,GAAG,CAAC;YAEtD,MAAMG,sBAAsB,CAAC;gBAAC;gBAAO;aAAO,CAAC1I,QAAQ,CACnD2G,EAAAA,kBAAAA,eAAe,8BAAfA,gBAA0BgC,WAAW,OAAM;YAG7C;;;;;;;;;SASC,GACD,MAAMC,2BACJ,kCAAkC;YAClClB,sBAAsBhI,aACtB,kCAAkC;YACjCoI,CAAAA,2BAA2BpI,aAC1B,+EAA+E;YAC/E,yEAAyE;YACzEoI,4BAA4B,SAAQ,KACtC,kCAAkC;YAClCX,0BAA0BzH;YAE5B,IAAImJ,cAActC,QAChB,AAACkC,CAAAA,wBAAwBC,mBAAkB,KACzCtB,CAAAA,mCAAAA,gBAAiBnF,UAAU,MAAK;YAGpC,IAAI6G,2BAA2B;YAE/B,IAAI,CAACD,eAAeD,0BAA0B;gBAC5C,gEAAgE;gBAChE,qEAAqE;gBACrE,kBAAkB;gBAClB,IAAI1H,UAAU6H,uBAAuB,EAAE;oBACrCD,2BAA2B;gBAC7B,OAAO;oBACLD,cAAc;gBAChB;YACF;YAEA,qEAAqE;YACrE,qEAAqE;YACrE,IAAID,4BAA4BnD,kBAAkB/F,WAAW;gBAC3D,OAAQ+F,cAAc4B,IAAI;oBACxB,KAAK;oBACL,KAAK;oBACL,oEAAoE;oBACpE,wEAAwE;oBACxE,2BAA2B;oBAC3B,KAAK;wBACH,IAAI3B,aAAa;4BACfA,YAAYsD,OAAO;4BACnBtD,cAAc;wBAChB;wBAEA,OAAOuD,IAAAA,yCAAkB,EACvBxD,cAAcyD,YAAY,EAC1BhI,UAAU1B,KAAK,EACf;oBAEJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH;oBACF;wBACEiG;gBACJ;YACF;YAEA,OAAQiC;gBACN,KAAK;oBAAkB;wBACrBK,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAiB;wBACpB,IACED,4BAA4B,iBAC3B,OAAOd,oBAAoB,eAAeA,kBAAkB,GAC7D;4BACA,MAAM,qBAEL,CAFK,IAAInH,MACR,CAAC,uCAAuC,EAAEiF,SAAS,gDAAgD,CAAC,GADhG,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACAiD,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAc;wBACjB,IAAID,4BAA4B,YAAY;4BAC1C,MAAM,qBAEL,CAFK,IAAIjI,MACR,CAAC,oCAAoC,EAAEiF,SAAS,6CAA6C,CAAC,GAD1F,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAe;wBAClB,IACE,OAAOqC,2BAA2B,eAClCA,2BAA2B,GAC3B;4BACAY,cAAc;4BACdf,kBAAkBrH,0BAAc;wBAClC;wBACA;oBACF;gBACA,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAKD;oBAKH;gBACF;oBACEgI;YACJ;YAEA,IAAI,OAAOV,oBAAoB,aAAa;gBAC1C,IAAIU,uBAAuB,mBAAmB,CAACE,gBAAgB;oBAC7DZ,kBAAkBrH,0BAAc;oBAChCoI,cAAc;gBAChB,OAAO,IAAIL,uBAAuB,oBAAoB;oBACpDV,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO,IAAIH,gBAAgB;oBACzBZ,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO,IAAIc,aAAa;oBACtB7B,kBAAkB;oBAClBe,cAAc;gBAChB,OAAO;oBACL,mDAAmD;oBACnDA,cAAc;oBACdf,kBAAkBI,kBACdA,gBAAgBnF,UAAU,GAC1BtC,0BAAc;gBACpB;YACF,OAAO,IAAI,CAACoI,aAAa;gBACvBA,cAAc,CAAC,YAAY,EAAEf,iBAAiB;YAChD;YAEA,IACE,qDAAqD;YACrD,yBAAyB;YACzB,CAAE9F,CAAAA,UAAUiI,WAAW,IAAInC,oBAAoB,CAAA,KAC/C,6DAA6D;YAC7D,CAAC6B,eACD,mEAAmE;YACnE,qEAAqE;YACrE,SAAS;YACTzB,mBACAJ,kBAAkBI,gBAAgBnF,UAAU,EAC5C;gBACA,iEAAiE;gBACjE,0BAA0B;gBAC1B,IAAI+E,oBAAoB,GAAG;oBACzB,IAAIvB,eAAe;wBACjB,OAAQA,cAAc4B,IAAI;4BACxB,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,IAAI3B,aAAa;oCACfA,YAAYsD,OAAO;oCACnBtD,cAAc;gCAChB;gCACA,OAAOuD,IAAAA,yCAAkB,EACvBxD,cAAcyD,YAAY,EAC1BhI,UAAU1B,KAAK,EACf;4BAEJ,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH;4BACF;gCACEiG;wBACJ;oBACF;oBAEA2D,IAAAA,2CAAyB,EACvBlI,WACAuE,eACA,CAAC,oBAAoB,EAAEhC,MAAM,CAAC,EAAEvC,UAAU1B,KAAK,EAAE;gBAErD;gBAEA,mEAAmE;gBACnE,8CAA8C;gBAC9C,2BAA2B;gBAC3B,IAAI4H,mBAAmBF,4BAA4BF,iBAAiB;oBAClEI,gBAAgBnF,UAAU,GAAG+E;gBAC/B;YACF;YAEA,MAAMqC,wBACJ,OAAOrC,oBAAoB,YAAYA,kBAAkB;YAE3D,IAAIlF;YACJ,MAAM,EAAEE,gBAAgB,EAAE,GAAGd;YAC7B,IAAIoI,eAAe;YACnB,IAAI9F;YAEJ,IAAIiC,eAAe;gBACjB,OAAQA,cAAc4B,IAAI;oBACxB,KAAK;oBACL,KAAK;oBACL,KAAK;wBACHiC,eAAe7D,cAAc6D,YAAY,IAAI;wBAC7C9F,2BAA2BiC,cAAcjC,wBAAwB;wBACjE;oBACF,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH;oBACF;wBACEiC;gBACJ;YACF;YAEA,IACEzD,oBACCqH,CAAAA,yBAAyB7F,wBAAuB,GACjD;gBACA,IAAI;oBACF1B,WAAW,MAAME,iBAAiBuH,gBAAgB,CAChDzE,UACA+B,iBAAkBpD,QAAwBgB;gBAE9C,EAAE,OAAO3E,KAAK;oBACZc,QAAQoD,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAEP;gBACpD;YACF;YAEA,MAAM+F,WAAWtI,UAAUS,WAAW,IAAI;YAC1CT,UAAUS,WAAW,GAAG6H,WAAW;YAEnC,IAAItH,eAA2C,KAAO;YAEtD,MAAMuH,kBAAkB,OACtBC,SACAC;gBAEA,MAAMC,qBAAqB;oBACzB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,8CAA8C;uBAC1CF,UAAU,EAAE,GAAG;wBAAC;qBAAS;iBAC9B;gBAED,IAAI7C,gBAAgB;oBAClB,MAAMgD,WAAoBpG;oBAC1B,MAAMqG,aAA0B;wBAC9BpH,MAAM,AAACmH,SAAiBE,OAAO,IAAIF,SAASnH,IAAI;oBAClD;oBAEA,KAAK,MAAMoE,SAAS8C,mBAAoB;wBACtC,iCAAiC;wBACjCE,UAAU,CAAChD,MAAM,GAAG+C,QAAQ,CAAC/C,MAAM;oBACrC;oBACArD,QAAQ,IAAIkB,QAAQkF,SAAS9G,GAAG,EAAE+G;gBACpC,OAAO,IAAIrF,MAAM;oBACf,MAAM,EAAEsF,OAAO,EAAErH,IAAI,EAAEsH,MAAM,EAAE,GAAGC,YAAY,GAC5CxF;oBACFA,OAAO;wBACL,GAAGwF,UAAU;wBACbvH,MAAMqH,WAAWrH;wBACjBsH,QAAQN,UAAUhK,YAAYsK;oBAChC;gBACF;gBAEA,oDAAoD;gBACpD,MAAME,aAAa;oBACjB,GAAGzF,IAAI;oBACPU,MAAM;2BAAKV,wBAAAA,KAAMU,IAAI,AAAb;wBAAegF,WAAW;wBAAUX;oBAAS;gBACvD;gBAEA,OAAOpF,YAAYX,OAAOyG,YACvBpG,IAAI,CAAC,OAAOjC;oBACX,IAAI,CAAC6H,WAAWnE,YAAY;wBAC1BtE,iBAAiBC,WAAW;4BAC1BkJ,OAAO7E;4BACPxC,KAAK+B;4BACLiD,aAAa4B,uBAAuB5B;4BACpCsC,aACErD,oBAAoB,KAAK2C,sBACrB,SACA;4BACN3B;4BACAlF,QAAQjB,IAAIiB,MAAM;4BAClBkC,QAAQkF,WAAWlF,MAAM,IAAI;wBAC/B;oBACF;oBACA,IACEnD,IAAIiB,MAAM,KAAK,OACfd,oBACAF,YACCuH,CAAAA,yBAAyB7F,wBAAuB,GACjD;wBACA,MAAM/D,uBACJuH,mBAAmBrH,0BAAc,GAC7B2K,0BAAc,GACdtD;wBAEN,MAAMuD,yBAEUlB,wBACZ;4BACE1B,YAAY;4BACZ7C;4BACA0E;4BACAvJ;4BACA6I;wBACF,IACApJ;wBAEJ,OAAQ+F,iCAAAA,cAAe4B,IAAI;4BACzB,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,OAAOzF,8BACLC,KACAC,UACAyI,wBACAvI,kBACAvC,sBACAyC;4BAEJ,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAKxC;gCACH,OAAO6D,4BACLrC,WACAW,KACAC,UACAyI,wBACAvI,kBACAwB,0BACA/D,sBACAgE,OACAvB;4BAEJ;gCACEuD;wBACJ;oBACF;oBAEA,yEAAyE;oBACzE,4EAA4E;oBAC5E,MAAMvD;oBAEN,OAAOL;gBACT,GACCkC,KAAK,CAAC,CAACC;oBACN9B;oBACA,MAAM8B;gBACR;YACJ;YAEA,IAAI2F;YACJ,IAAIa,yBAAyB;YAC7B,IAAIC,oBAAoB;YAExB,IAAI3I,YAAYE,kBAAkB;gBAChC,IAAI0I;gBAEJ,IAAIpB,gBAAgB9F,0BAA0B;oBAC5CkH,kBAAkBlH,yBAAyB+E,GAAG,CAACzG;oBAC/C2I,oBAAoB;gBACtB;gBAEA,IAAIpB,yBAAyB,CAACqB,iBAAiB;oBAC7CxI,eAAe,MAAMF,iBAAiB2I,IAAI,CAAC7I;oBAC3C,MAAM8I,QAAQ1J,UAAU2J,oBAAoB,GACxC,OACA,MAAM7I,iBAAiBuG,GAAG,CAACzG,UAAU;wBACnCmB,MAAM6H,mCAAoB,CAAC3H,KAAK;wBAChClB,YAAY+E;wBACZlC;wBACA0E;wBACAvJ;wBACA8K,QAAQ,EAAEtD,gCAAAA,aAAcxH,IAAI;oBAC9B;oBAEJ,IAAI2I,4BAA4BnD,eAAe;wBAC7C,OAAQA,cAAc4B,IAAI;4BACxB,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,2DAA2D;gCAC3D,2DAA2D;gCAC3D,qDAAqD;gCACrD,oEAAoE;gCACpE,QAAQ;gCACR,MAAM2D,IAAAA,wCAA6B;gCACnC;4BACF,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH;4BACF;gCACEvF;wBACJ;oBACF;oBAEA,IAAImF,OAAO;wBACT,MAAM1I;oBACR,OAAO;wBACL,4HAA4H;wBAC5HyH,sBAAsB;oBACxB;oBAEA,IAAIiB,CAAAA,yBAAAA,MAAO7D,KAAK,KAAI6D,MAAM7D,KAAK,CAAC9D,IAAI,KAAKC,8BAAe,CAACC,KAAK,EAAE;wBAC9D,wDAAwD;wBACxD,gDAAgD;wBAChD,IAAIjC,UAAU+J,YAAY,IAAIL,MAAMlB,OAAO,EAAE;4BAC3Cc,yBAAyB;wBAC3B,OAAO;4BACL,IAAII,MAAMlB,OAAO,EAAE;gCACjBxI,UAAUiD,kBAAkB,KAAK,CAAC;gCAClC,IAAI,CAACjD,UAAUiD,kBAAkB,CAACrC,SAAS,EAAE;oCAC3C,MAAMoJ,oBAAoBzB,gBAAgB,MACvC3F,IAAI,CAAC,OAAOqH,WAAc,CAAA;4CACzBzI,MAAM,MAAMyI,SAAS/I,WAAW;4CAChCE,SAAS6I,SAAS7I,OAAO;4CACzBQ,QAAQqI,SAASrI,MAAM;4CACvBQ,YAAY6H,SAAS7H,UAAU;wCACjC,CAAA,GACCW,OAAO,CAAC;wCACP/C,UAAUiD,kBAAkB,KAAK,CAAC;wCAClC,OAAOjD,UAAUiD,kBAAkB,CAACrC,YAAY,GAAG;oCACrD;oCAEF,2DAA2D;oCAC3D,8BAA8B;oCAC9BoJ,kBAAkBnH,KAAK,CAACnD,QAAQoD,KAAK;oCAErC9C,UAAUiD,kBAAkB,CAACrC,SAAS,GAAGoJ;gCAC3C;4BACF;4BAEAR,kBAAkBE,MAAM7D,KAAK,CAAC3D,IAAI;wBACpC;oBACF;gBACF;gBAEA,IAAIsH,iBAAiB;oBACnB,IAAInF,YAAY;wBACdtE,iBAAiBC,WAAW;4BAC1BkJ,OAAO7E;4BACPxC,KAAK+B;4BACLiD;4BACAsC,aAAaI,oBAAoB,QAAQ;4BACzCzC;4BACAlF,QAAQ4H,gBAAgB5H,MAAM,IAAI;4BAClCkC,QAAQP,CAAAA,wBAAAA,KAAMO,MAAM,KAAI;wBAC1B;oBACF;oBAEA,MAAMmG,WAAW,IAAI9H,SACnBV,OAAOC,IAAI,CAAC8H,gBAAgBhI,IAAI,EAAE,WAClC;wBACEJ,SAASoI,gBAAgBpI,OAAO;wBAChCQ,QAAQ4H,gBAAgB5H,MAAM;oBAChC;oBAGFP,OAAO6I,cAAc,CAACD,UAAU,OAAO;wBACrCpE,OAAO2D,gBAAgB3H,GAAG;oBAC5B;oBAEA,OAAOoI;gBACT;YACF;YAEA,IAAIjK,UAAUmK,kBAAkB,IAAI5G,QAAQ,OAAOA,SAAS,UAAU;gBACpE,MAAM,EAAE6G,KAAK,EAAE,GAAG7G;gBAElB,oEAAoE;gBACpE,IAAI1F,eAAe,OAAO0F,KAAK6G,KAAK;gBAEpC,IAAIA,UAAU,YAAY;oBACxB,uDAAuD;oBACvD,IAAI7F,eAAe;wBACjB,OAAQA,cAAc4B,IAAI;4BACxB,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH,IAAI3B,aAAa;oCACfA,YAAYsD,OAAO;oCACnBtD,cAAc;gCAChB;gCACA,OAAOuD,IAAAA,yCAAkB,EACvBxD,cAAcyD,YAAY,EAC1BhI,UAAU1B,KAAK,EACf;4BAEJ,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH;4BACF;gCACEiG;wBACJ;oBACF;oBACA2D,IAAAA,2CAAyB,EACvBlI,WACAuE,eACA,CAAC,eAAe,EAAEhC,MAAM,CAAC,EAAEvC,UAAU1B,KAAK,EAAE;gBAEhD;gBAEA,MAAM+L,gBAAgB,UAAU9G;gBAChC,MAAM,EAAEU,OAAO,CAAC,CAAC,EAAE,GAAGV;gBACtB,IACE,OAAOU,KAAKlD,UAAU,KAAK,YAC3BmF,mBACAjC,KAAKlD,UAAU,GAAGmF,gBAAgBnF,UAAU,EAC5C;oBACA,IAAIkD,KAAKlD,UAAU,KAAK,GAAG;wBACzB,uDAAuD;wBACvD,IAAIwD,eAAe;4BACjB,OAAQA,cAAc4B,IAAI;gCACxB,KAAK;gCACL,KAAK;gCACL,KAAK;oCACH,OAAO4B,IAAAA,yCAAkB,EACvBxD,cAAcyD,YAAY,EAC1BhI,UAAU1B,KAAK,EACf;gCAEJ,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;oCACH;gCACF;oCACEiG;4BACJ;wBACF;wBACA2D,IAAAA,2CAAyB,EACvBlI,WACAuE,eACA,CAAC,oBAAoB,EAAEhC,MAAM,CAAC,EAAEvC,UAAU1B,KAAK,EAAE;oBAErD;oBAEA,IAAI,CAAC0B,UAAUiI,WAAW,IAAIhE,KAAKlD,UAAU,KAAK,GAAG;wBACnDmF,gBAAgBnF,UAAU,GAAGkD,KAAKlD,UAAU;oBAC9C;gBACF;gBACA,IAAIsJ,eAAe,OAAO9G,KAAKU,IAAI;YACrC;YAEA,kEAAkE;YAClE,6DAA6D;YAC7D,wCAAwC;YACxC,IAAIrD,YAAY0I,wBAAwB;gBACtC,MAAMtG,uBAAuBpC;gBAC7BZ,UAAUiD,kBAAkB,KAAK,CAAC;gBAClC,IAAI+G,oBACFhK,UAAUiD,kBAAkB,CAACD,qBAAqB;gBAEpD,IAAIgH,mBAAmB;oBACrB,MAAMM,oBAKF,MAAMN;oBACV,OAAO,IAAI7H,SAASmI,kBAAkB9I,IAAI,EAAE;wBAC1CJ,SAASkJ,kBAAkBlJ,OAAO;wBAClCQ,QAAQ0I,kBAAkB1I,MAAM;wBAChCQ,YAAYkI,kBAAkBlI,UAAU;oBAC1C;gBACF;gBAEA,gEAAgE;gBAChE,4EAA4E;gBAC5E,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnE,iEAAiE;gBACjE,6CAA6C;gBAC7C,MAAMmI,kBAAkBhC,gBAAgB,MAAME,oBAC5C,8DAA8D;gBAC9D,8DAA8D;gBAC9D,mDAAmD;gBACnD,+CAA+C;iBAC9C7F,IAAI,CAACF,4BAAa;gBAErBsH,oBAAoBO,gBACjB3H,IAAI,CAAC,OAAO4H;oBACX,MAAMP,WAAWO,SAAS,CAAC,EAAE;oBAC7B,OAAO;wBACLhJ,MAAM,MAAMyI,SAAS/I,WAAW;wBAChCE,SAAS6I,SAAS7I,OAAO;wBACzBQ,QAAQqI,SAASrI,MAAM;wBACvBQ,YAAY6H,SAAS7H,UAAU;oBACjC;gBACF,GACCW,OAAO,CAAC;wBAGF/C;oBAFL,8DAA8D;oBAC9D,6BAA6B;oBAC7B,IAAI,GAACA,gCAAAA,UAAUiD,kBAAkB,qBAA5BjD,6BAA8B,CAACgD,qBAAqB,GAAE;wBACzD;oBACF;oBAEA,OAAOhD,UAAUiD,kBAAkB,CAACD,qBAAqB;gBAC3D;gBAEF,mEAAmE;gBACnE,qBAAqB;gBACrBgH,kBAAkBnH,KAAK,CAAC,KAAO;gBAE/B7C,UAAUiD,kBAAkB,CAACD,qBAAqB,GAAGgH;gBAErD,OAAOO,gBAAgB3H,IAAI,CAAC,CAAC4H,YAAcA,SAAS,CAAC,EAAE;YACzD,OAAO;gBACL,OAAOjC,gBAAgB,OAAOE;YAChC;QACF;QAGF,IAAIjE,aAAa;YACf,IAAI;gBACF,OAAO,MAAMG;YACf,SAAU;gBACR,IAAIH,aAAa;oBACfA,YAAYsD,OAAO;gBACrB;YACF;QACF;QACA,OAAOnD;IACT;IAEA,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,WAAW;IACXtB,QAAQoH,aAAa,GAAG;IACxBpH,QAAQqH,oBAAoB,GAAG,IAAMvH;IACrCE,QAAQsH,kBAAkB,GAAGzH;IAC3B9E,UAAsC,CAACZ,kBAAkB,GAAG;IAE9D,2EAA2E;IAC3E,iCAAiC;IACjC6D,OAAO6I,cAAc,CAAC7G,SAAS,QAAQ;QAAEwC,OAAO;QAAS+E,UAAU;IAAM;IAEzE,OAAOvH;AACT;AAIO,SAAS3F,WAAWmN,OAAwB;IACjD,gEAAgE;IAChE,IAAI1M,kBAAkB;IAEtB,0EAA0E;IAC1E,8BAA8B;IAC9B,MAAM2M,WAAWC,IAAAA,8BAAiB,EAAC3M,WAAWkF,KAAK;IAEnD,6CAA6C;IAC7ClF,WAAWkF,KAAK,GAAG7F,qBAAqBqN,UAAUD;AACpD", "ignoreList": [0]}