{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "sourcesContent": ["import type { ClientReferenceManifest } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { BinaryStreamOf } from './app-render'\n\nimport { htmlEscapeJsonString } from '../htmlescape'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst INLINE_FLIGHT_PAYLOAD_BOOTSTRAP = 0\nconst INLINE_FLIGHT_PAYLOAD_DATA = 1\nconst INLINE_FLIGHT_PAYLOAD_FORM_STATE = 2\nconst INLINE_FLIGHT_PAYLOAD_BINARY = 3\n\nconst flightResponses = new WeakMap<BinaryStreamOf<any>, Promise<any>>()\nconst encoder = new TextEncoder()\n\nconst findSourceMapURL =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .findSourceMapURLDEV\n    : undefined\n\n/**\n * Render Flight stream.\n * This is only used for renderToHTML, the Flight response does not need additional wrappers.\n */\nexport function useFlightStream<T>(\n  flightStream: BinaryStreamOf<T>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>,\n  nonce?: string\n): Promise<T> {\n  const response = flightResponses.get(flightStream)\n\n  if (response) {\n    return response\n  }\n\n  // react-server-dom-webpack/client.edge must not be hoisted for require cache clearing to work correctly\n  const { createFromReadableStream } =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client') as typeof import('react-server-dom-webpack/client')\n\n  const newResponse = createFromReadableStream<T>(flightStream, {\n    findSourceMapURL,\n    serverConsumerManifest: {\n      moduleLoading: clientReferenceManifest.moduleLoading,\n      moduleMap: isEdgeRuntime\n        ? clientReferenceManifest.edgeSSRModuleMapping\n        : clientReferenceManifest.ssrModuleMapping,\n      serverModuleMap: null,\n    },\n    nonce,\n  })\n\n  // Edge pages are never prerendered so they necessarily cannot have a workUnitStore type\n  // that requires the nextTick behavior. This is why it is safe to access a node only API here\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (!workUnitStore) {\n      throw new InvariantError('Expected workUnitAsyncStorage to have a store.')\n    }\n\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n        const responseOnNextTick = new Promise<T>((resolve) => {\n          process.nextTick(() => resolve(newResponse))\n        })\n        flightResponses.set(flightStream, responseOnNextTick)\n        return responseOnNextTick\n      case 'prerender':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  flightResponses.set(flightStream, newResponse)\n\n  return newResponse\n}\n\n/**\n * Creates a ReadableStream provides inline script tag chunks for writing hydration\n * data to the client outside the React render itself.\n *\n * @param flightStream The RSC render stream\n * @param nonce optionally a nonce used during this particular render\n * @param formState optionally the formState used with this particular render\n * @returns a ReadableStream without the complete property. This signifies a lazy ReadableStream\n */\nexport function createInlinedDataReadableStream(\n  flightStream: ReadableStream<Uint8Array>,\n  nonce: string | undefined,\n  formState: unknown | null\n): ReadableStream<Uint8Array> {\n  const startScriptTag = nonce\n    ? `<script nonce=${JSON.stringify(nonce)}>`\n    : '<script>'\n\n  const flightReader = flightStream.getReader()\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n\n  const readable = new ReadableStream({\n    type: 'bytes',\n    start(controller) {\n      try {\n        writeInitialInstructions(controller, startScriptTag, formState)\n      } catch (error) {\n        // during encoding or enqueueing forward the error downstream\n        controller.error(error)\n      }\n    },\n    async pull(controller) {\n      try {\n        const { done, value } = await flightReader.read()\n\n        if (value) {\n          try {\n            const decodedString = decoder.decode(value, { stream: !done })\n\n            // The chunk cannot be decoded as valid UTF-8 string as it might\n            // have arbitrary binary data.\n            writeFlightDataInstruction(\n              controller,\n              startScriptTag,\n              decodedString\n            )\n          } catch {\n            // The chunk cannot be decoded as valid UTF-8 string.\n            writeFlightDataInstruction(controller, startScriptTag, value)\n          }\n        }\n\n        if (done) {\n          controller.close()\n        }\n      } catch (error) {\n        // There was a problem in the upstream reader or during decoding or enqueuing\n        // forward the error downstream\n        controller.error(error)\n      }\n    },\n  })\n\n  return readable\n}\n\nfunction writeInitialInstructions(\n  controller: ReadableStreamDefaultController,\n  scriptStart: string,\n  formState: unknown | null\n) {\n  if (formState != null) {\n    controller.enqueue(\n      encoder.encode(\n        `${scriptStart}(self.__next_f=self.__next_f||[]).push(${htmlEscapeJsonString(\n          JSON.stringify([INLINE_FLIGHT_PAYLOAD_BOOTSTRAP])\n        )});self.__next_f.push(${htmlEscapeJsonString(\n          JSON.stringify([INLINE_FLIGHT_PAYLOAD_FORM_STATE, formState])\n        )})</script>`\n      )\n    )\n  } else {\n    controller.enqueue(\n      encoder.encode(\n        `${scriptStart}(self.__next_f=self.__next_f||[]).push(${htmlEscapeJsonString(\n          JSON.stringify([INLINE_FLIGHT_PAYLOAD_BOOTSTRAP])\n        )})</script>`\n      )\n    )\n  }\n}\n\nfunction writeFlightDataInstruction(\n  controller: ReadableStreamDefaultController,\n  scriptStart: string,\n  chunk: string | Uint8Array\n) {\n  let htmlInlinedData: string\n\n  if (typeof chunk === 'string') {\n    htmlInlinedData = htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_DATA, chunk])\n    )\n  } else {\n    // The chunk cannot be embedded as a UTF-8 string in the script tag.\n    // Instead let's inline it in base64.\n    // Credits to Devon Govett (devongovett) for the technique.\n    // https://github.com/devongovett/rsc-html-stream\n    const base64 = btoa(String.fromCodePoint(...chunk))\n    htmlInlinedData = htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_BINARY, base64])\n    )\n  }\n\n  controller.enqueue(\n    encoder.encode(\n      `${scriptStart}self.__next_f.push(${htmlInlinedData})</script>`\n    )\n  )\n}\n"], "names": ["htmlEscapeJsonString", "workUnitAsyncStorage", "InvariantError", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "INLINE_FLIGHT_PAYLOAD_BINARY", "flightResponses", "WeakMap", "encoder", "TextEncoder", "findSourceMapURL", "NODE_ENV", "require", "findSourceMapURLDEV", "undefined", "useFlightStream", "flightStream", "clientReferenceManifest", "nonce", "response", "get", "createFromReadableStream", "newResponse", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "serverModuleMap", "workUnitStore", "getStore", "type", "responseOnNextTick", "Promise", "resolve", "nextTick", "set", "createInlinedDataReadableStream", "formState", "startScriptTag", "JSON", "stringify", "flightReader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "fatal", "readable", "ReadableStream", "start", "controller", "writeInitialInstructions", "error", "pull", "done", "value", "read", "decodedString", "decode", "stream", "writeFlightDataInstruction", "close", "scriptStart", "enqueue", "encode", "chunk", "htmlInlinedData", "base64", "btoa", "String", "fromCodePoint"], "mappings": "AAGA,SAASA,oBAAoB,QAAQ,gBAAe;AAEpD,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AACzC,MAAMC,+BAA+B;AAErC,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAEpB,MAAMC,mBACJX,QAAQC,GAAG,CAACW,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAEN;;;CAGC,GACD,OAAO,SAASC,gBACdC,YAA+B,EAC/BC,uBAA8D,EAC9DC,KAAc;IAEd,MAAMC,WAAWb,gBAAgBc,GAAG,CAACJ;IAErC,IAAIG,UAAU;QACZ,OAAOA;IACT;IAEA,wGAAwG;IACxG,MAAM,EAAEE,wBAAwB,EAAE,GAChC,6DAA6D;IAC7DT,QAAQ;IAEV,MAAMU,cAAcD,yBAA4BL,cAAc;QAC5DN;QACAa,wBAAwB;YACtBC,eAAeP,wBAAwBO,aAAa;YACpDC,WAAW3B,gBACPmB,wBAAwBS,oBAAoB,GAC5CT,wBAAwBU,gBAAgB;YAC5CC,iBAAiB;QACnB;QACAV;IACF;IAEA,wFAAwF;IACxF,6FAA6F;IAC7F,IAAInB,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM4B,gBAAgBjC,qBAAqBkC,QAAQ;QAEnD,IAAI,CAACD,eAAe;YAClB,MAAM,qBAAoE,CAApE,IAAIhC,eAAe,mDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAmE;QAC3E;QAEA,OAAQgC,cAAcE,IAAI;YACxB,KAAK;gBACH,MAAMC,qBAAqB,IAAIC,QAAW,CAACC;oBACzCnC,QAAQoC,QAAQ,CAAC,IAAMD,QAAQZ;gBACjC;gBACAhB,gBAAgB8B,GAAG,CAACpB,cAAcgB;gBAClC,OAAOA;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEH;QACJ;IACF;IAEAvB,gBAAgB8B,GAAG,CAACpB,cAAcM;IAElC,OAAOA;AACT;AAEA;;;;;;;;CAQC,GACD,OAAO,SAASe,gCACdrB,YAAwC,EACxCE,KAAyB,EACzBoB,SAAyB;IAEzB,MAAMC,iBAAiBrB,QACnB,CAAC,cAAc,EAAEsB,KAAKC,SAAS,CAACvB,OAAO,CAAC,CAAC,GACzC;IAEJ,MAAMwB,eAAe1B,aAAa2B,SAAS;IAC3C,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IAEvD,MAAMC,WAAW,IAAIC,eAAe;QAClCjB,MAAM;QACNkB,OAAMC,UAAU;YACd,IAAI;gBACFC,yBAAyBD,YAAYX,gBAAgBD;YACvD,EAAE,OAAOc,OAAO;gBACd,6DAA6D;gBAC7DF,WAAWE,KAAK,CAACA;YACnB;QACF;QACA,MAAMC,MAAKH,UAAU;YACnB,IAAI;gBACF,MAAM,EAAEI,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMb,aAAac,IAAI;gBAE/C,IAAID,OAAO;oBACT,IAAI;wBACF,MAAME,gBAAgBb,QAAQc,MAAM,CAACH,OAAO;4BAAEI,QAAQ,CAACL;wBAAK;wBAE5D,gEAAgE;wBAChE,8BAA8B;wBAC9BM,2BACEV,YACAX,gBACAkB;oBAEJ,EAAE,OAAM;wBACN,qDAAqD;wBACrDG,2BAA2BV,YAAYX,gBAAgBgB;oBACzD;gBACF;gBAEA,IAAID,MAAM;oBACRJ,WAAWW,KAAK;gBAClB;YACF,EAAE,OAAOT,OAAO;gBACd,6EAA6E;gBAC7E,+BAA+B;gBAC/BF,WAAWE,KAAK,CAACA;YACnB;QACF;IACF;IAEA,OAAOL;AACT;AAEA,SAASI,yBACPD,UAA2C,EAC3CY,WAAmB,EACnBxB,SAAyB;IAEzB,IAAIA,aAAa,MAAM;QACrBY,WAAWa,OAAO,CAChBvD,QAAQwD,MAAM,CACZ,GAAGF,YAAY,uCAAuC,EAAEnE,qBACtD6C,KAAKC,SAAS,CAAC;YAACvC;SAAgC,GAChD,qBAAqB,EAAEP,qBACvB6C,KAAKC,SAAS,CAAC;YAACrC;YAAkCkC;SAAU,GAC5D,UAAU,CAAC;IAGnB,OAAO;QACLY,WAAWa,OAAO,CAChBvD,QAAQwD,MAAM,CACZ,GAAGF,YAAY,uCAAuC,EAAEnE,qBACtD6C,KAAKC,SAAS,CAAC;YAACvC;SAAgC,GAChD,UAAU,CAAC;IAGnB;AACF;AAEA,SAAS0D,2BACPV,UAA2C,EAC3CY,WAAmB,EACnBG,KAA0B;IAE1B,IAAIC;IAEJ,IAAI,OAAOD,UAAU,UAAU;QAC7BC,kBAAkBvE,qBAChB6C,KAAKC,SAAS,CAAC;YAACtC;YAA4B8D;SAAM;IAEtD,OAAO;QACL,oEAAoE;QACpE,qCAAqC;QACrC,2DAA2D;QAC3D,iDAAiD;QACjD,MAAME,SAASC,KAAKC,OAAOC,aAAa,IAAIL;QAC5CC,kBAAkBvE,qBAChB6C,KAAKC,SAAS,CAAC;YAACpC;YAA8B8D;SAAO;IAEzD;IAEAjB,WAAWa,OAAO,CAChBvD,QAAQwD,MAAM,CACZ,GAAGF,YAAY,mBAAmB,EAAEI,gBAAgB,UAAU,CAAC;AAGrE", "ignoreList": [0]}