{"version": 3, "sources": ["../../../src/build/swc/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-use-before-define */\nimport path from 'path'\nimport { pathToFileURL } from 'url'\nimport { arch, platform } from 'os'\nimport { platformArchTriples } from 'next/dist/compiled/@napi-rs/triples'\nimport * as Log from '../output/log'\nimport { getParserOptions } from './options'\nimport { eventSwcLoadFailure } from '../../telemetry/events/swc-load-failure'\nimport { patchIncorrectLockfile } from '../../lib/patch-incorrect-lockfile'\nimport { downloadNativeNextSwc, downloadWasmSwc } from '../../lib/download-swc'\nimport type {\n  NextConfigComplete,\n  ReactCompilerOptions,\n  TurbopackLoaderItem,\n  TurbopackRuleConfigItem,\n  TurbopackRuleConfigItemOptions,\n  TurbopackRuleConfigItemOrShortcut,\n} from '../../server/config-shared'\nimport { isDeepStrictEqual } from 'util'\nimport { type DefineEnvOptions, getDefineEnv } from '../define-env'\nimport { getReactCompilerLoader } from '../get-babel-loader-config'\nimport type {\n  NapiPartialProjectOptions,\n  NapiProjectOptions,\n  NapiSourceDiagnostic,\n} from './generated-native'\nimport type {\n  Binding,\n  CompilationEvent,\n  DefineEnv,\n  Endpoint,\n  HmrIdentifiers,\n  Project,\n  ProjectOptions,\n  RawEntrypoints,\n  Route,\n  TurboEngineOptions,\n  TurbopackResult,\n  TurbopackStackFrame,\n  Update,\n  UpdateMessage,\n  WrittenEndpoint,\n} from './types'\nimport { throwTurbopackInternalError } from '../../shared/lib/turbopack/internal-error'\n\ntype RawBindings = typeof import('./generated-native')\ntype RawWasmBindings = typeof import('./generated-wasm') & {\n  default?(): Promise<typeof import('./generated-wasm')>\n}\n\nconst nextVersion = process.env.__NEXT_VERSION as string\n\nconst ArchName = arch()\nconst PlatformName = platform()\n\nfunction infoLog(...args: any[]) {\n  if (process.env.NEXT_PRIVATE_BUILD_WORKER) {\n    return\n  }\n  if (process.env.DEBUG) {\n    Log.info(...args)\n  }\n}\n\n/**\n * Based on napi-rs's target triples, returns triples that have corresponding next-swc binaries.\n */\nexport function getSupportedArchTriples(): Record<string, any> {\n  const { darwin, win32, linux, freebsd, android } = platformArchTriples\n\n  return {\n    darwin,\n    win32: {\n      arm64: win32.arm64,\n      ia32: win32.ia32.filter((triple) => triple.abi === 'msvc'),\n      x64: win32.x64.filter((triple) => triple.abi === 'msvc'),\n    },\n    linux: {\n      // linux[x64] includes `gnux32` abi, with x64 arch.\n      x64: linux.x64.filter((triple) => triple.abi !== 'gnux32'),\n      arm64: linux.arm64,\n      // This target is being deprecated, however we keep it in `knownDefaultWasmFallbackTriples` for now\n      arm: linux.arm,\n    },\n    // Below targets are being deprecated, however we keep it in `knownDefaultWasmFallbackTriples` for now\n    freebsd: {\n      x64: freebsd.x64,\n    },\n    android: {\n      arm64: android.arm64,\n      arm: android.arm,\n    },\n  }\n}\n\nconst triples = (() => {\n  const supportedArchTriples = getSupportedArchTriples()\n  const targetTriple = supportedArchTriples[PlatformName]?.[ArchName]\n\n  // If we have supported triple, return it right away\n  if (targetTriple) {\n    return targetTriple\n  }\n\n  // If there isn't corresponding target triple in `supportedArchTriples`, check if it's excluded from original raw triples\n  // Otherwise, it is completely unsupported platforms.\n  let rawTargetTriple = platformArchTriples[PlatformName]?.[ArchName]\n\n  if (rawTargetTriple) {\n    Log.warn(\n      `Trying to load next-swc for target triple ${rawTargetTriple}, but there next-swc does not have native bindings support`\n    )\n  } else {\n    Log.warn(\n      `Trying to load next-swc for unsupported platforms ${PlatformName}/${ArchName}`\n    )\n  }\n\n  return []\n})()\n\n// Allow to specify an absolute path to the custom turbopack binary to load.\n// If one of env variables is set, `loadNative` will try to use specified\n// binary instead. This is thin, naive interface\n// - `loadBindings` will not validate neither path nor the binary.\n//\n// Note these are internal flag: there's no stability, feature guarantee.\nconst __INTERNAL_CUSTOM_TURBOPACK_BINDINGS =\n  process.env.__INTERNAL_CUSTOM_TURBOPACK_BINDINGS\n\nfunction checkVersionMismatch(pkgData: any) {\n  const version = pkgData.version\n\n  if (version && version !== nextVersion) {\n    Log.warn(\n      `Mismatching @next/swc version, detected: ${version} while Next.js is on ${nextVersion}. Please ensure these match`\n    )\n  }\n}\n\n// These are the platforms we'll try to load wasm bindings first,\n// only try to load native bindings if loading wasm binding somehow fails.\n// Fallback to native binding is for migration period only,\n// once we can verify loading-wasm-first won't cause visible regressions,\n// we'll not include native bindings for these platform at all.\nconst knownDefaultWasmFallbackTriples = [\n  'x86_64-unknown-freebsd',\n  'aarch64-linux-android',\n  'arm-linux-androideabi',\n  'armv7-unknown-linux-gnueabihf',\n  'i686-pc-windows-msvc',\n  // WOA targets are TBD, while current userbase is small we may support it in the future\n  //'aarch64-pc-windows-msvc',\n]\n\n// The last attempt's error code returned when cjs require to native bindings fails.\n// If node.js throws an error without error code, this should be `unknown` instead of undefined.\n// For the wasm-first targets (`knownDefaultWasmFallbackTriples`) this will be `unsupported_target`.\nlet lastNativeBindingsLoadErrorCode:\n  | 'unknown'\n  | 'unsupported_target'\n  | string\n  | undefined = undefined\n// Used to cache calls to `loadBindings`\nlet pendingBindings: Promise<Binding>\n// some things call `loadNative` directly instead of `loadBindings`... Cache calls to that\n// separately.\nlet nativeBindings: Binding\n// can allow hacky sync access to bindings for loadBindingsSync\nlet wasmBindings: Binding\nlet downloadWasmPromise: any\nlet swcTraceFlushGuard: any\nlet downloadNativeBindingsPromise: Promise<void> | undefined = undefined\n\nexport const lockfilePatchPromise: { cur?: Promise<void> } = {}\n\n/**\n * Attempts to load a native or wasm binding.\n *\n * By default, this first tries to use a native binding, falling back to a wasm binding if that\n * fails.\n *\n * This function is `async` as wasm requires an asynchronous import in browsers.\n */\nexport async function loadBindings(\n  useWasmBinary: boolean = false\n): Promise<Binding> {\n  // Increase Rust stack size as some npm packages being compiled need more than the default.\n  if (!process.env.RUST_MIN_STACK) {\n    process.env.RUST_MIN_STACK = '8388608'\n  }\n\n  if (pendingBindings) {\n    return pendingBindings\n  }\n\n  if (process.env.NEXT_TEST_WASM) {\n    useWasmBinary = true\n  }\n\n  // rust needs stdout to be blocking, otherwise it will throw an error (on macOS at least) when writing a lot of data (logs) to it\n  // see https://github.com/napi-rs/napi-rs/issues/1630\n  // and https://github.com/nodejs/node/blob/main/doc/api/process.md#a-note-on-process-io\n  if (process.stdout._handle != null) {\n    // @ts-ignore\n    process.stdout._handle.setBlocking?.(true)\n  }\n  if (process.stderr._handle != null) {\n    // @ts-ignore\n    process.stderr._handle.setBlocking?.(true)\n  }\n\n  pendingBindings = new Promise(async (resolve, _reject) => {\n    if (!lockfilePatchPromise.cur) {\n      // always run lockfile check once so that it gets patched\n      // even if it doesn't fail to load locally\n      lockfilePatchPromise.cur = patchIncorrectLockfile(process.cwd()).catch(\n        console.error\n      )\n    }\n\n    let attempts: any[] = []\n    const disableWasmFallback = process.env.NEXT_DISABLE_SWC_WASM\n    const unsupportedPlatform = triples.some(\n      (triple: any) =>\n        !!triple?.raw && knownDefaultWasmFallbackTriples.includes(triple.raw)\n    )\n    const isWebContainer = process.versions.webcontainer\n    // Normal execution relies on the param `useWasmBinary` flag to load, but\n    // in certain cases where there isn't a native binary we always load wasm fallback first.\n    const shouldLoadWasmFallbackFirst =\n      (!disableWasmFallback && useWasmBinary) ||\n      unsupportedPlatform ||\n      isWebContainer\n\n    if (!unsupportedPlatform && useWasmBinary) {\n      Log.warn(\n        `experimental.useWasmBinary is not an option for supported platform ${PlatformName}/${ArchName} and will be ignored.`\n      )\n    }\n\n    if (shouldLoadWasmFallbackFirst) {\n      lastNativeBindingsLoadErrorCode = 'unsupported_target'\n      const fallbackBindings = await tryLoadWasmWithFallback(attempts)\n      if (fallbackBindings) {\n        return resolve(fallbackBindings)\n      }\n    }\n\n    // Trickle down loading `fallback` bindings:\n    //\n    // - First, try to load native bindings installed in node_modules.\n    // - If that fails with `ERR_MODULE_NOT_FOUND`, treat it as case of https://github.com/npm/cli/issues/4828\n    // that host system where generated package lock is not matching to the guest system running on, try to manually\n    // download corresponding target triple and load it. This won't be triggered if native bindings are failed to load\n    // with other reasons than `ERR_MODULE_NOT_FOUND`.\n    // - Lastly, falls back to wasm binding where possible.\n    try {\n      return resolve(loadNative())\n    } catch (a) {\n      if (\n        Array.isArray(a) &&\n        a.every((m) => m.includes('it was not installed'))\n      ) {\n        let fallbackBindings = await tryLoadNativeWithFallback(attempts)\n\n        if (fallbackBindings) {\n          return resolve(fallbackBindings)\n        }\n      }\n\n      attempts = attempts.concat(a)\n    }\n\n    // For these platforms we already tried to load wasm and failed, skip reattempt\n    if (!shouldLoadWasmFallbackFirst && !disableWasmFallback) {\n      const fallbackBindings = await tryLoadWasmWithFallback(attempts)\n      if (fallbackBindings) {\n        return resolve(fallbackBindings)\n      }\n    }\n\n    logLoadFailure(attempts, true)\n  })\n  return pendingBindings\n}\n\nasync function tryLoadNativeWithFallback(attempts: Array<string>) {\n  const nativeBindingsDirectory = path.join(\n    path.dirname(require.resolve('next/package.json')),\n    'next-swc-fallback'\n  )\n\n  if (!downloadNativeBindingsPromise) {\n    downloadNativeBindingsPromise = downloadNativeNextSwc(\n      nextVersion,\n      nativeBindingsDirectory,\n      triples.map((triple: any) => triple.platformArchABI)\n    )\n  }\n  await downloadNativeBindingsPromise\n\n  try {\n    return loadNative(nativeBindingsDirectory)\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n\n  return undefined\n}\n\n// helper for loadBindings\nasync function tryLoadWasmWithFallback(\n  attempts: any[]\n): Promise<Binding | undefined> {\n  try {\n    let bindings = await loadWasm('')\n    // @ts-expect-error TODO: this event has a wrong type.\n    eventSwcLoadFailure({\n      wasm: 'enabled',\n      nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n    })\n    return bindings\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n\n  try {\n    // if not installed already download wasm package on-demand\n    // we download to a custom directory instead of to node_modules\n    // as node_module import attempts are cached and can't be re-attempted\n    // x-ref: https://github.com/nodejs/modules/issues/307\n    const wasmDirectory = path.join(\n      path.dirname(require.resolve('next/package.json')),\n      'wasm'\n    )\n    if (!downloadWasmPromise) {\n      downloadWasmPromise = downloadWasmSwc(nextVersion, wasmDirectory)\n    }\n    await downloadWasmPromise\n    let bindings = await loadWasm(wasmDirectory)\n    // @ts-expect-error TODO: this event has a wrong type.\n    eventSwcLoadFailure({\n      wasm: 'fallback',\n      nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n    })\n\n    // still log native load attempts so user is\n    // aware it failed and should be fixed\n    for (const attempt of attempts) {\n      Log.warn(attempt)\n    }\n    return bindings\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n}\n\nfunction loadBindingsSync() {\n  let attempts: any[] = []\n  try {\n    return loadNative()\n  } catch (a) {\n    attempts = attempts.concat(a)\n  }\n\n  // HACK: we can leverage the wasm bindings if they are already loaded\n  // this may introduce race conditions\n  if (wasmBindings) {\n    return wasmBindings\n  }\n\n  logLoadFailure(attempts)\n  throw new Error('Failed to load bindings', { cause: attempts })\n}\n\nlet loggingLoadFailure = false\n\nfunction logLoadFailure(attempts: any, triedWasm = false) {\n  // make sure we only emit the event and log the failure once\n  if (loggingLoadFailure) return\n  loggingLoadFailure = true\n\n  for (let attempt of attempts) {\n    Log.warn(attempt)\n  }\n\n  // @ts-expect-error TODO: this event has a wrong type.\n  eventSwcLoadFailure({\n    wasm: triedWasm ? 'failed' : undefined,\n    nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n  })\n    .then(() => lockfilePatchPromise.cur || Promise.resolve())\n    .finally(() => {\n      Log.error(\n        `Failed to load SWC binary for ${PlatformName}/${ArchName}, see more info here: https://nextjs.org/docs/messages/failed-loading-swc`\n      )\n      process.exit(1)\n    })\n}\n\ntype RustifiedEnv = { name: string; value: string }[]\ntype RustifiedOptionEnv = { name: string; value: string | undefined }[]\n\nexport function createDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  projectPath,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  middlewareMatchers,\n  rewrites,\n}: Omit<\n  DefineEnvOptions,\n  'isClient' | 'isNodeOrEdgeCompilation' | 'isEdgeServer' | 'isNodeServer'\n>): DefineEnv {\n  let defineEnv: DefineEnv = {\n    client: [],\n    edge: [],\n    nodejs: [],\n  }\n\n  for (const variant of Object.keys(defineEnv) as (keyof typeof defineEnv)[]) {\n    defineEnv[variant] = rustifyOptionEnv(\n      getDefineEnv({\n        isTurbopack,\n        clientRouterFilters,\n        config,\n        dev,\n        distDir,\n        projectPath,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient: variant === 'client',\n        isEdgeServer: variant === 'edge',\n        isNodeServer: variant === 'nodejs',\n        middlewareMatchers,\n        rewrites,\n      })\n    )\n  }\n\n  return defineEnv\n}\n\nfunction rustifyEnv(env: Record<string, string>): RustifiedEnv {\n  return Object.entries(env)\n    .filter(([_, value]) => value != null)\n    .map(([name, value]) => ({\n      name,\n      value,\n    }))\n}\n\nfunction rustifyOptionEnv(\n  env: Record<string, string | undefined>\n): RustifiedOptionEnv {\n  return Object.entries(env).map(([name, value]) => ({\n    name,\n    value,\n  }))\n}\n\n// TODO(sokra) Support wasm option.\nfunction bindingToApi(\n  binding: RawBindings,\n  _wasm: boolean\n): Binding['turbo']['createProject'] {\n  type NativeFunction<T> = (\n    callback: (err: Error, value: T) => void\n  ) => Promise<{ __napiType: 'RootTask' }>\n\n  type NapiEndpoint = { __napiType: 'Endpoint' }\n\n  type NapiEntrypoints = {\n    routes: NapiRoute[]\n    middleware?: NapiMiddleware\n    instrumentation?: NapiInstrumentation\n    pagesDocumentEndpoint: NapiEndpoint\n    pagesAppEndpoint: NapiEndpoint\n    pagesErrorEndpoint: NapiEndpoint\n  }\n\n  type NapiMiddleware = {\n    endpoint: NapiEndpoint\n    runtime: 'nodejs' | 'edge'\n    matcher?: string[]\n  }\n\n  type NapiInstrumentation = {\n    nodeJs: NapiEndpoint\n    edge: NapiEndpoint\n  }\n\n  type NapiRoute = {\n    pathname: string\n  } & (\n    | {\n        type: 'page'\n        htmlEndpoint: NapiEndpoint\n        dataEndpoint: NapiEndpoint\n      }\n    | {\n        type: 'page-api'\n        endpoint: NapiEndpoint\n      }\n    | {\n        type: 'app-page'\n        pages: {\n          originalName: string\n          htmlEndpoint: NapiEndpoint\n          rscEndpoint: NapiEndpoint\n        }[]\n      }\n    | {\n        type: 'app-route'\n        originalName: string\n        endpoint: NapiEndpoint\n      }\n    | {\n        type: 'conflict'\n      }\n  )\n\n  const cancel = new (class Cancel extends Error {})()\n\n  /**\n   * Utility function to ensure all variants of an enum are handled.\n   */\n  function invariant(\n    never: never,\n    computeMessage: (arg: any) => string\n  ): never {\n    throw new Error(`Invariant: ${computeMessage(never)}`)\n  }\n\n  /**\n   * Calls a native function and streams the result.\n   * If useBuffer is true, all values will be preserved, potentially buffered\n   * if consumed slower than produced. Else, only the latest value will be\n   * preserved.\n   */\n  function subscribe<T>(\n    useBuffer: boolean,\n    nativeFunction:\n      | NativeFunction<T>\n      | ((callback: (err: Error, value: T) => void) => Promise<void>)\n  ): AsyncIterableIterator<T> {\n    type BufferItem =\n      | { err: Error; value: undefined }\n      | { err: undefined; value: T }\n    // A buffer of produced items. This will only contain values if the\n    // consumer is slower than the producer.\n    let buffer: BufferItem[] = []\n    // A deferred value waiting for the next produced item. This will only\n    // exist if the consumer is faster than the producer.\n    let waiting:\n      | {\n          resolve: (value: T) => void\n          reject: (error: Error) => void\n        }\n      | undefined\n    let canceled = false\n\n    // The native function will call this every time it emits a new result. We\n    // either need to notify a waiting consumer, or buffer the new result until\n    // the consumer catches up.\n    function emitResult(err: Error | undefined, value: T | undefined) {\n      if (waiting) {\n        let { resolve, reject } = waiting\n        waiting = undefined\n        if (err) reject(err)\n        else resolve(value!)\n      } else {\n        const item = { err, value } as BufferItem\n        if (useBuffer) buffer.push(item)\n        else buffer[0] = item\n      }\n    }\n\n    async function* createIterator() {\n      const task = await nativeFunction(emitResult)\n      try {\n        while (!canceled) {\n          if (buffer.length > 0) {\n            const item = buffer.shift()!\n            if (item.err) throw item.err\n            yield item.value\n          } else {\n            // eslint-disable-next-line no-loop-func\n            yield new Promise<T>((resolve, reject) => {\n              waiting = { resolve, reject }\n            })\n          }\n        }\n      } catch (e) {\n        if (e === cancel) return\n        throw e\n      } finally {\n        if (task) {\n          binding.rootTaskDispose(task)\n        }\n      }\n    }\n\n    const iterator = createIterator()\n    iterator.return = async () => {\n      canceled = true\n      if (waiting) waiting.reject(cancel)\n      return { value: undefined, done: true } as IteratorReturnResult<never>\n    }\n    return iterator\n  }\n\n  async function rustifyProjectOptions(\n    options: ProjectOptions\n  ): Promise<NapiProjectOptions> {\n    return {\n      ...options,\n      nextConfig: await serializeNextConfig(\n        options.nextConfig,\n        path.join(options.rootPath, options.projectPath)\n      ),\n      jsConfig: JSON.stringify(options.jsConfig),\n      env: rustifyEnv(options.env),\n    }\n  }\n\n  async function rustifyPartialProjectOptions(\n    options: Partial<ProjectOptions>\n  ): Promise<NapiPartialProjectOptions> {\n    return {\n      ...options,\n      nextConfig:\n        options.nextConfig &&\n        (await serializeNextConfig(\n          options.nextConfig,\n          path.join(options.rootPath!, options.projectPath!)\n        )),\n      jsConfig: options.jsConfig && JSON.stringify(options.jsConfig),\n      env: options.env && rustifyEnv(options.env),\n    }\n  }\n\n  class ProjectImpl implements Project {\n    private readonly _nativeProject: { __napiType: 'Project' }\n\n    constructor(nativeProject: { __napiType: 'Project' }) {\n      this._nativeProject = nativeProject\n    }\n\n    async update(options: Partial<ProjectOptions>) {\n      await binding.projectUpdate(\n        this._nativeProject,\n        await rustifyPartialProjectOptions(options)\n      )\n    }\n\n    async writeAllEntrypointsToDisk(\n      appDirOnly: boolean\n    ): Promise<TurbopackResult<RawEntrypoints>> {\n      const napiEndpoints = (await binding.projectWriteAllEntrypointsToDisk(\n        this._nativeProject,\n        appDirOnly\n      )) as TurbopackResult<NapiEntrypoints>\n\n      return napiEntrypointsToRawEntrypoints(napiEndpoints)\n    }\n\n    entrypointsSubscribe() {\n      const subscription = subscribe<TurbopackResult<NapiEntrypoints>>(\n        false,\n        async (callback) =>\n          binding.projectEntrypointsSubscribe(this._nativeProject, callback)\n      )\n      return (async function* () {\n        for await (const entrypoints of subscription) {\n          yield napiEntrypointsToRawEntrypoints(entrypoints)\n        }\n      })()\n    }\n\n    hmrEvents(identifier: string) {\n      return subscribe<TurbopackResult<Update>>(true, async (callback) =>\n        binding.projectHmrEvents(this._nativeProject, identifier, callback)\n      )\n    }\n\n    hmrIdentifiersSubscribe() {\n      return subscribe<TurbopackResult<HmrIdentifiers>>(\n        false,\n        async (callback) =>\n          binding.projectHmrIdentifiersSubscribe(this._nativeProject, callback)\n      )\n    }\n\n    traceSource(\n      stackFrame: TurbopackStackFrame,\n      currentDirectoryFileUrl: string\n    ): Promise<TurbopackStackFrame | null> {\n      return binding.projectTraceSource(\n        this._nativeProject,\n        stackFrame,\n        currentDirectoryFileUrl\n      )\n    }\n\n    getSourceForAsset(filePath: string): Promise<string | null> {\n      return binding.projectGetSourceForAsset(this._nativeProject, filePath)\n    }\n\n    getSourceMap(filePath: string): Promise<string | null> {\n      return binding.projectGetSourceMap(this._nativeProject, filePath)\n    }\n\n    getSourceMapSync(filePath: string): string | null {\n      return binding.projectGetSourceMapSync(this._nativeProject, filePath)\n    }\n\n    updateInfoSubscribe(aggregationMs: number) {\n      return subscribe<TurbopackResult<UpdateMessage>>(true, async (callback) =>\n        binding.projectUpdateInfoSubscribe(\n          this._nativeProject,\n          aggregationMs,\n          callback\n        )\n      )\n    }\n\n    compilationEventsSubscribe(eventTypes?: string[]) {\n      return subscribe<TurbopackResult<CompilationEvent>>(\n        true,\n        async (callback) => {\n          binding.projectCompilationEventsSubscribe(\n            this._nativeProject,\n            callback,\n            eventTypes\n          )\n        }\n      )\n    }\n\n    invalidatePersistentCache(): Promise<void> {\n      return binding.projectInvalidatePersistentCache(this._nativeProject)\n    }\n\n    shutdown(): Promise<void> {\n      return binding.projectShutdown(this._nativeProject)\n    }\n\n    onExit(): Promise<void> {\n      return binding.projectOnExit(this._nativeProject)\n    }\n  }\n\n  class EndpointImpl implements Endpoint {\n    private readonly _nativeEndpoint: { __napiType: 'Endpoint' }\n\n    constructor(nativeEndpoint: { __napiType: 'Endpoint' }) {\n      this._nativeEndpoint = nativeEndpoint\n    }\n\n    async writeToDisk(): Promise<TurbopackResult<WrittenEndpoint>> {\n      return (await binding.endpointWriteToDisk(\n        this._nativeEndpoint\n      )) as TurbopackResult<WrittenEndpoint>\n    }\n\n    async clientChanged(): Promise<AsyncIterableIterator<TurbopackResult<{}>>> {\n      const clientSubscription = subscribe<TurbopackResult>(\n        false,\n        async (callback) =>\n          binding.endpointClientChangedSubscribe(this._nativeEndpoint, callback)\n      )\n      await clientSubscription.next()\n      return clientSubscription\n    }\n\n    async serverChanged(\n      includeIssues: boolean\n    ): Promise<AsyncIterableIterator<TurbopackResult<{}>>> {\n      const serverSubscription = subscribe<TurbopackResult>(\n        false,\n        async (callback) =>\n          binding.endpointServerChangedSubscribe(\n            this._nativeEndpoint,\n            includeIssues,\n            callback\n          )\n      )\n      await serverSubscription.next()\n      return serverSubscription\n    }\n  }\n\n  /**\n   * Returns a new copy of next.js config object to avoid mutating the original.\n   *\n   * Also it does some augmentation to the configuration as well, for example set the\n   * turbopack's rules if `experimental.reactCompilerOptions` is set.\n   */\n  function augmentNextConfig(\n    originalNextConfig: NextConfigComplete,\n    projectPath: string\n  ): Record<string, any> {\n    let nextConfig = { ...(originalNextConfig as NextConfigComplete) }\n\n    const reactCompilerOptions = nextConfig.experimental?.reactCompiler\n\n    // It is not easy to set the rules inside of rust as resolving, and passing the context identical to the webpack\n    // config is bit hard, also we can reuse same codes between webpack config in here.\n    if (reactCompilerOptions) {\n      const options: ReactCompilerOptions =\n        typeof reactCompilerOptions === 'object' ? reactCompilerOptions : {}\n      const ruleKeys = ['*.ts', '*.js', '*.jsx', '*.tsx']\n      if (\n        Object.keys(nextConfig?.turbopack?.rules ?? {}).some((key) =>\n          ruleKeys.includes(key)\n        )\n      ) {\n        Log.warn(\n          \"The React Compiler cannot be enabled automatically because 'turbopack.rules' contains \" +\n            \"a rule for '*.ts', '*.js', '*.jsx', and '*.tsx'. Remove this rule, or add \" +\n            \"'babel-loader' and 'babel-plugin-react-compiler' to the Turbopack configuration \" +\n            'manually.'\n        )\n      } else {\n        nextConfig.turbopack ??= {}\n        nextConfig.turbopack.conditions ??= {}\n        nextConfig.turbopack.rules ??= {}\n\n        for (const key of ruleKeys) {\n          nextConfig.turbopack.conditions[`#reactCompiler/${key}`] = {\n            path: key,\n            content:\n              options.compilationMode === 'annotation'\n                ? /['\"]use memo['\"]/\n                : !options.compilationMode ||\n                    options.compilationMode === 'infer'\n                  ? // Matches declaration or useXXX or </ (closing jsx) or /> (self closing jsx)\n                    /['\"]use memo['\"]|\\Wuse[A-Z]|<\\/|\\/>/\n                  : undefined,\n          }\n          nextConfig.turbopack.rules[`#reactCompiler/${key}`] = {\n            browser: {\n              foreign: false,\n              loaders: [\n                getReactCompilerLoader(\n                  reactCompilerOptions,\n                  projectPath,\n                  nextConfig.dev,\n                  /* isServer */ false,\n                  /* reactCompilerExclude */ undefined\n                ),\n              ],\n            },\n          }\n        }\n      }\n    }\n\n    return nextConfig\n  }\n\n  async function serializeNextConfig(\n    nextConfig: NextConfigComplete,\n    projectPath: string\n  ): Promise<string> {\n    // Avoid mutating the existing `nextConfig` object.\n    let nextConfigSerializable = augmentNextConfig(nextConfig, projectPath)\n\n    nextConfigSerializable.generateBuildId =\n      await nextConfig.generateBuildId?.()\n\n    // TODO: these functions takes arguments, have to be supported in a different way\n    nextConfigSerializable.exportPathMap = {}\n    nextConfigSerializable.webpack = nextConfig.webpack && {}\n\n    if (nextConfigSerializable.turbopack?.rules) {\n      ensureLoadersHaveSerializableOptions(\n        nextConfigSerializable.turbopack?.rules\n      )\n    }\n\n    nextConfigSerializable.modularizeImports =\n      nextConfigSerializable.modularizeImports\n        ? Object.fromEntries(\n            Object.entries<any>(nextConfigSerializable.modularizeImports).map(\n              ([mod, config]) => [\n                mod,\n                {\n                  ...config,\n                  transform:\n                    typeof config.transform === 'string'\n                      ? config.transform\n                      : Object.entries(config.transform).map(([key, value]) => [\n                          key,\n                          value,\n                        ]),\n                },\n              ]\n            )\n          )\n        : undefined\n\n    // loaderFile is an absolute path, we need it to be relative for turbopack.\n    if (nextConfigSerializable.images.loaderFile) {\n      nextConfigSerializable.images = {\n        ...nextConfig.images,\n        loaderFile:\n          './' + path.relative(projectPath, nextConfig.images.loaderFile),\n      }\n    }\n\n    const conditions: (typeof nextConfig)['turbopack']['conditions'] =\n      nextConfigSerializable.turbopack?.conditions\n    if (conditions) {\n      type SerializedConditions = {\n        [key: string]: {\n          path?:\n            | { type: 'regex'; value: { source: string; flags: string } }\n            | { type: 'glob'; value: string }\n          content?: { source: string; flags: string }\n        }\n      }\n\n      function regexComponents(regex: RegExp): {\n        source: string\n        flags: string\n      } {\n        return {\n          source: regex.source,\n          flags: regex.flags,\n        }\n      }\n\n      const serializedConditions: SerializedConditions = {}\n      for (const [key, value] of Object.entries(conditions)) {\n        serializedConditions[key] = {\n          ...value,\n          path: !value.path\n            ? undefined\n            : value.path instanceof RegExp\n              ? {\n                  type: 'regex',\n                  value: regexComponents(value.path),\n                }\n              : { type: 'glob', value: value.path },\n          content: !value.content ? undefined : regexComponents(value.content),\n        }\n      }\n      nextConfigSerializable.turbopack.conditions = serializedConditions\n    }\n\n    return JSON.stringify(nextConfigSerializable, null, 2)\n  }\n\n  function ensureLoadersHaveSerializableOptions(\n    turbopackRules: Record<string, TurbopackRuleConfigItemOrShortcut>\n  ) {\n    for (const [glob, rule] of Object.entries(turbopackRules)) {\n      if (Array.isArray(rule)) {\n        checkLoaderItems(rule, glob)\n      } else {\n        checkConfigItem(rule, glob)\n      }\n    }\n\n    function checkConfigItem(rule: TurbopackRuleConfigItem, glob: string) {\n      if (!rule) return\n      if ('loaders' in rule) {\n        checkLoaderItems((rule as TurbopackRuleConfigItemOptions).loaders, glob)\n      } else {\n        for (const value of Object.values(rule)) {\n          if (typeof value === 'object' && value) {\n            checkConfigItem(value, glob)\n          }\n        }\n      }\n    }\n\n    function checkLoaderItems(\n      loaderItems: TurbopackLoaderItem[],\n      glob: string\n    ) {\n      for (const loaderItem of loaderItems) {\n        if (\n          typeof loaderItem !== 'string' &&\n          !isDeepStrictEqual(loaderItem, JSON.parse(JSON.stringify(loaderItem)))\n        ) {\n          throw new Error(\n            `loader ${loaderItem.loader} for match \"${glob}\" does not have serializable options. Ensure that options passed are plain JavaScript objects and values.`\n          )\n        }\n      }\n    }\n  }\n\n  function napiEntrypointsToRawEntrypoints(\n    entrypoints: TurbopackResult<NapiEntrypoints>\n  ): TurbopackResult<RawEntrypoints> {\n    const routes = new Map()\n    for (const { pathname, ...nativeRoute } of entrypoints.routes) {\n      let route: Route\n      const routeType = nativeRoute.type\n      switch (routeType) {\n        case 'page':\n          route = {\n            type: 'page',\n            htmlEndpoint: new EndpointImpl(nativeRoute.htmlEndpoint),\n            dataEndpoint: new EndpointImpl(nativeRoute.dataEndpoint),\n          }\n          break\n        case 'page-api':\n          route = {\n            type: 'page-api',\n            endpoint: new EndpointImpl(nativeRoute.endpoint),\n          }\n          break\n        case 'app-page':\n          route = {\n            type: 'app-page',\n            pages: nativeRoute.pages.map((page) => ({\n              originalName: page.originalName,\n              htmlEndpoint: new EndpointImpl(page.htmlEndpoint),\n              rscEndpoint: new EndpointImpl(page.rscEndpoint),\n            })),\n          }\n          break\n        case 'app-route':\n          route = {\n            type: 'app-route',\n            originalName: nativeRoute.originalName,\n            endpoint: new EndpointImpl(nativeRoute.endpoint),\n          }\n          break\n        case 'conflict':\n          route = {\n            type: 'conflict',\n          }\n          break\n        default:\n          const _exhaustiveCheck: never = routeType\n          invariant(\n            nativeRoute,\n            () => `Unknown route type: ${_exhaustiveCheck}`\n          )\n      }\n      routes.set(pathname, route)\n    }\n    const napiMiddlewareToMiddleware = (middleware: NapiMiddleware) => ({\n      endpoint: new EndpointImpl(middleware.endpoint),\n      runtime: middleware.runtime,\n      matcher: middleware.matcher,\n    })\n    const middleware = entrypoints.middleware\n      ? napiMiddlewareToMiddleware(entrypoints.middleware)\n      : undefined\n    const napiInstrumentationToInstrumentation = (\n      instrumentation: NapiInstrumentation\n    ) => ({\n      nodeJs: new EndpointImpl(instrumentation.nodeJs),\n      edge: new EndpointImpl(instrumentation.edge),\n    })\n    const instrumentation = entrypoints.instrumentation\n      ? napiInstrumentationToInstrumentation(entrypoints.instrumentation)\n      : undefined\n\n    return {\n      routes,\n      middleware,\n      instrumentation,\n      pagesDocumentEndpoint: new EndpointImpl(\n        entrypoints.pagesDocumentEndpoint\n      ),\n      pagesAppEndpoint: new EndpointImpl(entrypoints.pagesAppEndpoint),\n      pagesErrorEndpoint: new EndpointImpl(entrypoints.pagesErrorEndpoint),\n      issues: entrypoints.issues,\n      diagnostics: entrypoints.diagnostics,\n    }\n  }\n\n  return async function createProject(\n    options: ProjectOptions,\n    turboEngineOptions\n  ) {\n    return new ProjectImpl(\n      await binding.projectNew(\n        await rustifyProjectOptions(options),\n        turboEngineOptions || {},\n        {\n          throwTurbopackInternalError,\n        }\n      )\n    )\n  }\n}\n\n// helper for loadWasm\nasync function loadWasmRawBindings(importPath = ''): Promise<RawWasmBindings> {\n  let attempts = []\n\n  // Used by `run-tests` to force use of a locally-built wasm binary. This environment variable is\n  // unstable and subject to change.\n  const testWasmDir = process.env.NEXT_TEST_WASM_DIR\n\n  if (testWasmDir) {\n    // assume these are node.js bindings and don't need a call to `.default()`\n    const rawBindings = await import(\n      pathToFileURL(path.join(testWasmDir, 'wasm.js')).toString()\n    )\n    infoLog(`next-swc build: wasm build ${testWasmDir}`)\n    return rawBindings\n  } else {\n    for (let pkg of ['@next/swc-wasm-nodejs', '@next/swc-wasm-web']) {\n      try {\n        let pkgPath = pkg\n\n        if (importPath) {\n          // the import path must be exact when not in node_modules\n          pkgPath = path.join(importPath, pkg, 'wasm.js')\n        }\n        const importedRawBindings = await import(\n          pathToFileURL(pkgPath).toString()\n        )\n        let rawBindings\n        if (pkg === '@next/swc-wasm-web') {\n          // https://rustwasm.github.io/docs/wasm-bindgen/examples/without-a-bundler.html\n          // `default` must be called to initialize the module\n          rawBindings = await importedRawBindings.default!()\n        } else {\n          rawBindings = importedRawBindings\n        }\n        infoLog(`next-swc build: wasm build ${pkg}`)\n        return rawBindings\n      } catch (e: any) {\n        // Only log attempts for loading wasm when loading as fallback\n        if (importPath) {\n          if (e?.code === 'ERR_MODULE_NOT_FOUND') {\n            attempts.push(`Attempted to load ${pkg}, but it was not installed`)\n          } else {\n            attempts.push(\n              `Attempted to load ${pkg}, but an error occurred: ${e.message ?? e}`\n            )\n          }\n        }\n      }\n    }\n  }\n\n  throw attempts\n}\n\n// helper for tryLoadWasmWithFallback / loadBindings.\nasync function loadWasm(importPath = '') {\n  const rawBindings = await loadWasmRawBindings(importPath)\n\n  function removeUndefined(obj: any): any {\n    // serde-wasm-bindgen expect that `undefined` values map to `()` in rust, but we want to treat\n    // those fields as non-existent, so remove them before passing them to rust.\n    //\n    // The native (non-wasm) bindings use `JSON.stringify`, which strips undefined values.\n    if (typeof obj !== 'object' || obj === null) {\n      return obj\n    }\n    if (Array.isArray(obj)) {\n      return obj.map(removeUndefined)\n    }\n    const newObj: { [key: string]: any } = {}\n    for (const [k, v] of Object.entries(obj)) {\n      if (typeof v !== 'undefined') {\n        newObj[k] = removeUndefined(v)\n      }\n    }\n    return newObj\n  }\n\n  // Note wasm binary does not support async intefaces yet, all async\n  // interface coereces to sync interfaces.\n  wasmBindings = {\n    css: {\n      lightning: {\n        transform: function (_options: any) {\n          throw new Error(\n            '`css.lightning.transform` is not supported by the wasm bindings.'\n          )\n        },\n        transformStyleAttr: function (_options: any) {\n          throw new Error(\n            '`css.lightning.transformStyleAttr` is not supported by the wasm bindings.'\n          )\n        },\n      },\n    },\n    isWasm: true,\n    transform(src: string, options: any): Promise<any> {\n      return rawBindings.transform(src.toString(), removeUndefined(options))\n    },\n    transformSync(src: string, options: any) {\n      return rawBindings.transformSync(src.toString(), removeUndefined(options))\n    },\n    minify(src: string, options: any): Promise<any> {\n      return rawBindings.minify(src.toString(), removeUndefined(options))\n    },\n    minifySync(src: string, options: any) {\n      return rawBindings.minifySync(src.toString(), removeUndefined(options))\n    },\n    parse(src: string, options: any): Promise<any> {\n      return rawBindings.parse(src.toString(), removeUndefined(options))\n    },\n    getTargetTriple() {\n      return undefined\n    },\n    turbo: {\n      createProject(\n        _options: ProjectOptions,\n        _turboEngineOptions?: TurboEngineOptions | undefined\n      ): Promise<Project> {\n        throw new Error(\n          '`turbo.createProject` is not supported by the wasm bindings.'\n        )\n      },\n      startTurbopackTraceServer(_traceFilePath: string): void {\n        throw new Error(\n          '`turbo.startTurbopackTraceServer` is not supported by the wasm bindings.'\n        )\n      },\n    },\n    mdx: {\n      compile(src: string, options: any) {\n        return rawBindings.mdxCompile(\n          src,\n          removeUndefined(getMdxOptions(options))\n        )\n      },\n      compileSync(src: string, options: any) {\n        return rawBindings.mdxCompileSync(\n          src,\n          removeUndefined(getMdxOptions(options))\n        )\n      },\n    },\n    reactCompiler: {\n      isReactCompilerRequired(_filename: string) {\n        return Promise.resolve(true)\n      },\n    },\n    rspack: {\n      getModuleNamedExports(_resourcePath: string): Promise<string[]> {\n        throw new Error(\n          '`rspack.getModuleNamedExports` is not supported by the wasm bindings.'\n        )\n      },\n      warnForEdgeRuntime(\n        _source: string,\n        _isProduction: boolean\n      ): Promise<NapiSourceDiagnostic[]> {\n        throw new Error(\n          '`rspack.warnForEdgeRuntime` is not supported by the wasm bindings.'\n        )\n      },\n    },\n    expandNextJsTemplate(\n      content: Buffer,\n      templatePath: string,\n      nextPackageDirPath: string,\n      replacements: Record<`VAR_${string}`, string>,\n      injections: Record<string, string>,\n      imports: Record<string, string | null>\n    ): string {\n      return rawBindings.expandNextJsTemplate(\n        content,\n        templatePath,\n        nextPackageDirPath,\n        replacements,\n        injections,\n        imports\n      )\n    },\n  }\n  return wasmBindings\n}\n\n/**\n * Loads the native (non-wasm) bindings. Prefer `loadBindings` over this API, as that includes a\n * wasm fallback.\n */\nfunction loadNative(importPath?: string) {\n  if (nativeBindings) {\n    return nativeBindings\n  }\n\n  if (process.env.NEXT_TEST_WASM) {\n    throw new Error('cannot run loadNative when `NEXT_TEST_WASM` is set')\n  }\n\n  const customBindings: RawBindings = !!__INTERNAL_CUSTOM_TURBOPACK_BINDINGS\n    ? require(__INTERNAL_CUSTOM_TURBOPACK_BINDINGS)\n    : null\n  let bindings: RawBindings = customBindings\n  let attempts: any[] = []\n\n  const NEXT_TEST_NATIVE_DIR = process.env.NEXT_TEST_NATIVE_DIR\n  for (const triple of triples) {\n    if (NEXT_TEST_NATIVE_DIR) {\n      try {\n        // Use the binary directly to skip `pnpm pack` for testing as it's slow because of the large native binary.\n        bindings = require(\n          `${NEXT_TEST_NATIVE_DIR}/next-swc.${triple.platformArchABI}.node`\n        )\n        infoLog(\n          'next-swc build: local built @next/swc from NEXT_TEST_NATIVE_DIR'\n        )\n        break\n      } catch (e) {}\n    } else {\n      try {\n        bindings = require(\n          `@next/swc/native/next-swc.${triple.platformArchABI}.node`\n        )\n        infoLog('next-swc build: local built @next/swc')\n        break\n      } catch (e) {}\n    }\n  }\n\n  if (!bindings) {\n    for (const triple of triples) {\n      let pkg = importPath\n        ? path.join(\n            importPath,\n            `@next/swc-${triple.platformArchABI}`,\n            `next-swc.${triple.platformArchABI}.node`\n          )\n        : `@next/swc-${triple.platformArchABI}`\n      try {\n        bindings = require(pkg)\n        if (!importPath) {\n          checkVersionMismatch(require(`${pkg}/package.json`))\n        }\n        break\n      } catch (e: any) {\n        if (e?.code === 'MODULE_NOT_FOUND') {\n          attempts.push(`Attempted to load ${pkg}, but it was not installed`)\n        } else {\n          attempts.push(\n            `Attempted to load ${pkg}, but an error occurred: ${e.message ?? e}`\n          )\n        }\n        lastNativeBindingsLoadErrorCode = e?.code ?? 'unknown'\n      }\n    }\n  }\n\n  if (bindings) {\n    nativeBindings = {\n      isWasm: false,\n      transform(src: string, options: any) {\n        const isModule =\n          typeof src !== 'undefined' &&\n          typeof src !== 'string' &&\n          !Buffer.isBuffer(src)\n        options = options || {}\n\n        if (options?.jsc?.parser) {\n          options.jsc.parser.syntax = options.jsc.parser.syntax ?? 'ecmascript'\n        }\n\n        return bindings.transform(\n          isModule ? JSON.stringify(src) : src,\n          isModule,\n          toBuffer(options)\n        )\n      },\n\n      transformSync(src: string, options: any) {\n        if (typeof src === 'undefined') {\n          throw new Error(\n            \"transformSync doesn't implement reading the file from filesystem\"\n          )\n        } else if (Buffer.isBuffer(src)) {\n          throw new Error(\n            \"transformSync doesn't implement taking the source code as Buffer\"\n          )\n        }\n        const isModule = typeof src !== 'string'\n        options = options || {}\n\n        if (options?.jsc?.parser) {\n          options.jsc.parser.syntax = options.jsc.parser.syntax ?? 'ecmascript'\n        }\n\n        return bindings.transformSync(\n          isModule ? JSON.stringify(src) : src,\n          isModule,\n          toBuffer(options)\n        )\n      },\n\n      minify(src: string, options: any) {\n        return bindings.minify(Buffer.from(src), toBuffer(options ?? {}))\n      },\n\n      minifySync(src: string, options: any) {\n        return bindings.minifySync(Buffer.from(src), toBuffer(options ?? {}))\n      },\n\n      parse(src: string, options: any) {\n        return bindings.parse(src, toBuffer(options ?? {}))\n      },\n\n      getTargetTriple: bindings.getTargetTriple,\n      initCustomTraceSubscriber: bindings.initCustomTraceSubscriber,\n      teardownTraceSubscriber: bindings.teardownTraceSubscriber,\n      turbo: {\n        createProject: bindingToApi(customBindings ?? bindings, false),\n        startTurbopackTraceServer(traceFilePath) {\n          Log.warn(\n            'Turbopack trace server started. View trace at https://trace.nextjs.org'\n          )\n          ;(customBindings ?? bindings).startTurbopackTraceServer(traceFilePath)\n        },\n      },\n      mdx: {\n        compile(src: string, options: any) {\n          return bindings.mdxCompile(src, toBuffer(getMdxOptions(options)))\n        },\n        compileSync(src: string, options: any) {\n          bindings.mdxCompileSync(src, toBuffer(getMdxOptions(options)))\n        },\n      },\n      css: {\n        lightning: {\n          transform(transformOptions: any) {\n            return bindings.lightningCssTransform(transformOptions)\n          },\n          transformStyleAttr(transformAttrOptions: any) {\n            return bindings.lightningCssTransformStyleAttribute(\n              transformAttrOptions\n            )\n          },\n        },\n      },\n      reactCompiler: {\n        isReactCompilerRequired: (filename: string) => {\n          return bindings.isReactCompilerRequired(filename)\n        },\n      },\n      rspack: {\n        getModuleNamedExports: function (\n          resourcePath: string\n        ): Promise<string[]> {\n          return bindings.getModuleNamedExports(resourcePath)\n        },\n        warnForEdgeRuntime: function (\n          source: string,\n          isProduction: boolean\n        ): Promise<NapiSourceDiagnostic[]> {\n          return bindings.warnForEdgeRuntime(source, isProduction)\n        },\n      },\n      expandNextJsTemplate(\n        content: Buffer,\n        templatePath: string,\n        nextPackageDirPath: string,\n        replacements: Record<`VAR_${string}`, string>,\n        injections: Record<string, string>,\n        imports: Record<string, string | null>\n      ): string {\n        return bindings.expandNextJsTemplate(\n          content,\n          templatePath,\n          nextPackageDirPath,\n          replacements,\n          injections,\n          imports\n        )\n      },\n    }\n    return nativeBindings\n  }\n\n  throw attempts\n}\n\n/// Build a mdx options object contains default values that\n/// can be parsed with serde_wasm_bindgen.\nfunction getMdxOptions(options: any = {}) {\n  return {\n    ...options,\n    development: options.development ?? false,\n    jsx: options.jsx ?? false,\n    mdxType: options.mdxType ?? 'commonMark',\n  }\n}\n\nfunction toBuffer(t: any) {\n  return Buffer.from(JSON.stringify(t))\n}\n\nexport async function isWasm(): Promise<boolean> {\n  let bindings = await loadBindings()\n  return bindings.isWasm\n}\n\nexport async function transform(src: string, options?: any): Promise<any> {\n  let bindings = await loadBindings()\n  return bindings.transform(src, options)\n}\n\nexport function transformSync(src: string, options?: any): any {\n  let bindings = loadBindingsSync()\n  return bindings.transformSync(src, options)\n}\n\nexport async function minify(\n  src: string,\n  options: any\n): Promise<{ code: string; map: any }> {\n  let bindings = await loadBindings()\n  return bindings.minify(src, options)\n}\n\nexport async function isReactCompilerRequired(\n  filename: string\n): Promise<boolean> {\n  let bindings = await loadBindings()\n  return bindings.reactCompiler.isReactCompilerRequired(filename)\n}\n\nexport async function parse(src: string, options: any): Promise<any> {\n  let bindings = await loadBindings()\n  let parserOptions = getParserOptions(options)\n  return bindings\n    .parse(src, parserOptions)\n    .then((astStr: any) => JSON.parse(astStr))\n}\n\nexport function getBinaryMetadata() {\n  let bindings\n  try {\n    bindings = loadNative()\n  } catch (e) {\n    // Suppress exceptions, this fn allows to fail to load native bindings\n  }\n\n  return {\n    target: bindings?.getTargetTriple?.(),\n  }\n}\n\n/**\n * Initialize trace subscriber to emit traces.\n *\n */\nexport function initCustomTraceSubscriber(traceFileName?: string) {\n  if (!swcTraceFlushGuard) {\n    // Wasm binary doesn't support trace emission\n    let bindings = loadNative()\n    swcTraceFlushGuard = bindings.initCustomTraceSubscriber?.(traceFileName)\n  }\n}\n\nfunction once(fn: () => void): () => void {\n  let executed = false\n\n  return function (): void {\n    if (!executed) {\n      executed = true\n\n      fn()\n    }\n  }\n}\n\n/**\n * Teardown swc's trace subscriber if there's an initialized flush guard exists.\n *\n * This is workaround to amend behavior with process.exit\n * (https://github.com/vercel/next.js/blob/4db8c49cc31e4fc182391fae6903fb5ef4e8c66e/packages/next/bin/next.ts#L134=)\n * seems preventing napi's cleanup hook execution (https://github.com/swc-project/swc/blob/main/crates/node/src/util.rs#L48-L51=),\n *\n * instead parent process manually drops guard when process gets signal to exit.\n */\nexport const teardownTraceSubscriber = once(() => {\n  try {\n    let bindings = loadNative()\n    if (swcTraceFlushGuard) {\n      bindings.teardownTraceSubscriber?.(swcTraceFlushGuard)\n    }\n  } catch (e) {\n    // Suppress exceptions, this fn allows to fail to load native bindings\n  }\n})\n\nexport async function getModuleNamedExports(\n  resourcePath: string\n): Promise<string[]> {\n  const bindings = await loadBindings()\n  return bindings.rspack.getModuleNamedExports(resourcePath)\n}\n\nexport async function warnForEdgeRuntime(\n  source: string,\n  isProduction: boolean\n): Promise<NapiSourceDiagnostic[]> {\n  const bindings = await loadBindings()\n  return bindings.rspack.warnForEdgeRuntime(source, isProduction)\n}\n"], "names": ["path", "pathToFileURL", "arch", "platform", "platformArchTriples", "Log", "getParserOptions", "eventSwcLoadFailure", "patchIncorrectLockfile", "downloadNativeNextSwc", "downloadWasmSwc", "isDeepStrictEqual", "getDefineEnv", "getReactCompilerLoader", "throwTurbopackInternalError", "nextVersion", "process", "env", "__NEXT_VERSION", "Arch<PERSON>ame", "PlatformName", "infoLog", "args", "NEXT_PRIVATE_BUILD_WORKER", "DEBUG", "info", "getSupportedArchTriples", "darwin", "win32", "linux", "freebsd", "android", "arm64", "ia32", "filter", "triple", "abi", "x64", "arm", "triples", "supportedArchTriples", "targetTriple", "rawTargetTriple", "warn", "__INTERNAL_CUSTOM_TURBOPACK_BINDINGS", "checkVersionMismatch", "pkgData", "version", "knownDefaultWasmFallbackTriples", "lastNativeBindingsLoadErrorCode", "undefined", "pendingBindings", "nativeBindings", "wasmBindings", "downloadWasmPromise", "swcTraceFlushGuard", "downloadNativeBindingsPromise", "lockfilePatchPromise", "loadBindings", "useWasmBinary", "RUST_MIN_STACK", "NEXT_TEST_WASM", "stdout", "_handle", "setBlocking", "stderr", "Promise", "resolve", "_reject", "cur", "cwd", "catch", "console", "error", "attempts", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NEXT_DISABLE_SWC_WASM", "unsupportedPlatform", "some", "raw", "includes", "isWebContainer", "versions", "webcontainer", "shouldLoadWasmFallbackFirst", "fallback<PERSON><PERSON><PERSON>", "tryLoadWasmWithFallback", "loadNative", "a", "Array", "isArray", "every", "m", "tryLoadNativeWithFallback", "concat", "logLoadFailure", "nativeBindingsDirectory", "join", "dirname", "require", "map", "platformArchABI", "push", "bindings", "loadWasm", "wasm", "nativeBindingsErrorCode", "wasmDirectory", "attempt", "loadBindingsSync", "Error", "cause", "loggingLoadFailure", "triedWasm", "then", "finally", "exit", "createDefineEnv", "isTurbopack", "clientRouterFilters", "config", "dev", "distDir", "projectPath", "fetchCacheKeyPrefix", "hasRewrites", "middlewareMatchers", "rewrites", "defineEnv", "client", "edge", "nodejs", "variant", "Object", "keys", "rustifyOptionEnv", "isClient", "isEdgeServer", "isNodeServer", "rustifyEnv", "entries", "_", "value", "name", "bindingToApi", "binding", "_wasm", "cancel", "Cancel", "invariant", "never", "computeMessage", "subscribe", "useBuffer", "nativeFunction", "buffer", "waiting", "canceled", "emitResult", "err", "reject", "item", "createIterator", "task", "length", "shift", "e", "rootTaskDispose", "iterator", "return", "done", "rustifyProjectOptions", "options", "nextConfig", "serializeNextConfig", "rootPath", "jsConfig", "JSON", "stringify", "rustifyPartialProjectOptions", "ProjectImpl", "constructor", "nativeProject", "_nativeProject", "update", "projectUpdate", "writeAllEntrypointsToDisk", "appDirOnly", "napiEndpoints", "projectWriteAllEntrypointsToDisk", "napiEntrypointsToRawEntrypoints", "entrypointsSubscribe", "subscription", "callback", "projectEntrypointsSubscribe", "entrypoints", "hmrEvents", "identifier", "projectHmrEvents", "hmrIdentifiersSubscribe", "projectHmrIdentifiersSubscribe", "traceSource", "stackFrame", "currentDirectoryFileUrl", "projectTraceSource", "getSourceForAsset", "filePath", "projectGetSourceForAsset", "getSourceMap", "projectGetSourceMap", "getSourceMapSync", "projectGetSourceMapSync", "updateInfoSubscribe", "aggregationMs", "projectUpdateInfoSubscribe", "compilationEventsSubscribe", "eventTypes", "projectCompilationEventsSubscribe", "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectInvalidatePersistentCache", "shutdown", "projectShutdown", "onExit", "projectOnExit", "EndpointImpl", "nativeEndpoint", "_nativeEndpoint", "writeToDisk", "endpointWriteToDisk", "clientChanged", "clientSubscription", "endpointClientChangedSubscribe", "next", "serverChanged", "includeIssues", "serverSubscription", "endpointServerChangedSubscribe", "augmentNextConfig", "originalNextConfig", "reactCompilerOptions", "experimental", "reactCompiler", "rule<PERSON>eys", "turbopack", "rules", "key", "conditions", "content", "compilationMode", "browser", "foreign", "loaders", "nextConfigSerializable", "generateBuildId", "exportPathMap", "webpack", "ensureLoadersHaveSerializableOptions", "modularizeImports", "fromEntries", "mod", "transform", "images", "loaderFile", "relative", "regexComponents", "regex", "source", "flags", "serializedConditions", "RegExp", "type", "turbopackRules", "glob", "rule", "checkLoaderItems", "checkConfigItem", "values", "loaderItems", "loaderItem", "parse", "loader", "routes", "Map", "pathname", "nativeRoute", "route", "routeType", "htmlEndpoint", "dataEndpoint", "endpoint", "pages", "page", "originalName", "rscEndpoint", "_exhaustiveCheck", "set", "napiMiddlewareToMiddleware", "middleware", "runtime", "matcher", "napiInstrumentationToInstrumentation", "instrumentation", "nodeJs", "pagesDocumentEndpoint", "pagesAppEndpoint", "pagesErrorEndpoint", "issues", "diagnostics", "createProject", "turboEngineOptions", "projectNew", "loadWasmRawBindings", "importPath", "testWasmDir", "NEXT_TEST_WASM_DIR", "rawBindings", "toString", "pkg", "pkgPath", "importedRawBindings", "default", "code", "message", "removeUndefined", "obj", "newObj", "k", "v", "css", "lightning", "_options", "transformStyleAttr", "isWasm", "src", "transformSync", "minify", "minifySync", "getTargetTriple", "turbo", "_turboEngineOptions", "startTurbopackTraceServer", "_traceFilePath", "mdx", "compile", "mdxCompile", "getMdxOptions", "compileSync", "mdxCompileSync", "isReactCompilerRequired", "_filename", "rspack", "getModuleNamedExports", "_resourcePath", "warnForEdgeRuntime", "_source", "_isProduction", "expandNextJsTemplate", "templatePath", "nextPackageDirPath", "replacements", "injections", "imports", "customBindings", "NEXT_TEST_NATIVE_DIR", "isModule", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsc", "parser", "syntax", "<PERSON><PERSON><PERSON><PERSON>", "from", "initCustomTraceSubscriber", "teardownTraceSubscriber", "traceFilePath", "transformOptions", "lightningCssTransform", "transformAttrOptions", "lightningCssTransformStyleAttribute", "filename", "resourcePath", "isProduction", "development", "jsx", "mdxType", "t", "parserOptions", "astStr", "getBinaryMetadata", "target", "traceFileName", "once", "fn", "executed"], "mappings": "AAAA,0DAA0D,GAC1D,OAAOA,UAAU,OAAM;AACvB,SAASC,aAAa,QAAQ,MAAK;AACnC,SAASC,IAAI,EAAEC,QAAQ,QAAQ,KAAI;AACnC,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,YAAYC,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,YAAW;AAC5C,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,sBAAsB,QAAQ,qCAAoC;AAC3E,SAASC,qBAAqB,EAAEC,eAAe,QAAQ,yBAAwB;AAS/E,SAASC,iBAAiB,QAAQ,OAAM;AACxC,SAAgCC,YAAY,QAAQ,gBAAe;AACnE,SAASC,sBAAsB,QAAQ,6BAA4B;AAuBnE,SAASC,2BAA2B,QAAQ,4CAA2C;AAOvF,MAAMC,cAAcC,QAAQC,GAAG,CAACC,cAAc;AAE9C,MAAMC,WAAWjB;AACjB,MAAMkB,eAAejB;AAErB,SAASkB,QAAQ,GAAGC,IAAW;IAC7B,IAAIN,QAAQC,GAAG,CAACM,yBAAyB,EAAE;QACzC;IACF;IACA,IAAIP,QAAQC,GAAG,CAACO,KAAK,EAAE;QACrBnB,IAAIoB,IAAI,IAAIH;IACd;AACF;AAEA;;CAEC,GACD,OAAO,SAASI;IACd,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAG3B;IAEnD,OAAO;QACLuB;QACAC,OAAO;YACLI,OAAOJ,MAAMI,KAAK;YAClBC,MAAML,MAAMK,IAAI,CAACC,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;YACnDC,KAAKT,MAAMS,GAAG,CAACH,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;QACnD;QACAP,OAAO;YACL,mDAAmD;YACnDQ,KAAKR,MAAMQ,GAAG,CAACH,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;YACjDJ,OAAOH,MAAMG,KAAK;YAClB,mGAAmG;YACnGM,KAAKT,MAAMS,GAAG;QAChB;QACA,sGAAsG;QACtGR,SAAS;YACPO,KAAKP,QAAQO,GAAG;QAClB;QACAN,SAAS;YACPC,OAAOD,QAAQC,KAAK;YACpBM,KAAKP,QAAQO,GAAG;QAClB;IACF;AACF;AAEA,MAAMC,UAAU,AAAC,CAAA;QAEMC,oCASCpC;IAVtB,MAAMoC,uBAAuBd;IAC7B,MAAMe,gBAAeD,qCAAAA,oBAAoB,CAACpB,aAAa,qBAAlCoB,kCAAoC,CAACrB,SAAS;IAEnE,oDAAoD;IACpD,IAAIsB,cAAc;QAChB,OAAOA;IACT;IAEA,yHAAyH;IACzH,qDAAqD;IACrD,IAAIC,mBAAkBtC,oCAAAA,mBAAmB,CAACgB,aAAa,qBAAjChB,iCAAmC,CAACe,SAAS;IAEnE,IAAIuB,iBAAiB;QACnBrC,IAAIsC,IAAI,CACN,CAAC,0CAA0C,EAAED,gBAAgB,0DAA0D,CAAC;IAE5H,OAAO;QACLrC,IAAIsC,IAAI,CACN,CAAC,kDAAkD,EAAEvB,aAAa,CAAC,EAAED,UAAU;IAEnF;IAEA,OAAO,EAAE;AACX,CAAA;AAEA,4EAA4E;AAC5E,yEAAyE;AACzE,gDAAgD;AAChD,kEAAkE;AAClE,EAAE;AACF,yEAAyE;AACzE,MAAMyB,uCACJ5B,QAAQC,GAAG,CAAC2B,oCAAoC;AAElD,SAASC,qBAAqBC,OAAY;IACxC,MAAMC,UAAUD,QAAQC,OAAO;IAE/B,IAAIA,WAAWA,YAAYhC,aAAa;QACtCV,IAAIsC,IAAI,CACN,CAAC,yCAAyC,EAAEI,QAAQ,qBAAqB,EAAEhC,YAAY,2BAA2B,CAAC;IAEvH;AACF;AAEA,iEAAiE;AACjE,0EAA0E;AAC1E,2DAA2D;AAC3D,yEAAyE;AACzE,+DAA+D;AAC/D,MAAMiC,kCAAkC;IACtC;IACA;IACA;IACA;IACA;CAGD;AAED,oFAAoF;AACpF,gGAAgG;AAChG,oGAAoG;AACpG,IAAIC,kCAIYC;AAChB,wCAAwC;AACxC,IAAIC;AACJ,0FAA0F;AAC1F,cAAc;AACd,IAAIC;AACJ,+DAA+D;AAC/D,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,gCAA2DN;AAE/D,OAAO,MAAMO,uBAAgD,CAAC,EAAC;AAE/D;;;;;;;CAOC,GACD,OAAO,eAAeC,aACpBC,gBAAyB,KAAK;IAE9B,2FAA2F;IAC3F,IAAI,CAAC3C,QAAQC,GAAG,CAAC2C,cAAc,EAAE;QAC/B5C,QAAQC,GAAG,CAAC2C,cAAc,GAAG;IAC/B;IAEA,IAAIT,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAInC,QAAQC,GAAG,CAAC4C,cAAc,EAAE;QAC9BF,gBAAgB;IAClB;IAEA,iIAAiI;IACjI,qDAAqD;IACrD,uFAAuF;IACvF,IAAI3C,QAAQ8C,MAAM,CAACC,OAAO,IAAI,MAAM;QAClC,aAAa;QACb/C,QAAQ8C,MAAM,CAACC,OAAO,CAACC,WAAW,oBAAlChD,QAAQ8C,MAAM,CAACC,OAAO,CAACC,WAAW,MAAlChD,QAAQ8C,MAAM,CAACC,OAAO,EAAe;IACvC;IACA,IAAI/C,QAAQiD,MAAM,CAACF,OAAO,IAAI,MAAM;QAClC,aAAa;QACb/C,QAAQiD,MAAM,CAACF,OAAO,CAACC,WAAW,oBAAlChD,QAAQiD,MAAM,CAACF,OAAO,CAACC,WAAW,MAAlChD,QAAQiD,MAAM,CAACF,OAAO,EAAe;IACvC;IAEAZ,kBAAkB,IAAIe,QAAQ,OAAOC,SAASC;QAC5C,IAAI,CAACX,qBAAqBY,GAAG,EAAE;YAC7B,yDAAyD;YACzD,0CAA0C;YAC1CZ,qBAAqBY,GAAG,GAAG7D,uBAAuBQ,QAAQsD,GAAG,IAAIC,KAAK,CACpEC,QAAQC,KAAK;QAEjB;QAEA,IAAIC,WAAkB,EAAE;QACxB,MAAMC,sBAAsB3D,QAAQC,GAAG,CAAC2D,qBAAqB;QAC7D,MAAMC,sBAAsBtC,QAAQuC,IAAI,CACtC,CAAC3C,SACC,CAAC,EAACA,0BAAAA,OAAQ4C,GAAG,KAAI/B,gCAAgCgC,QAAQ,CAAC7C,OAAO4C,GAAG;QAExE,MAAME,iBAAiBjE,QAAQkE,QAAQ,CAACC,YAAY;QACpD,yEAAyE;QACzE,yFAAyF;QACzF,MAAMC,8BACJ,AAAC,CAACT,uBAAuBhB,iBACzBkB,uBACAI;QAEF,IAAI,CAACJ,uBAAuBlB,eAAe;YACzCtD,IAAIsC,IAAI,CACN,CAAC,mEAAmE,EAAEvB,aAAa,CAAC,EAAED,SAAS,qBAAqB,CAAC;QAEzH;QAEA,IAAIiE,6BAA6B;YAC/BnC,kCAAkC;YAClC,MAAMoC,mBAAmB,MAAMC,wBAAwBZ;YACvD,IAAIW,kBAAkB;gBACpB,OAAOlB,QAAQkB;YACjB;QACF;QAEA,4CAA4C;QAC5C,EAAE;QACF,kEAAkE;QAClE,0GAA0G;QAC1G,gHAAgH;QAChH,kHAAkH;QAClH,kDAAkD;QAClD,uDAAuD;QACvD,IAAI;YACF,OAAOlB,QAAQoB;QACjB,EAAE,OAAOC,GAAG;YACV,IACEC,MAAMC,OAAO,CAACF,MACdA,EAAEG,KAAK,CAAC,CAACC,IAAMA,EAAEZ,QAAQ,CAAC,0BAC1B;gBACA,IAAIK,mBAAmB,MAAMQ,0BAA0BnB;gBAEvD,IAAIW,kBAAkB;oBACpB,OAAOlB,QAAQkB;gBACjB;YACF;YAEAX,WAAWA,SAASoB,MAAM,CAACN;QAC7B;QAEA,+EAA+E;QAC/E,IAAI,CAACJ,+BAA+B,CAACT,qBAAqB;YACxD,MAAMU,mBAAmB,MAAMC,wBAAwBZ;YACvD,IAAIW,kBAAkB;gBACpB,OAAOlB,QAAQkB;YACjB;QACF;QAEAU,eAAerB,UAAU;IAC3B;IACA,OAAOvB;AACT;AAEA,eAAe0C,0BAA0BnB,QAAuB;IAC9D,MAAMsB,0BAA0BhG,KAAKiG,IAAI,CACvCjG,KAAKkG,OAAO,CAACC,QAAQhC,OAAO,CAAC,uBAC7B;IAGF,IAAI,CAACX,+BAA+B;QAClCA,gCAAgC/C,sBAC9BM,aACAiF,yBACAzD,QAAQ6D,GAAG,CAAC,CAACjE,SAAgBA,OAAOkE,eAAe;IAEvD;IACA,MAAM7C;IAEN,IAAI;QACF,OAAO+B,WAAWS;IACpB,EAAE,OAAOR,GAAQ;QACfd,SAAS4B,IAAI,IAAI,EAAE,CAACR,MAAM,CAACN;IAC7B;IAEA,OAAOtC;AACT;AAEA,0BAA0B;AAC1B,eAAeoC,wBACbZ,QAAe;IAEf,IAAI;QACF,IAAI6B,WAAW,MAAMC,SAAS;QAC9B,sDAAsD;QACtDjG,oBAAoB;YAClBkG,MAAM;YACNC,yBAAyBzD;QAC3B;QACA,OAAOsD;IACT,EAAE,OAAOf,GAAQ;QACfd,SAAS4B,IAAI,IAAI,EAAE,CAACR,MAAM,CAACN;IAC7B;IAEA,IAAI;QACF,2DAA2D;QAC3D,+DAA+D;QAC/D,sEAAsE;QACtE,sDAAsD;QACtD,MAAMmB,gBAAgB3G,KAAKiG,IAAI,CAC7BjG,KAAKkG,OAAO,CAACC,QAAQhC,OAAO,CAAC,uBAC7B;QAEF,IAAI,CAACb,qBAAqB;YACxBA,sBAAsB5C,gBAAgBK,aAAa4F;QACrD;QACA,MAAMrD;QACN,IAAIiD,WAAW,MAAMC,SAASG;QAC9B,sDAAsD;QACtDpG,oBAAoB;YAClBkG,MAAM;YACNC,yBAAyBzD;QAC3B;QAEA,4CAA4C;QAC5C,sCAAsC;QACtC,KAAK,MAAM2D,WAAWlC,SAAU;YAC9BrE,IAAIsC,IAAI,CAACiE;QACX;QACA,OAAOL;IACT,EAAE,OAAOf,GAAQ;QACfd,SAAS4B,IAAI,IAAI,EAAE,CAACR,MAAM,CAACN;IAC7B;AACF;AAEA,SAASqB;IACP,IAAInC,WAAkB,EAAE;IACxB,IAAI;QACF,OAAOa;IACT,EAAE,OAAOC,GAAG;QACVd,WAAWA,SAASoB,MAAM,CAACN;IAC7B;IAEA,qEAAqE;IACrE,qCAAqC;IACrC,IAAInC,cAAc;QAChB,OAAOA;IACT;IAEA0C,eAAerB;IACf,MAAM,qBAAyD,CAAzD,IAAIoC,MAAM,2BAA2B;QAAEC,OAAOrC;IAAS,IAAvD,qBAAA;eAAA;oBAAA;sBAAA;IAAwD;AAChE;AAEA,IAAIsC,qBAAqB;AAEzB,SAASjB,eAAerB,QAAa,EAAEuC,YAAY,KAAK;IACtD,4DAA4D;IAC5D,IAAID,oBAAoB;IACxBA,qBAAqB;IAErB,KAAK,IAAIJ,WAAWlC,SAAU;QAC5BrE,IAAIsC,IAAI,CAACiE;IACX;IAEA,sDAAsD;IACtDrG,oBAAoB;QAClBkG,MAAMQ,YAAY,WAAW/D;QAC7BwD,yBAAyBzD;IAC3B,GACGiE,IAAI,CAAC,IAAMzD,qBAAqBY,GAAG,IAAIH,QAAQC,OAAO,IACtDgD,OAAO,CAAC;QACP9G,IAAIoE,KAAK,CACP,CAAC,8BAA8B,EAAErD,aAAa,CAAC,EAAED,SAAS,yEAAyE,CAAC;QAEtIH,QAAQoG,IAAI,CAAC;IACf;AACJ;AAKA,OAAO,SAASC,gBAAgB,EAC9BC,WAAW,EACXC,mBAAmB,EACnBC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,kBAAkB,EAClBC,QAAQ,EAIT;IACC,IAAIC,YAAuB;QACzBC,QAAQ,EAAE;QACVC,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ;IAEA,KAAK,MAAMC,WAAWC,OAAOC,IAAI,CAACN,WAA0C;QAC1EA,SAAS,CAACI,QAAQ,GAAGG,iBACnB3H,aAAa;YACX0G;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAW,UAAUJ,YAAY;YACtBK,cAAcL,YAAY;YAC1BM,cAAcN,YAAY;YAC1BN;YACAC;QACF;IAEJ;IAEA,OAAOC;AACT;AAEA,SAASW,WAAW1H,GAA2B;IAC7C,OAAOoH,OAAOO,OAAO,CAAC3H,KACnBiB,MAAM,CAAC,CAAC,CAAC2G,GAAGC,MAAM,GAAKA,SAAS,MAChC1C,GAAG,CAAC,CAAC,CAAC2C,MAAMD,MAAM,GAAM,CAAA;YACvBC;YACAD;QACF,CAAA;AACJ;AAEA,SAASP,iBACPtH,GAAuC;IAEvC,OAAOoH,OAAOO,OAAO,CAAC3H,KAAKmF,GAAG,CAAC,CAAC,CAAC2C,MAAMD,MAAM,GAAM,CAAA;YACjDC;YACAD;QACF,CAAA;AACF;AAEA,mCAAmC;AACnC,SAASE,aACPC,OAAoB,EACpBC,KAAc;IA0Dd,MAAMC,SAAS,IAAK,MAAMC,eAAetC;IAAO;IAEhD;;GAEC,GACD,SAASuC,UACPC,KAAY,EACZC,cAAoC;QAEpC,MAAM,qBAAgD,CAAhD,IAAIzC,MAAM,CAAC,WAAW,EAAEyC,eAAeD,QAAQ,GAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAA+C;IACvD;IAEA;;;;;GAKC,GACD,SAASE,UACPC,SAAkB,EAClBC,cAEiE;QAKjE,mEAAmE;QACnE,wCAAwC;QACxC,IAAIC,SAAuB,EAAE;QAC7B,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC;QAMJ,IAAIC,WAAW;QAEf,0EAA0E;QAC1E,2EAA2E;QAC3E,2BAA2B;QAC3B,SAASC,WAAWC,GAAsB,EAAEjB,KAAoB;YAC9D,IAAIc,SAAS;gBACX,IAAI,EAAEzF,OAAO,EAAE6F,MAAM,EAAE,GAAGJ;gBAC1BA,UAAU1G;gBACV,IAAI6G,KAAKC,OAAOD;qBACX5F,QAAQ2E;YACf,OAAO;gBACL,MAAMmB,OAAO;oBAAEF;oBAAKjB;gBAAM;gBAC1B,IAAIW,WAAWE,OAAOrD,IAAI,CAAC2D;qBACtBN,MAAM,CAAC,EAAE,GAAGM;YACnB;QACF;QAEA,gBAAgBC;YACd,MAAMC,OAAO,MAAMT,eAAeI;YAClC,IAAI;gBACF,MAAO,CAACD,SAAU;oBAChB,IAAIF,OAAOS,MAAM,GAAG,GAAG;wBACrB,MAAMH,OAAON,OAAOU,KAAK;wBACzB,IAAIJ,KAAKF,GAAG,EAAE,MAAME,KAAKF,GAAG;wBAC5B,MAAME,KAAKnB,KAAK;oBAClB,OAAO;wBACL,wCAAwC;wBACxC,MAAM,IAAI5E,QAAW,CAACC,SAAS6F;4BAC7BJ,UAAU;gCAAEzF;gCAAS6F;4BAAO;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAOM,GAAG;gBACV,IAAIA,MAAMnB,QAAQ;gBAClB,MAAMmB;YACR,SAAU;gBACR,IAAIH,MAAM;oBACRlB,QAAQsB,eAAe,CAACJ;gBAC1B;YACF;QACF;QAEA,MAAMK,WAAWN;QACjBM,SAASC,MAAM,GAAG;YAChBZ,WAAW;YACX,IAAID,SAASA,QAAQI,MAAM,CAACb;YAC5B,OAAO;gBAAEL,OAAO5F;gBAAWwH,MAAM;YAAK;QACxC;QACA,OAAOF;IACT;IAEA,eAAeG,sBACbC,OAAuB;QAEvB,OAAO;YACL,GAAGA,OAAO;YACVC,YAAY,MAAMC,oBAChBF,QAAQC,UAAU,EAClB7K,KAAKiG,IAAI,CAAC2E,QAAQG,QAAQ,EAAEH,QAAQjD,WAAW;YAEjDqD,UAAUC,KAAKC,SAAS,CAACN,QAAQI,QAAQ;YACzC/J,KAAK0H,WAAWiC,QAAQ3J,GAAG;QAC7B;IACF;IAEA,eAAekK,6BACbP,OAAgC;QAEhC,OAAO;YACL,GAAGA,OAAO;YACVC,YACED,QAAQC,UAAU,IACjB,MAAMC,oBACLF,QAAQC,UAAU,EAClB7K,KAAKiG,IAAI,CAAC2E,QAAQG,QAAQ,EAAGH,QAAQjD,WAAW;YAEpDqD,UAAUJ,QAAQI,QAAQ,IAAIC,KAAKC,SAAS,CAACN,QAAQI,QAAQ;YAC7D/J,KAAK2J,QAAQ3J,GAAG,IAAI0H,WAAWiC,QAAQ3J,GAAG;QAC5C;IACF;IAEA,MAAMmK;QAGJC,YAAYC,aAAwC,CAAE;YACpD,IAAI,CAACC,cAAc,GAAGD;QACxB;QAEA,MAAME,OAAOZ,OAAgC,EAAE;YAC7C,MAAM3B,QAAQwC,aAAa,CACzB,IAAI,CAACF,cAAc,EACnB,MAAMJ,6BAA6BP;QAEvC;QAEA,MAAMc,0BACJC,UAAmB,EACuB;YAC1C,MAAMC,gBAAiB,MAAM3C,QAAQ4C,gCAAgC,CACnE,IAAI,CAACN,cAAc,EACnBI;YAGF,OAAOG,gCAAgCF;QACzC;QAEAG,uBAAuB;YACrB,MAAMC,eAAexC,UACnB,OACA,OAAOyC,WACLhD,QAAQiD,2BAA2B,CAAC,IAAI,CAACX,cAAc,EAAEU;YAE7D,OAAO,AAAC;gBACN,WAAW,MAAME,eAAeH,aAAc;oBAC5C,MAAMF,gCAAgCK;gBACxC;YACF;QACF;QAEAC,UAAUC,UAAkB,EAAE;YAC5B,OAAO7C,UAAmC,MAAM,OAAOyC,WACrDhD,QAAQqD,gBAAgB,CAAC,IAAI,CAACf,cAAc,EAAEc,YAAYJ;QAE9D;QAEAM,0BAA0B;YACxB,OAAO/C,UACL,OACA,OAAOyC,WACLhD,QAAQuD,8BAA8B,CAAC,IAAI,CAACjB,cAAc,EAAEU;QAElE;QAEAQ,YACEC,UAA+B,EAC/BC,uBAA+B,EACM;YACrC,OAAO1D,QAAQ2D,kBAAkB,CAC/B,IAAI,CAACrB,cAAc,EACnBmB,YACAC;QAEJ;QAEAE,kBAAkBC,QAAgB,EAA0B;YAC1D,OAAO7D,QAAQ8D,wBAAwB,CAAC,IAAI,CAACxB,cAAc,EAAEuB;QAC/D;QAEAE,aAAaF,QAAgB,EAA0B;YACrD,OAAO7D,QAAQgE,mBAAmB,CAAC,IAAI,CAAC1B,cAAc,EAAEuB;QAC1D;QAEAI,iBAAiBJ,QAAgB,EAAiB;YAChD,OAAO7D,QAAQkE,uBAAuB,CAAC,IAAI,CAAC5B,cAAc,EAAEuB;QAC9D;QAEAM,oBAAoBC,aAAqB,EAAE;YACzC,OAAO7D,UAA0C,MAAM,OAAOyC,WAC5DhD,QAAQqE,0BAA0B,CAChC,IAAI,CAAC/B,cAAc,EACnB8B,eACApB;QAGN;QAEAsB,2BAA2BC,UAAqB,EAAE;YAChD,OAAOhE,UACL,MACA,OAAOyC;gBACLhD,QAAQwE,iCAAiC,CACvC,IAAI,CAAClC,cAAc,EACnBU,UACAuB;YAEJ;QAEJ;QAEAE,4BAA2C;YACzC,OAAOzE,QAAQ0E,gCAAgC,CAAC,IAAI,CAACpC,cAAc;QACrE;QAEAqC,WAA0B;YACxB,OAAO3E,QAAQ4E,eAAe,CAAC,IAAI,CAACtC,cAAc;QACpD;QAEAuC,SAAwB;YACtB,OAAO7E,QAAQ8E,aAAa,CAAC,IAAI,CAACxC,cAAc;QAClD;IACF;IAEA,MAAMyC;QAGJ3C,YAAY4C,cAA0C,CAAE;YACtD,IAAI,CAACC,eAAe,GAAGD;QACzB;QAEA,MAAME,cAAyD;YAC7D,OAAQ,MAAMlF,QAAQmF,mBAAmB,CACvC,IAAI,CAACF,eAAe;QAExB;QAEA,MAAMG,gBAAqE;YACzE,MAAMC,qBAAqB9E,UACzB,OACA,OAAOyC,WACLhD,QAAQsF,8BAA8B,CAAC,IAAI,CAACL,eAAe,EAAEjC;YAEjE,MAAMqC,mBAAmBE,IAAI;YAC7B,OAAOF;QACT;QAEA,MAAMG,cACJC,aAAsB,EAC+B;YACrD,MAAMC,qBAAqBnF,UACzB,OACA,OAAOyC,WACLhD,QAAQ2F,8BAA8B,CACpC,IAAI,CAACV,eAAe,EACpBQ,eACAzC;YAGN,MAAM0C,mBAAmBH,IAAI;YAC7B,OAAOG;QACT;IACF;IAEA;;;;;GAKC,GACD,SAASE,kBACPC,kBAAsC,EACtCnH,WAAmB;YAIUkD;QAF7B,IAAIA,aAAa;YAAE,GAAIiE,kBAAkB;QAAwB;QAEjE,MAAMC,wBAAuBlE,2BAAAA,WAAWmE,YAAY,qBAAvBnE,yBAAyBoE,aAAa;QAEnE,gHAAgH;QAChH,mFAAmF;QACnF,IAAIF,sBAAsB;gBAKVlE;YAJd,MAAMD,UACJ,OAAOmE,yBAAyB,WAAWA,uBAAuB,CAAC;YACrE,MAAMG,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;aAAQ;YACnD,IACE7G,OAAOC,IAAI,CAACuC,CAAAA,+BAAAA,wBAAAA,WAAYsE,SAAS,qBAArBtE,sBAAuBuE,KAAK,KAAI,CAAC,GAAGtK,IAAI,CAAC,CAACuK,MACpDH,SAASlK,QAAQ,CAACqK,OAEpB;gBACAhP,IAAIsC,IAAI,CACN,2FACE,+EACA,qFACA;YAEN,OAAO;gBACLkI,WAAWsE,SAAS,KAAK,CAAC;gBAC1BtE,WAAWsE,SAAS,CAACG,UAAU,KAAK,CAAC;gBACrCzE,WAAWsE,SAAS,CAACC,KAAK,KAAK,CAAC;gBAEhC,KAAK,MAAMC,OAAOH,SAAU;oBAC1BrE,WAAWsE,SAAS,CAACG,UAAU,CAAC,CAAC,eAAe,EAAED,KAAK,CAAC,GAAG;wBACzDrP,MAAMqP;wBACNE,SACE3E,QAAQ4E,eAAe,KAAK,eACxB,qBACA,CAAC5E,QAAQ4E,eAAe,IACtB5E,QAAQ4E,eAAe,KAAK,UAE5B,wCACAtM;oBACV;oBACA2H,WAAWsE,SAAS,CAACC,KAAK,CAAC,CAAC,eAAe,EAAEC,KAAK,CAAC,GAAG;wBACpDI,SAAS;4BACPC,SAAS;4BACTC,SAAS;gCACP9O,uBACEkO,sBACApH,aACAkD,WAAWpD,GAAG,EACd,YAAY,GAAG,OACf,wBAAwB,GAAGvE;6BAE9B;wBACH;oBACF;gBACF;YACF;QACF;QAEA,OAAO2H;IACT;IAEA,eAAeC,oBACbD,UAA8B,EAC9BlD,WAAmB;YAYfiI,mCAqCFA;QA/CF,mDAAmD;QACnD,IAAIA,yBAAyBf,kBAAkBhE,YAAYlD;QAE3DiI,uBAAuBC,eAAe,GACpC,OAAMhF,WAAWgF,eAAe,oBAA1BhF,WAAWgF,eAAe,MAA1BhF;QAER,iFAAiF;QACjF+E,uBAAuBE,aAAa,GAAG,CAAC;QACxCF,uBAAuBG,OAAO,GAAGlF,WAAWkF,OAAO,IAAI,CAAC;QAExD,KAAIH,oCAAAA,uBAAuBT,SAAS,qBAAhCS,kCAAkCR,KAAK,EAAE;gBAEzCQ;YADFI,sCACEJ,qCAAAA,uBAAuBT,SAAS,qBAAhCS,mCAAkCR,KAAK;QAE3C;QAEAQ,uBAAuBK,iBAAiB,GACtCL,uBAAuBK,iBAAiB,GACpC5H,OAAO6H,WAAW,CAChB7H,OAAOO,OAAO,CAAMgH,uBAAuBK,iBAAiB,EAAE7J,GAAG,CAC/D,CAAC,CAAC+J,KAAK3I,OAAO,GAAK;gBACjB2I;gBACA;oBACE,GAAG3I,MAAM;oBACT4I,WACE,OAAO5I,OAAO4I,SAAS,KAAK,WACxB5I,OAAO4I,SAAS,GAChB/H,OAAOO,OAAO,CAACpB,OAAO4I,SAAS,EAAEhK,GAAG,CAAC,CAAC,CAACiJ,KAAKvG,MAAM,GAAK;4BACrDuG;4BACAvG;yBACD;gBACT;aACD,KAGL5F;QAEN,2EAA2E;QAC3E,IAAI0M,uBAAuBS,MAAM,CAACC,UAAU,EAAE;YAC5CV,uBAAuBS,MAAM,GAAG;gBAC9B,GAAGxF,WAAWwF,MAAM;gBACpBC,YACE,OAAOtQ,KAAKuQ,QAAQ,CAAC5I,aAAakD,WAAWwF,MAAM,CAACC,UAAU;YAClE;QACF;QAEA,MAAMhB,cACJM,qCAAAA,uBAAuBT,SAAS,qBAAhCS,mCAAkCN,UAAU;QAC9C,IAAIA,YAAY;YAUd,SAASkB,gBAAgBC,KAAa;gBAIpC,OAAO;oBACLC,QAAQD,MAAMC,MAAM;oBACpBC,OAAOF,MAAME,KAAK;gBACpB;YACF;YAEA,MAAMC,uBAA6C,CAAC;YACpD,KAAK,MAAM,CAACvB,KAAKvG,MAAM,IAAIT,OAAOO,OAAO,CAAC0G,YAAa;gBACrDsB,oBAAoB,CAACvB,IAAI,GAAG;oBAC1B,GAAGvG,KAAK;oBACR9I,MAAM,CAAC8I,MAAM9I,IAAI,GACbkD,YACA4F,MAAM9I,IAAI,YAAY6Q,SACpB;wBACEC,MAAM;wBACNhI,OAAO0H,gBAAgB1H,MAAM9I,IAAI;oBACnC,IACA;wBAAE8Q,MAAM;wBAAQhI,OAAOA,MAAM9I,IAAI;oBAAC;oBACxCuP,SAAS,CAACzG,MAAMyG,OAAO,GAAGrM,YAAYsN,gBAAgB1H,MAAMyG,OAAO;gBACrE;YACF;YACAK,uBAAuBT,SAAS,CAACG,UAAU,GAAGsB;QAChD;QAEA,OAAO3F,KAAKC,SAAS,CAAC0E,wBAAwB,MAAM;IACtD;IAEA,SAASI,qCACPe,cAAiE;QAEjE,KAAK,MAAM,CAACC,MAAMC,KAAK,IAAI5I,OAAOO,OAAO,CAACmI,gBAAiB;YACzD,IAAItL,MAAMC,OAAO,CAACuL,OAAO;gBACvBC,iBAAiBD,MAAMD;YACzB,OAAO;gBACLG,gBAAgBF,MAAMD;YACxB;QACF;QAEA,SAASG,gBAAgBF,IAA6B,EAAED,IAAY;YAClE,IAAI,CAACC,MAAM;YACX,IAAI,aAAaA,MAAM;gBACrBC,iBAAiB,AAACD,KAAwCtB,OAAO,EAAEqB;YACrE,OAAO;gBACL,KAAK,MAAMlI,SAAST,OAAO+I,MAAM,CAACH,MAAO;oBACvC,IAAI,OAAOnI,UAAU,YAAYA,OAAO;wBACtCqI,gBAAgBrI,OAAOkI;oBACzB;gBACF;YACF;QACF;QAEA,SAASE,iBACPG,WAAkC,EAClCL,IAAY;YAEZ,KAAK,MAAMM,cAAcD,YAAa;gBACpC,IACE,OAAOC,eAAe,YACtB,CAAC3Q,kBAAkB2Q,YAAYrG,KAAKsG,KAAK,CAACtG,KAAKC,SAAS,CAACoG,eACzD;oBACA,MAAM,qBAEL,CAFK,IAAIxK,MACR,CAAC,OAAO,EAAEwK,WAAWE,MAAM,CAAC,YAAY,EAAER,KAAK,yGAAyG,CAAC,GADrJ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,SAASlF,gCACPK,WAA6C;QAE7C,MAAMsF,SAAS,IAAIC;QACnB,KAAK,MAAM,EAAEC,QAAQ,EAAE,GAAGC,aAAa,IAAIzF,YAAYsF,MAAM,CAAE;YAC7D,IAAII;YACJ,MAAMC,YAAYF,YAAYd,IAAI;YAClC,OAAQgB;gBACN,KAAK;oBACHD,QAAQ;wBACNf,MAAM;wBACNiB,cAAc,IAAI/D,aAAa4D,YAAYG,YAAY;wBACvDC,cAAc,IAAIhE,aAAa4D,YAAYI,YAAY;oBACzD;oBACA;gBACF,KAAK;oBACHH,QAAQ;wBACNf,MAAM;wBACNmB,UAAU,IAAIjE,aAAa4D,YAAYK,QAAQ;oBACjD;oBACA;gBACF,KAAK;oBACHJ,QAAQ;wBACNf,MAAM;wBACNoB,OAAON,YAAYM,KAAK,CAAC9L,GAAG,CAAC,CAAC+L,OAAU,CAAA;gCACtCC,cAAcD,KAAKC,YAAY;gCAC/BL,cAAc,IAAI/D,aAAamE,KAAKJ,YAAY;gCAChDM,aAAa,IAAIrE,aAAamE,KAAKE,WAAW;4BAChD,CAAA;oBACF;oBACA;gBACF,KAAK;oBACHR,QAAQ;wBACNf,MAAM;wBACNsB,cAAcR,YAAYQ,YAAY;wBACtCH,UAAU,IAAIjE,aAAa4D,YAAYK,QAAQ;oBACjD;oBACA;gBACF,KAAK;oBACHJ,QAAQ;wBACNf,MAAM;oBACR;oBACA;gBACF;oBACE,MAAMwB,mBAA0BR;oBAChCzI,UACEuI,aACA,IAAM,CAAC,oBAAoB,EAAEU,kBAAkB;YAErD;YACAb,OAAOc,GAAG,CAACZ,UAAUE;QACvB;QACA,MAAMW,6BAA6B,CAACC,aAAgC,CAAA;gBAClER,UAAU,IAAIjE,aAAayE,WAAWR,QAAQ;gBAC9CS,SAASD,WAAWC,OAAO;gBAC3BC,SAASF,WAAWE,OAAO;YAC7B,CAAA;QACA,MAAMF,aAAatG,YAAYsG,UAAU,GACrCD,2BAA2BrG,YAAYsG,UAAU,IACjDvP;QACJ,MAAM0P,uCAAuC,CAC3CC,kBACI,CAAA;gBACJC,QAAQ,IAAI9E,aAAa6E,gBAAgBC,MAAM;gBAC/C5K,MAAM,IAAI8F,aAAa6E,gBAAgB3K,IAAI;YAC7C,CAAA;QACA,MAAM2K,kBAAkB1G,YAAY0G,eAAe,GAC/CD,qCAAqCzG,YAAY0G,eAAe,IAChE3P;QAEJ,OAAO;YACLuO;YACAgB;YACAI;YACAE,uBAAuB,IAAI/E,aACzB7B,YAAY4G,qBAAqB;YAEnCC,kBAAkB,IAAIhF,aAAa7B,YAAY6G,gBAAgB;YAC/DC,oBAAoB,IAAIjF,aAAa7B,YAAY8G,kBAAkB;YACnEC,QAAQ/G,YAAY+G,MAAM;YAC1BC,aAAahH,YAAYgH,WAAW;QACtC;IACF;IAEA,OAAO,eAAeC,cACpBxI,OAAuB,EACvByI,kBAAkB;QAElB,OAAO,IAAIjI,YACT,MAAMnC,QAAQqK,UAAU,CACtB,MAAM3I,sBAAsBC,UAC5ByI,sBAAsB,CAAC,GACvB;YACEvS;QACF;IAGN;AACF;AAEA,sBAAsB;AACtB,eAAeyS,oBAAoBC,aAAa,EAAE;IAChD,IAAI9O,WAAW,EAAE;IAEjB,gGAAgG;IAChG,kCAAkC;IAClC,MAAM+O,cAAczS,QAAQC,GAAG,CAACyS,kBAAkB;IAElD,IAAID,aAAa;QACf,0EAA0E;QAC1E,MAAME,cAAc,MAAM,MAAM,CAC9B1T,cAAcD,KAAKiG,IAAI,CAACwN,aAAa,YAAYG,QAAQ;QAE3DvS,QAAQ,CAAC,2BAA2B,EAAEoS,aAAa;QACnD,OAAOE;IACT,OAAO;QACL,KAAK,IAAIE,OAAO;YAAC;YAAyB;SAAqB,CAAE;YAC/D,IAAI;gBACF,IAAIC,UAAUD;gBAEd,IAAIL,YAAY;oBACd,yDAAyD;oBACzDM,UAAU9T,KAAKiG,IAAI,CAACuN,YAAYK,KAAK;gBACvC;gBACA,MAAME,sBAAsB,MAAM,MAAM,CACtC9T,cAAc6T,SAASF,QAAQ;gBAEjC,IAAID;gBACJ,IAAIE,QAAQ,sBAAsB;oBAChC,+EAA+E;oBAC/E,oDAAoD;oBACpDF,cAAc,MAAMI,oBAAoBC,OAAO;gBACjD,OAAO;oBACLL,cAAcI;gBAChB;gBACA1S,QAAQ,CAAC,2BAA2B,EAAEwS,KAAK;gBAC3C,OAAOF;YACT,EAAE,OAAOrJ,GAAQ;gBACf,8DAA8D;gBAC9D,IAAIkJ,YAAY;oBACd,IAAIlJ,CAAAA,qBAAAA,EAAG2J,IAAI,MAAK,wBAAwB;wBACtCvP,SAAS4B,IAAI,CAAC,CAAC,kBAAkB,EAAEuN,IAAI,0BAA0B,CAAC;oBACpE,OAAO;wBACLnP,SAAS4B,IAAI,CACX,CAAC,kBAAkB,EAAEuN,IAAI,yBAAyB,EAAEvJ,EAAE4J,OAAO,IAAI5J,GAAG;oBAExE;gBACF;YACF;QACF;IACF;IAEA,MAAM5F;AACR;AAEA,qDAAqD;AACrD,eAAe8B,SAASgN,aAAa,EAAE;IACrC,MAAMG,cAAc,MAAMJ,oBAAoBC;IAE9C,SAASW,gBAAgBC,GAAQ;QAC/B,8FAA8F;QAC9F,4EAA4E;QAC5E,EAAE;QACF,sFAAsF;QACtF,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,MAAM;YAC3C,OAAOA;QACT;QACA,IAAI3O,MAAMC,OAAO,CAAC0O,MAAM;YACtB,OAAOA,IAAIhO,GAAG,CAAC+N;QACjB;QACA,MAAME,SAAiC,CAAC;QACxC,KAAK,MAAM,CAACC,GAAGC,EAAE,IAAIlM,OAAOO,OAAO,CAACwL,KAAM;YACxC,IAAI,OAAOG,MAAM,aAAa;gBAC5BF,MAAM,CAACC,EAAE,GAAGH,gBAAgBI;YAC9B;QACF;QACA,OAAOF;IACT;IAEA,mEAAmE;IACnE,yCAAyC;IACzChR,eAAe;QACbmR,KAAK;YACHC,WAAW;gBACTrE,WAAW,SAAUsE,QAAa;oBAChC,MAAM,qBAEL,CAFK,IAAI5N,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA6N,oBAAoB,SAAUD,QAAa;oBACzC,MAAM,qBAEL,CAFK,IAAI5N,MACR,8EADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QACA8N,QAAQ;QACRxE,WAAUyE,GAAW,EAAEjK,OAAY;YACjC,OAAO+I,YAAYvD,SAAS,CAACyE,IAAIjB,QAAQ,IAAIO,gBAAgBvJ;QAC/D;QACAkK,eAAcD,GAAW,EAAEjK,OAAY;YACrC,OAAO+I,YAAYmB,aAAa,CAACD,IAAIjB,QAAQ,IAAIO,gBAAgBvJ;QACnE;QACAmK,QAAOF,GAAW,EAAEjK,OAAY;YAC9B,OAAO+I,YAAYoB,MAAM,CAACF,IAAIjB,QAAQ,IAAIO,gBAAgBvJ;QAC5D;QACAoK,YAAWH,GAAW,EAAEjK,OAAY;YAClC,OAAO+I,YAAYqB,UAAU,CAACH,IAAIjB,QAAQ,IAAIO,gBAAgBvJ;QAChE;QACA2G,OAAMsD,GAAW,EAAEjK,OAAY;YAC7B,OAAO+I,YAAYpC,KAAK,CAACsD,IAAIjB,QAAQ,IAAIO,gBAAgBvJ;QAC3D;QACAqK;YACE,OAAO/R;QACT;QACAgS,OAAO;YACL9B,eACEsB,QAAwB,EACxBS,mBAAoD;gBAEpD,MAAM,qBAEL,CAFK,IAAIrO,MACR,iEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAsO,2BAA0BC,cAAsB;gBAC9C,MAAM,qBAEL,CAFK,IAAIvO,MACR,6EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACAwO,KAAK;YACHC,SAAQV,GAAW,EAAEjK,OAAY;gBAC/B,OAAO+I,YAAY6B,UAAU,CAC3BX,KACAV,gBAAgBsB,cAAc7K;YAElC;YACA8K,aAAYb,GAAW,EAAEjK,OAAY;gBACnC,OAAO+I,YAAYgC,cAAc,CAC/Bd,KACAV,gBAAgBsB,cAAc7K;YAElC;QACF;QACAqE,eAAe;YACb2G,yBAAwBC,SAAiB;gBACvC,OAAO3R,QAAQC,OAAO,CAAC;YACzB;QACF;QACA2R,QAAQ;YACNC,uBAAsBC,aAAqB;gBACzC,MAAM,qBAEL,CAFK,IAAIlP,MACR,0EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAmP,oBACEC,OAAe,EACfC,aAAsB;gBAEtB,MAAM,qBAEL,CAFK,IAAIrP,MACR,uEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACAsP,sBACE7G,OAAe,EACf8G,YAAoB,EACpBC,kBAA0B,EAC1BC,YAA6C,EAC7CC,UAAkC,EAClCC,OAAsC;YAEtC,OAAO9C,YAAYyC,oBAAoB,CACrC7G,SACA8G,cACAC,oBACAC,cACAC,YACAC;QAEJ;IACF;IACA,OAAOpT;AACT;AAEA;;;CAGC,GACD,SAASkC,WAAWiO,UAAmB;IACrC,IAAIpQ,gBAAgB;QAClB,OAAOA;IACT;IAEA,IAAIpC,QAAQC,GAAG,CAAC4C,cAAc,EAAE;QAC9B,MAAM,qBAA+D,CAA/D,IAAIiD,MAAM,uDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEA,MAAM4P,iBAA8B,CAAC,CAAC9T,uCAClCuD,QAAQvD,wCACR;IACJ,IAAI2D,WAAwBmQ;IAC5B,IAAIhS,WAAkB,EAAE;IAExB,MAAMiS,uBAAuB3V,QAAQC,GAAG,CAAC0V,oBAAoB;IAC7D,KAAK,MAAMxU,UAAUI,QAAS;QAC5B,IAAIoU,sBAAsB;YACxB,IAAI;gBACF,2GAA2G;gBAC3GpQ,WAAWJ,QACT,GAAGwQ,qBAAqB,UAAU,EAAExU,OAAOkE,eAAe,CAAC,KAAK,CAAC;gBAEnEhF,QACE;gBAEF;YACF,EAAE,OAAOiJ,GAAG,CAAC;QACf,OAAO;YACL,IAAI;gBACF/D,WAAWJ,QACT,CAAC,0BAA0B,EAAEhE,OAAOkE,eAAe,CAAC,KAAK,CAAC;gBAE5DhF,QAAQ;gBACR;YACF,EAAE,OAAOiJ,GAAG,CAAC;QACf;IACF;IAEA,IAAI,CAAC/D,UAAU;QACb,KAAK,MAAMpE,UAAUI,QAAS;YAC5B,IAAIsR,MAAML,aACNxT,KAAKiG,IAAI,CACPuN,YACA,CAAC,UAAU,EAAErR,OAAOkE,eAAe,EAAE,EACrC,CAAC,SAAS,EAAElE,OAAOkE,eAAe,CAAC,KAAK,CAAC,IAE3C,CAAC,UAAU,EAAElE,OAAOkE,eAAe,EAAE;YACzC,IAAI;gBACFE,WAAWJ,QAAQ0N;gBACnB,IAAI,CAACL,YAAY;oBACf3Q,qBAAqBsD,QAAQ,GAAG0N,IAAI,aAAa,CAAC;gBACpD;gBACA;YACF,EAAE,OAAOvJ,GAAQ;gBACf,IAAIA,CAAAA,qBAAAA,EAAG2J,IAAI,MAAK,oBAAoB;oBAClCvP,SAAS4B,IAAI,CAAC,CAAC,kBAAkB,EAAEuN,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLnP,SAAS4B,IAAI,CACX,CAAC,kBAAkB,EAAEuN,IAAI,yBAAyB,EAAEvJ,EAAE4J,OAAO,IAAI5J,GAAG;gBAExE;gBACArH,kCAAkCqH,CAAAA,qBAAAA,EAAG2J,IAAI,KAAI;YAC/C;QACF;IACF;IAEA,IAAI1N,UAAU;QACZnD,iBAAiB;YACfwR,QAAQ;YACRxE,WAAUyE,GAAW,EAAEjK,OAAY;oBAO7BA;gBANJ,MAAMgM,WACJ,OAAO/B,QAAQ,eACf,OAAOA,QAAQ,YACf,CAACgC,OAAOC,QAAQ,CAACjC;gBACnBjK,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASmM,GAAG,qBAAZnM,aAAcoM,MAAM,EAAE;oBACxBpM,QAAQmM,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGrM,QAAQmM,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAO1Q,SAAS6J,SAAS,CACvBwG,WAAW3L,KAAKC,SAAS,CAAC2J,OAAOA,KACjC+B,UACAM,SAAStM;YAEb;YAEAkK,eAAcD,GAAW,EAAEjK,OAAY;oBAajCA;gBAZJ,IAAI,OAAOiK,QAAQ,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAI/N,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAI+P,OAAOC,QAAQ,CAACjC,MAAM;oBAC/B,MAAM,qBAEL,CAFK,IAAI/N,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM8P,WAAW,OAAO/B,QAAQ;gBAChCjK,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASmM,GAAG,qBAAZnM,aAAcoM,MAAM,EAAE;oBACxBpM,QAAQmM,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGrM,QAAQmM,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAO1Q,SAASuO,aAAa,CAC3B8B,WAAW3L,KAAKC,SAAS,CAAC2J,OAAOA,KACjC+B,UACAM,SAAStM;YAEb;YAEAmK,QAAOF,GAAW,EAAEjK,OAAY;gBAC9B,OAAOrE,SAASwO,MAAM,CAAC8B,OAAOM,IAAI,CAACtC,MAAMqC,SAAStM,WAAW,CAAC;YAChE;YAEAoK,YAAWH,GAAW,EAAEjK,OAAY;gBAClC,OAAOrE,SAASyO,UAAU,CAAC6B,OAAOM,IAAI,CAACtC,MAAMqC,SAAStM,WAAW,CAAC;YACpE;YAEA2G,OAAMsD,GAAW,EAAEjK,OAAY;gBAC7B,OAAOrE,SAASgL,KAAK,CAACsD,KAAKqC,SAAStM,WAAW,CAAC;YAClD;YAEAqK,iBAAiB1O,SAAS0O,eAAe;YACzCmC,2BAA2B7Q,SAAS6Q,yBAAyB;YAC7DC,yBAAyB9Q,SAAS8Q,uBAAuB;YACzDnC,OAAO;gBACL9B,eAAepK,aAAa0N,kBAAkBnQ,UAAU;gBACxD6O,2BAA0BkC,aAAa;oBACrCjX,IAAIsC,IAAI,CACN;oBAEA+T,CAAAA,kBAAkBnQ,QAAO,EAAG6O,yBAAyB,CAACkC;gBAC1D;YACF;YACAhC,KAAK;gBACHC,SAAQV,GAAW,EAAEjK,OAAY;oBAC/B,OAAOrE,SAASiP,UAAU,CAACX,KAAKqC,SAASzB,cAAc7K;gBACzD;gBACA8K,aAAYb,GAAW,EAAEjK,OAAY;oBACnCrE,SAASoP,cAAc,CAACd,KAAKqC,SAASzB,cAAc7K;gBACtD;YACF;YACA4J,KAAK;gBACHC,WAAW;oBACTrE,WAAUmH,gBAAqB;wBAC7B,OAAOhR,SAASiR,qBAAqB,CAACD;oBACxC;oBACA5C,oBAAmB8C,oBAAyB;wBAC1C,OAAOlR,SAASmR,mCAAmC,CACjDD;oBAEJ;gBACF;YACF;YACAxI,eAAe;gBACb2G,yBAAyB,CAAC+B;oBACxB,OAAOpR,SAASqP,uBAAuB,CAAC+B;gBAC1C;YACF;YACA7B,QAAQ;gBACNC,uBAAuB,SACrB6B,YAAoB;oBAEpB,OAAOrR,SAASwP,qBAAqB,CAAC6B;gBACxC;gBACA3B,oBAAoB,SAClBvF,MAAc,EACdmH,YAAqB;oBAErB,OAAOtR,SAAS0P,kBAAkB,CAACvF,QAAQmH;gBAC7C;YACF;YACAzB,sBACE7G,OAAe,EACf8G,YAAoB,EACpBC,kBAA0B,EAC1BC,YAA6C,EAC7CC,UAAkC,EAClCC,OAAsC;gBAEtC,OAAOlQ,SAAS6P,oBAAoB,CAClC7G,SACA8G,cACAC,oBACAC,cACAC,YACAC;YAEJ;QACF;QACA,OAAOrT;IACT;IAEA,MAAMsB;AACR;AAEA,2DAA2D;AAC3D,0CAA0C;AAC1C,SAAS+Q,cAAc7K,UAAe,CAAC,CAAC;IACtC,OAAO;QACL,GAAGA,OAAO;QACVkN,aAAalN,QAAQkN,WAAW,IAAI;QACpCC,KAAKnN,QAAQmN,GAAG,IAAI;QACpBC,SAASpN,QAAQoN,OAAO,IAAI;IAC9B;AACF;AAEA,SAASd,SAASe,CAAM;IACtB,OAAOpB,OAAOM,IAAI,CAAClM,KAAKC,SAAS,CAAC+M;AACpC;AAEA,OAAO,eAAerD;IACpB,IAAIrO,WAAW,MAAM7C;IACrB,OAAO6C,SAASqO,MAAM;AACxB;AAEA,OAAO,eAAexE,UAAUyE,GAAW,EAAEjK,OAAa;IACxD,IAAIrE,WAAW,MAAM7C;IACrB,OAAO6C,SAAS6J,SAAS,CAACyE,KAAKjK;AACjC;AAEA,OAAO,SAASkK,cAAcD,GAAW,EAAEjK,OAAa;IACtD,IAAIrE,WAAWM;IACf,OAAON,SAASuO,aAAa,CAACD,KAAKjK;AACrC;AAEA,OAAO,eAAemK,OACpBF,GAAW,EACXjK,OAAY;IAEZ,IAAIrE,WAAW,MAAM7C;IACrB,OAAO6C,SAASwO,MAAM,CAACF,KAAKjK;AAC9B;AAEA,OAAO,eAAegL,wBACpB+B,QAAgB;IAEhB,IAAIpR,WAAW,MAAM7C;IACrB,OAAO6C,SAAS0I,aAAa,CAAC2G,uBAAuB,CAAC+B;AACxD;AAEA,OAAO,eAAepG,MAAMsD,GAAW,EAAEjK,OAAY;IACnD,IAAIrE,WAAW,MAAM7C;IACrB,IAAIwU,gBAAgB5X,iBAAiBsK;IACrC,OAAOrE,SACJgL,KAAK,CAACsD,KAAKqD,eACXhR,IAAI,CAAC,CAACiR,SAAgBlN,KAAKsG,KAAK,CAAC4G;AACtC;AAEA,OAAO,SAASC;QASJ7R;IARV,IAAIA;IACJ,IAAI;QACFA,WAAWhB;IACb,EAAE,OAAO+E,GAAG;IACV,sEAAsE;IACxE;IAEA,OAAO;QACL+N,MAAM,EAAE9R,6BAAAA,4BAAAA,SAAU0O,eAAe,qBAAzB1O,+BAAAA;IACV;AACF;AAEA;;;CAGC,GACD,OAAO,SAAS6Q,0BAA0BkB,aAAsB;IAC9D,IAAI,CAAC/U,oBAAoB;QACvB,6CAA6C;QAC7C,IAAIgD,WAAWhB;QACfhC,qBAAqBgD,SAAS6Q,yBAAyB,oBAAlC7Q,SAAS6Q,yBAAyB,MAAlC7Q,UAAqC+R;IAC5D;AACF;AAEA,SAASC,KAAKC,EAAc;IAC1B,IAAIC,WAAW;IAEf,OAAO;QACL,IAAI,CAACA,UAAU;YACbA,WAAW;YAEXD;QACF;IACF;AACF;AAEA;;;;;;;;CAQC,GACD,OAAO,MAAMnB,0BAA0BkB,KAAK;IAC1C,IAAI;QACF,IAAIhS,WAAWhB;QACf,IAAIhC,oBAAoB;YACtBgD,SAAS8Q,uBAAuB,oBAAhC9Q,SAAS8Q,uBAAuB,MAAhC9Q,UAAmChD;QACrC;IACF,EAAE,OAAO+G,GAAG;IACV,sEAAsE;IACxE;AACF,GAAE;AAEF,OAAO,eAAeyL,sBACpB6B,YAAoB;IAEpB,MAAMrR,WAAW,MAAM7C;IACvB,OAAO6C,SAASuP,MAAM,CAACC,qBAAqB,CAAC6B;AAC/C;AAEA,OAAO,eAAe3B,mBACpBvF,MAAc,EACdmH,YAAqB;IAErB,MAAMtR,WAAW,MAAM7C;IACvB,OAAO6C,SAASuP,MAAM,CAACG,kBAAkB,CAACvF,QAAQmH;AACpD", "ignoreList": [0]}