{"version": 3, "sources": ["../../../../../src/next-devtools/userspace/app/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { isNextRouterError } from '../../../../client/components/is-next-router-error'\nimport {\n  formatConsoleArgs,\n  parseConsoleArgs,\n} from '../../../../client/lib/console'\nimport isError from '../../../../lib/is-error'\nimport { createConsoleError } from '../../../shared/console-error'\nimport { coerceError, setOwnerStackIfAvailable } from './stitched-error'\nimport {\n  forwardUnhandledError,\n  isTerminalLoggingEnabled,\n  logUnhandledRejection,\n} from '../forward-logs'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\ntype ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleConsoleError(\n  originError: unknown,\n  consoleErrorArgs: any[]\n) {\n  let error: Error\n  const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n  if (isError(originError)) {\n    error = createConsoleError(originError, environmentName)\n  } else {\n    error = createConsoleError(\n      formatConsoleArgs(consoleErrorArgs),\n      environmentName\n    )\n  }\n  setOwnerStackIfAvailable(error)\n\n  errorQueue.push(error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function handleClientError(error: Error) {\n  errorQueue.push(error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  const thrownValue: unknown = event.error\n  if (isNextRouterError(thrownValue)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (thrownValue) {\n    const error = coerceError(thrownValue)\n    setOwnerStackIfAvailable(error)\n    handleClientError(error)\n    if (isTerminalLoggingEnabled) {\n      forwardUnhandledError(error)\n    }\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason: unknown = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  const error = coerceError(reason)\n  setOwnerStackIfAvailable(error)\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n\n  if (isTerminalLoggingEnabled) {\n    logUnhandledRejection(reason)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["useEffect", "isNextRouterError", "formatConsoleArgs", "parseConsoleArgs", "isError", "createConsoleError", "coerceError", "setOwnerStackIfAvailable", "forwardUnhandledError", "isTerminalLoggingEnabled", "logUnhandledRejection", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "handleConsoleError", "originError", "consoleErrorArgs", "error", "environmentName", "push", "handler", "handleClientError", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "splice", "indexOf", "length", "onUnhandledError", "event", "thrownValue", "preventDefault", "onUnhandledRejection", "ev", "reason", "handleGlobalErrors", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,qDAAoD;AACtF,SACEC,iBAAiB,EACjBC,gBAAgB,QACX,iCAAgC;AACvC,OAAOC,aAAa,2BAA0B;AAC9C,SAASC,kBAAkB,QAAQ,gCAA+B;AAClE,SAASC,WAAW,EAAEC,wBAAwB,QAAQ,mBAAkB;AACxE,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,qBAAqB,QAChB,kBAAiB;AAExB,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAEjD,OAAO,SAASC,mBACdC,WAAoB,EACpBC,gBAAuB;IAEvB,IAAIC;IACJ,MAAM,EAAEC,eAAe,EAAE,GAAGvB,iBAAiBqB;IAC7C,IAAIpB,QAAQmB,cAAc;QACxBE,QAAQpB,mBAAmBkB,aAAaG;IAC1C,OAAO;QACLD,QAAQpB,mBACNH,kBAAkBsB,mBAClBE;IAEJ;IACAnB,yBAAyBkB;IAEzBP,WAAWS,IAAI,CAACF;IAChB,KAAK,MAAMG,WAAWT,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbiB,QAAQH;QACV;IACF;AACF;AAEA,OAAO,SAASI,kBAAkBJ,KAAY;IAC5CP,WAAWS,IAAI,CAACF;IAChB,KAAK,MAAMG,WAAWT,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbiB,QAAQH;QACV;IACF;AACF;AAEA,OAAO,SAASK,gBACdC,sBAAoC,EACpCC,0BAAwC;IAExChC,UAAU;QACR,wBAAwB;QACxBkB,WAAWe,OAAO,CAACF;QACnBX,eAAea,OAAO,CAACD;QAEvB,wBAAwB;QACxBb,cAAcQ,IAAI,CAACI;QACnBV,kBAAkBM,IAAI,CAACK;QAEvB,OAAO;YACL,oBAAoB;YACpBb,cAAce,MAAM,CAACf,cAAcgB,OAAO,CAACJ,yBAAyB;YACpEV,kBAAkBa,MAAM,CACtBb,kBAAkBc,OAAO,CAACH,6BAC1B;YAGF,sBAAsB;YACtBd,WAAWgB,MAAM,CAAC,GAAGhB,WAAWkB,MAAM;YACtChB,eAAec,MAAM,CAAC,GAAGd,eAAegB,MAAM;QAChD;IACF,GAAG;QAACL;QAAwBC;KAA2B;AACzD;AAEA,SAASK,iBAAiBC,KAA8B;IACtD,MAAMC,cAAuBD,MAAMb,KAAK;IACxC,IAAIxB,kBAAkBsC,cAAc;QAClCD,MAAME,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAID,aAAa;QACf,MAAMd,QAAQnB,YAAYiC;QAC1BhC,yBAAyBkB;QACzBI,kBAAkBJ;QAClB,IAAIhB,0BAA0B;YAC5BD,sBAAsBiB;QACxB;IACF;AACF;AAEA,SAASgB,qBAAqBC,EAAwC;IACpE,MAAMC,SAAkBD,sBAAAA,GAAIC,MAAM;IAClC,IAAI1C,kBAAkB0C,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,MAAMf,QAAQnB,YAAYqC;IAC1BpC,yBAAyBkB;IAEzBL,eAAeO,IAAI,CAACF;IACpB,KAAK,MAAMG,WAAWP,kBAAmB;QACvCO,QAAQH;IACV;IAEA,IAAIhB,0BAA0B;QAC5BC,sBAAsBiC;IACxB;AACF;AAEA,OAAO,SAASC;IACd,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETF,OAAOG,gBAAgB,CAAC,SAASX;QACjCQ,OAAOG,gBAAgB,CAAC,sBAAsBP;IAChD;AACF", "ignoreList": [0]}