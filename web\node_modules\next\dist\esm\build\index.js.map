{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n  eventBuildFailed,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport {\n  createPagesMapping,\n  collectAppFiles,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n  processPageRoutes,\n  processAppRoutes,\n  processLayoutRoutes,\n  extractSlotsFromAppRoutes,\n  extractSlotsFromDefaultFiles,\n  combineSlots,\n  type RouteInfo,\n  type SlotInfo,\n  collectPagesFiles,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildInversePrefetchSegmentDataRoute,\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\nimport { isPersistentCachingEnabled } from '../shared/lib/turbopack/utils'\nimport { inlineStaticEnv } from '../lib/inline-static-env'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { durationToString } from './duration-to-string'\nimport { traceGlobals } from '../trace/shared'\nimport { extractNextErrorCode } from '../lib/error-telemetry-utils'\nimport { runAfterProductionCompile } from './after-production-compile'\nimport { generatePreviewKeys } from './preview-key-utils'\nimport { handleBuildComplete } from './adapter/build-complete'\nimport {\n  sortPageObjects,\n  sortPages,\n  sortSortableRouteObjects,\n} from '../shared/lib/router/utils/sortable-routes'\nimport { mkdir } from 'fs/promises'\nimport {\n  createRouteTypesManifest,\n  writeRouteTypesManifest,\n  writeValidatorFile,\n} from '../server/lib/router-utils/route-types-utils'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n\n  /**\n   * If true, this indicates that the route has fallback root params. This is\n   * used to simplify the route regex for matching.\n   */\n  hasFallbackRootParams?: boolean\n\n  /**\n   * The prefetch segment data routes for this route. This is used to rewrite\n   * the prefetch segment data routes (or the inverse) to the correct\n   * destination.\n   */\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n\n  /**\n   * If true, this indicates that the route should not be considered for routing\n   * for the internal router, and instead has been added to support external\n   * routers.\n   */\n  skipInternalRouting?: boolean\n}\n\ntype DynamicManifestRoute = ManifestRoute & {\n  /**\n   * The source page that this route is based on. This is used to determine the\n   * source page for the route and is only relevant for app pages where PPR is\n   * enabled and the page differs from the source page.\n   */\n  sourcePage: string | undefined\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<ManifestRedirectRoute>\n  rewrites: {\n    beforeFiles: Array<ManifestRewriteRoute>\n    afterFiles: Array<ManifestRewriteRoute>\n    fallback: Array<ManifestRewriteRoute>\n  }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: ReadonlyArray<DynamicManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\n/**\n * Converts a page to a manifest route.\n *\n * @param page The page to convert to a route.\n * @returns A route object.\n */\nfunction pageToRoute(page: string): ManifestRoute\n/**\n * Converts a page to a dynamic manifest route.\n *\n * @param page The page to convert to a route.\n * @param sourcePage The source page that this route is based on. This is used\n * to determine the source page for the route and is only relevant for app\n * pages when PPR is enabled on them.\n * @returns A route object.\n */\nfunction pageToRoute(\n  page: string,\n  sourcePage: string | undefined\n): DynamicManifestRoute\nfunction pageToRoute(\n  page: string,\n  sourcePage?: string\n): DynamicManifestRoute | ManifestRoute {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    sourcePage,\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      regions?: string[] | string\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\nexport interface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol?.replace(/:$/, '') as 'http' | 'https' | undefined,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\nexport type StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  options: {\n    debuggerPortOffset: number\n    progress?: {\n      run: () => void\n      clear: () => void\n    }\n  }\n): StaticWorker {\n  const { debuggerPortOffset, progress } = options\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    debuggerPortOffset,\n    enableSourceMaps: config.experimental.enablePrerenderSourceMaps,\n    // remove --max-old-space-size flag as it can cause memory issues.\n    isolatedMemory: true,\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = (require('../export') as typeof import('../export'))\n    .default as typeof import('../export').default\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  debugPrerender = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  isTurbopack = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n  NextBuildContext.isCompileMode = isCompileMode\n  const buildStartTime = Date.now()\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(isTurbopack),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n    NextBuildContext.debugPrerender = debugPrerender\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n                debugPrerender,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      if (experimentalBuildMode === 'generate-env') {\n        if (isTurbopack) {\n          Log.warn('generate-env is not needed with turbopack')\n          process.exit(0)\n        }\n        Log.info('Inlining static env ...')\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n\n        Log.info('Complete')\n        await flushAllTraces()\n        teardownTraceSubscriber()\n        process.exit(0)\n      }\n\n      // when using compile mode static env isn't inlined so we\n      // need to populate in normal runtime env\n      if (isCompileMode || isGenerateMode) {\n        populateStaticEnv(config)\n      }\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo({\n        dir,\n        dev: false,\n        debugPrerender,\n      })\n\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan\n              .traceChild('collect-pages')\n              .traceAsyncFn(() => collectPagesFiles(pagesDir, validFileMatcher))\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = await generatePreviewKeys({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let mappedAppLayouts: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths: string[]\n        let layoutPaths: string[]\n\n        if (Boolean(process.env.NEXT_PRIVATE_APP_PATHS)) {\n          // used for testing?\n          appPaths = providedAppPaths\n          layoutPaths = []\n        } else {\n          // Collect app pages, layouts, and default files in a single directory traversal\n          const result = await nextBuildSpan\n            .traceChild('collect-app-files')\n            .traceAsyncFn(() => collectAppFiles(appDir, validFileMatcher))\n\n          appPaths = result.appPaths\n          layoutPaths = result.layoutPaths\n          // Note: defaultPaths are not used in the build process, only for slot detection in generating route types\n        }\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        mappedAppLayouts = await nextBuildSpan\n          .traceChild('create-app-layouts')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: layoutPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      await nextBuildSpan\n        .traceChild('generate-route-types')\n        .traceAsyncFn(async () => {\n          const routeTypesFilePath = path.join(distDir, 'types', 'routes.d.ts')\n          const validatorFilePath = path.join(distDir, 'types', 'validator.ts')\n          await mkdir(path.dirname(routeTypesFilePath), { recursive: true })\n\n          let appRoutes: RouteInfo[] = []\n          let appRouteHandlers: RouteInfo[] = []\n          let layoutRoutes: RouteInfo[] = []\n          let slots: SlotInfo[] = []\n\n          const { pageRoutes, pageApiRoutes } = processPageRoutes(\n            mappedPages,\n            dir,\n            isSrcDir\n          )\n\n          // Build app routes\n          if (appDir && mappedAppPages) {\n            // Extract slots from both pages and default files\n            const slotsFromPages = extractSlotsFromAppRoutes(mappedAppPages)\n            let slotsFromDefaults: SlotInfo[] = []\n\n            // Collect and map default files for slot extraction\n            const { defaultPaths } = await nextBuildSpan\n              .traceChild('collect-default-files')\n              .traceAsyncFn(() => collectAppFiles(appDir, validFileMatcher))\n\n            if (defaultPaths.length > 0) {\n              const mappedDefaultFiles = await nextBuildSpan\n                .traceChild('create-default-mapping')\n                .traceAsyncFn(() =>\n                  createPagesMapping({\n                    pagePaths: defaultPaths,\n                    isDev: false,\n                    pagesType: PAGE_TYPES.APP,\n                    pageExtensions: config.pageExtensions,\n                    pagesDir,\n                    appDir,\n                  })\n                )\n              slotsFromDefaults =\n                extractSlotsFromDefaultFiles(mappedDefaultFiles)\n            }\n\n            // Combine slots and deduplicate using Set\n            slots = combineSlots(slotsFromPages, slotsFromDefaults)\n\n            const result = processAppRoutes(\n              mappedAppPages,\n              validFileMatcher,\n              dir,\n              isSrcDir\n            )\n            appRoutes = result.appRoutes\n            appRouteHandlers = result.appRouteHandlers\n          }\n\n          // Build app layouts\n          if (appDir && mappedAppLayouts) {\n            layoutRoutes = processLayoutRoutes(mappedAppLayouts, dir, isSrcDir)\n          }\n\n          const routeTypesManifest = await createRouteTypesManifest({\n            dir,\n            pageRoutes,\n            appRoutes,\n            appRouteHandlers,\n            pageApiRoutes,\n            layoutRoutes,\n            slots,\n            redirects: config.redirects,\n            rewrites: config.rewrites,\n            validatorFilePath,\n          })\n\n          await writeRouteTypesManifest(\n            routeTypesManifest,\n            routeTypesFilePath,\n            config\n          )\n          await writeValidatorFile(routeTypesManifest, validatorFilePath)\n        })\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!isTurbopack) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppCacheComponentsEnabled = Boolean(\n        config.experimental.cacheComponents\n      )\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const dynamicRoutes: Array<DynamicManifestRoute> = []\n\n      /**\n       * A map of all the pages to their sourcePage value. This is only used for\n       * routes that have PPR enabled and clientSegmentEnabled is true.\n       */\n      const sourcePages = new Map<string, string>()\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = sortPages([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const staticRoutes: Array<ManifestRoute> = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(\n                pageToRoute(\n                  route,\n                  // This property is only relevant when PPR is enabled.\n                  undefined\n                )\n              )\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            rewrites: {\n              beforeFiles: rewrites.beforeFiles.map((r) =>\n                buildCustomRoute('rewrite', r)\n              ),\n              afterFiles: rewrites.afterFiles.map((r) =>\n                buildCustomRoute('rewrite', r)\n              ),\n              fallback: rewrites.fallback.map((r) =>\n                buildCustomRoute('rewrite', r)\n              ),\n            },\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      // For pages directory, we run type checking after route collection but before build.\n      if (!appDir && !isCompileMode) {\n        await startTypeChecking(typeCheckingOptions)\n      }\n\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (isTurbopack) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              bundler: 'turbopack',\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    debuggerPortOffset: -1,\n                    isolatedMemory: false,\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                    isTurbopack: false,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n        await runAfterProductionCompile({\n          config,\n          buildSpan: nextBuildSpan,\n          telemetry,\n          metadata: {\n            projectDir: dir,\n            distDir,\n          },\n        })\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config, { debuggerPortOffset: -1 })\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              cacheComponents: isAppCacheComponentsEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/builtin/not-found'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                if (staticInfo?.hadUnsupportedValue) {\n                  errorFromUnsupportedSegmentConfig()\n                }\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined' ||\n                  typeof staticInfo?.preferredRegion !== 'undefined'\n                ) {\n                  const regions = staticInfo?.preferredRegion\n                    ? typeof staticInfo.preferredRegion === 'string'\n                      ? [staticInfo.preferredRegion]\n                      : staticInfo.preferredRegion\n                    : undefined\n\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                    ...(regions && { regions }),\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            cacheComponents: isAppCacheComponentsEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  params: {},\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: [],\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: [],\n                                  throwOnEmptyStaticShell: true,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  originalAppPath,\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyStaticShell: undefined,\n                })\n              })\n            })\n        )\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (!isTurbopack && (edgeRuntimeAppCount || edgeRuntimePagesCount)) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!isTurbopack\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !isTurbopack\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              SERVER_FILES_MANIFEST,\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.hadUnsupportedValue) {\n          errorFromUnsupportedSegmentConfig()\n        }\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (isTurbopack) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n          isTurbopack: true,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = sortPages([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/cacheComponents',\n          invocationCount: config.experimental.cacheComponents ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n        {\n          featureName: 'turbopackPersistentCaching',\n          invocationCount: isPersistentCachingEnabled(config) ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      // we don't need to inline for turbopack build as\n      // it will handle it's own caching separate of compile\n      if (isGenerateMode && !isTurbopack) {\n        Log.info('Inlining static env ...')\n\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n      }\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n\n          const exportApp = (require('../export') as typeof import('../export'))\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled: boolean = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : false\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                    _allowEmptyStaticShell: !route.throwOnEmptyStaticShell,\n                  }\n                })\n              })\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              debugPrerender,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getFallbackMode = (route: PrerenderedRoute) => {\n            const hasEmptyStaticShell = exportResult.byPath.get(\n              route.pathname\n            )?.hasEmptyStaticShell\n\n            // If the route has an empty static shell and is not configured to\n            // throw on empty static shell, then we should use the blocking\n            // static render mode.\n            if (\n              hasEmptyStaticShell &&\n              !route.throwOnEmptyStaticShell &&\n              route.fallbackMode === FallbackMode.PRERENDER\n            ) {\n              return FallbackMode.BLOCKING_STATIC_RENDER\n            }\n\n            // If the route has no fallback mode, then we should use the\n            // `NOT_FOUND` fallback mode.\n            if (!route.fallbackMode) {\n              return FallbackMode.NOT_FOUND\n            }\n\n            return route.fallbackMode\n          }\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const staticPrerenderedRoutes: PrerenderedRoute[] = []\n            const dynamicPrerenderedRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            const unsortedUnknownPrerenderRoutes: PrerenderedRoute[] = []\n            const unsortedKnownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unsortedUnknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                unsortedKnownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            const sortedUnknownPrerenderRoutes = sortPageObjects(\n              unsortedUnknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            const sortedKnownPrerenderRoutes = sortPageObjects(\n              unsortedKnownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...sortedKnownPrerenderRoutes,\n              ...sortedUnknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicPrerenderedRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of static routes.\n                staticPrerenderedRoutes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of staticPrerenderedRoutes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n\n              const {\n                metadata = {},\n                hasEmptyStaticShell,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n                const status =\n                  route.pathname === UNDERSCORE_NOT_FOUND_ROUTE\n                    ? 404\n                    : meta.status\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicPrerenderedRoutes.push({\n                  params: {},\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: [],\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: [],\n                  throwOnEmptyStaticShell: true,\n                })\n              }\n\n              for (const route of dynamicPrerenderedRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                let dynamicRoute = routesManifest.dynamicRoutes.find(\n                  (r) => r.page === route.pathname\n                )\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n\n                  // If the dynamic route wasn't found, then we need to create\n                  // it. This ensures that for each fallback shell there's an\n                  // entry in the app routes manifest which enables routing for\n                  // this fallback shell.\n                  if (!dynamicRoute) {\n                    dynamicRoute = pageToRoute(route.pathname, page)\n                    sourcePages.set(route.pathname, page)\n\n                    // This route is not for the internal router, but instead\n                    // for external routers.\n                    dynamicRoute.skipInternalRouting = true\n\n                    // Push this to the end of the array. The dynamic routes are\n                    // sorted by page later.\n                    dynamicRoutes.push(dynamicRoute)\n                  }\n                }\n\n                if (\n                  !isAppRouteHandler &&\n                  (metadata?.segmentPaths ||\n                    (route.fallbackRootParams &&\n                      route.fallbackRootParams.length > 0))\n                ) {\n                  // If PPR isn't enabled, then we might not find the dynamic\n                  // route by pathname. If that's the case, we need to find the\n                  // route by page.\n                  if (!dynamicRoute) {\n                    dynamicRoute = dynamicRoutes.find((r) => r.page === page)\n\n                    // If it can't be found by page, we must throw an error.\n                    if (!dynamicRoute) {\n                      throw new InvariantError('Dynamic route not found')\n                    }\n                  }\n\n                  if (metadata?.segmentPaths) {\n                    const pageSegmentPath = metadata.segmentPaths.find((item) =>\n                      item.endsWith('__PAGE__')\n                    )\n                    if (!pageSegmentPath) {\n                      throw new Error(`Invariant: missing __PAGE__ segmentPath`)\n                    }\n\n                    // We build a combined segment data route from the\n                    // page segment as we need to limit the number of\n                    // routes we output and they can be shared\n                    const builtSegmentDataRoute = buildPrefetchSegmentDataRoute(\n                      route.pathname,\n                      pageSegmentPath\n                    )\n\n                    builtSegmentDataRoute.source =\n                      builtSegmentDataRoute.source.replace(\n                        '/__PAGE__\\\\.segment\\\\.rsc$',\n                        `(?<segment>/__PAGE__\\\\.segment\\\\.rsc|\\\\.segment\\\\.rsc)(?:/)?$`\n                      )\n                    builtSegmentDataRoute.destination =\n                      builtSegmentDataRoute.destination.replace(\n                        '/__PAGE__.segment.rsc',\n                        '$segment'\n                      )\n                    dynamicRoute.prefetchSegmentDataRoutes ??= []\n                    dynamicRoute.prefetchSegmentDataRoutes.push(\n                      builtSegmentDataRoute\n                    )\n                  }\n                  // If the route has fallback root params, and we don't have\n                  // any segment paths, we need to write the inverse prefetch\n                  // segment data route so that it can first rewrite the /_tree\n                  // request to the prefetch RSC route. We also need to set the\n                  // `hasFallbackRootParams` flag so that we can simplify the\n                  // route regex for matching.\n                  else if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    dynamicRoute.hasFallbackRootParams = true\n                    dynamicRoute.prefetchSegmentDataRoutes = [\n                      buildInversePrefetchSegmentDataRoute(\n                        dynamicRoute.page,\n                        // We use the special segment path of `/_tree` because it's\n                        // the first one sent by the client router so it's the only\n                        // one we need to rewrite to the regular prefetch RSC route.\n                        '/_tree'\n                      ),\n                    ]\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode = getFallbackMode(route)\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: fallback\n                    ? route.fallbackRootParams\n                    : undefined,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n\n                  // since the app router not found is prioritized over pages router,\n                  // we have to ensure the app router entries are available for all locales\n                  if (i18n) {\n                    for (const locale of i18n.locales) {\n                      const curPath = `/${locale}/404`\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                  }\n\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n        })\n\n        // As we may have modified the dynamicRoutes, we need to sort the\n        // dynamic routes by page.\n        routesManifest.dynamicRoutes = sortSortableRouteObjects(\n          dynamicRoutes,\n          (route) => ({\n            // If the route is PPR enabled, and has an associated source page,\n            // use it. Otherwise fallback to the page which should be the same.\n            sourcePage: sourcePages.get(route.page) ?? route.page,\n            page: route.page,\n          })\n        )\n\n        // Now write the routes manifest out.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (isCompileMode) {\n        Log.info(\n          `Build ran with \"compile\" mode, to finalize the build run either \"generate\" or \"generate-env\" mode as well`\n        )\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.experimental.adapterPath) {\n        await handleBuildComplete({\n          dir,\n          distDir,\n          tracingRoot: outputFileTracingRoot,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          adapterPath: config.experimental.adapterPath,\n          pageKeys: pageKeys.pages,\n          appPageKeys: denormalizedAppPages,\n          routesManifest,\n          prerenderManifest,\n          middlewareManifest,\n          functionsConfigManifest,\n          hasStatic404: useStaticPages404,\n          requiredServerFiles: requiredServerFilesManifest.files,\n        })\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } catch (e) {\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(\n        eventBuildFailed({\n          bundler: getBundlerForTelemetry(isTurbopack),\n          errorCode: getErrorCodeForTelemetry(e),\n          durationInSeconds: Math.floor((Date.now() - buildStartTime) / 1000),\n        })\n      )\n    }\n    throw e\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: isTurbopack,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction errorFromUnsupportedSegmentConfig(): never {\n  Log.error(\n    `Invalid segment configuration export detected. This can cause unexpected behavior from the configs not being applied. You should see the relevant failures in the logs above. Please fix them to continue.`\n  )\n  process.exit(1)\n}\n\nfunction getBundlerForTelemetry(isTurbopack: boolean) {\n  if (isTurbopack) {\n    return 'turbopack'\n  }\n\n  if (process.env.NEXT_RSPACK) {\n    return 'rspack'\n  }\n\n  return 'webpack'\n}\n\nfunction getErrorCodeForTelemetry(err: unknown) {\n  const code = extractNextErrorCode(err)\n  if (code != null) {\n    return code\n  }\n\n  if (err instanceof Error && 'code' in err && typeof err.code === 'string') {\n    return err.code\n  }\n\n  if (err instanceof Error) {\n    return err.name\n  }\n\n  return 'Unknown'\n}\n"], "names": ["loadEnvConfig", "bold", "yellow", "makeRe", "existsSync", "promises", "fs", "os", "Worker", "defaultConfig", "devalue", "findUp", "nanoid", "path", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "MIDDLEWARE_FILENAME", "PAGES_DIR_ALIAS", "INSTRUMENTATION_HOOK_FILENAME", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "NEXT_RESUME_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "MATCHED_PATH_HEADER", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "FileType", "fileExists", "findPagesDir", "loadCustomRoutes", "normalizeRouteRegex", "nonNullable", "recursiveDelete", "verifyPartytownSetup", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "EXPORT_DETAIL", "EXPORT_MARKER", "IMAGES_MANIFEST", "PAGES_MANIFEST", "PHASE_PRODUCTION_BUILD", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "STATIC_STATUS_PAGES", "MIDDLEWARE_MANIFEST", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "APP_BUILD_MANIFEST", "RSC_MODULE_TYPES", "NEXT_FONT_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "DYNAMIC_CSS_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "isDynamicRoute", "loadConfig", "normalizePagePath", "getPagePath", "ciEnvironment", "turborepoTraceAccess", "TurborepoAccessTraceResult", "writeTurborepoAccessTraceResult", "eventBuildOptimize", "eventCliSession", "eventBuildFeatureUsage", "eventNextPlugins", "EVENT_BUILD_FEATURE_USAGE", "eventPackageUsedInGetServerSideProps", "eventBuildCompleted", "eventBuildFailed", "Telemetry", "createPagesMapping", "collectAppFiles", "getStaticInfoIncludingLayouts", "sortByPageExts", "processPageRoutes", "processAppRoutes", "processLayoutRoutes", "extractSlotsFromAppRoutes", "extractSlotsFromDefaultFiles", "combineSlots", "collectPagesFiles", "PAGE_TYPES", "generateBuildId", "isWriteable", "Log", "createSpinner", "trace", "flushAllTraces", "setGlobal", "detectConflictingPaths", "computeFromManifest", "getJsPageSizeInKb", "printCustomRoutes", "printTreeView", "copyTracedFiles", "isReservedPage", "isAppBuiltinNotFoundPage", "collectRoutesUsingEdgeRuntime", "collectMeta", "writeBuildId", "normalizeLocalePath", "isError", "isEdgeRuntime", "recursiveCopy", "lockfilePatchPromise", "teardownTraceSubscriber", "getNamedRouteRegex", "getFilesInDir", "eventSwcPlugins", "normalizeAppPath", "ACTION_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "webpackBuild", "NextBuildContext", "normalizePathSep", "isAppRouteRoute", "createClientRouterFilter", "createValidFileMatcher", "startTypeChecking", "generateInterceptionRoutesRewrites", "buildDataRoute", "collectBuildTraces", "formatManifest", "recordFrameworkVersion", "updateBuildDiagnostics", "recordFetchMetrics", "getStartServerInfo", "logStartInfo", "hasCustomExportOutput", "buildCustomRoute", "traceMemoryUsage", "generateEncryptionKeyBase64", "uploadTrace", "checkIsAppPPREnabled", "checkIsRoutePPREnabled", "FallbackMode", "fallbackModeToFallbackField", "RenderingMode", "InvariantError", "HTML_LIMITED_BOT_UA_RE_STRING", "buildInversePrefetchSegmentDataRoute", "buildPrefetchSegmentDataRoute", "turbopackBuild", "isPersistentCachingEnabled", "inlineStaticEnv", "populateStaticEnv", "durationToString", "traceGlobals", "extractNextErrorCode", "runAfterProductionCompile", "generatePreviewKeys", "handleBuildComplete", "sortPageObjects", "sortPages", "sortSortableRouteObjects", "mkdir", "createRouteTypesManifest", "writeRouteTypesManifest", "writeValidatorFile", "ALLOWED_HEADERS", "pageToRoute", "page", "sourcePage", "routeRegex", "prefixRouteKeys", "regex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "join", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "console", "log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "writeFunctionsConfigManifest", "writeRequiredServerFilesManifest", "requiredServerFiles", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "replace", "hostname", "port", "dot", "search", "localPatterns", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "dirname", "recursive", "copyFile", "middlewareOutput", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "createStaticWorker", "options", "debuggerPortOffset", "progress", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "enableSourceMaps", "enablePrerenderSourceMaps", "isolated<PERSON><PERSON><PERSON>", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "buildExport", "nextConfig", "silent", "outdir", "getBuildId", "isGenerateMode", "build", "reactProductionProfiling", "debugOutput", "debugPrerender", "runLint", "noMangling", "appDirOnly", "isTurbopack", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "buildStartTime", "Date", "now", "loadedConfig", "undefined", "buildMode", "isTurboBuild", "String", "process", "env", "__NEXT_VERSION", "mappedPages", "traceFn", "turborepoAccessTraceResult", "NEXT_DEPLOYMENT_ID", "deploymentId", "exit", "info", "customRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "publicDir", "pagesDir", "app", "<PERSON><PERSON><PERSON>", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "cwd", "isCustomServer", "turboFlag", "then", "events", "envInfo", "experimentalFeatures", "dev", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "code", "Error", "cleanDistDir", "error", "flush", "buildLintEvent", "featureName", "invocationCount", "eventName", "payload", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "middlewareDetectionRegExp", "RegExp", "instrumentationHookDetectionRegExp", "rootDir", "rootPaths", "Array", "from", "some", "include", "test", "hasMiddlewareFile", "previewProps", "isDev", "pagesType", "PAGES", "pagePaths", "mappedAppPages", "mappedAppLayouts", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "layoutPaths", "result", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "pagePath", "appPath", "add", "basePath", "totalAppPagesCount", "routeTypesFilePath", "validatorFilePath", "appRoutes", "appRouteHandlers", "layoutRoutes", "slots", "pageRoutes", "pageApiRoutes", "slotsFromPages", "slotsFromDefaults", "defaultPaths", "mappedDefaultFiles", "routeTypesManifest", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "hasApp404", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "hasPublicPageFile", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppCacheComponentsEnabled", "cacheComponents", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "ppr", "routesManifestPath", "sourcePages", "Map", "routesManifest", "sortedRoutes", "staticRoutes", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "dataRoutes", "i18n", "rsc", "header", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "contentTypeHeader", "suffix", "prefetchSuffix", "prefetchSegmentHeader", "prefetchSegmentSuffix", "prefetchSegmentDirSuffix", "rewriteHeaders", "pathHeader", "query<PERSON>eader", "skipMiddlewareUrlNormalize", "chain", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "buildStage", "pagesManifestPath", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "NEXT_TURBOPACK_USE_WORKER", "durationString", "event", "bundler", "durationInSeconds", "round", "serverBuildPromise", "res", "buildTraceWorker", "edgeRuntimeRoutes", "hasSsrAmpPages", "catch", "edgeBuildPromise", "getBundlerForTelemetry", "buildSpan", "metadata", "projectDir", "postCompileSpinner", "buildManifestPath", "appBuildManifestPath", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "key", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "gzipSize", "actionManifest", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "size", "totalSize", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isInsideAppDir", "staticInfo", "hadUnsupportedValue", "errorFromUnsupportedSegmentConfig", "runtime", "maxDuration", "preferredRegion", "regions", "pageRuntime", "client", "edgeInfo", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "params", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "throwOnEmptyStaticShell", "dynamic", "hasStaticProps", "isAmpOnly", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "delete", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyStaticShell", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "instrumentationHookEntryFiles", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "useStaticPages404", "pg", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "_allowEmptyStaticShell", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getFallbackMode", "by<PERSON><PERSON>", "NOT_FOUND", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "traces", "turborepoAccessTraceResults", "values", "ssgNotFoundPaths", "serverBundle", "unlink", "hasRevalidateZero", "isAppRouteHandler", "htmlBotsRegexString", "htmlLimitedBots", "bypassFor", "type", "staticPrerenderedRoutes", "dynamicPrerenderedRoutes", "unsortedUnknownPrerenderRoutes", "unsortedKnownPrerenderRoutes", "prerenderedRoute", "sortedUnknownPrerenderRoutes", "sortedKnownPrerenderRoutes", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "status", "initialStatus", "initialHeaders", "renderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "dynamicRoute", "skipInternalRouting", "segmentPaths", "pageSegmentPath", "item", "endsWith", "builtSegmentDataRoute", "destination", "prefetchSegmentDataRoutes", "hasFallbackRootParams", "isDynamicAppRoute", "fallbackCacheControl", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "postBuildSpinner", "buildTracesSpinner", "end", "analysisEnd", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "usages", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "previewModeId", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "adapterPath", "tracingRoot", "distPath", "e", "errorCode", "getErrorCodeForTelemetry", "cur", "mode", "isTurboSession", "sync", "NEXT_RSPACK", "name"], "mappings": "AAOA,OAAO,mCAAkC;AAEzC,SAASA,aAAa,QAA6B,YAAW;AAC9D,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,QAAQ,KAAI;AACnB,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,aAAa,QAAQ,0BAAyB;AACvD,OAAOC,aAAa,6BAA4B;AAChD,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,sCAAqC;AAC5D,OAAOC,UAAU,OAAM;AACvB,SACEC,0CAA0C,EAC1CC,8BAA8B,EAC9BC,mBAAmB,EACnBC,eAAe,EACfC,6BAA6B,EAC7BC,mBAAmB,EACnBC,UAAU,EACVC,kBAAkB,EAClBC,2BAA2B,EAC3BC,0CAA0C,EAC1CC,sCAAsC,EACtCC,kCAAkC,EAClCC,mBAAmB,EACnBC,uBAAuB,EACvBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,QAAQ,EAAEC,UAAU,QAAQ,qBAAoB;AACzD,SAASC,YAAY,QAAQ,wBAAuB;AACpD,OAAOC,oBACLC,mBAAmB,QACd,4BAA2B;AAQlC,SAASC,WAAW,QAAQ,sBAAqB;AACjD,SAASC,eAAe,QAAQ,0BAAyB;AACzD,SAASC,oBAAoB,QAAQ,gCAA+B;AACpE,SACEC,aAAa,EACbC,cAAc,EACdC,wBAAwB,EACxBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,EAClBC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,kCAAkC,EAClCC,yBAAyB,EACzBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,oBAAoB,EACpBC,oCAAoC,QAC/B,0BAAyB;AAChC,SAASC,cAAc,QAAQ,6BAA4B;AAE3D,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,WAAW,QAAQ,oBAAmB;AAC/C,YAAYC,mBAAmB,oBAAmB;AAClD,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,+BAA+B,QAC1B,2BAA0B;AAEjC,SACEC,kBAAkB,EAClBC,eAAe,EACfC,sBAAsB,EACtBC,gBAAgB,EAChBC,yBAAyB,EACzBC,oCAAoC,EACpCC,mBAAmB,EACnBC,gBAAgB,QACX,sBAAqB;AAE5B,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SACEC,kBAAkB,EAClBC,eAAe,EACfC,6BAA6B,EAC7BC,cAAc,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,EACzBC,4BAA4B,EAC5BC,YAAY,EAGZC,iBAAiB,QACZ,YAAW;AAClB,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,YAAYC,SAAS,eAAc;AACnC,OAAOC,mBAAmB,YAAW;AACrC,SAASC,KAAK,EAAEC,cAAc,EAAEC,SAAS,QAAmB,WAAU;AACtE,SACEC,sBAAsB,EACtBC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,wBAAwB,EACxBC,6BAA6B,EAC7BC,WAAW,QACN,UAAS;AAIhB,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,OAAOC,aAAa,kBAAiB;AAErC,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAuB;AACrD,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,QAAO;AACrE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,aAAa,QAAQ,0BAAyB;AACvD,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SACEC,aAAa,EACbC,2BAA2B,EAC3BC,UAAU,EACVC,uBAAuB,EACvBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,mCAAmC,EACnCC,0BAA0B,EAC1BC,2BAA2B,QACtB,0CAAyC;AAChD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,gBAAgB,QAA0B,kBAAiB;AACpE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,wBAAwB,QAAQ,qCAAoC;AAC7E,SAASC,sBAAsB,QAAQ,+BAA8B;AACrE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,kCAAkC,QAAQ,+CAA8C;AAEjG,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,kBAAkB,QACb,mCAAkC;AACzC,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,6BAA4B;AAE7E,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,2BAA2B,QAAQ,+CAA8C;AAE1F,OAAOC,iBAAiB,wBAAuB;AAC/C,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,iCAAgC;AACvC,SAASC,YAAY,EAAEC,2BAA2B,QAAQ,kBAAiB;AAC3E,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,6BAA6B,QAAQ,oCAAmC;AAEjF,SACEC,oCAAoC,EACpCC,6BAA6B,QAExB,+DAA8D;AAErE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,0BAA0B,QAAQ,gCAA+B;AAC1E,SAASC,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,oBAAmB;AACrD,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,+BAA8B;AACnE,SAASC,yBAAyB,QAAQ,6BAA4B;AACtE,SAASC,mBAAmB,QAAQ,sBAAqB;AACzD,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SACEC,eAAe,EACfC,SAAS,EACTC,wBAAwB,QACnB,6CAA4C;AACnD,SAASC,KAAK,QAAQ,cAAa;AACnC,SACEC,wBAAwB,EACxBC,uBAAuB,EACvBC,kBAAkB,QACb,+CAA8C;AA2HrD;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACAzJ;IACAJ;IACAC;IACAE;IACAD;CACD;AA+ID,SAAS4J,YACPC,IAAY,EACZC,UAAmB;IAEnB,MAAMC,aAAahE,mBAAmB8D,MAAM;QAC1CG,iBAAiB;IACnB;IACA,OAAO;QACLF;QACAD;QACAI,OAAOxJ,oBAAoBsJ,WAAWG,EAAE,CAACC,MAAM;QAC/CC,WAAWL,WAAWK,SAAS;QAC/BC,YAAYN,WAAWM,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWnL,KAAKoL,IAAI,CAACF,SAAS;IACpC,IAAIzH,cAAc4H,IAAI,IAAI,CAAC5H,cAAc6H,cAAc,EAAE;QACvD,MAAMC,WAAWhM,WAAW4L;QAE5B,IAAI,CAACI,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBC,QAAQC,GAAG,CACT,GAAGrG,IAAIsG,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOR;AACT;AAEA,eAAeS,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMrM,GAAGsM,SAAS,CAACF,UAAUC,SAAS;AACxC;AAEA,SAASE,aAAaH,QAAgB;IACpC,OAAOpM,GAAGwM,QAAQ,CAACJ,UAAU;AAC/B;AAEA,eAAeK,cACbL,QAAgB,EAChBM,QAAW;IAEX,MAAMP,cAAcC,UAAU5D,eAAekE;AAC/C;AAEA,eAAeC,aAA+BP,QAAgB;IAC5D,OAAOQ,KAAKC,KAAK,CAAC,MAAMN,aAAaH;AACvC;AAEA,eAAeU,uBACbrB,OAAe,EACfiB,QAAyC;IAEzC,MAAMD,cAAclM,KAAKoL,IAAI,CAACF,SAASlJ,qBAAqBmK;AAC9D;AAEA,eAAeK,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACPxB,OAAO,EACPyB,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKhH,oBAAoBgH,OAAOT,SAASU,QAAQ;WAC7DP,OAAOQ,IAAI,CAACb,kBAAkBc,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAE5N,QACtD+M,UACA,iDAAiD,CAAC;IAEpD,MAAMhB,cACJ5L,KAAKoL,IAAI,CAACF,SAASxJ,0BAA0BgL,SAAS,oBACtDe;AAEJ;AAoBA,eAAeC,6BACbxC,OAAe,EACfiB,QAAiC;IAEjC,MAAMD,cACJlM,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkBa,4BACrCmJ;AAEJ;AAWA,eAAewB,iCACbzC,OAAe,EACf0C,mBAAgD;IAEhD,MAAM1B,cACJlM,KAAKoL,IAAI,CAACF,SAAS9I,wBACnBwL;AAEJ;AAEA,eAAeC,oBACb3C,OAAe,EACf4C,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGhB,GAAG,CAAC,CAACiB;YAExDA;eAF+D;YACzE,iEAAiE;YACjEC,QAAQ,GAAED,cAAAA,EAAEC,QAAQ,qBAAVD,YAAYE,OAAO,CAAC,MAAM;YACpCC,UAAUjP,OAAO8O,EAAEG,QAAQ,EAAEzD,MAAM;YACnC0D,MAAMJ,EAAEI,IAAI;YACZnB,UAAU/N,OAAO8O,EAAEf,QAAQ,IAAI,MAAM;gBAAEoB,KAAK;YAAK,GAAG3D,MAAM;YAC1D4D,QAAQN,EAAEM,MAAM;QAClB;;IAEA,oEAAoE;IACpE,IAAIZ,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBa,aAAa,EAAE;QACjCZ,OAAOY,aAAa,GAAGb,OAAOC,MAAM,CAACY,aAAa,CAACxB,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEf,UAAU/N,OAAO8O,EAAEf,QAAQ,IAAI,MAAM;oBAAEoB,KAAK;gBAAK,GAAG3D,MAAM;gBAC1D4D,QAAQN,EAAEM,MAAM;YAClB,CAAA;IACF;IAEA,MAAMxC,cAAclM,KAAKoL,IAAI,CAACF,SAASrJ,kBAAkB;QACvD+M,SAAS;QACTb;IACF;AACF;AAEA,MAAMc,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB7D,OAAe,EACf8D,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BtB,mBAAgD,EAChDuB,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAM5J,gBACJ,kFAAkF;QAClF8H,oBAAoB4B,MAAM,EAC1BtE,SACA8D,SAASW,KAAK,EACdV,sBACAC,uBACAtB,oBAAoBE,MAAM,EAC1BqB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMM,QAAQ;eACdhC,oBAAoBiC,KAAK;YAC5B7P,KAAKoL,IAAI,CAACwC,oBAAoBE,MAAM,CAAC5C,OAAO,EAAE9I;eAC3CmN,eAAeO,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQhQ,IAAI,GAAG;oBACtD+P,IAAIG,IAAI,CAACF,QAAQhQ,IAAI;gBACvB;gBACA,OAAO+P;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAMlE,WAAW7L,KAAKoL,IAAI,CAACwC,oBAAoB4B,MAAM,EAAEI;YACvD,MAAMO,aAAanQ,KAAKoL,IAAI,CAC1BF,SACA2D,sBACA7O,KAAKoQ,QAAQ,CAAClB,uBAAuBrD;YAEvC,MAAMpM,GAAGyK,KAAK,CAAClK,KAAKqQ,OAAO,CAACF,aAAa;gBACvCG,WAAW;YACb;YACA,MAAM7Q,GAAG8Q,QAAQ,CAAC1E,UAAUsE;QAC9B;QAEA,IAAIf,mBAAmB;YACrB,MAAMoB,mBAAmBxQ,KAAKoL,IAAI,CAChCF,SACA2D,sBACA7O,KAAKoQ,QAAQ,CAAClB,uBAAuBhE,UACrC/I,kBACA;YAGF,MAAM1C,GAAGyK,KAAK,CAAClK,KAAKqQ,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAM7Q,GAAG8Q,QAAQ,CACfvQ,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkB,kBACrCqO;QAEJ;QAEA,MAAMjK,cACJvG,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkB,UACrCnC,KAAKoL,IAAI,CACPF,SACA2D,sBACA7O,KAAKoQ,QAAQ,CAAClB,uBAAuBhE,UACrC/I,kBACA,UAEF;YAAEsO,WAAW;QAAK;QAEpB,IAAIjB,QAAQ;YACV,MAAMkB,oBAAoB1Q,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkB;YAC/D,IAAI5C,WAAWmR,oBAAoB;gBACjC,MAAMnK,cACJmK,mBACA1Q,KAAKoL,IAAI,CACPF,SACA2D,sBACA7O,KAAKoQ,QAAQ,CAAClB,uBAAuBhE,UACrC/I,kBACA,QAEF;oBAAEsO,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmB7C,MAA0B;IACpD,IACEA,OAAO8C,YAAY,CAACC,IAAI,IACxB/C,OAAO8C,YAAY,CAACC,IAAI,KAAKjR,cAAcgR,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAO/C,OAAO8C,YAAY,CAACC,IAAI;IACjC;IAEA,IAAI/C,OAAO8C,YAAY,CAACE,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACnD,OAAO8C,YAAY,CAACC,IAAI,IAAI,GAAGE,KAAKG,KAAK,CAACxR,GAAGyR,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAIrD,OAAO8C,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAO/C,OAAO8C,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMO,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAED,OAAO,SAASC,mBACd1D,MAA0B,EAC1B2D,OAMC;IAED,MAAM,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE,GAAGF;IACzC,OAAO,IAAI9R,OAAOyR,kBAAkB;QAClCQ,QAAQxM;QACRyM,YAAYlB,mBAAmB7C;QAC/BgE,YAAY;YACVH,4BAAAA,SAAUI,GAAG;QACf;QACAC,iBAAiB;YACfL,4BAAAA,SAAUM,KAAK;QACjB;QACAP;QACAQ,kBAAkBpE,OAAO8C,YAAY,CAACuB,yBAAyB;QAC/D,kEAAkE;QAClEC,gBAAgB;QAChBC,qBAAqBvE,OAAO8C,YAAY,CAAC0B,aAAa;QACtDC,gBAAgBhB;IAClB;AACF;AAEA,eAAeiB,uBACb1E,MAA0B,EAC1B2E,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpB5D,aAAmB;IAEnB,MAAM6D,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;IAEV,MAAMD,UACJH,KACA;QACEK,aAAa;QACbC,YAAYjF;QACZ4E;QACAM,QAAQ;QACRC,QAAQjT,KAAKoL,IAAI,CAACqH,KAAKE;QACvBd,YAAYlB,mBAAmB7C;IACjC,GACAiB;AAEJ;AAEA,eAAemE,WACbC,cAAuB,EACvBjI,OAAe,EACf6D,aAAmB,EACnBjB,MAA0B;IAE1B,IAAIqF,gBAAgB;QAClB,OAAO,MAAM1T,GAAGwM,QAAQ,CAACjM,KAAKoL,IAAI,CAACF,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM6D,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAMxK,gBAAgB4I,OAAO5I,eAAe,EAAEnF;AAChE;AAEA,eAAe,eAAeqT,MAC5BX,GAAW,EACXY,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,iBAAiB,KAAK,EACtBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,cAAc,KAAK,EACnBC,qBAA0E,EAC1EC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMT,iBAAiBS,0BAA0B;IACjDpM,iBAAiBsM,aAAa,GAAGA;IACjC,MAAMC,iBAAiBC,KAAKC,GAAG;IAE/B,IAAIC;IACJ,IAAI;QACF,MAAMnF,gBAAgBzJ,MAAM,cAAc6O,WAAW;YACnDC,WAAWR;YACXS,cAAcC,OAAOX;YACrB/E,SAAS2F,QAAQC,GAAG,CAACC,cAAc;QACrC;QAEAjN,iBAAiBuH,aAAa,GAAGA;QACjCvH,iBAAiBiL,GAAG,GAAGA;QACvBjL,iBAAiBkM,UAAU,GAAGA;QAC9BlM,iBAAiB6L,wBAAwB,GAAGA;QAC5C7L,iBAAiBiM,UAAU,GAAGA;QAC9BjM,iBAAiB+L,cAAc,GAAGA;QAElC,MAAMxE,cAAcW,YAAY,CAAC;gBA+dXgF;YA9dpB,4EAA4E;YAC5E,MAAM,EAAEnF,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACXkF,OAAO,CAAC,IAAMxV,cAAcsT,KAAK,OAAOrN;YAC3CoC,iBAAiB+H,cAAc,GAAGA;YAElC,MAAMqF,6BAA6B,IAAIjR;YACvC,MAAMmK,SAA6B,MAAMiB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZhM,qBACE,IACEJ,WAAWvB,wBAAwB0Q,KAAK;wBACtC,sCAAsC;wBACtCO,QAAQ;wBACRK;wBACAE;oBACF,IACFqB;YAGNV,eAAepG;YAEfyG,QAAQC,GAAG,CAACK,kBAAkB,GAAG/G,OAAOgH,YAAY,IAAI;YACxDtN,iBAAiBsG,MAAM,GAAGA;YAE1B,IAAI6E,eAAe;YACnB,IAAIpK,sBAAsBuF,SAAS;gBACjC6E,eAAe7E,OAAO5C,OAAO;gBAC7B4C,OAAO5C,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUlL,KAAKoL,IAAI,CAACqH,KAAK3E,OAAO5C,OAAO;YAC7C1D,iBAAiB0D,OAAO,GAAGA;YAC3B1F,UAAU,SAASzD;YACnByD,UAAU,WAAW0F;YAErB,MAAMwB,UAAU,MAAMwG,WACpBC,gBACAjI,SACA6D,eACAjB;YAEFtG,iBAAiBkF,OAAO,GAAGA;YAE3B,IAAIkH,0BAA0B,gBAAgB;gBAC5C,IAAID,aAAa;oBACfvO,IAAIuG,IAAI,CAAC;oBACT4I,QAAQQ,IAAI,CAAC;gBACf;gBACA3P,IAAI4P,IAAI,CAAC;gBACT,MAAMjG,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMnG,gBAAgB;wBACpB2B;wBACA4C;oBACF;gBACF;gBAEF1I,IAAI4P,IAAI,CAAC;gBACT,MAAMzP;gBACNkB;gBACA8N,QAAQQ,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,yCAAyC;YACzC,IAAIjB,iBAAiBX,gBAAgB;gBACnC3J,kBAAkBsE;YACpB;YAEA,MAAMmH,eAA6B,MAAMlG,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAMvO,iBAAiB2M;YAEvC,MAAM,EAAEoH,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;YACzC,MAAMI,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9ClO,iBAAiBiO,WAAW,GAAGA;YAC/BjO,iBAAiBmO,gBAAgB,GAAG7H,OAAO8H,iBAAiB;YAC5DpO,iBAAiBqO,iBAAiB,GAAG/H,OAAOgI,kBAAkB;YAE9D,MAAM3K,WAAWF,YAAYC;YAE7B,MAAM6K,YAAY,IAAI1R,UAAU;gBAAE6G;YAAQ;YAE1C1F,UAAU,aAAauQ;YAEvB,MAAMC,YAAYhW,KAAKoL,IAAI,CAACqH,KAAK;YACjC,MAAM,EAAEwD,QAAQ,EAAEzG,MAAM,EAAE,GAAGtO,aAAauR;YAC1CjL,iBAAiByO,QAAQ,GAAGA;YAC5BzO,iBAAiBgI,MAAM,GAAGA;YAE1B,MAAMkD,qBAA6C;gBACjDwD,KAAK,OAAO1G,WAAW;gBACvBG,OAAO,OAAOsG,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAME,gBAAgB,MAAMzN,4BAA4B;gBACtD0N,SAAS;gBACTlL;YACF;YACA1D,iBAAiB2O,aAAa,GAAGA;YAEjC,MAAME,WAAWrW,KACdoQ,QAAQ,CAACqC,KAAKwD,YAAYzG,UAAU,IACpC8G,UAAU,CAAC;YACd,MAAMC,eAAehX,WAAWyW;YAEhCD,UAAUS,MAAM,CACd1S,gBAAgB2O,KAAK3E,QAAQ;gBAC3B2I,gBAAgB;gBAChBC,YAAY;gBACZL;gBACAM,YAAY,CAAC,CAAE,MAAM7W,OAAO,YAAY;oBAAE8W,KAAKnE;gBAAI;gBACnDoE,gBAAgB;gBAChBC,WAAW;gBACXb,UAAU,CAAC,CAACA;gBACZzG,QAAQ,CAAC,CAACA;YACZ;YAGFxL,iBAAiBhE,KAAKsR,OAAO,CAACmB,MAAMsE,IAAI,CAAC,CAACC,SACxCjB,UAAUS,MAAM,CAACQ;YAGnBpQ,gBAAgB5G,KAAKsR,OAAO,CAACmB,MAAM3E,QAAQiJ,IAAI,CAAC,CAACC,SAC/CjB,UAAUS,MAAM,CAACQ;YAGnB,qDAAqD;YACrD,MAAM,EAAEC,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAM7O,mBAAmB;gBACjEoK;gBACA0E,KAAK;gBACL5D;YACF;YAEAjL,aAAa;gBACX8O,YAAY;gBACZC,QAAQ;gBACRJ;gBACAC;YACF;YAEA,MAAMI,eAAeC,QAAQzJ,OAAO0J,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgB9D;YAEpC,MAAMmE,sBAA+D;gBACnElF;gBACAjD;gBACAyG;gBACAzC;gBACAkE;gBACAJ;gBACAvB;gBACAhH;gBACAjB;gBACA3C;YACF;YAEA,MAAMyM,iBAAiB,MAAM7I,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMjQ,GAAGyK,KAAK,CAACgB,SAAS;wBAAEoF,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOuH,KAAK;oBACZ,IAAIxR,QAAQwR,QAAQA,IAAIC,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMzS,YAAY+F,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAI6M,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIjK,OAAOkK,YAAY,IAAI,CAAC7E,gBAAgB;gBAC1C,MAAM7R,gBAAgB4J,SAAS;YACjC;YAEA,IAAIsE,UAAU,mBAAmB1B,QAAQ;gBACvC1I,IAAI6S,KAAK,CACP;gBAEF,MAAMlC,UAAUmC,KAAK;gBACrB3D,QAAQQ,IAAI,CAAC;YACf;YAEA,MAAMoD,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBX,aAAa,IAAI;YACpC;YACA3B,UAAUS,MAAM,CAAC;gBACf8B,WAAWrU;gBACXsU,SAASJ;YACX;YAEA,MAAMK,mBAAmB5Q,uBACvBkG,OAAO2K,cAAc,EACrBjJ;YAGF,MAAMkJ,oBAA8BrM,KAAKC,KAAK,CAC5CiI,QAAQC,GAAG,CAACmE,uBAAuB,IAAI;YAGzC,IAAIC,aAAarB,QAAQhD,QAAQC,GAAG,CAACmE,uBAAuB,IACxDD,oBACA,CAAChF,cAAcuC,WACb,MAAMlH,cACHU,UAAU,CAAC,iBACXC,YAAY,CAAC,IAAM1K,kBAAkBiR,UAAUuC,qBAClD,EAAE;YAER,MAAMK,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAE3Y,oBAAoB,MAAM,EAAE2N,OAAO2K,cAAc,CAACrN,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAM2N,qCAAqC,IAAID,OAC7C,CAAC,CAAC,EAAEzY,8BAA8B,MAAM,EAAEyN,OAAO2K,cAAc,CAACrN,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAM4N,UAAUhZ,KAAKoL,IAAI,CAAE6K,YAAYzG,QAAU;YACjD,MAAMS,WAAW;gBACf4I;gBACAE;aACD;YAED,MAAME,YAAYC,MAAMC,IAAI,CAAC,MAAMxS,cAAcqS,UAC9C/L,MAAM,CAAC,CAAC2C,OAASK,SAASmJ,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAAC1J,QACzDpC,IAAI,CAAC/I,eAAeqJ,OAAO2K,cAAc,GACzCtL,GAAG,CAAC,CAACyC,OAAS5P,KAAKoL,IAAI,CAAC4N,SAASpJ,MAAMtB,OAAO,CAACmE,KAAK;YAEvD,MAAMpD,yBAAyB4J,UAAUG,IAAI,CAAC,CAAChL,IAC7CA,EAAE6B,QAAQ,CAAC5P;YAEb,MAAMkZ,oBAAoBN,UAAUG,IAAI,CAAC,CAAChL,IACxCA,EAAE6B,QAAQ,CAAC9P;YAGbqH,iBAAiB6H,sBAAsB,GAAGA;YAE1C,MAAMmK,eAAkC,MAAM3P,oBAAoB;gBAChEuM,SAAS;gBACTlL;YACF;YACA1D,iBAAiBgS,YAAY,GAAGA;YAEhC,MAAM9E,cAAc,MAAM3F,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZpL,mBAAmB;oBACjBmV,OAAO;oBACPhB,gBAAgB3K,OAAO2K,cAAc;oBACrCiB,WAAWzU,WAAW0U,KAAK;oBAC3BC,WAAWhB;oBACX3C;oBACAzG;gBACF;YAEJhI,iBAAiBkN,WAAW,GAAGA;YAE/B,IAAImF;YACJ,IAAIC;YACJ,IAAI7K;YAEJ,IAAIO,QAAQ;gBACV,MAAMuK,mBAA6B1N,KAAKC,KAAK,CAC3CiI,QAAQC,GAAG,CAACwF,sBAAsB,IAAI;gBAGxC,IAAIC;gBACJ,IAAIC;gBAEJ,IAAI3C,QAAQhD,QAAQC,GAAG,CAACwF,sBAAsB,GAAG;oBAC/C,oBAAoB;oBACpBC,WAAWF;oBACXG,cAAc,EAAE;gBAClB,OAAO;oBACL,gFAAgF;oBAChF,MAAMC,SAAS,MAAMpL,cAClBU,UAAU,CAAC,qBACXC,YAAY,CAAC,IAAMnL,gBAAgBiL,QAAQgJ;oBAE9CyB,WAAWE,OAAOF,QAAQ;oBAC1BC,cAAcC,OAAOD,WAAW;gBAChC,0GAA0G;gBAC5G;gBAEAL,iBAAiB,MAAM9K,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZpL,mBAAmB;wBACjBsV,WAAWK;wBACXR,OAAO;wBACPC,WAAWzU,WAAWmV,GAAG;wBACzB3B,gBAAgB3K,OAAO2K,cAAc;wBACrCxC;wBACAzG;oBACF;gBAGJsK,mBAAmB,MAAM/K,cACtBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZpL,mBAAmB;wBACjBsV,WAAWM;wBACXT,OAAO;wBACPC,WAAWzU,WAAWmV,GAAG;wBACzB3B,gBAAgB3K,OAAO2K,cAAc;wBACrCxC;wBACAzG;oBACF;gBAGJhI,iBAAiBqS,cAAc,GAAGA;YACpC;YAEA,MAAMQ,kBAAkB,MAAM/V,mBAAmB;gBAC/CmV,OAAO;gBACPhB,gBAAgB3K,OAAO2K,cAAc;gBACrCmB,WAAWX;gBACXS,WAAWzU,WAAWqV,IAAI;gBAC1BrE,UAAUA;gBACVzG;YACF;YACAhI,iBAAiB6S,eAAe,GAAGA;YAEnC,MAAME,gBAAgBzN,OAAOQ,IAAI,CAACoH;YAElC,MAAM8F,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAI5N;YACxB,IAAIgN,gBAAgB;gBAClB5K,uBAAuBnC,OAAOQ,IAAI,CAACuM;gBACnC,KAAK,MAAMa,UAAUzL,qBAAsB;oBACzC,MAAM0L,uBAAuB9T,iBAAiB6T;oBAC9C,MAAME,WAAWlG,WAAW,CAACiG,qBAAqB;oBAClD,IAAIC,UAAU;wBACZ,MAAMC,UAAUhB,cAAc,CAACa,OAAO;wBACtCF,wBAAwBtK,IAAI,CAAC;4BAC3B0K,SAAStM,OAAO,CAAC,uBAAuB;4BACxCuM,QAAQvM,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACAmM,YAAYK,GAAG,CAACH;gBAClB;YACF;YAEA,MAAMV,WAAWf,MAAMC,IAAI,CAACsB;YAC5B,2DAA2D;YAC3DtF,SAASG,WAAW,CAACpF,IAAI,IACpBpI,mCAAmCmS,UAAUnM,OAAOiN,QAAQ;YAGjEvT,iBAAiB2N,QAAQ,GAAGA;YAE5B,MAAM6F,qBAAqBf,SAASvE,MAAM;YAE1C,MAAM1G,WAAW;gBACfW,OAAO4K;gBACPrE,KAAK+D,SAASvE,MAAM,GAAG,IAAIuE,WAAW9F;YACxC;YAEA,MAAMpF,cACHU,UAAU,CAAC,wBACXC,YAAY,CAAC;gBACZ,MAAMuL,qBAAqBjb,KAAKoL,IAAI,CAACF,SAAS,SAAS;gBACvD,MAAMgQ,oBAAoBlb,KAAKoL,IAAI,CAACF,SAAS,SAAS;gBACtD,MAAMhB,MAAMlK,KAAKqQ,OAAO,CAAC4K,qBAAqB;oBAAE3K,WAAW;gBAAK;gBAEhE,IAAI6K,YAAyB,EAAE;gBAC/B,IAAIC,mBAAgC,EAAE;gBACtC,IAAIC,eAA4B,EAAE;gBAClC,IAAIC,QAAoB,EAAE;gBAE1B,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAE,GAAG9W,kBACpCgQ,aACAjC,KACA4D;gBAGF,mBAAmB;gBACnB,IAAI7G,UAAUqK,gBAAgB;oBAC5B,kDAAkD;oBAClD,MAAM4B,iBAAiB5W,0BAA0BgV;oBACjD,IAAI6B,oBAAgC,EAAE;oBAEtC,oDAAoD;oBACpD,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAM5M,cAC5BU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMnL,gBAAgBiL,QAAQgJ;oBAE9C,IAAImD,aAAajG,MAAM,GAAG,GAAG;wBAC3B,MAAMkG,qBAAqB,MAAM7M,cAC9BU,UAAU,CAAC,0BACXC,YAAY,CAAC,IACZpL,mBAAmB;gCACjBsV,WAAW+B;gCACXlC,OAAO;gCACPC,WAAWzU,WAAWmV,GAAG;gCACzB3B,gBAAgB3K,OAAO2K,cAAc;gCACrCxC;gCACAzG;4BACF;wBAEJkM,oBACE5W,6BAA6B8W;oBACjC;oBAEA,0CAA0C;oBAC1CN,QAAQvW,aAAa0W,gBAAgBC;oBAErC,MAAMvB,SAASxV,iBACbkV,gBACArB,kBACA/F,KACA4D;oBAEF8E,YAAYhB,OAAOgB,SAAS;oBAC5BC,mBAAmBjB,OAAOiB,gBAAgB;gBAC5C;gBAEA,oBAAoB;gBACpB,IAAI5L,UAAUsK,kBAAkB;oBAC9BuB,eAAezW,oBAAoBkV,kBAAkBrH,KAAK4D;gBAC5D;gBAEA,MAAMwF,qBAAqB,MAAM1R,yBAAyB;oBACxDsI;oBACA8I;oBACAJ;oBACAC;oBACAI;oBACAH;oBACAC;oBACAlG,WAAWtH,OAAOsH,SAAS;oBAC3BD,UAAUrH,OAAOqH,QAAQ;oBACzB+F;gBACF;gBAEA,MAAM9Q,wBACJyR,oBACAZ,oBACAnN;gBAEF,MAAMzD,mBAAmBwR,oBAAoBX;YAC/C;YAEF,6DAA6D;YAC7D,IAAI,CAACvH,aAAa;gBAChB,MAAMmI,yBAAyBtB,wBAAwB9E,MAAM;gBAC7D,IAAImE,kBAAkBiC,yBAAyB,GAAG;oBAChD1W,IAAI6S,KAAK,CACP,CAAC,6BAA6B,EAC5B6D,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAAClB,UAAUC,QAAQ,IAAIL,wBAAyB;wBACzDpV,IAAI6S,KAAK,CAAC,CAAC,GAAG,EAAE2C,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAM9E,UAAUmC,KAAK;oBACrB3D,QAAQQ,IAAI,CAAC;gBACf;YACF;YAEA,MAAMgH,yBAAmC,EAAE;YAC3C,MAAMC,eAActH,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB4B,UAAU,CAAClW;YACpD,MAAM6b,YAAY,CAAC,EAACpC,kCAAAA,cAAgB,CAAC3W,iCAAiC;YACtE,MAAMgZ,qBACJxH,WAAW,CAAC,UAAU,CAAC4B,UAAU,CAAClW;YAEpC,IAAImW,cAAc;gBAChB,MAAM4F,6BAA6B5c,WACjCS,KAAKoL,IAAI,CAAC4K,WAAW;gBAEvB,IAAImG,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAIpE,MAAM7X,iCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAM6O,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMlF,QAAQkK,YAAa;oBAC9B,MAAM0H,oBAAoB,MAAMnb,WAC9BjB,KAAKoL,IAAI,CAAC4K,WAAWxL,SAAS,MAAM,WAAWA,OAC/CxJ,SAASqb,IAAI;oBAEf,IAAID,mBAAmB;wBACrBL,uBAAuB7L,IAAI,CAAC1F;oBAC9B;gBACF;gBAEA,MAAM8R,iBAAiBP,uBAAuBrG,MAAM;gBAEpD,IAAI4G,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAIvE,MACR,CAAC,gCAAgC,EAC/BuE,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEP,uBAAuB3Q,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAMmR,sBAAsBvN,SAASW,KAAK,CAAC1C,MAAM,CAAC,CAACzC;gBACjD,OACEA,KAAKgS,KAAK,CAAC,iCAAiCxc,KAAKqQ,OAAO,CAAC7F,UAAU;YAEvE;YAEA,IAAI+R,oBAAoB7G,MAAM,EAAE;gBAC9BtQ,IAAIuG,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5F4Q,oBAAoBnR,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAMqR,0BAA0B;gBAAC;aAAS,CAACtP,GAAG,CAAC,CAACiB,IAC9CN,OAAOiN,QAAQ,GAAG,GAAGjN,OAAOiN,QAAQ,GAAG3M,GAAG,GAAGA;YAG/C,MAAMsO,8BAA8BnF,QAClCzJ,OAAO8C,YAAY,CAAC+L,eAAe;YAErC,MAAMC,0BAA0BrF,QAC9BzJ,OAAO8C,YAAY,CAACiM,cAAc;YAEpC,MAAMC,kBAAkBlU,qBAAqBkF,OAAO8C,YAAY,CAACmM,GAAG;YAEpE,MAAMC,qBAAqBhd,KAAKoL,IAAI,CAACF,SAAShJ;YAC9C,MAAMqL,gBAA6C,EAAE;YAErD;;;OAGC,GACD,MAAM0P,cAAc,IAAIC;YACxB,MAAMC,iBAAiCpO,cACpCU,UAAU,CAAC,4BACXkF,OAAO,CAAC;gBACP,MAAMyI,eAAepT,UAAU;uBAC1BgF,SAASW,KAAK;uBACbX,SAASkH,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAMmH,eAAqC,EAAE;gBAE7C,KAAK,MAAMjQ,SAASgQ,aAAc;oBAChC,IAAI/Z,eAAe+J,QAAQ;wBACzBG,cAAc2C,IAAI,CAChB3F,YACE6C,OACA,sDAAsD;wBACtD+G;oBAGN,OAAO,IAAI,CAACpO,eAAeqH,QAAQ;wBACjCiQ,aAAanN,IAAI,CAAC3F,YAAY6C;oBAChC;gBACF;gBAEA,OAAO;oBACLwB,SAAS;oBACT0O,UAAU;oBACVC,eAAe,CAAC,CAACzP,OAAO8C,YAAY,CAAC4M,mBAAmB;oBACxDzC,UAAUjN,OAAOiN,QAAQ;oBACzB3F,WAAWA,UAAUjI,GAAG,CAAC,CAACsQ,IACxBjV,iBAAiB,YAAYiV,GAAGhB;oBAElCvH,SAASA,QAAQ/H,GAAG,CAAC,CAACsQ,IAAMjV,iBAAiB,UAAUiV;oBACvDtI,UAAU;wBACRG,aAAaH,SAASG,WAAW,CAACnI,GAAG,CAAC,CAACsQ,IACrCjV,iBAAiB,WAAWiV;wBAE9BlI,YAAYJ,SAASI,UAAU,CAACpI,GAAG,CAAC,CAACsQ,IACnCjV,iBAAiB,WAAWiV;wBAE9BjI,UAAUL,SAASK,QAAQ,CAACrI,GAAG,CAAC,CAACsQ,IAC/BjV,iBAAiB,WAAWiV;oBAEhC;oBACAlQ;oBACA8P;oBACAK,YAAY,EAAE;oBACdC,MAAM7P,OAAO6P,IAAI,IAAIxJ;oBACrByJ,KAAK;wBACHC,QAAQ7W;wBACR,yFAAyF;wBACzF,4DAA4D;wBAC5D8W,YAAY,GAAG9W,WAAW,EAAE,EAAEE,8BAA8B,EAAE,EAAEH,4BAA4B,EAAE,EAAEK,qCAAqC;wBACrI2W,gBAAgBhX;wBAChBiX,mBAAmB7W;wBACnB8W,mBAAmBhX;wBACnBiX,QAAQ3d;wBACR4d,gBAAgB7d;wBAChB8d,uBAAuBhX;wBACvBiX,uBAAuBtd;wBACvBud,0BAA0Bxd;oBAC5B;oBACAyd,gBAAgB;wBACdC,YAAYnX;wBACZoX,aAAanX;oBACf;oBACAoX,4BAA4B5Q,OAAO4Q,0BAA0B;oBAC7D3B,KAAKD,kBACD;wBACE6B,OAAO;4BACLzJ,SAAS;gCACP,CAAC1U,mBAAmB,EAAE;4BACxB;wBACF;oBACF,IACA2T;gBACN;YACF;YAEF,qFAAqF;YACrF,IAAI,CAAC3E,UAAU,CAACsE,eAAe;gBAC7B,MAAMjM,kBAAkB8P;YAC1B;YAEA,IAAIiH;YAIJ,IAAI9Q,OAAO8C,YAAY,CAACiO,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAAChR,CAAAA,OAAOgI,kBAAkB,IAAI,EAAE,AAAD,EAAG7I,MAAM,CACnE,CAACwQ,IAAW,CAACA,EAAEsB,QAAQ;gBAEzBH,sBAAsBjX,yBACpB;uBAAIsS;iBAAS,EACbnM,OAAO8C,YAAY,CAACoO,2BAA2B,GAC3CF,uBACA,EAAE,EACNhR,OAAO8C,YAAY,CAACqO,6BAA6B;gBAEnDzX,iBAAiBoX,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAMhT,cACJ5L,KAAKoL,IAAI,CAACF,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAMhD,uBAAuBqM,QAAQC,GAAG,CAACC,cAAc;YACvD,MAAMtM,uBAAuB;gBAC3B+W,YAAY;YACd;YAEA,MAAMhQ,wBAAwBpB,OAAOoB,qBAAqB,IAAIuD;YAE9D,MAAM0M,oBAAoBnf,KAAKoL,IAAI,CACjCF,SACA/I,kBACAL;YAGF,IAAIsd;YACJ,IAAIC,qBAA+ClL;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAMmL,iBACJxR,OAAO8C,YAAY,CAAC2O,kBAAkB,IACrCzR,OAAO8C,YAAY,CAAC2O,kBAAkB,KAAKpL,aAC1C,CAACrG,OAAO0R,OAAO;YACnB,MAAMC,6BACJ3R,OAAO8C,YAAY,CAAC8O,sBAAsB;YAC5C,MAAMC,qCACJ7R,OAAO8C,YAAY,CAACgP,yBAAyB,IAC5C9R,OAAO8C,YAAY,CAACgP,yBAAyB,KAAKzL,aACjDL;YAEJ/E,cAAc8Q,YAAY,CACxB,6BACAvL,OAAO,CAAC,CAACxG,OAAO0R,OAAO;YAEzBzQ,cAAc8Q,YAAY,CAAC,oBAAoBvL,OAAOgL;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAI5H,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA3S,IAAI4P,IAAI,CAAC;YACTvM,iBAAiB,kBAAkBsG;YAEnC,MAAM5G,uBAAuB;gBAC3B+W,YAAY;gBACZY,cAAc;oBACZR,gBAAgBhL,OAAOgL;gBACzB;YACF;YAEA,IAAIS,kBAAkBC,QAAQ1O,OAAO;YACrC,IAAI,CAAC6B,gBAAgB;gBACnB,IAAIQ,aAAa;oBACf,MAAM,EACJsM,UAAUC,gBAAgB,EAC1BH,iBAAiB3R,CAAC,EAClB,GAAG+R,MACJ,GAAG,MAAM9W,eACRkL,QAAQC,GAAG,CAAC4L,yBAAyB,KAAKjM,aACxCI,QAAQC,GAAG,CAAC4L,yBAAyB,KAAK;oBAE9CL,kBAAkB3R;oBAClB3F,iBAAiB,kBAAkBsG;oBAEnCqQ,oBAAoBe,KAAKf,iBAAiB;oBAE1C,MAAMiB,iBAAiB5W,iBAAiByW;oBACxC9a,IAAIkb,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;oBAEtDtK,UAAUS,MAAM,CACdrS,oBAAoByU,YAAY;wBAC9B2H,SAAS;wBACTC,mBAAmBzP,KAAK0P,KAAK,CAACP;wBAC9BlF;oBACF;gBAEJ,OAAO;oBACL,IACEyE,8BACAE,oCACA;wBACA,IAAIa,oBAAoB;wBAExB,MAAMrY,uBAAuB;4BAC3B+W,YAAY;wBACd;wBAEA,MAAMwB,qBAAqBnZ,aAAa+X,gBAAgB;4BACtD;yBACD,EAAEvI,IAAI,CAAC,CAAC4J;4BACPlY,iBAAiB,+BAA+BsG;4BAChDqQ,oBAAoBuB,IAAIvB,iBAAiB;4BACzCoB,qBAAqBG,IAAIV,QAAQ;4BAEjC,IAAIN,oCAAoC;gCACtC,MAAMiB,mBAAmB,IAAIjhB,OAC3B0R,QAAQC,OAAO,CAAC,2BAChB;oCACEI,oBAAoB,CAAC;oCACrBU,gBAAgB;oCAChBP,YAAY;oCACZU,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGF8M,qBAAqBuB,iBAClB5Y,kBAAkB,CAAC;oCAClByK;oCACA3E;oCACA5C;oCACA,+CAA+C;oCAC/C2V,mBAAmB5a,8BAA8B,IAAIiX;oCACrD5N,aAAa,EAAE;oCACfwR,gBAAgB;oCAChB1B;oCACAlQ;oCACAyE,aAAa;gCACf,GACCoN,KAAK,CAAC,CAAClJ;oCACNrM,QAAQyM,KAAK,CAACJ;oCACdtD,QAAQQ,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAAC0K,4BAA4B;4BAC/B,MAAMiB;4BACN,MAAMvY,uBAAuB;gCAC3B+W,YAAY;4BACd;wBACF;wBAEA,MAAM8B,mBAAmBzZ,aAAa+X,gBAAgB;4BACpD;yBACD,EAAEvI,IAAI,CAAC,CAAC4J;4BACPH,qBAAqBG,IAAIV,QAAQ;4BACjCxX,iBACE,oCACAsG;wBAEJ;wBACA,IAAI0Q,4BAA4B;4BAC9B,MAAMiB;4BACN,MAAMvY,uBAAuB;gCAC3B+W,YAAY;4BACd;wBACF;wBACA,MAAM8B;wBAEN,MAAM7Y,uBAAuB;4BAC3B+W,YAAY;wBACd;wBAEA,MAAM3X,aAAa+X,gBAAgB;4BAAC;yBAAS,EAAEvI,IAAI,CAAC,CAAC4J;4BACnDH,qBAAqBG,IAAIV,QAAQ;4BACjCxX,iBAAiB,+BAA+BsG;wBAClD;wBAEA,MAAMsR,iBAAiB5W,iBAAiB+W;wBACxCpb,IAAIkb,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;wBAEtDtK,UAAUS,MAAM,CACdrS,oBAAoByU,YAAY;4BAC9B2H,SAASU,uBAAuBtN;4BAChC6M;4BACAxF;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAEiF,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAM5Y,aACpD+X,gBACA;wBAEF7W,iBAAiB,kBAAkBsG;wBAEnCqQ,oBAAoBe,KAAKf,iBAAiB;wBAE1CrJ,UAAUS,MAAM,CACdrS,oBAAoByU,YAAY;4BAC9B2H,SAASU,uBAAuBtN;4BAChC6M,mBAAmBN;4BACnBlF;wBACF;oBAEJ;gBACF;gBACA,MAAMpR,0BAA0B;oBAC9BkE;oBACAoT,WAAWnS;oBACXgH;oBACAoL,UAAU;wBACRC,YAAY3O;wBACZvH;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAIsE,UAAU,CAACsE,iBAAiB,CAACX,gBAAgB;gBAC/C,MAAMhL,uBAAuB;oBAC3B+W,YAAY;gBACd;gBACA,MAAMrX,kBAAkB8P;gBACxBlP,iBAAiB,0BAA0BsG;YAC7C;YAEA,MAAMsS,qBAAqBhc,cAAc;YAEzC,MAAMic,oBAAoBthB,KAAKoL,IAAI,CAACF,SAASzJ;YAC7C,MAAM8f,uBAAuBvhB,KAAKoL,IAAI,CAACF,SAASzI;YAEhD,IAAI+e,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAM/U,WAAW,IAAIC;YACrB,MAAM+U,yBAAyB,IAAI/U;YACnC,MAAMgV,2BAA2B,IAAIhV;YACrC,MAAMyC,cAAc,IAAIzC;YACxB,MAAMiV,eAAe,IAAIjV;YACzB,MAAMkV,iBAAiB,IAAIlV;YAC3B,MAAMmV,mBAAmB,IAAInV;YAC7B,MAAMoV,kBAAkB,IAAI/E;YAC5B,MAAMgF,cAAc,IAAIhF;YACxB,MAAMiF,qBAAqB,IAAIjF;YAC/B,MAAMkF,gBAAgB,IAAIlF;YAC1B,MAAMmF,oBAAoB,IAAInF;YAC9B,MAAMoF,YAAuB,IAAIpF;YACjC,IAAIqF,gBAAgB,MAAMnW,aAA4B+S;YACtD,MAAMqD,gBAAgB,MAAMpW,aAA4BkV;YACxD,MAAMmB,mBAAmBjT,SACrB,MAAMpD,aAA+BmV,wBACrCpN;YAEJ,MAAMuO,gBAAwC,CAAC;YAE/C,IAAIlT,QAAQ;gBACV,MAAMmT,mBAAmB,MAAMvW,aAC7BpM,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkBI;gBAGvC,IAAK,MAAMqgB,OAAOD,iBAAkB;oBAClCD,aAAa,CAACE,IAAI,GAAG/b,iBAAiB+b;gBACxC;gBAEA,MAAM1W,cACJlM,KAAKoL,IAAI,CAACF,SAAS1I,2BACnBkgB;YAEJ;YAEAnO,QAAQC,GAAG,CAACqO,UAAU,GAAG9gB;YAEzB,MAAM+gB,SAAStR,mBAAmB1D,QAAQ;gBAAE4D,oBAAoB,CAAC;YAAE;YAEnE,MAAMqR,gBAAgBxO,QAAQyO,MAAM;YACpC,MAAMC,kBAAkBlU,cAAcU,UAAU,CAAC;YAEjD,MAAMyT,0BAAmD;gBACvDtU,SAAS;gBACTuU,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnBxC,cAAc,EACdyC,qBAAqB,EACtB,GAAG,MAAMN,gBAAgBvT,YAAY,CAAC;oBAcV5B;gBAb3B,IAAIgG,eAAe;oBACjB,OAAO;wBACLsP,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrBxC,gBAAgB,CAAC,CAAC7K;wBAClBsN,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChE5V;gBACF,MAAM6V,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAarM,SAAQzJ,2BAAAA,OAAO8C,YAAY,CAACiT,GAAG,qBAAvB/V,yBAAyBgW,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgBxT,UAAU,CACvD;gBAEF,MAAMuU,oCACJD,uBAAuBrU,YAAY,CACjC,UACEwM,sBACC,MAAM4G,OAAOmB,wBAAwB,CAAC;wBACrCzZ,MAAM;wBACNU;wBACAyY;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuBrU,YAAY,CAC/D;wBAWa5B,cACMA;2BAXjBoO,sBACA4G,OAAOsB,YAAY,CAAC;wBAClB3R;wBACAjI,MAAM;wBACNU;wBACAsY;wBACAG;wBACAhH,iBAAiBD;wBACjBG,gBAAgBD;wBAChByH,kBAAkBvW,OAAOuW,gBAAgB;wBACzC1X,OAAO,GAAEmB,eAAAA,OAAO6P,IAAI,qBAAX7P,aAAanB,OAAO;wBAC7B2X,aAAa,GAAExW,gBAAAA,OAAO6P,IAAI,qBAAX7P,cAAawW,aAAa;wBACzCC,kBAAkBzW,OAAO0W,MAAM;wBAC/BC,WAAW3W,OAAO8C,YAAY,CAACmM,GAAG;wBAClC2H,mBAAmB5W,OAAO8C,YAAY,CAAC+T,SAAS;wBAChDjY;wBACAkX;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACEzZ,MAAMoa;oBACN1Z;oBACAyY;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxDva,MAAMoa;oBACN1Z;oBACAyY;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAIxC,iBAAiB;gBAErB,MAAMkE,uBAAuB,MAAMtf,oBACjC;oBAAE0N,OAAOoP;oBAAetM,KAAKuM;gBAAiB,GAC9CvX,SACA4C,OAAO8C,YAAY,CAACqU,QAAQ;gBAG9B,MAAM9V,qBAAyCkC,QAC7CrR,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkBG;gBAGvC,MAAM4iB,iBAAiB1V,SAClB6B,QACCrR,KAAKoL,IAAI,CACPF,SACA/I,kBACAY,4BAA4B,YAGhC;gBACJ,MAAMoiB,oBAAoBD,iBAAiB,IAAIrY,QAAQ;gBACvD,IAAIqY,kBAAkBC,mBAAmB;oBACvC,IAAK,MAAMC,MAAMF,eAAeG,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASJ,eAAeG,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBrK,GAAG,CAACwK;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMF,eAAeM,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASJ,eAAeM,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBrK,GAAG,CAACwK;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM1C,OAAO9V,OAAOQ,IAAI,CAAC6B,sCAAAA,mBAAoBgU,SAAS,EAAG;oBAC5D,IAAIP,IAAItM,UAAU,CAAC,SAAS;wBAC1BqL;oBACF;gBACF;gBAEA,MAAM3B,QAAQyF,GAAG,CACf3Y,OAAOC,OAAO,CAACiC,UACZc,MAAM,CACL,CAACC,KAAK,CAAC6S,KAAK/S,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAM2V,WAAW9C;oBAEjB,KAAK,MAAMpY,QAAQqF,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAEwV;4BAAUlb;wBAAK;oBAC5B;oBAEA,OAAOuF;gBACT,GACA,EAAE,EAEH5C,GAAG,CAAC,CAAC,EAAEuY,QAAQ,EAAElb,IAAI,EAAE;oBACtB,MAAMmb,gBAAgB1C,gBAAgBxT,UAAU,CAAC,cAAc;wBAC7DjF;oBACF;oBACA,OAAOmb,cAAcjW,YAAY,CAAC;wBAChC,MAAMkW,aAAariB,kBAAkBiH;wBACrC,MAAM,CAACqb,MAAMC,UAAU,GAAG,MAAMngB,kBAC9B+f,UACAE,YACA1a,SACAsX,eACAC,kBACA3U,OAAO8C,YAAY,CAACqU,QAAQ,EAC5BD;wBAGF,IAAIe,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAIxL,WAAW;wBAEf,IAAI8K,aAAa,SAAS;4BACxB9K,WACEhC,WAAWyN,IAAI,CAAC,CAACjY;gCACfA,IAAI3G,iBAAiB2G;gCACrB,OACEA,EAAEkI,UAAU,CAACsP,aAAa,QAC1BxX,EAAEkI,UAAU,CAACsP,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIU;wBAEJ,IAAIZ,aAAa,SAAS7L,gBAAgB;4BACxC,KAAK,MAAM,CAAC0M,cAAcC,eAAe,IAAI1Z,OAAOC,OAAO,CACzD2V,eACC;gCACD,IAAI8D,mBAAmBhc,MAAM;oCAC3BoQ,WAAWf,cAAc,CAAC0M,aAAa,CAACjY,OAAO,CAC7C,yBACA;oCAEFgY,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAezgB,yBAAyB4U,YAC1CvJ,QAAQC,OAAO,CACb,mDAEFtR,KAAKoL,IAAI,CACP,AAACsa,CAAAA,aAAa,UAAUzP,WAAWzG,MAAK,KAAM,IAC9CoL;wBAGN,MAAM8L,iBAAiBhB,aAAa;wBACpC,MAAMiB,aAAa/L,WACf,MAAMpW,8BAA8B;4BAClCkiB;4BACAD;4BACAhO,gBAAgB3K,OAAO2K,cAAc;4BACrCjJ;4BACA1B;4BACA2L,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChEjP,MAAMkc,iBAAiBJ,kBAAmB9b;wBAC5C,KACA2J;wBAEJ,IAAIwS,8BAAAA,WAAYC,mBAAmB,EAAE;4BACnCC;wBACF;wBAEA,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAOF,8BAAAA,WAAYG,OAAO,MAAK,eAC/B,QAAOH,8BAAAA,WAAYI,WAAW,MAAK,eACnC,QAAOJ,8BAAAA,WAAYK,eAAe,MAAK,aACvC;4BACA,MAAMC,UAAUN,CAAAA,8BAAAA,WAAYK,eAAe,IACvC,OAAOL,WAAWK,eAAe,KAAK,WACpC;gCAACL,WAAWK,eAAe;6BAAC,GAC5BL,WAAWK,eAAe,GAC5B7S;4BAEJ+O,wBAAwBC,SAAS,CAAC3Y,KAAK,GAAG;gCACxCuc,WAAW,EAAEJ,8BAAAA,WAAYI,WAAW;gCACpC,GAAIE,WAAW;oCAAEA;gCAAQ,CAAC;4BAC5B;wBACF;wBAEA,MAAMC,cAAc/X,mBAAmBgU,SAAS,CAC9CmD,mBAAmB9b,KACpB,GACG,SACAmc,8BAAAA,WAAYG,OAAO;wBAEvB,IAAI,CAAChT,eAAe;4BAClBoS,oBACER,aAAa,SACbiB,CAAAA,8BAAAA,WAAY/I,GAAG,MAAKlb,iBAAiBykB,MAAM;4BAE7C,IAAIzB,aAAa,SAAS,CAAC3f,eAAeyE,OAAO;gCAC/C,IAAI;oCACF,IAAI4c;oCAEJ,IAAI9gB,cAAc4gB,cAAc;wCAC9B,IAAIxB,aAAa,OAAO;4CACtBhE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAM0F,cACJ3B,aAAa,UAAUlb,OAAO8b,mBAAmB;wCAEnDc,WAAWjY,mBAAmBgU,SAAS,CAACkE,YAAY;oCACtD;oCAEA,IAAIC,mBACF3B,cAAclW,UAAU,CAAC;oCAC3B,IAAI8X,eAAe,MAAMD,iBAAiB5X,YAAY,CACpD;4CASa5B,cACMA;wCATjB,OAAOgV,OAAOsB,YAAY,CAAC;4CACzB3R;4CACAjI;4CACA8b;4CACApb;4CACAsY;4CACAG;4CACAU,kBAAkBvW,OAAOuW,gBAAgB;4CACzC1X,OAAO,GAAEmB,eAAAA,OAAO6P,IAAI,qBAAX7P,aAAanB,OAAO;4CAC7B2X,aAAa,GAAExW,gBAAAA,OAAO6P,IAAI,qBAAX7P,cAAawW,aAAa;4CACzCkD,UAAUF,iBAAiBG,KAAK;4CAChCP;4CACAE;4CACA1B;4CACA/I,iBAAiBD;4CACjBG,gBAAgBD;4CAChB8K,cAAc5Z,OAAO4Z,YAAY;4CACjCC,eAAe7Z,OAAO8C,YAAY,CAAC+W,aAAa;4CAChDC,gBAAgBnkB,cAAc6H,cAAc,GACxC,QACAwC,OAAO8C,YAAY,CAACgX,cAAc;4CACtCC,oBAAoB/Z,OAAOga,kBAAkB;4CAC7CvD,kBAAkBzW,OAAO0W,MAAM;4CAC/BC,WAAW3W,OAAO8C,YAAY,CAACmM,GAAG;4CAClC2H,mBAAmB5W,OAAO8C,YAAY,CAAC+T,SAAS;4CAChDjY;4CACAkX;wCACF;oCACF;oCAGF,IAAI8B,aAAa,SAASY,iBAAiB;wCACzCnE,mBAAmB4F,GAAG,CAACzB,iBAAiB9b;wCACxC,0CAA0C;wCAC1C,IAAIlE,cAAc4gB,cAAc;4CAC9BjB,WAAW;4CACXD,QAAQ;4CAER5gB,IAAI4iB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAY5kB,eAAemH;4CAEjC,IACE,OAAO+c,aAAaxB,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoBwB,aAAaxB,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAIwB,aAAaxB,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEX/D,YAAY6F,GAAG,CAACzB,iBAAiB,EAAE;4CACrC;4CAEA,IAAIiB,aAAaW,iBAAiB,EAAE;gDAClChG,YAAY6F,GAAG,CACbzB,iBACAiB,aAAaW,iBAAiB;gDAEhC9B,gBAAgBmB,aAAaW,iBAAiB,CAAC/a,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;gDAE3B2Y,QAAQ;4CACV;4CAEA,MAAMmC,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAACxS,MAAM,GAAG;gDAE1C,IACE5H,OAAO0W,MAAM,KAAK,YAClByD,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAItQ,MACR,CAAC,MAAM,EAAEvN,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAACyd,WAAW;oDACd/F,YAAY6F,GAAG,CAACzB,iBAAiB;wDAC/B;4DACEgC,QAAQ,CAAC;4DACTjb,UAAU7C;4DACV+d,iBAAiB/d;4DACjBge,qBAAqB,EAAE;4DACvBC,cACElB,aAAamB,qBAAqB;4DACpCC,oBAAoB,EAAE;4DACtBC,yBAAyB;wDAC3B;qDACD;oDACD3C,WAAW;gDACb,OAAO,IACL,CAACoC,2BACAF,CAAAA,UAAUU,OAAO,KAAK,WACrBV,UAAUU,OAAO,KAAK,cAAa,GACrC;oDACA3G,YAAY6F,GAAG,CAACzB,iBAAiB,EAAE;oDACnCL,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAIwB,aAAamB,qBAAqB,EAAE;gDACtCtG,cAAc2F,GAAG,CACfzB,iBACAiB,aAAamB,qBAAqB;4CAEtC;4CAEArG,kBAAkB0F,GAAG,CAACzB,iBAAiB6B;wCACzC;oCACF,OAAO;wCACL,IAAI7hB,cAAc4gB,cAAc;4CAC9B,IAAIK,aAAauB,cAAc,EAAE;gDAC/Btd,QAAQG,IAAI,CACV,CAAC,kFAAkF,EAAEnB,MAAM;4CAE/F;4CACA+c,aAAatB,QAAQ,GAAG;4CACxBsB,aAAauB,cAAc,GAAG;wCAChC;wCAEA,IACEvB,aAAatB,QAAQ,KAAK,SACzBsB,CAAAA,aAAapB,WAAW,IAAIoB,aAAawB,SAAS,AAAD,GAClD;4CACAjI,iBAAiB;wCACnB;wCAEA,IAAIyG,aAAapB,WAAW,EAAE;4CAC5BA,cAAc;4CACdpE,eAAejH,GAAG,CAACtQ;wCACrB;wCAEA,IAAI+c,aAAajE,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAIiE,aAAauB,cAAc,EAAE;4CAC/Blc,SAASkO,GAAG,CAACtQ;4CACbwb,QAAQ;4CAER,IACEuB,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAACxS,MAAM,GAAG,GACxC;gDACAuM,gBAAgB8F,GAAG,CACjBvd,MACA+c,aAAaW,iBAAiB;gDAEhC9B,gBAAgBmB,aAAaW,iBAAiB,CAAC/a,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;4CAE7B;4CAEA,IACEka,aAAamB,qBAAqB,KAClC5f,aAAakgB,sBAAsB,EACnC;gDACAnH,yBAAyB/G,GAAG,CAACtQ;4CAC/B,OAAO,IACL+c,aAAamB,qBAAqB,KAClC5f,aAAamgB,SAAS,EACtB;gDACArH,uBAAuB9G,GAAG,CAACtQ;4CAC7B;wCACF,OAAO,IAAI+c,aAAa2B,cAAc,EAAE;4CACtClH,iBAAiBlH,GAAG,CAACtQ;wCACvB,OAAO,IACL+c,aAAatB,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAMrB,oCAAqC,OAC5C;4CACAvV,YAAYwL,GAAG,CAACtQ;4CAChByb,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChDtZ,SAASkO,GAAG,CAACtQ;4CACbwb,QAAQ;wCACV;wCAEA,IAAIhK,eAAexR,SAAS,QAAQ;4CAClC,IACE,CAAC+c,aAAatB,QAAQ,IACtB,CAACsB,aAAauB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAI/Q,MACR,CAAC,cAAc,EAAE9X,4CAA4C,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAM4kB,mCACP,CAAC0C,aAAauB,cAAc,EAC5B;gDACAxZ,YAAY6Z,MAAM,CAAC3e;4CACrB;wCACF;wCAEA,IACEnI,oBAAoB4N,QAAQ,CAACzF,SAC7B,CAAC+c,aAAatB,QAAQ,IACtB,CAACsB,aAAauB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAI/Q,MACR,CAAC,OAAO,EAAEvN,KAAK,GAAG,EAAEvK,4CAA4C,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAO4X,KAAK;oCACZ,IACE,CAACxR,QAAQwR,QACTA,IAAIuR,OAAO,KAAK,0BAEhB,MAAMvR;oCACRiK,aAAahH,GAAG,CAACtQ;gCACnB;4BACF;4BAEA,IAAIkb,aAAa,OAAO;gCACtB,IAAIM,SAASC,UAAU;oCACrBzE;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAa,UAAUyF,GAAG,CAACvd,MAAM;4BAClB8b;4BACAT;4BACAC;4BACAG;4BACAD;4BACAD;4BACAI;4BACAC;4BACAiD,qBAAqBlV;4BACrB2S,SAASI;4BACToC,cAAcnV;4BACdoV,kBAAkBpV;4BAClBqV,qBAAqBrV;wBACvB;oBACF;gBACF;gBAGJ,MAAMsV,kBAAkB,MAAMtF;gBAC9B,MAAMuF,qBACJ,AAAC,MAAM1F,qCACNyF,mBAAmBA,gBAAgBP,cAAc;gBAEpD,MAAMS,cAAc;oBAClBvG,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACAxC;oBACAyC,uBAAuBmG;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAItI,oBAAoBA,mBAAmBuI,cAAc;YACzDnhB,iBAAiB,iCAAiCsG;YAElD,IAAIqU,0BAA0B;gBAC5B5X,QAAQG,IAAI,CACVvM,KAAKC,OAAO,CAAC,SAAS,CAAC,KACrBA,OACE,CAAC,qJAAqJ,CAAC;gBAG7JmM,QAAQG,IAAI,CACV;YAEJ;YAEA,MAAM,EAAE+b,YAAY,EAAE,GAAG5Z;YAEzB,MAAM+b,gCAA0C,EAAE;YAClD,IAAIxa,wBAAwB;gBAC1Bwa,8BAA8B3Z,IAAI,CAChClQ,KAAKoL,IAAI,CAACjJ,kBAAkB,GAAG9B,8BAA8B,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IAAI,CAACsT,eAAgB+N,CAAAA,uBAAuBC,qBAAoB,GAAI;oBAClEkI,8BAA8B3Z,IAAI,CAChClQ,KAAKoL,IAAI,CACPjJ,kBACA,CAAC,KAAK,EAAE9B,8BAA8B,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAMypB,8BAA8B/a,cACjCU,UAAU,CAAC,kCACXkF,OAAO,CAAC;gBACP,MAAMoV,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAACnH,KAAKoH,MAAM,IAAIld,OAAOC,OAAO,CACvCe,OAAO8C,YAAY,CAAC+W,aAAa,IAAI,CAAC,GACrC;oBACD,IAAI/E,OAAOoH,OAAO;wBAChBD,uBAAuB,CAACnH,IAAI,GAAG5iB,KAAKoQ,QAAQ,CAAClF,SAAS8e;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvDrb,SAAS;oBACTd,QAAQ;wBACN,GAAGA,MAAM;wBACToc,YAAY/V;wBACZ,GAAI1Q,cAAc6H,cAAc,GAC5B;4BACE6e,UAAU;wBACZ,IACA,CAAC,CAAC;wBACNzC,cAAcA,eACV1nB,KAAKoQ,QAAQ,CAAClF,SAASwc,gBACvB5Z,OAAO4Z,YAAY;wBACvB9W,cAAc;4BACZ,GAAG9C,OAAO8C,YAAY;4BACtB+W,eAAeoC;4BACfK,iBAAiB3mB,cAAc6H,cAAc;4BAC7C+e,uBAAuBvW;wBACzB;oBACF;oBACAtE,QAAQiD;oBACR6X,gBAAgBtqB,KAAKoQ,QAAQ,CAAClB,uBAAuBuD;oBACrD5C,OAAO;wBACL3N;wBACAlC,KAAKoQ,QAAQ,CAAClF,SAASiU;wBACvB1d;wBACAO;wBACAhC,KAAKoL,IAAI,CAACjJ,kBAAkBa;wBAC5BhD,KAAKoL,IAAI,CAACjJ,kBAAkBG;wBAC5BtC,KAAKoL,IAAI,CAACjJ,kBAAkBU,4BAA4B;2BACpD,CAAC8Q,cACD;4BACE3T,KAAKoL,IAAI,CACPjJ,kBACAW,qCAAqC;4BAEvCb;yBACD,GACD,EAAE;2BACFuN,SACA;+BACM1B,OAAO8C,YAAY,CAACiT,GAAG,GACvB;gCACE7jB,KAAKoL,IAAI,CACPjJ,kBACAS,iCAAiC;gCAEnC5C,KAAKoL,IAAI,CACPjJ,kBACAS,iCAAiC;6BAEpC,GACD,EAAE;4BACN5C,KAAKoL,IAAI,CAACjJ,kBAAkBI;4BAC5BvC,KAAKoL,IAAI,CAAC5I;4BACVC;4BACAzC,KAAKoL,IAAI,CACPjJ,kBACAY,4BAA4B;4BAE9B/C,KAAKoL,IAAI,CACPjJ,kBACAY,4BAA4B;yBAE/B,GACD,EAAE;2BACFkT,YAAY,CAACtC,cACb;4BACExQ,uBAAuB;4BACvBnD,KAAKoL,IAAI,CAACjJ,kBAAkBgB,uBAAuB;yBACpD,GACD,EAAE;wBACN3B;wBACAxB,KAAKoL,IAAI,CAACjJ,kBAAkBQ,qBAAqB;wBACjD3C,KAAKoL,IAAI,CAACjJ,kBAAkBQ,qBAAqB;wBACjDP;2BACGynB;qBACJ,CACE5c,MAAM,CAAC5L,aACP8L,GAAG,CAAC,CAACyC,OAAS5P,KAAKoL,IAAI,CAAC0C,OAAO5C,OAAO,EAAE0E;oBAC3C2a,QAAQ,EAAE;gBACZ;gBAEA,OAAON;YACT;YAEF,IAAI,CAACnJ,gBAAgB;gBACnBgJ,4BAA4BS,MAAM,CAACra,IAAI,CACrClQ,KAAKoQ,QAAQ,CACXqC,KACAzS,KAAKoL,IAAI,CACPpL,KAAKqQ,OAAO,CACVgB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMkZ,iBAAiBvR,UAAUoN,IAAI,CAAC,CAACjY,IACrCA,EAAE6B,QAAQ,CAAC9P;YAEb,IAAIiP,oBAAoB;YAExB,IAAIob,gBAAgB;gBAClB,MAAM7D,aAAa,MAAMniB,8BAA8B;oBACrDkiB,gBAAgB;oBAChBD,cAAczmB,KAAKoL,IAAI,CAACqH,KAAK+X;oBAC7B1c;oBACA0B;oBACAiJ,gBAAgB3K,OAAO2K,cAAc;oBACrCgB,OAAO;oBACPjP,MAAM;gBACR;gBAEA,IAAImc,WAAWC,mBAAmB,EAAE;oBAClCC;gBACF;gBAEA,IAAIF,WAAWG,OAAO,KAAK,UAAU;wBAIvBH;oBAHZvX,oBAAoB;oBACpB8T,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClD2D,SAASH,WAAWG,OAAO;wBAC3B2D,UAAU9D,EAAAA,yBAAAA,WAAW+D,UAAU,qBAArB/D,uBAAuB8D,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAIjX,aAAa;wBACf,MAAMzH,cACJlM,KAAKoL,IAAI,CACPF,SACA,UACAwB,SACAtJ,uCAEF8f,wBAAwBC,SAAS,CAAC,eAAe,CAACsH,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAM/c,6BAA6BxC,SAASgY;YAE5C,IAAI,CAAC/P,kBAAkB,CAACkM,oBAAoB;gBAC1CA,qBAAqBrX,mBAAmB;oBACtCyK;oBACA3E;oBACA5C;oBACA2V,mBAAmB5a,8BAA8Bqc;oBACjDhT,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACA+R;oBACA1B;oBACAlQ;oBACAyE,aAAa;gBACf,GAAGoN,KAAK,CAAC,CAAClJ;oBACRrM,QAAQyM,KAAK,CAACJ;oBACdtD,QAAQQ,IAAI,CAAC;gBACf;YACF;YAEA,IAAIiN,iBAAiB6D,IAAI,GAAG,KAAKjZ,SAASiZ,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/D1I,eAAeO,UAAU,GAAG1T,UAAU;uBACjCgY;uBACApV;iBACJ,EAAEO,GAAG,CAAC,CAAC3C;oBACN,OAAOzC,eAAeyC,MAAMkC;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAMqC,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMxD,cAAc8Q,oBAAoBG;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAM0N,oBACJ,CAACzH,4BAA6B,CAAA,CAACG,yBAAyBvH,WAAU;YAEpE,IAAI8F,aAAa+D,IAAI,GAAG,GAAG;gBACzB,MAAMhO,MAAM,qBAQX,CARW,IAAIE,MACd,CAAC,qCAAqC,EACpC+J,aAAa+D,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAI/D;iBAAa,CACnE3U,GAAG,CAAC,CAAC2d,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxB1f,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACAyM,IAAIC,IAAI,GAAG;gBACX,MAAMD;YACR;YAEA,MAAM1R,aAAa+E,SAASwB;YAE5B,IAAIoB,OAAO8C,YAAY,CAACma,WAAW,EAAE;gBACnC,MAAMC,WACJ3Z,QAAQ;gBAEV,MAAM4Z,eAAe,MAAM,IAAIjL,QAAkB,CAAC1O,SAAS4Z;oBACzDF,SACE,YACA;wBAAEpU,KAAK5W,KAAKoL,IAAI,CAACF,SAAS;oBAAU,GACpC,CAAC2M,KAAKhI;wBACJ,IAAIgI,KAAK;4BACP,OAAOqT,OAAOrT;wBAChB;wBACAvG,QAAQzB;oBACV;gBAEJ;gBAEAia,4BAA4Bja,KAAK,CAACK,IAAI,IACjC+a,aAAa9d,GAAG,CAAC,CAACtB,WACnB7L,KAAKoL,IAAI,CAAC0C,OAAO5C,OAAO,EAAE,UAAUW;YAG1C;YAEA,MAAMsf,WAAqC;gBACzC;oBACE/S,aAAa;oBACbC,iBAAiBvK,OAAO8C,YAAY,CAAC+L,eAAe,GAAG,IAAI;gBAC7D;gBACA;oBACEvE,aAAa;oBACbC,iBAAiBvK,OAAO8C,YAAY,CAACma,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACE3S,aAAa;oBACbC,iBAAiBvK,OAAO8C,YAAY,CAACwa,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACEhT,aAAa;oBACbC,iBAAiBvK,OAAO8C,YAAY,CAACmM,GAAG,GAAG,IAAI;gBACjD;gBACA;oBACE3E,aAAa;oBACbC,iBAAiB/O,2BAA2BwE,UAAU,IAAI;gBAC5D;aACD;YACDiI,UAAUS,MAAM,CACd2U,SAAShe,GAAG,CAAC,CAACke;gBACZ,OAAO;oBACL/S,WAAWrU;oBACXsU,SAAS8S;gBACX;YACF;YAGF,MAAM1d,iCACJzC,SACA4e;YAGF,iDAAiD;YACjD,sDAAsD;YACtD,IAAI3W,kBAAkB,CAACQ,aAAa;gBAClCvO,IAAI4P,IAAI,CAAC;gBAET,MAAMjG,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMnG,gBAAgB;wBACpB2B;wBACA4C;oBACF;gBACF;YACJ;YAEA,MAAMqB,qBAAyC,MAAM/C,aACnDpM,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkBG;YAGvC,MAAMmK,oBAAuC;gBAC3CmC,SAAS;gBACT5B,QAAQ,CAAC;gBACTO,eAAe,CAAC;gBAChB+d,gBAAgB,EAAE;gBAClBC,SAAS/R;YACX;YAEA,MAAMgS,qBAA+B,EAAE;YAEvC,MAAM,EAAE7N,IAAI,EAAE,GAAG7P;YAEjB,MAAM2d,wBAAwBppB,oBAAoB4K,MAAM,CACtD,CAACzC,OACCkK,WAAW,CAAClK,KAAK,IACjBkK,WAAW,CAAClK,KAAK,CAAC8L,UAAU,CAAC;YAEjCmV,sBAAsBC,OAAO,CAAC,CAAClhB;gBAC7B,IAAI,CAACoC,SAAS+e,GAAG,CAACnhB,SAAS,CAAC4Y,0BAA0B;oBACpD9T,YAAYwL,GAAG,CAACtQ;gBAClB;YACF;YAEA,MAAMohB,cAAcH,sBAAsBxb,QAAQ,CAAC;YACnD,MAAM4b,sBACJ,CAACD,eAAe,CAACrI,yBAAyB,CAACH;YAE7C,MAAM0I,gBAAgB;mBAAIxc;mBAAgB1C;aAAS;YACnD,MAAMmf,iBAAiB7J,YAAYyJ,GAAG,CAACzoB;YACvC,MAAM8oB,kBAAkB/P,aAAa8P;YAErC,MAAM5jB,uBAAuB;gBAC3B+W,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAACpL,iBACAgY,CAAAA,cAAcpW,MAAM,GAAG,KACtBmV,qBACAgB,uBACArc,MAAK,GACP;gBACA,MAAMyc,uBACJld,cAAcU,UAAU,CAAC;gBAC3B,MAAMwc,qBAAqBvc,YAAY,CAAC;oBACtCjK,uBACE;2BACKqmB;2BACA9c,SAASW,KAAK,CAAC1C,MAAM,CAAC,CAACzC,OAAS,CAACshB,cAAc7b,QAAQ,CAACzF;qBAC5D,EACDoC,UACA,IAAIsQ,IACFhE,MAAMC,IAAI,CAAC8I,gBAAgBlV,OAAO,IAAII,GAAG,CACvC,CAAC,CAAC3C,MAAMwC,OAAO;wBACb,OAAO;4BAACxC;4BAAMwC,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAMC,QAAQ;yBAAE;oBACtD;oBAKN,MAAMuF,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;oBAEV,MAAMqZ,eAAmC;wBACvC,GAAGpe,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7Dqe,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7Dxf,SAAS8e,OAAO,CAAC,CAAClhB;gCAChB,IAAInH,eAAemH,OAAO;oCACxBghB,mBAAmBtb,IAAI,CAAC1F;oCAExB,IAAIoX,uBAAuB+J,GAAG,CAACnhB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAImT,MAAM;4CACRyO,UAAU,CAAC,CAAC,CAAC,EAAEzO,KAAK2G,aAAa,GAAG9Z,MAAM,CAAC,GAAG;gDAC5CA;gDACA6hB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAAC5hB,KAAK,GAAG;gDACjBA;gDACA6hB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAAC5hB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACdyX,gBAAgByJ,OAAO,CAAC,CAAC1e,QAAQxC;gCAC/BwC,OAAO0e,OAAO,CAAC,CAACte;oCACdgf,UAAU,CAAChf,MAAMC,QAAQ,CAAC,GAAG;wCAC3B7C;wCACA8hB,UAAUlf,MAAMmb,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIsC,mBAAmB;gCACrBuB,UAAU,CAAC,OAAO,GAAG;oCACnB5hB,MAAMwR,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAI6P,qBAAqB;gCACvBO,UAAU,CAAC,OAAO,GAAG;oCACnB5hB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChD0X,YAAYwJ,OAAO,CAAC,CAAC1e,QAAQsZ;gCAC3B,MAAM6B,YAAY9F,kBAAkBkK,GAAG,CAACjG;gCACxC,MAAMkG,iBAAiBrE,CAAAA,6BAAAA,UAAWU,OAAO,MAAK;gCAE9C,MAAM9C,oBAA6BoC,YAC/Btf,uBAAuBiF,OAAO8C,YAAY,CAACmM,GAAG,EAAEoL,aAChD;gCAEJnb,OAAO0e,OAAO,CAAC,CAACte;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAMub,kBAAkB,IACxBvb,MAAMub,kBAAkB,CAACjT,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEA0W,UAAU,CAAChf,MAAMC,QAAQ,CAAC,GAAG;wCAC3B7C,MAAM8b;wCACNgG,UAAUlf,MAAMmb,eAAe;wCAC/BkE,sBAAsBrf,MAAMob,mBAAmB;wCAC/CkE,iBAAiBF;wCACjBG,WAAW;wCACXC,oBAAoB7G;wCACpB8G,wBAAwB,CAACzf,MAAMwb,uBAAuB;oCACxD;gCACF;4BACF;4BAEA,IAAIjL,MAAM;gCACR,KAAK,MAAMnT,QAAQ;uCACd8E;uCACA1C;uCACCie,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCgB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMiB,QAAQlgB,SAAS+e,GAAG,CAACnhB;oCAC3B,MAAMyd,YAAY5kB,eAAemH;oCACjC,MAAMuiB,aAAaD,SAASlL,uBAAuB+J,GAAG,CAACnhB;oCAEvD,KAAK,MAAMwiB,UAAUrP,KAAKhR,OAAO,CAAE;4CAMzByf;wCALR,+DAA+D;wCAC/D,IAAIU,SAAS7E,aAAa,CAAC8E,YAAY;wCACvC,MAAM5c,aAAa,CAAC,CAAC,EAAE6c,SAASxiB,SAAS,MAAM,KAAKA,MAAM;wCAE1D4hB,UAAU,CAACjc,WAAW,GAAG;4CACvB3F,MAAM4hB,EAAAA,mBAAAA,UAAU,CAAC5hB,KAAK,qBAAhB4hB,iBAAkB5hB,IAAI,KAAIA;4CAChCyiB,SAASD;4CACTX,gBAAgBU;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOV,UAAU,CAAC5hB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAO4hB;wBACT;oBACF;oBAEA,MAAMnZ,SAASjT,KAAKoL,IAAI,CAACF,SAAS;oBAClC,MAAMgiB,eAAe,MAAMta,UACzBH,KACA;wBACEM,YAAYmZ;wBACZxZ;wBACAM,QAAQ;wBACRF,aAAa;wBACbQ;wBACAC;wBACA5D,OAAOmc;wBACP7Y;wBACAka,eAAe;wBACftb,YAAYlB,mBAAmBub;oBACjC,GACAnd;oBAGF,sDAAsD;oBACtD,IAAI,CAACme,cAAc;oBAEnB,MAAME,kBAAkB,CAAChgB;4BACK8f;wBAA5B,MAAM1D,uBAAsB0D,2BAAAA,aAAaG,MAAM,CAACd,GAAG,CACjDnf,MAAMC,QAAQ,sBADY6f,yBAEzB1D,mBAAmB;wBAEtB,kEAAkE;wBAClE,+DAA+D;wBAC/D,sBAAsB;wBACtB,IACEA,uBACA,CAACpc,MAAMwb,uBAAuB,IAC9Bxb,MAAMqb,YAAY,KAAK3f,aAAamgB,SAAS,EAC7C;4BACA,OAAOngB,aAAakgB,sBAAsB;wBAC5C;wBAEA,4DAA4D;wBAC5D,6BAA6B;wBAC7B,IAAI,CAAC5b,MAAMqb,YAAY,EAAE;4BACvB,OAAO3f,aAAawkB,SAAS;wBAC/B;wBAEA,OAAOlgB,MAAMqb,YAAY;oBAC3B;oBAEA,MAAM8E,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCP;wBADF,MAAMQ,gBACJR,2BAAAA,aAAaG,MAAM,CAACd,GAAG,CAACiB,gCAAxBN,yBAAqCQ,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAEtF,YAAYqF;gCAAmBE,QAAQxZ;4BAAU;wBAC5D;wBAEA,IACEuZ,aAAatF,UAAU,KAAK,SAC5BsF,aAAatF,UAAU,GAAG,KAC1BsF,aAAaC,MAAM,KAAKxZ,WACxB;4BACA,OAAO;gCACLiU,YAAYsF,aAAatF,UAAU;gCACnCuF,QAAQ7f,OAAO8f,UAAU;4BAC3B;wBACF;wBAEA,OAAOF;oBACT;oBAEA,IAAIpa,eAAeiB,QAAQC,GAAG,CAACqZ,sBAAsB,KAAK,KAAK;wBAC7DzlB,mBAAmB8kB;oBACrB;oBAEAtpB,gCAAgC;wBAC9BsH,SAAS4C,OAAO5C,OAAO;wBACvB4iB,QAAQ;4BACNlZ;+BACGsY,aAAaa,2BAA2B,CAACC,MAAM;yBACnD;oBACH;oBAEAvhB,kBAAkB6e,cAAc,GAAGpS,MAAMC,IAAI,CAC3C+T,aAAae,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMzjB,QAAQ8E,YAAa;wBAC9B,MAAM4e,eAAe1qB,YAAYgH,MAAMU,SAASiJ,WAAW;wBAC3D,MAAM1U,GAAG0uB,MAAM,CAACD;oBAClB;oBAEAhM,YAAYwJ,OAAO,CAAC,CAACxD,mBAAmB5B;4BAWbhE;wBAVzB,MAAM9X,OAAO2X,mBAAmBoK,GAAG,CAACjG;wBACpC,IAAI,CAAC9b,MAAM,MAAM,qBAAoC,CAApC,IAAIvB,eAAe,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAMkf,YAAY9F,kBAAkBkK,GAAG,CAACjG;wBACxC,IAAI,CAAC6B,WAAW,MAAM,qBAA0C,CAA1C,IAAIlf,eAAe,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAImlB,oBACFjG,UAAUC,UAAU,KAAK,KACzBmF,gBAAgB/iB,MAAM4d,UAAU,KAAK;wBAEvC,IAAIgG,uBAAqB9L,iBAAAA,UAAUiK,GAAG,CAAC/hB,0BAAd8X,eAAqB2D,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrF3D,UAAUyF,GAAG,CAACvd,MAAM;gCAClB,GAAI8X,UAAUiK,GAAG,CAAC/hB,KAAK;gCACvByb,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAMqI,oBAAoB3mB,gBAAgB4e;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMP,oBACJ,CAACsI,qBACDxlB,uBAAuBiF,OAAO8C,YAAY,CAACmM,GAAG,EAAEoL,aAC5C,OACAhU;wBAEN,MAAMma,sBACJ,uEAAuE;wBACvExgB,OAAOygB,eAAe,IAAIrlB;wBAE5B,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMslB,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAU7L,KAAK9b;4BAAc;4BACrC;gCACE2nB,MAAM;gCACN7L,KAAK;gCACLoH,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7FjE,oBACA;gCACE;oCACE0I,MAAM;oCACN7L,KAAK;oCACLoH,OAAOsE;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAMI,0BAA8C,EAAE;wBACtD,MAAMC,2BAA+C,EAAE;wBAEvD,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,MAAMC,iCAAqD,EAAE;wBAC7D,MAAMC,+BAAmD,EAAE;wBAC3D,KAAK,MAAMC,oBAAoB5G,kBAAmB;4BAChD,IACE4G,iBAAiBtG,mBAAmB,IACpCsG,iBAAiBtG,mBAAmB,CAAC9S,MAAM,GAAG,GAC9C;gCACAkZ,+BAA+B1e,IAAI,CAAC4e;4BACtC,OAAO;gCACLD,6BAA6B3e,IAAI,CAAC4e;4BACpC;wBACF;wBAEA,MAAMC,+BAA+BhlB,gBACnC6kB,gCACA,CAACE,mBAAqBA,iBAAiBzhB,QAAQ;wBAEjD,MAAM2hB,6BAA6BjlB,gBACjC8kB,8BACA,CAACC,mBAAqBA,iBAAiBzhB,QAAQ;wBAGjD6a,oBAAoB;+BACf8G;+BACAD;yBACJ;wBAED,KAAK,MAAMD,oBAAoB5G,kBAAmB;4BAChD,IACEnC,qBACA+I,iBAAiBtG,mBAAmB,IACpCsG,iBAAiBtG,mBAAmB,CAAC9S,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9BiZ,yBAAyBze,IAAI,CAAC4e;4BAChC,OAAO;gCACL,4DAA4D;gCAC5D,uCAAuC;gCACvCJ,wBAAwBxe,IAAI,CAAC4e;4BAC/B;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAM1hB,SAASshB,wBAAyB;4BAC3C,IAAIrrB,eAAemH,SAAS4C,MAAMC,QAAQ,KAAK7C,MAAM;4BAErD,MAAM,EACJ2W,WAAW,CAAC,CAAC,EACbqI,mBAAmB,EACnByF,YAAY,EACb,GAAG/B,aAAaG,MAAM,CAACd,GAAG,CAACnf,MAAMC,QAAQ,KAAK,CAAC;4BAEhD,MAAMqgB,eAAeH,gBACnBngB,MAAMC,QAAQ,EACd8a,UAAUC,UAAU;4BAGtB9F,UAAUyF,GAAG,CAAC3a,MAAMC,QAAQ,EAAE;gCAC5B,GAAIiV,UAAUiK,GAAG,CAACnf,MAAMC,QAAQ,CAAC;gCACjC4hB;gCACAzF;gCACAH,qBAAqBqE;4BACvB;4BAEA,uEAAuE;4BACvEpL,UAAUyF,GAAG,CAACvd,MAAM;gCAClB,GAAI8X,UAAUiK,GAAG,CAAC/hB,KAAK;gCACvBykB;gCACAzF;gCACAH,qBAAqBqE;4BACvB;4BAEA,IAAIA,aAAatF,UAAU,KAAK,GAAG;gCACjC,MAAM8G,kBAAkB3rB,kBAAkB6J,MAAMC,QAAQ;gCAExD,IAAI8hB;gCACJ,IAAId,mBAAmB;oCACrBc,YAAY;gCACd,OAAO;oCACLA,YAAYnvB,KAAKovB,KAAK,CAAChkB,IAAI,CAAC,GAAG8jB,kBAAkB3uB,YAAY;gCAC/D;gCAEA,IAAI8uB;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAAChB,qBAAqBvR,iBAAiB;oCACzCuS,oBAAoBrvB,KAAKovB,KAAK,CAAChkB,IAAI,CACjC,GAAG8jB,kBAAkB5uB,qBAAqB;gCAE9C;gCAEA,MAAMgvB,OAAOppB,YAAYib;gCACzB,MAAMoO,SACJniB,MAAMC,QAAQ,KAAKpK,6BACf,MACAqsB,KAAKC,MAAM;gCAEjB9iB,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;oCACzCmiB,eAAeD;oCACfE,gBAAgBH,KAAKpa,OAAO;oCAC5Bwa,eAAe5S,kBACXiJ,oBACE/c,cAAc2mB,gBAAgB,GAC9B3mB,cAAc4mB,MAAM,GACtBzb;oCACJ0b,iBAAiB9J;oCACjB+J,uBAAuBtB;oCACvBuB,0BAA0BrC,aAAatF,UAAU;oCACjD4H,sBAAsBtC,aAAaC,MAAM;oCACzCzgB,UAAU1C;oCACV2kB;oCACAE;oCACAY,aAAa3lB;gCACf;4BACF,OAAO;gCACL8jB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpB9L,UAAUyF,GAAG,CAAC3a,MAAMC,QAAQ,EAAE;oCAC5B,GAAIiV,UAAUiK,GAAG,CAACnf,MAAMC,QAAQ,CAAC;oCACjC2Y,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAACmI,qBAAqB/qB,eAAemH,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAACub,mBAAmB;gCACtB4I,yBAAyBze,IAAI,CAAC;oCAC5BoY,QAAQ,CAAC;oCACTjb,UAAU7C;oCACV+d,iBAAiB/d;oCACjBge,qBAAqB,EAAE;oCACvBC,cACErG,cAAcmK,GAAG,CAACjG,oBAClBxd,aAAawkB,SAAS;oCACxB3E,oBAAoB,EAAE;oCACtBC,yBAAyB;gCAC3B;4BACF;4BAEA,KAAK,MAAMxb,SAASuhB,yBAA0B;oCAG3BzB,0BAmKM9f;gCArKvB,MAAM8hB,kBAAkB3rB,kBAAkB6J,MAAMC,QAAQ;gCAExD,MAAM8T,YAAW+L,2BAAAA,aAAaG,MAAM,CAACd,GAAG,CACtCnf,MAAMC,QAAQ,sBADC6f,yBAEd/L,QAAQ;gCAEX,MAAMuM,eAAeH,gBAAgBngB,MAAMC,QAAQ;gCAEnD,IAAI8hB,YAA2B;gCAC/B,IAAI,CAACd,mBAAmB;oCACtBc,YAAYnvB,KAAKovB,KAAK,CAAChkB,IAAI,CAAC,GAAG8jB,kBAAkB3uB,YAAY;gCAC/D;gCAEA,IAAI8uB;gCACJ,IAAIa,eAAe/S,eAAe5P,aAAa,CAAC8Y,IAAI,CAClD,CAAC5I,IAAMA,EAAEjT,IAAI,KAAK4C,MAAMC,QAAQ;gCAElC,IAAI,CAACghB,qBAAqBvR,iBAAiB;oCACzCuS,oBAAoBrvB,KAAKovB,KAAK,CAAChkB,IAAI,CACjC,GAAG8jB,kBAAkB5uB,qBAAqB;oCAG5C,4DAA4D;oCAC5D,2DAA2D;oCAC3D,6DAA6D;oCAC7D,uBAAuB;oCACvB,IAAI,CAAC4vB,cAAc;wCACjBA,eAAe3lB,YAAY6C,MAAMC,QAAQ,EAAE7C;wCAC3CyS,YAAY8K,GAAG,CAAC3a,MAAMC,QAAQ,EAAE7C;wCAEhC,yDAAyD;wCACzD,wBAAwB;wCACxB0lB,aAAaC,mBAAmB,GAAG;wCAEnC,4DAA4D;wCAC5D,wBAAwB;wCACxB5iB,cAAc2C,IAAI,CAACggB;oCACrB;gCACF;gCAEA,IACE,CAAC7B,qBACAlN,CAAAA,CAAAA,4BAAAA,SAAUiP,YAAY,KACpBhjB,MAAMub,kBAAkB,IACvBvb,MAAMub,kBAAkB,CAACjT,MAAM,GAAG,CAAC,GACvC;oCACA,2DAA2D;oCAC3D,6DAA6D;oCAC7D,iBAAiB;oCACjB,IAAI,CAACwa,cAAc;wCACjBA,eAAe3iB,cAAc8Y,IAAI,CAAC,CAAC5I,IAAMA,EAAEjT,IAAI,KAAKA;wCAEpD,wDAAwD;wCACxD,IAAI,CAAC0lB,cAAc;4CACjB,MAAM,qBAA6C,CAA7C,IAAIjnB,eAAe,4BAAnB,qBAAA;uDAAA;4DAAA;8DAAA;4CAA4C;wCACpD;oCACF;oCAEA,IAAIkY,4BAAAA,SAAUiP,YAAY,EAAE;wCAC1B,MAAMC,kBAAkBlP,SAASiP,YAAY,CAAC/J,IAAI,CAAC,CAACiK,OAClDA,KAAKC,QAAQ,CAAC;wCAEhB,IAAI,CAACF,iBAAiB;4CACpB,MAAM,qBAAoD,CAApD,IAAItY,MAAM,CAAC,uCAAuC,CAAC,GAAnD,qBAAA;uDAAA;4DAAA;8DAAA;4CAAmD;wCAC3D;wCAEA,kDAAkD;wCAClD,iDAAiD;wCACjD,0CAA0C;wCAC1C,MAAMyY,wBAAwBpnB,8BAC5BgE,MAAMC,QAAQ,EACdgjB;wCAGFG,sBAAsB1lB,MAAM,GAC1B0lB,sBAAsB1lB,MAAM,CAACwD,OAAO,CAClC,8BACA,CAAC,6DAA6D,CAAC;wCAEnEkiB,sBAAsBC,WAAW,GAC/BD,sBAAsBC,WAAW,CAACniB,OAAO,CACvC,yBACA;wCAEJ4hB,aAAaQ,yBAAyB,KAAK,EAAE;wCAC7CR,aAAaQ,yBAAyB,CAACxgB,IAAI,CACzCsgB;oCAEJ,OAOK,IACHpjB,MAAMub,kBAAkB,IACxBvb,MAAMub,kBAAkB,CAACjT,MAAM,GAAG,GAClC;wCACAwa,aAAaS,qBAAqB,GAAG;wCACrCT,aAAaQ,yBAAyB,GAAG;4CACvCvnB,qCACE+mB,aAAa1lB,IAAI,EACjB,2DAA2D;4CAC3D,2DAA2D;4CAC3D,4DAA4D;4CAC5D;yCAEH;oCACH;gCACF;gCAEA8X,UAAUyF,GAAG,CAAC3a,MAAMC,QAAQ,EAAE;oCAC5B,GAAIiV,UAAUiK,GAAG,CAACnf,MAAMC,QAAQ,CAAC;oCACjCujB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3C3B,cAAclJ;gCAChB;gCAEA,MAAM0C,eAAe2E,gBAAgBhgB;gCAErC,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAMyjB,uBACJ9K,qBAAqB0C,iBAAiB3f,aAAamgB,SAAS,GACxDyE,eACAvZ;gCAEN,MAAMqB,WAAqBzM,4BACzB0f,cACArb,MAAMC,QAAQ;gCAGhB,MAAMiiB,OACJnO,YACA4E,qBACA0C,iBAAiB3f,aAAamgB,SAAS,GACnC/iB,YAAYib,YACZ,CAAC;gCAEP1U,kBAAkBc,aAAa,CAACH,MAAMC,QAAQ,CAAC,GAAG;oCAChDwiB,iBAAiB9J;oCACjB2J,eAAe5S,kBACXiJ,oBACE/c,cAAc2mB,gBAAgB,GAC9B3mB,cAAc4mB,MAAM,GACtBzb;oCACJ2b,uBAAuBtB;oCACvB9jB,YAAYtJ,oBACVsF,mBAAmB0G,MAAMC,QAAQ,EAAE;wCACjC1C,iBAAiB;oCACnB,GAAGE,EAAE,CAACC,MAAM;oCAEdqkB;oCACA3Z;oCACAsb,kBAAkB,EAAED,wCAAAA,qBAAsBzI,UAAU;oCACpD2I,cAAc,EAAEF,wCAAAA,qBAAsBlD,MAAM;oCAC5CqD,gBAAgB1B,KAAKC,MAAM;oCAC3B0B,iBAAiB3B,KAAKpa,OAAO;oCAC7ByT,oBAAoBnT,WAChBpI,MAAMub,kBAAkB,GACxBxU;oCACJ+c,qBAAqB9jB,EAAAA,6BAAAA,MAAMob,mBAAmB,qBAAzBpb,2BAA2BsI,MAAM,IAClDlL,OACA2J;oCACJgd,gBAAgB,CAAChC,YACb,OACA/tB,oBACEsF,mBAAmByoB,WAAW;wCAC5BxkB,iBAAiB;wCACjBymB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGxmB,EAAE,CAACC,MAAM;oCAElBukB;oCACAiC,wBAAwB,CAACjC,oBACrBlb,YACA/S,oBACEsF,mBAAmB2oB,mBAAmB;wCACpC1kB,iBAAiB;wCACjBymB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGxmB,EAAE,CAACC,MAAM;oCAElBmlB,aAAa3lB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAMinB,mBAAmB,OACvBC,YACAhnB,MACAoF,MACAkd,OACA2E,KACAC,oBAAoB,KAAK;wBAEzB,OAAOzF,qBACJxc,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZE,OAAO,GAAGA,KAAK,CAAC,EAAE6hB,KAAK;4BACvB,MAAME,OAAO3xB,KAAKoL,IAAI,CAAC6H,QAAQrD;4BAC/B,MAAMgL,WAAWpX,YACfguB,YACAtmB,SACAiJ,WACA;4BAGF,MAAMyd,eAAe5xB,KAClBoQ,QAAQ,CACPpQ,KAAKoL,IAAI,CAACF,SAAS/I,mBACnBnC,KAAKoL,IAAI,CACPpL,KAAKoL,IAAI,CACPwP,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5B4W,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACN3kB,GAAG,CAAC,IAAM,MACV/B,IAAI,CAAC,OAEVwE,OAGHtB,OAAO,CAAC,OAAO;4BAElB,IACE,CAACwe,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhDzqB,CAAAA,oBAAoB4N,QAAQ,CAACzF,SAC7B,CAACihB,sBAAsBxb,QAAQ,CAACzF,KAAI,GAGxC;gCACA+X,aAAa,CAAC/X,KAAK,GAAGonB;4BACxB;4BAEA,MAAMG,OAAO/xB,KAAKoL,IAAI,CAACF,SAAS/I,kBAAkByvB;4BAClD,MAAMI,aACJvlB,kBAAkB6e,cAAc,CAACrb,QAAQ,CAACzF;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAACmT,QAAQ+T,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMvyB,GAAGyK,KAAK,CAAClK,KAAKqQ,OAAO,CAAC0hB,OAAO;oCAAEzhB,WAAW;gCAAK;gCACrD,MAAM7Q,GAAGwyB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAIpU,QAAQ,CAACmP,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOvK,aAAa,CAAC/X,KAAK;4BAC5B;4BAEA,IAAImT,MAAM;gCACR,IAAI+T,mBAAmB;gCAEvB,MAAMQ,YAAY1nB,SAAS,MAAMxK,KAAKmyB,OAAO,CAACviB,QAAQ;gCACtD,MAAMwiB,sBAAsBR,aAAaC,KAAK,CAC5C,SAASnc,MAAM;gCAGjB,KAAK,MAAMsX,UAAUrP,KAAKhR,OAAO,CAAE;oCACjC,MAAM0lB,UAAU,CAAC,CAAC,EAAErF,SAASxiB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACEsiB,SACArgB,kBAAkB6e,cAAc,CAACrb,QAAQ,CAACoiB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsBtyB,KACzBoL,IAAI,CACH,SACA4hB,SAASkF,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/B1nB,SAAS,MAAM,KAAK4nB,qBAErB9jB,OAAO,CAAC,OAAO;oCAElB,MAAMikB,cAAcvyB,KAAKoL,IAAI,CAC3B6H,QACA+Z,SAASkF,WACT1nB,SAAS,MAAM,KAAKoF;oCAEtB,MAAM4iB,cAAcxyB,KAAKoL,IAAI,CAC3BF,SACA/I,kBACAmwB;oCAGF,IAAI,CAACxF,OAAO;wCACVvK,aAAa,CAAC8P,QAAQ,GAAGC;oCAC3B;oCACA,MAAM7yB,GAAGyK,KAAK,CAAClK,KAAKqQ,OAAO,CAACmiB,cAAc;wCACxCliB,WAAW;oCACb;oCACA,MAAM7Q,GAAGwyB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAOxG,qBACJxc,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAMiiB,OAAO3xB,KAAKoL,IAAI,CACpBF,SACA,UACA,OACA;4BAEF,MAAMonB,sBAAsBtyB,KACzBoL,IAAI,CAAC,SAAS,YACdkD,OAAO,CAAC,OAAO;4BAElB,IAAI/O,WAAWoyB,OAAO;gCACpB,MAAMlyB,GAAG8Q,QAAQ,CACfohB,MACA3xB,KAAKoL,IAAI,CAACF,SAAS,UAAUonB;gCAG/B,mEAAmE;gCACnE,yEAAyE;gCACzE,IAAI3U,MAAM;oCACR,KAAK,MAAMqP,UAAUrP,KAAKhR,OAAO,CAAE;wCACjC,MAAM0lB,UAAU,CAAC,CAAC,EAAErF,OAAO,IAAI,CAAC;wCAChCzK,aAAa,CAAC8P,QAAQ,GAAGC;oCAC3B;gCACF;gCAEA/P,aAAa,CAAC,OAAO,GAAG+P;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAItG,iBAAiB;wBACnB,MAAMyG;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAACzW,eAAe,CAACC,aAAa4O,mBAAmB;4BACnD,MAAM0G,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAI1F,qBAAqB;wBACvB,MAAM0F,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAM/mB,QAAQshB,cAAe;wBAChC,MAAMgB,QAAQlgB,SAAS+e,GAAG,CAACnhB;wBAC3B,MAAMkoB,sBAAsB9Q,uBAAuB+J,GAAG,CAACnhB;wBACvD,MAAMyd,YAAY5kB,eAAemH;wBACjC,MAAMmoB,SAAS5Q,eAAe4J,GAAG,CAACnhB;wBAClC,MAAMoF,OAAOrM,kBAAkBiH;wBAE/B,MAAMooB,WAAWtQ,UAAUiK,GAAG,CAAC/hB;wBAC/B,MAAMqoB,eAAe3F,aAAa4F,MAAM,CAACvG,GAAG,CAAC/hB;wBAC7C,IAAIooB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAASxM,aAAa,EAAE;gCAC1BwM,SAASrJ,gBAAgB,GAAGqJ,SAASxM,aAAa,CAACjZ,GAAG,CACpD,CAACyN;oCACC,MAAMqF,WAAW4S,aAAaE,eAAe,CAACxG,GAAG,CAAC3R;oCAClD,IAAI,OAAOqF,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAIlI,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAOkI;gCACT;4BAEJ;4BACA2S,SAAStJ,YAAY,GAAGuJ,aAAaE,eAAe,CAACxG,GAAG,CAAC/hB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMwoB,gBAAgB,CAAElG,CAAAA,SAAS7E,aAAa,CAACyK,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiB/mB,MAAMA,MAAMoF,MAAMkd,OAAO;wBAClD;wBAEA,IAAI6F,UAAW,CAAA,CAAC7F,SAAUA,SAAS,CAAC7E,SAAS,GAAI;4BAC/C,MAAMgL,UAAU,GAAGrjB,KAAK,IAAI,CAAC;4BAC7B,MAAM2hB,iBAAiB/mB,MAAMyoB,SAASA,SAASnG,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAMyE,iBAAiB/mB,MAAMyoB,SAASA,SAASnG,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAAC7E,WAAW;gCACd,MAAMsJ,iBAAiB/mB,MAAMA,MAAMoF,MAAMkd,OAAO;gCAEhD,IAAInP,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAMqP,UAAUrP,KAAKhR,OAAO,CAAE;wCACjC,MAAMumB,aAAa,CAAC,CAAC,EAAElG,SAASxiB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAMkjB,eAAeH,gBAAgB2F;wCAErCzmB,kBAAkBO,MAAM,CAACkmB,WAAW,GAAG;4CACrCnD,0BAA0BrC,aAAatF,UAAU;4CACjD4H,sBAAsBtC,aAAaC,MAAM;4CACzCkC,iBAAiB1b;4CACjBub,eAAevb;4CACfjH,UAAU;4CACViiB,WAAWnvB,KAAKovB,KAAK,CAAChkB,IAAI,CACxB,eACAsB,SACA,GAAGkD,KAAK,KAAK,CAAC;4CAEhByf,mBAAmBlb;4CACnB8b,aAAa3lB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAMojB,eAAeH,gBAAgB/iB;oCAErCiC,kBAAkBO,MAAM,CAACxC,KAAK,GAAG;wCAC/BulB,0BAA0BrC,aAAatF,UAAU;wCACjD4H,sBAAsBtC,aAAaC,MAAM;wCACzCkC,iBAAiB1b;wCACjBub,eAAevb;wCACfjH,UAAU;wCACViiB,WAAWnvB,KAAKovB,KAAK,CAAChkB,IAAI,CACxB,eACAsB,SACA,GAAGkD,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7Cyf,mBAAmBlb;wCACnB8b,aAAa3lB;oCACf;gCACF;gCACA,IAAIsoB,UAAU;oCACZA,SAASvJ,mBAAmB,GAAGkE,gBAAgB/iB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAM4C,SAAS6U,gBAAgBsK,GAAG,CAAC/hB,SAAS,EAAE,CAAE;oCACnD,MAAM2oB,WAAW5vB,kBAAkB6J,MAAMC,QAAQ;oCACjD,MAAMkkB,iBACJ/mB,MACA4C,MAAMC,QAAQ,EACd8lB,UACArG,OACA,QACA;oCAEF,MAAMyE,iBACJ/mB,MACA4C,MAAMC,QAAQ,EACd8lB,UACArG,OACA,QACA;oCAGF,IAAI6F,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJ/mB,MACAyoB,SACAA,SACAnG,OACA,QACA;wCAEF,MAAMyE,iBACJ/mB,MACAyoB,SACAA,SACAnG,OACA,QACA;oCAEJ;oCAEA,MAAMY,eAAeH,gBAAgBngB,MAAMC,QAAQ;oCAEnDZ,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;wCACzC0iB,0BAA0BrC,aAAatF,UAAU;wCACjD4H,sBAAsBtC,aAAaC,MAAM;wCACzCkC,iBAAiB1b;wCACjBub,eAAevb;wCACfjH,UAAU1C;wCACV2kB,WAAWnvB,KAAKovB,KAAK,CAAChkB,IAAI,CACxB,eACAsB,SACA,GAAGnJ,kBAAkB6J,MAAMC,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7CgiB,mBAAmBlb;wCACnB8b,aAAa3lB;oCACf;oCAEA,IAAIsoB,UAAU;wCACZA,SAASvJ,mBAAmB,GAAGqE;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAMjuB,GAAG2zB,EAAE,CAACngB,QAAQ;wBAAE3C,WAAW;wBAAM+iB,OAAO;oBAAK;oBACnD,MAAMnnB,cAAciT,mBAAmBoD;gBACzC;gBAEA,iEAAiE;gBACjE,0BAA0B;gBAC1BpF,eAAe5P,aAAa,GAAGtD,yBAC7BsD,eACA,CAACH,QAAW,CAAA;wBACV,kEAAkE;wBAClE,mEAAmE;wBACnE3C,YAAYwS,YAAYsP,GAAG,CAACnf,MAAM5C,IAAI,KAAK4C,MAAM5C,IAAI;wBACrDA,MAAM4C,MAAM5C,IAAI;oBAClB,CAAA;gBAGF,qCAAqC;gBACrC,MAAMuE,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMxD,cAAc8Q,oBAAoBG;YAC1D;YAEA,MAAMmW,mBAAmBjuB,cAAc;YACvC,IAAIkuB,qBAAqBluB,cAAc,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxCyd,OAAO0Q,GAAG;YAEV,MAAMC,cAAclf,QAAQyO,MAAM,CAACD;YACnChN,UAAUS,MAAM,CACd3S,mBAAmB+U,YAAY;gBAC7B4H,mBAAmBiT,WAAW,CAAC,EAAE;gBACjCC,iBAAiBpkB,YAAYuW,IAAI;gBACjC8N,sBAAsB/mB,SAASiZ,IAAI;gBACnC+N,sBAAsB5R,iBAAiB6D,IAAI;gBAC3CgO,cACEjb,WAAWlD,MAAM,GAChBpG,CAAAA,YAAYuW,IAAI,GAAGjZ,SAASiZ,IAAI,GAAG7D,iBAAiB6D,IAAI,AAAD;gBAC1DiO,cAAcjJ;gBACdkJ,oBACE1Q,CAAAA,gCAAAA,aAAcpT,QAAQ,CAAC,uBAAsB;gBAC/C+jB,eAAe3e,iBAAiBK,MAAM;gBACtCue,cAAc/e,QAAQQ,MAAM;gBAC5Bwe,gBAAgB9e,UAAUM,MAAM,GAAG;gBACnCye,qBAAqBjf,QAAQjI,MAAM,CAAC,CAACwQ,IAAW,CAAC,CAACA,EAAEkO,GAAG,EAAEjW,MAAM;gBAC/D0e,sBAAsB/e,iBAAiBpI,MAAM,CAAC,CAACwQ,IAAW,CAAC,CAACA,EAAEkO,GAAG,EAC9DjW,MAAM;gBACT2e,uBAAuBjf,UAAUnI,MAAM,CAAC,CAACwQ,IAAW,CAAC,CAACA,EAAEkO,GAAG,EAAEjW,MAAM;gBACnE4e,iBAAiB/a,oBAAoB,IAAI;gBACzCyB;gBACAwG;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAIna,iBAAiB+sB,cAAc,EAAE;gBACnC,MAAMvd,SAASjT,uBACbyD,iBAAiB+sB,cAAc,CAACC,MAAM;gBAExCze,UAAUS,MAAM,CAACQ;gBACjBjB,UAAUS,MAAM,CACdtS,qCACEsD,iBAAiB+sB,cAAc,CAACE,6BAA6B;gBAGjE,MAAMC,kBAAkBltB,iBAAiB+sB,cAAc,CAACG,eAAe;gBAEvE,KAAK,MAAM,CAAC9R,KAAKoH,MAAM,IAAIld,OAAOC,OAAO,CAAC2nB,iBAAkB;oBAC1D3e,UAAUS,MAAM,CACdzS,uBAAuB;wBACrB;4BACEqU,aAAawK;4BACbvK,iBAAiB2R;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAIpd,SAASiZ,IAAI,GAAG,KAAKrW,QAAQ;oBAmDpB1B;gBAlDX0d,mBAAmBE,OAAO,CAAC,CAACiJ;oBAC1B,MAAMzF,kBAAkB3rB,kBAAkBoxB;oBAC1C,MAAMxF,YAAYnvB,KAAKovB,KAAK,CAAChkB,IAAI,CAC/B,eACAsB,SACA,GAAGwiB,gBAAgB,KAAK,CAAC;oBAG3BziB,kBAAkBc,aAAa,CAAConB,SAAS,GAAG;wBAC1CjqB,YAAYtJ,oBACVsF,mBAAmBiuB,UAAU;4BAC3BhqB,iBAAiB;wBACnB,GAAGE,EAAE,CAACC,MAAM;wBAEd+kB,iBAAiB1b;wBACjBub,eAAevb;wBACfgb;wBACA3Z,UAAUqM,yBAAyB8J,GAAG,CAACgJ,YACnC,OACA/S,uBAAuB+J,GAAG,CAACgJ,YACzB,GAAGzF,gBAAgB,KAAK,CAAC,GACzB;wBACN4B,oBAAoB3c;wBACpB4c,gBAAgB5c;wBAChB+c,qBAAqB/c;wBACrBwU,oBAAoBxU;wBACpBgd,gBAAgB/vB,oBACdsF,mBAAmByoB,WAAW;4BAC5BxkB,iBAAiB;4BACjBymB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGxmB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7CukB,mBAAmBlb;wBACnBmd,wBAAwBnd;wBACxB8b,aAAa3lB;oBACf;gBACF;gBAEA9C,iBAAiBotB,aAAa,GAAGpb,aAAaob,aAAa;gBAC3DptB,iBAAiBqtB,mBAAmB,GAClC/mB,OAAO8C,YAAY,CAACikB,mBAAmB;gBACzCrtB,iBAAiBstB,2BAA2B,GAC1ChnB,OAAO8C,YAAY,CAACkkB,2BAA2B;gBAEjD,MAAMvoB,uBAAuBrB,SAASuB;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9CvB;oBACAwB;oBACAC,OAAO,GAAEmB,eAAAA,OAAO6P,IAAI,qBAAX7P,aAAanB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAMJ,uBAAuBrB,SAAS;oBACpC0D,SAAS;oBACT5B,QAAQ,CAAC;oBACTO,eAAe,CAAC;oBAChBge,SAAS/R;oBACT8R,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMzd,oBAAoB3C,SAAS4C;YACnC,MAAM5B,cAAclM,KAAKoL,IAAI,CAACF,SAAStJ,gBAAgB;gBACrDgN,SAAS;gBACTmmB,kBAAkB,OAAOjnB,OAAOqe,aAAa,KAAK;gBAClD6I,qBAAqBlnB,OAAOmnB,aAAa,KAAK;gBAC9C3R,qBAAqBA,wBAAwB;YAC/C;YACA,MAAM7jB,GAAG0uB,MAAM,CAACnuB,KAAKoL,IAAI,CAACF,SAASvJ,gBAAgBof,KAAK,CAAC,CAAClJ;gBACxD,IAAIA,IAAIC,IAAI,KAAK,UAAU;oBACzB,OAAOkI,QAAQ1O,OAAO;gBACxB;gBACA,OAAO0O,QAAQkL,MAAM,CAACrT;YACxB;YAEA,IAAIN,QAAQzJ,OAAO8C,YAAY,CAACwa,iBAAiB,GAAG;gBAClD,MAAMrc,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAMnO,qBACJkR,KACAzS,KAAKoL,IAAI,CAACF,SAASxJ;gBAEvB;YACJ;YAEA,MAAM2d;YAEN,IAAIkU,oBAAoB;gBACtBA,mBAAmB3J,cAAc;gBACjC2J,qBAAqBpf;YACvB;YAEA,IAAIL,eAAe;gBACjB1O,IAAI4P,IAAI,CACN,CAAC,yGAAyG,CAAC;YAE/G;YAEA,IAAIlH,OAAO0W,MAAM,KAAK,UAAU;gBAC9B,MAAMhS,uBACJ1E,QACA2E,KACAC,oBACAC,cACA5D;YAEJ;YAEA,IAAIjB,OAAO8C,YAAY,CAACskB,WAAW,EAAE;gBACnC,MAAMprB,oBAAoB;oBACxB2I;oBACAvH;oBACAiqB,aAAajmB;oBACbE;oBACAC;oBACA6lB,aAAapnB,OAAO8C,YAAY,CAACskB,WAAW;oBAC5ClmB,UAAUA,SAASW,KAAK;oBACxB8K,aAAaxL;oBACbkO;oBACA1Q;oBACA0C;oBACA+T;oBACA4Q,cAAcjJ;oBACdjd,qBAAqBkc,4BAA4Bja,KAAK;gBACxD;YACF;YAEA,IAAI/B,OAAO0W,MAAM,KAAK,cAAc;gBAClC,MAAM1V,yBACJC,eACA7D,SACA8D,UACAC,sBACAC,uBACA4a,6BACA3a,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAI8jB,kBAAkBA,iBAAiB1J,cAAc;YACrDpe,QAAQC,GAAG;YAEX,IAAI6H,aAAa;gBACfvE,cACGU,UAAU,CAAC,uBACXkF,OAAO,CAAC,IAAM/O,kBAAkB;wBAAEwP;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMnG,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7D7J,cAAcmJ,UAAUsT,WAAW;oBACjC8S,UAAUlqB;oBACVwB,SAASA;oBACTuJ;oBACA4U;oBACApS,gBAAgB3K,OAAO2K,cAAc;oBACrCgK;oBACAD;oBACArT;oBACA8V,UAAUnX,OAAO8C,YAAY,CAACqU,QAAQ;gBACxC;YAGF,MAAMlW,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMqG,UAAUmC,KAAK;YAErC,MAAM6H;QACR;IACF,EAAE,OAAOsV,GAAG;QACV,MAAMtf,YAAmCrM,aAAa6iB,GAAG,CAAC;QAC1D,IAAIxW,WAAW;YACbA,UAAUS,MAAM,CACdpS,iBAAiB;gBACfmc,SAASU,uBAAuBtN;gBAChC2hB,WAAWC,yBAAyBF;gBACpC7U,mBAAmBzP,KAAKG,KAAK,CAAC,AAAC8C,CAAAA,KAAKC,GAAG,KAAKF,cAAa,IAAK;YAChE;QAEJ;QACA,MAAMshB;IACR,SAAU;QACR,kDAAkD;QAClD,MAAM7uB,qBAAqBgvB,GAAG;QAE9B,6DAA6D;QAC7D,MAAMjwB;QACNkB;QAEA,IAAIoN,kBAAkBK,cAAc;YAClCvL,YAAY;gBACVkL;gBACA4hB,MAAM;gBACNrU,YAAY3O;gBACZvH,SAASgJ,aAAahJ,OAAO;gBAC7BwqB,gBAAgB/hB;gBAChBgiB,MAAM;YACR;QACF;IACF;AACF;AAEA,SAAS9O;IACPzhB,IAAI6S,KAAK,CACP,CAAC,0MAA0M,CAAC;IAE9M1D,QAAQQ,IAAI,CAAC;AACf;AAEA,SAASkM,uBAAuBtN,WAAoB;IAClD,IAAIA,aAAa;QACf,OAAO;IACT;IAEA,IAAIY,QAAQC,GAAG,CAACohB,WAAW,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASL,yBAAyB1d,GAAY;IAC5C,MAAMC,OAAOnO,qBAAqBkO;IAClC,IAAIC,QAAQ,MAAM;QAChB,OAAOA;IACT;IAEA,IAAID,eAAeE,SAAS,UAAUF,OAAO,OAAOA,IAAIC,IAAI,KAAK,UAAU;QACzE,OAAOD,IAAIC,IAAI;IACjB;IAEA,IAAID,eAAeE,OAAO;QACxB,OAAOF,IAAIge,IAAI;IACjB;IAEA,OAAO;AACT", "ignoreList": [0]}