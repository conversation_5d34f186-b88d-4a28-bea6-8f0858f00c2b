{"version": 3, "sources": ["../../../../../src/server/route-modules/app-page/helpers/prerender-manifest-matcher.ts"], "sourcesContent": ["import type {\n  DynamicPrerenderManifestRoute,\n  PrerenderManifest,\n} from '../../../../build'\nimport type { DeepReadonly } from '../../../../shared/lib/deep-readonly'\nimport {\n  getRouteMatcher,\n  type RouteMatchFn,\n} from '../../../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../../../shared/lib/router/utils/route-regex'\n\n/**\n * A matcher for a dynamic route.\n */\ntype Matcher = {\n  /**\n   * The matcher for the dynamic route. This is lazily created when the matcher\n   * is first used.\n   */\n  matcher?: RouteMatchFn\n\n  /**\n   * The source of the dynamic route.\n   */\n  source: string\n\n  /**\n   * The route that matches the source.\n   */\n  route: DeepReadonly<DynamicPrerenderManifestRoute>\n}\n\n/**\n * A matcher for the prerender manifest.\n *\n * This class is used to match the pathname to the dynamic route.\n */\nexport class PrerenderManifestMatcher {\n  private readonly matchers: Array<Matcher>\n  constructor(\n    pathname: string,\n    prerenderManifest: DeepReadonly<PrerenderManifest>\n  ) {\n    this.matchers = Object.entries(prerenderManifest.dynamicRoutes)\n      .filter(([source, route]) => {\n        // If the pathname is a fallback source route, or the source route is\n        // the same as the pathname, then we should include it in the matchers.\n        return route.fallbackSourceRoute === pathname || source === pathname\n      })\n      .map(([source, route]) => ({ source, route }))\n  }\n\n  /**\n   * Match the pathname to the dynamic route. If no match is found, an error is\n   * thrown.\n   *\n   * @param pathname - The pathname to match.\n   * @returns The dynamic route that matches the pathname.\n   */\n  public match(\n    pathname: string\n  ): DeepReadonly<DynamicPrerenderManifestRoute> | null {\n    // Iterate over the matchers. They're already in the correct order of\n    // specificity as they were inserted into the prerender manifest that way\n    // and iterating over them with Object.entries guarantees that.\n    for (const matcher of this.matchers) {\n      // Lazily create the matcher, this is only done once per matcher.\n      if (!matcher.matcher) {\n        matcher.matcher = getRouteMatcher(getRouteRegex(matcher.source))\n      }\n\n      const match = matcher.matcher(pathname)\n      if (match) {\n        return matcher.route\n      }\n    }\n\n    return null\n  }\n}\n"], "names": ["getRouteMatcher", "getRouteRegex", "PrerenderManifestMatcher", "constructor", "pathname", "prerenderManifest", "matchers", "Object", "entries", "dynamicRoutes", "filter", "source", "route", "fallbackSourceRoute", "map", "match", "matcher"], "mappings": "AAKA,SACEA,eAAe,QAEV,oDAAmD;AAC1D,SAASC,aAAa,QAAQ,kDAAiD;AAuB/E;;;;CAIC,GACD,OAAO,MAAMC;IAEXC,YACEC,QAAgB,EAChBC,iBAAkD,CAClD;QACA,IAAI,CAACC,QAAQ,GAAGC,OAAOC,OAAO,CAACH,kBAAkBI,aAAa,EAC3DC,MAAM,CAAC,CAAC,CAACC,QAAQC,MAAM;YACtB,qEAAqE;YACrE,uEAAuE;YACvE,OAAOA,MAAMC,mBAAmB,KAAKT,YAAYO,WAAWP;QAC9D,GACCU,GAAG,CAAC,CAAC,CAACH,QAAQC,MAAM,GAAM,CAAA;gBAAED;gBAAQC;YAAM,CAAA;IAC/C;IAEA;;;;;;GAMC,GACD,AAAOG,MACLX,QAAgB,EACoC;QACpD,qEAAqE;QACrE,yEAAyE;QACzE,+DAA+D;QAC/D,KAAK,MAAMY,WAAW,IAAI,CAACV,QAAQ,CAAE;YACnC,iEAAiE;YACjE,IAAI,CAACU,QAAQA,OAAO,EAAE;gBACpBA,QAAQA,OAAO,GAAGhB,gBAAgBC,cAAce,QAAQL,MAAM;YAChE;YAEA,MAAMI,QAAQC,QAAQA,OAAO,CAACZ;YAC9B,IAAIW,OAAO;gBACT,OAAOC,QAAQJ,KAAK;YACtB;QACF;QAEA,OAAO;IACT;AACF", "ignoreList": [0]}