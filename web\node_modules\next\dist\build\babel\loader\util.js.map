{"version": 3, "sources": ["../../../../src/build/babel/loader/util.ts"], "sourcesContent": ["import type { TransformOptions } from 'next/dist/compiled/babel/core'\n\nexport function consumeIterator(iter: Iterator<any>) {\n  while (true) {\n    const { value, done } = iter.next()\n    if (done) {\n      return value\n    }\n  }\n}\n\n/**\n * Source map standard format as to revision 3.\n *\n * `TransformOptions` uses this type, but doesn't export it separately\n */\nexport type SourceMap = NonNullable<TransformOptions['inputSourceMap']>\n\n/**\n * An extension of the normal babel configuration, with extra `babel-loader`-specific fields that transforms can read.\n *\n * See: https://github.com/babel/babel-loader/blob/main/src/injectCaller.js\n */\nexport type BabelLoaderTransformOptions = TransformOptions & {\n  target?: string\n}\n"], "names": ["consumeIterator", "iter", "value", "done", "next"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,IAAmB;IACjD,MAAO,KAAM;QACX,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGF,KAAKG,IAAI;QACjC,IAAID,MAAM;YACR,OAAOD;QACT;IACF;AACF", "ignoreList": [0]}