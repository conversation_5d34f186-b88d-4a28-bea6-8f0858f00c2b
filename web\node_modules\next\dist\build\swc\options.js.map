{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "sourcesContent": ["import path from 'path'\nimport { WEBPACK_LAYERS, type WebpackLayerName } from '../../lib/constants'\nimport type {\n  NextConfig,\n  ExperimentalConfig,\n  EmotionConfig,\n  StyledComponentsConfig,\n} from '../../server/config-shared'\nimport type { ResolvedBaseUrl } from '../load-jsconfig'\nimport { shouldUseReactServerCondition, isWebpackAppPagesLayer } from '../utils'\nimport { escapeStringRegexp } from '../../shared/lib/escape-regexp'\n\nconst nextDirname = path.dirname(require.resolve('next/package.json'))\n\nconst nextDistPath = new RegExp(\n  `${escapeStringRegexp(nextDirname)}[\\\\/]dist[\\\\/](shared[\\\\/]lib|client|pages)`\n)\n\nconst nodeModulesPath = /[\\\\/]node_modules[\\\\/]/\n\nconst regeneratorRuntimePath = require.resolve(\n  'next/dist/compiled/regenerator-runtime'\n)\n\nfunction isTypeScriptFile(filename: string) {\n  return filename.endsWith('.ts') || filename.endsWith('.tsx')\n}\n\nfunction isCommonJSFile(filename: string) {\n  return filename.endsWith('.cjs')\n}\n\n// Ensure Next.js internals and .cjs files are output as CJS modules,\n// By default all modules are output as ESM or will treated as CJS if next-swc/auto-cjs plugin detects file is CJS.\nfunction shouldOutputCommonJs(filename: string) {\n  return isCommonJSFile(filename) || nextDistPath.test(filename)\n}\n\nexport function getParserOptions({ filename, jsConfig, ...rest }: any) {\n  const isTSFile = filename.endsWith('.ts')\n  const hasTsSyntax = isTypeScriptFile(filename)\n  const enableDecorators = Boolean(\n    jsConfig?.compilerOptions?.experimentalDecorators\n  )\n  return {\n    ...rest,\n    syntax: hasTsSyntax ? 'typescript' : 'ecmascript',\n    dynamicImport: true,\n    decorators: enableDecorators,\n    // Exclude regular TypeScript files from React transformation to prevent e.g. generic parameters and angle-bracket type assertion from being interpreted as JSX tags.\n    [hasTsSyntax ? 'tsx' : 'jsx']: !isTSFile,\n    importAssertions: true,\n  }\n}\n\nfunction getBaseSWCOptions({\n  filename,\n  jest,\n  development,\n  hasReactRefresh,\n  globalWindow,\n  esm,\n  modularizeImports,\n  swcPlugins,\n  compilerOptions,\n  resolvedBaseUrl,\n  jsConfig,\n  supportedBrowsers,\n  swcCacheDir,\n  serverComponents,\n  serverReferenceHashSalt,\n  bundleLayer,\n  isCacheComponents,\n  cacheHandlers,\n  useCacheEnabled,\n  trackDynamicImports,\n}: {\n  filename: string\n  jest?: boolean\n  development: boolean\n  hasReactRefresh: boolean\n  globalWindow: boolean\n  esm: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  compilerOptions: NextConfig['compiler']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  resolvedBaseUrl?: ResolvedBaseUrl\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir?: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  isCacheComponents?: boolean\n  cacheHandlers?: ExperimentalConfig['cacheHandlers']\n  useCacheEnabled?: boolean\n  trackDynamicImports?: boolean\n}) {\n  const isReactServerLayer = shouldUseReactServerCondition(bundleLayer)\n  const isAppRouterPagesLayer = isWebpackAppPagesLayer(bundleLayer)\n  const parserConfig = getParserOptions({ filename, jsConfig })\n  const paths = jsConfig?.compilerOptions?.paths\n  const enableDecorators = Boolean(\n    jsConfig?.compilerOptions?.experimentalDecorators\n  )\n  const emitDecoratorMetadata = Boolean(\n    jsConfig?.compilerOptions?.emitDecoratorMetadata\n  )\n  const useDefineForClassFields = Boolean(\n    jsConfig?.compilerOptions?.useDefineForClassFields\n  )\n  const plugins = (swcPlugins ?? [])\n    .filter(Array.isArray)\n    .map(([name, options]: any) => [require.resolve(name), options])\n\n  return {\n    jsc: {\n      ...(resolvedBaseUrl && paths\n        ? {\n            baseUrl: resolvedBaseUrl.baseUrl,\n            paths,\n          }\n        : {}),\n      externalHelpers: !process.versions.pnp && !jest,\n      parser: parserConfig,\n      experimental: {\n        keepImportAttributes: true,\n        emitAssertForImportAttributes: true,\n        plugins,\n        cacheRoot: swcCacheDir,\n      },\n      transform: {\n        // Enables https://github.com/swc-project/swc/blob/0359deb4841be743d73db4536d4a22ac797d7f65/crates/swc_ecma_ext_transforms/src/jest.rs\n        ...(jest\n          ? {\n              hidden: {\n                jest: true,\n              },\n            }\n          : {}),\n        legacyDecorator: enableDecorators,\n        decoratorMetadata: emitDecoratorMetadata,\n        useDefineForClassFields: useDefineForClassFields,\n        react: {\n          importSource:\n            jsConfig?.compilerOptions?.jsxImportSource ??\n            (compilerOptions?.emotion && !isReactServerLayer\n              ? '@emotion/react'\n              : 'react'),\n          runtime: 'automatic',\n          pragmaFrag: 'React.Fragment',\n          throwIfNamespace: true,\n          development: !!development,\n          useBuiltins: true,\n          refresh: !!hasReactRefresh,\n        },\n        optimizer: {\n          simplify: false,\n          globals: jest\n            ? null\n            : {\n                typeofs: {\n                  window: globalWindow ? 'object' : 'undefined',\n                },\n                envs: {\n                  NODE_ENV: development ? '\"development\"' : '\"production\"',\n                },\n                // TODO: handle process.browser to match babel replacing as well\n              },\n        },\n        regenerator: {\n          importPath: regeneratorRuntimePath,\n        },\n      },\n    },\n    sourceMaps: jest ? 'inline' : undefined,\n    removeConsole: compilerOptions?.removeConsole,\n    // disable \"reactRemoveProperties\" when \"jest\" is true\n    // otherwise the setting from next.config.js will be used\n    reactRemoveProperties: jest\n      ? false\n      : compilerOptions?.reactRemoveProperties,\n    // Map the k-v map to an array of pairs.\n    modularizeImports: modularizeImports\n      ? Object.fromEntries(\n          Object.entries(modularizeImports).map(([mod, config]) => [\n            mod,\n            {\n              ...config,\n              transform:\n                typeof config.transform === 'string'\n                  ? config.transform\n                  : Object.entries(config.transform).map(([key, value]) => [\n                      key,\n                      value,\n                    ]),\n            },\n          ])\n        )\n      : undefined,\n    relay: compilerOptions?.relay,\n    // Always transform styled-jsx and error when `client-only` condition is triggered\n    styledJsx: compilerOptions?.styledJsx ?? {\n      useLightningcss: jsConfig?.experimental?.useLightningcss ?? false,\n    },\n    // Disable css-in-js libs (without client-only integration) transform on server layer for server components\n    ...(!isReactServerLayer && {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      emotion: getEmotionOptions(compilerOptions?.emotion, development),\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      styledComponents: getStyledComponentsOptions(\n        compilerOptions?.styledComponents,\n        development\n      ),\n    }),\n    serverComponents:\n      serverComponents && !jest\n        ? {\n            isReactServerLayer,\n            cacheComponentsEnabled: isCacheComponents,\n            useCacheEnabled,\n          }\n        : undefined,\n    serverActions:\n      isAppRouterPagesLayer && !jest\n        ? {\n            isReactServerLayer,\n            isDevelopment: development,\n            useCacheEnabled,\n            hashSalt: serverReferenceHashSalt,\n            cacheKinds: ['default', 'remote', 'private'].concat(\n              cacheHandlers ? Object.keys(cacheHandlers) : []\n            ),\n          }\n        : undefined,\n    // For app router we prefer to bundle ESM,\n    // On server side of pages router we prefer CJS.\n    preferEsm: esm,\n    lintCodemodComments: true,\n    trackDynamicImports: trackDynamicImports,\n    debugFunctionName: development,\n\n    ...(supportedBrowsers && supportedBrowsers.length > 0\n      ? {\n          cssEnv: {\n            targets: supportedBrowsers,\n          },\n        }\n      : {}),\n  }\n}\n\nfunction getStyledComponentsOptions(\n  styledComponentsConfig: undefined | boolean | StyledComponentsConfig,\n  development: any\n) {\n  if (!styledComponentsConfig) {\n    return null\n  } else if (typeof styledComponentsConfig === 'object') {\n    return {\n      ...styledComponentsConfig,\n      displayName: styledComponentsConfig.displayName ?? Boolean(development),\n    }\n  } else {\n    return {\n      displayName: Boolean(development),\n    }\n  }\n}\n\nfunction getEmotionOptions(\n  emotionConfig: undefined | boolean | EmotionConfig,\n  development: boolean\n) {\n  if (!emotionConfig) {\n    return null\n  }\n  let autoLabel = !!development\n  if (typeof emotionConfig === 'object' && emotionConfig.autoLabel) {\n    switch (emotionConfig.autoLabel) {\n      case 'never':\n        autoLabel = false\n        break\n      case 'always':\n        autoLabel = true\n        break\n      case 'dev-only':\n        break\n      default:\n        emotionConfig.autoLabel satisfies never\n    }\n  }\n  return {\n    enabled: true,\n    autoLabel,\n    sourcemap: development,\n    ...(typeof emotionConfig === 'object' && {\n      importMap: emotionConfig.importMap,\n      labelFormat: emotionConfig.labelFormat,\n      sourcemap: development && emotionConfig.sourceMap,\n    }),\n  }\n}\n\nexport function getJestSWCOptions({\n  isServer,\n  filename,\n  esm,\n  modularizeImports,\n  swcPlugins,\n  compilerOptions,\n  jsConfig,\n  resolvedBaseUrl,\n  pagesDir,\n  serverReferenceHashSalt,\n}: {\n  isServer: boolean\n  filename: string\n  esm: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n  jsConfig: any\n  resolvedBaseUrl?: ResolvedBaseUrl\n  pagesDir?: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n}) {\n  let baseOptions = getBaseSWCOptions({\n    filename,\n    jest: true,\n    development: false,\n    hasReactRefresh: false,\n    globalWindow: !isServer,\n    modularizeImports,\n    swcPlugins,\n    compilerOptions,\n    jsConfig,\n    resolvedBaseUrl,\n    supportedBrowsers: undefined,\n    esm,\n    // Don't apply server layer transformations for Jest\n    // Disable server / client graph assertions for Jest\n    bundleLayer: undefined,\n    serverComponents: false,\n    serverReferenceHashSalt,\n  })\n\n  const useCjsModules = shouldOutputCommonJs(filename)\n  return {\n    ...baseOptions,\n    env: {\n      targets: {\n        // Targets the current version of Node.js\n        node: process.versions.node,\n      },\n    },\n    module: {\n      type: esm && !useCjsModules ? 'es6' : 'commonjs',\n    },\n    disableNextSsg: true,\n    disablePageConfig: true,\n    pagesDir,\n  }\n}\n\nexport function getLoaderSWCOptions({\n  // This is not passed yet as \"paths\" resolving is handled by webpack currently.\n  // resolvedBaseUrl,\n  filename,\n  development,\n  isServer,\n  pagesDir,\n  appDir,\n  isPageFile,\n  isCacheComponents,\n  hasReactRefresh,\n  modularizeImports,\n  optimizeServerReact,\n  optimizePackageImports,\n  swcPlugins,\n  compilerOptions,\n  jsConfig,\n  supportedBrowsers,\n  swcCacheDir,\n  relativeFilePathFromRoot,\n  serverComponents,\n  serverReferenceHashSalt,\n  bundleLayer,\n  esm,\n  cacheHandlers,\n  useCacheEnabled,\n  trackDynamicImports,\n}: {\n  filename: string\n  development: boolean\n  isServer: boolean\n  pagesDir?: string\n  appDir?: string\n  isPageFile: boolean\n  hasReactRefresh: boolean\n  optimizeServerReact?: boolean\n  modularizeImports: NextConfig['modularizeImports']\n  isCacheComponents?: boolean\n  optimizePackageImports?: NonNullable<\n    NextConfig['experimental']\n  >['optimizePackageImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir: string\n  relativeFilePathFromRoot: string\n  esm?: boolean\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  cacheHandlers: ExperimentalConfig['cacheHandlers']\n  useCacheEnabled?: boolean\n  trackDynamicImports?: boolean\n}) {\n  let baseOptions: any = getBaseSWCOptions({\n    filename,\n    development,\n    globalWindow: !isServer,\n    hasReactRefresh,\n    modularizeImports,\n    swcPlugins,\n    compilerOptions,\n    jsConfig,\n    // resolvedBaseUrl,\n    supportedBrowsers,\n    swcCacheDir,\n    bundleLayer,\n    serverComponents,\n    serverReferenceHashSalt,\n    esm: !!esm,\n    isCacheComponents,\n    cacheHandlers,\n    useCacheEnabled,\n    trackDynamicImports,\n  })\n  baseOptions.fontLoaders = {\n    fontLoaders: ['next/font/local', 'next/font/google'],\n    relativeFilePathFromRoot,\n  }\n  baseOptions.cjsRequireOptimizer = {\n    packages: {\n      'next/server': {\n        transforms: {\n          NextRequest: 'next/dist/server/web/spec-extension/request',\n          NextResponse: 'next/dist/server/web/spec-extension/response',\n          ImageResponse: 'next/dist/server/web/spec-extension/image-response',\n          userAgentFromString: 'next/dist/server/web/spec-extension/user-agent',\n          userAgent: 'next/dist/server/web/spec-extension/user-agent',\n        },\n      },\n    },\n  }\n\n  if (optimizeServerReact && isServer && !development) {\n    baseOptions.optimizeServerReact = {\n      optimize_use_state: false,\n    }\n  }\n\n  // Modularize import optimization for barrel files\n  if (optimizePackageImports) {\n    baseOptions.autoModularizeImports = {\n      packages: optimizePackageImports,\n    }\n  }\n\n  const isNodeModules = nodeModulesPath.test(filename)\n  const isAppBrowserLayer = bundleLayer === WEBPACK_LAYERS.appPagesBrowser\n  const moduleResolutionConfig = shouldOutputCommonJs(filename)\n    ? {\n        module: {\n          type: 'commonjs',\n        },\n      }\n    : {}\n\n  let options: any\n  if (isServer) {\n    options = {\n      ...baseOptions,\n      ...moduleResolutionConfig,\n      // Disables getStaticProps/getServerSideProps tree shaking on the server compilation for pages\n      disableNextSsg: true,\n      disablePageConfig: true,\n      isDevelopment: development,\n      isServerCompiler: isServer,\n      pagesDir,\n      appDir,\n      preferEsm: !!esm,\n      isPageFile,\n      env: {\n        targets: {\n          // Targets the current version of Node.js\n          node: process.versions.node,\n        },\n      },\n    }\n  } else {\n    options = {\n      ...baseOptions,\n      ...moduleResolutionConfig,\n      disableNextSsg: !isPageFile,\n      isDevelopment: development,\n      isServerCompiler: isServer,\n      pagesDir,\n      appDir,\n      isPageFile,\n      ...(supportedBrowsers && supportedBrowsers.length > 0\n        ? {\n            env: {\n              targets: supportedBrowsers,\n            },\n          }\n        : {}),\n    }\n    if (!options.env) {\n      // Matches default @babel/preset-env behavior\n      options.jsc.target = 'es5'\n    }\n  }\n\n  // For node_modules in app browser layer, we don't need to do any server side transformation.\n  // Only keep server actions transform to discover server actions from client components.\n  if (isAppBrowserLayer && isNodeModules) {\n    options.disableNextSsg = true\n    options.disablePageConfig = true\n    options.isPageFile = false\n    options.optimizeServerReact = undefined\n    options.cjsRequireOptimizer = undefined\n    // Disable optimizer for node_modules in app browser layer, to avoid unnecessary replacement.\n    // e.g. typeof window could result differently in js worker or browser.\n    if (\n      options.jsc.transform.optimizer.globals?.typeofs &&\n      !filename.includes(nextDirname)\n    ) {\n      delete options.jsc.transform.optimizer.globals.typeofs.window\n    }\n  }\n\n  return options\n}\n"], "names": ["getJestSWCOptions", "getLoaderSWCOptions", "getParserOptions", "nextDirname", "path", "dirname", "require", "resolve", "nextDistPath", "RegExp", "escapeStringRegexp", "nodeModulesPath", "regeneratorRuntimePath", "isTypeScriptFile", "filename", "endsWith", "isCommonJSFile", "shouldOutputCommonJs", "test", "jsConfig", "rest", "isTSFile", "hasTsSyntax", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "esm", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "supportedBrowsers", "swcCacheDir", "serverComponents", "serverReferenceHashSalt", "bundleLayer", "isCacheComponents", "cacheHandlers", "useCacheEnabled", "trackDynamicImports", "isReactServerLayer", "shouldUseReactServerCondition", "isAppRouterPagesLayer", "isWebpackAppPagesLayer", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "useLightningcss", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "cacheComponentsEnabled", "serverActions", "isDevelopment", "hashSalt", "cacheKinds", "concat", "keys", "preferEsm", "lintCodemodComments", "debugFunctionName", "length", "cssEnv", "targets", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "enabled", "sourcemap", "importMap", "labelFormat", "sourceMap", "isServer", "pagesDir", "baseOptions", "useCjsModules", "env", "node", "module", "type", "disableNextSsg", "disablePageConfig", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isNodeModules", "isAppBrowserLayer", "WEBPACK_LAYERS", "appPagesBrowser", "moduleResolutionConfig", "isServerCompiler", "target", "includes"], "mappings": ";;;;;;;;;;;;;;;;IAgTgBA,iBAAiB;eAAjBA;;IA8DAC,mBAAmB;eAAnBA;;IAxUAC,gBAAgB;eAAhBA;;;6DAtCC;2BACqC;uBAQgB;8BACnC;;;;;;AAEnC,MAAMC,cAAcC,aAAI,CAACC,OAAO,CAACC,QAAQC,OAAO,CAAC;AAEjD,MAAMC,eAAe,IAAIC,OACvB,GAAGC,IAAAA,gCAAkB,EAACP,aAAa,2CAA2C,CAAC;AAGjF,MAAMQ,kBAAkB;AAExB,MAAMC,yBAAyBN,QAAQC,OAAO,CAC5C;AAGF,SAASM,iBAAiBC,QAAgB;IACxC,OAAOA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC;AACvD;AAEA,SAASC,eAAeF,QAAgB;IACtC,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,qEAAqE;AACrE,mHAAmH;AACnH,SAASE,qBAAqBH,QAAgB;IAC5C,OAAOE,eAAeF,aAAaN,aAAaU,IAAI,CAACJ;AACvD;AAEO,SAASZ,iBAAiB,EAAEY,QAAQ,EAAEK,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWP,SAASC,QAAQ,CAAC;IACnC,MAAMO,cAAcT,iBAAiBC;IACrC,MAAMS,mBAAmBC,QACvBL,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BO,sBAAsB;IAEnD,OAAO;QACL,GAAGN,IAAI;QACPO,QAAQL,cAAc,eAAe;QACrCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,cAAc,QAAQ,MAAM,EAAE,CAACD;QAChCS,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBjB,QAAQ,EACRkB,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfc,eAAe,EACfpB,QAAQ,EACRqB,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,mBAAmB,EAsBpB;QAIe7B,2BAEZA,4BAGAA,4BAGAA,4BAoCQA,4BA0DWA;IAzGrB,MAAM8B,qBAAqBC,IAAAA,oCAA6B,EAACN;IACzD,MAAMO,wBAAwBC,IAAAA,6BAAsB,EAACR;IACrD,MAAMS,eAAenD,iBAAiB;QAAEY;QAAUK;IAAS;IAC3D,MAAMmC,QAAQnC,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BmC,KAAK;IAC9C,MAAM/B,mBAAmBC,QACvBL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BO,sBAAsB;IAEnD,MAAM6B,wBAAwB/B,QAC5BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BoC,qBAAqB;IAElD,MAAMC,0BAA0BhC,QAC9BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BqC,uBAAuB;IAEpD,MAAMC,UAAU,AAACnB,CAAAA,cAAc,EAAE,AAAD,EAC7BoB,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAACzD,QAAQC,OAAO,CAACuD;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIzB,mBAAmBe,QACnB;gBACEW,SAAS1B,gBAAgB0B,OAAO;gBAChCX;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAACrC;YAC3CsC,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAWjC;YACb;YACAkC,WAAW;gBACT,sIAAsI;gBACtI,GAAI3C,OACA;oBACE4C,QAAQ;wBACN5C,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACN6C,iBAAiBtD;gBACjBuD,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACE7D,CAAAA,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B8D,eAAe,KACzCxD,CAAAA,CAAAA,mCAAAA,gBAAiByD,OAAO,KAAI,CAACjC,qBAC1B,mBACA,OAAM;oBACZkC,SAAS;oBACTC,YAAY;oBACZC,kBAAkB;oBAClBpD,aAAa,CAAC,CAACA;oBACfqD,aAAa;oBACbC,SAAS,CAAC,CAACrD;gBACb;gBACAsD,WAAW;oBACTC,UAAU;oBACVC,SAAS1D,OACL,OACA;wBACE2D,SAAS;4BACPC,QAAQzD,eAAe,WAAW;wBACpC;wBACA0D,MAAM;4BACJC,UAAU7D,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACA8D,aAAa;oBACXC,YAAYpF;gBACd;YACF;QACF;QACAqF,YAAYjE,OAAO,WAAWkE;QAC9BC,aAAa,EAAE1E,mCAAAA,gBAAiB0E,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuBpE,OACnB,QACAP,mCAAAA,gBAAiB2E,qBAAqB;QAC1C,wCAAwC;QACxC/D,mBAAmBA,oBACfgE,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAAClE,mBAAmBwB,GAAG,CAAC,CAAC,CAAC2C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT9B,WACE,OAAO8B,OAAO9B,SAAS,KAAK,WACxB8B,OAAO9B,SAAS,GAChB0B,OAAOE,OAAO,CAACE,OAAO9B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC6C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAEnF,mCAAAA,gBAAiBmF,KAAK;QAC7B,kFAAkF;QAClFC,WAAWpF,CAAAA,mCAAAA,gBAAiBoF,SAAS,KAAI;YACvCC,iBAAiB3F,CAAAA,6BAAAA,yBAAAA,SAAUoD,YAAY,qBAAtBpD,uBAAwB2F,eAAe,KAAI;QAC9D;QACA,2GAA2G;QAC3G,GAAI,CAAC7D,sBAAsB;YACzB,mEAAmE;YACnEiC,SAAS6B,kBAAkBtF,mCAAAA,gBAAiByD,OAAO,EAAEjD;YACrD,mEAAmE;YACnE+E,kBAAkBC,2BAChBxF,mCAAAA,gBAAiBuF,gBAAgB,EACjC/E;QAEJ,CAAC;QACDS,kBACEA,oBAAoB,CAACV,OACjB;YACEiB;YACAiE,wBAAwBrE;YACxBE;QACF,IACAmD;QACNiB,eACEhE,yBAAyB,CAACnB,OACtB;YACEiB;YACAmE,eAAenF;YACfc;YACAsE,UAAU1E;YACV2E,YAAY;gBAAC;gBAAW;gBAAU;aAAU,CAACC,MAAM,CACjDzE,gBAAgBuD,OAAOmB,IAAI,CAAC1E,iBAAiB,EAAE;QAEnD,IACAoD;QACN,0CAA0C;QAC1C,gDAAgD;QAChDuB,WAAWrF;QACXsF,qBAAqB;QACrB1E,qBAAqBA;QACrB2E,mBAAmB1F;QAEnB,GAAIO,qBAAqBA,kBAAkBoF,MAAM,GAAG,IAChD;YACEC,QAAQ;gBACNC,SAAStF;YACX;QACF,IACA,CAAC,CAAC;IACR;AACF;AAEA,SAASyE,2BACPc,sBAAoE,EACpE9F,WAAgB;IAEhB,IAAI,CAAC8F,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAIxG,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACL+F,aAAaxG,QAAQS;QACvB;IACF;AACF;AAEA,SAAS8E,kBACPkB,aAAkD,EAClDhG,WAAoB;IAEpB,IAAI,CAACgG,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAACjG;IAClB,IAAI,OAAOgG,kBAAkB,YAAYA,cAAcC,SAAS,EAAE;QAChE,OAAQD,cAAcC,SAAS;YAC7B,KAAK;gBACHA,YAAY;gBACZ;YACF,KAAK;gBACHA,YAAY;gBACZ;YACF,KAAK;gBACH;YACF;gBACED,cAAcC,SAAS;QAC3B;IACF;IACA,OAAO;QACLC,SAAS;QACTD;QACAE,WAAWnG;QACX,GAAI,OAAOgG,kBAAkB,YAAY;YACvCI,WAAWJ,cAAcI,SAAS;YAClCC,aAAaL,cAAcK,WAAW;YACtCF,WAAWnG,eAAegG,cAAcM,SAAS;QACnD,CAAC;IACH;AACF;AAEO,SAASvI,kBAAkB,EAChCwI,QAAQ,EACR1H,QAAQ,EACRsB,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRoB,eAAe,EACfkG,QAAQ,EACR9F,uBAAuB,EAaxB;IACC,IAAI+F,cAAc3G,kBAAkB;QAClCjB;QACAkB,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAACqG;QACfnG;QACAC;QACAb;QACAN;QACAoB;QACAC,mBAAmB0D;QACnB9D;QACA,oDAAoD;QACpD,oDAAoD;QACpDQ,aAAasD;QACbxD,kBAAkB;QAClBC;IACF;IAEA,MAAMgG,gBAAgB1H,qBAAqBH;IAC3C,OAAO;QACL,GAAG4H,WAAW;QACdE,KAAK;YACHd,SAAS;gBACP,yCAAyC;gBACzCe,MAAM1E,QAAQC,QAAQ,CAACyE,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAM3G,OAAO,CAACuG,gBAAgB,QAAQ;QACxC;QACAK,gBAAgB;QAChBC,mBAAmB;QACnBR;IACF;AACF;AAEO,SAASxI,oBAAoB,EAClC,+EAA+E;AAC/E,mBAAmB;AACnBa,QAAQ,EACRmB,WAAW,EACXuG,QAAQ,EACRC,QAAQ,EACRS,MAAM,EACNC,UAAU,EACVtG,iBAAiB,EACjBX,eAAe,EACfG,iBAAiB,EACjB+G,mBAAmB,EACnBC,sBAAsB,EACtB/G,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRqB,iBAAiB,EACjBC,WAAW,EACX6G,wBAAwB,EACxB5G,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXR,GAAG,EACHU,aAAa,EACbC,eAAe,EACfC,mBAAmB,EA4BpB;IACC,IAAI0F,cAAmB3G,kBAAkB;QACvCjB;QACAmB;QACAE,cAAc,CAACqG;QACftG;QACAG;QACAC;QACAb;QACAN;QACA,mBAAmB;QACnBqB;QACAC;QACAG;QACAF;QACAC;QACAP,KAAK,CAAC,CAACA;QACPS;QACAC;QACAC;QACAC;IACF;IACA0F,YAAYa,WAAW,GAAG;QACxBA,aAAa;YAAC;YAAmB;SAAmB;QACpDD;IACF;IACAZ,YAAYc,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIX,uBAAuBZ,YAAY,CAACvG,aAAa;QACnDyG,YAAYU,mBAAmB,GAAG;YAChCY,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIX,wBAAwB;QAC1BX,YAAYuB,qBAAqB,GAAG;YAClCR,UAAUJ;QACZ;IACF;IAEA,MAAMa,gBAAgBvJ,gBAAgBO,IAAI,CAACJ;IAC3C,MAAMqJ,oBAAoBvH,gBAAgBwH,yBAAc,CAACC,eAAe;IACxE,MAAMC,yBAAyBrJ,qBAAqBH,YAChD;QACEgI,QAAQ;YACNC,MAAM;QACR;IACF,IACA,CAAC;IAEL,IAAIhF;IACJ,IAAIyE,UAAU;QACZzE,UAAU;YACR,GAAG2E,WAAW;YACd,GAAG4B,sBAAsB;YACzB,8FAA8F;YAC9FtB,gBAAgB;YAChBC,mBAAmB;YACnB7B,eAAenF;YACfsI,kBAAkB/B;YAClBC;YACAS;YACAzB,WAAW,CAAC,CAACrF;YACb+G;YACAP,KAAK;gBACHd,SAAS;oBACP,yCAAyC;oBACzCe,MAAM1E,QAAQC,QAAQ,CAACyE,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACL9E,UAAU;YACR,GAAG2E,WAAW;YACd,GAAG4B,sBAAsB;YACzBtB,gBAAgB,CAACG;YACjB/B,eAAenF;YACfsI,kBAAkB/B;YAClBC;YACAS;YACAC;YACA,GAAI3G,qBAAqBA,kBAAkBoF,MAAM,GAAG,IAChD;gBACEgB,KAAK;oBACHd,SAAStF;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAACuB,QAAQ6E,GAAG,EAAE;YAChB,6CAA6C;YAC7C7E,QAAQC,GAAG,CAACwG,MAAM,GAAG;QACvB;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAIL,qBAAqBD,eAAe;YASpCnG;QARFA,QAAQiF,cAAc,GAAG;QACzBjF,QAAQkF,iBAAiB,GAAG;QAC5BlF,QAAQoF,UAAU,GAAG;QACrBpF,QAAQqF,mBAAmB,GAAGlD;QAC9BnC,QAAQyF,mBAAmB,GAAGtD;QAC9B,6FAA6F;QAC7F,uEAAuE;QACvE,IACEnC,EAAAA,2CAAAA,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,qBAAvC3B,yCAAyC4B,OAAO,KAChD,CAAC7E,SAAS2J,QAAQ,CAACtK,cACnB;YACA,OAAO4D,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,CAACC,OAAO,CAACC,MAAM;QAC/D;IACF;IAEA,OAAO7B;AACT", "ignoreList": [0]}