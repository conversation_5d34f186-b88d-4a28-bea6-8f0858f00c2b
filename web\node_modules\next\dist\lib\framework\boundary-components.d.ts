import type { ReactNode } from 'react';
export declare const MetadataBoundary: ({ children }: {
    children: ReactNode;
}) => ReactNode;
export declare const ViewportBoundary: ({ children }: {
    children: ReactNode;
}) => ReactNode;
export declare const OutletBoundary: ({ children }: {
    children: ReactNode;
}) => ReactNode;
export declare const RootLayoutBoundary: ({ children, }: {
    children: ReactNode;
}) => ReactNode;
