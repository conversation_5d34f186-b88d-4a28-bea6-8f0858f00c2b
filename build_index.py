import os, glob, uuid, pickle, hashlib, warnings, json
from pathlib import Path

# --- Splitter: hỗ trợ cả 2 kiểu import tùy phiên bản langchain ---
try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
except Exception:
    from langchain.text_splitter import RecursiveCharacterTextSplitter

from langchain_community.document_loaders import PyPDFLoader, TextLoader

# Đọc DOCX/CSV/JSON thuần (không phụ thuộc loader ngoài)
import pandas as pd                 # pip install pandas
from docx import Document           # pip install python-docx

# Tuỳ chọn loaders (nếu có lib):
try:
    from bs4 import BeautifulSoup   # pip install beautifulsoup4
    HAS_BS4 = True
except Exception:
    HAS_BS4 = False

try:
    import pptx                     # pip install python-pptx
    HAS_PPTX = True
except Exception:
    HAS_PPTX = False

# OCR & hình ảnh
from PIL import Image               # pip install pillow
import pytesseract                  # pip install pytesseract
from pdf2image import convert_from_path  # pip install pdf2image

from charset_normalizer import from_path
from sentence_transformers import SentenceTransformer
import faiss, numpy as np

# ================= Cấu hình =================
EMBED_MODEL = os.getenv("EMBED_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "500"))
CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "100"))
OCR_LANG = os.getenv("OCR_LANG", "vie+eng")   # nếu chưa có 'vie' thì tesseract sẽ fallback 'eng'
MIN_CHARS = int(os.getenv("MIN_CHARS", "30")) # bỏ qua đoạn quá ngắn
DATA_GLOB = os.getenv("DATA_GLOB", "data/**/*")

# Nếu bạn dùng Windows + đã cài Tesseract tại đường dẫn dưới đây:
if os.name == "nt":
    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
# Nếu cần chỉ định thư mục traineddata:
# os.environ["TESSDATA_PREFIX"] = r"C:\Program Files\Tesseract-OCR\tessdata"

model = SentenceTransformer(EMBED_MODEL)

# ================= Tiện ích =================
def dedupe_texts(texts):
    """Loại trùng theo hash nội dung (bỏ khoảng trắng dư)."""
    seen, unique = set(), []
    for t in texts:
        key = hashlib.blake2s(t.strip().encode("utf-8")).hexdigest()
        if key not in seen:
            seen.add(key); unique.append(t)
    return unique

def safe_read_text(path):
    """Đọc TXT/MD với tự phát hiện encoding."""
    try:
        encoding = from_path(path).best().encoding
    except Exception:
        encoding = "utf-8"
    return TextLoader(path, encoding=encoding).load()[0].page_content

def read_docx(path):
    """Trích text đơn giản từ DOCX."""
    try:
        doc = Document(path)
        parts = []
        for p in doc.paragraphs:
            if p.text and p.text.strip():
                parts.append(p.text)
        # (tuỳ chọn) đọc thêm text trong bảng
        for tbl in doc.tables:
            for row in tbl.rows:
                cells = [c.text.strip() for c in row.cells if c.text.strip()]
                if cells:
                    parts.append(" | ".join(cells))
        return "\n".join(parts)
    except Exception as e:
        warnings.warn(f"DOCX load failed for {path}: {e}")
        return ""

def ocr_image(image_path):
    """OCR một ảnh (png/jpg/jpeg/webp)."""
    try:
        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, lang=OCR_LANG)
        return text
    except Exception as e:
        warnings.warn(f"OCR failed for image {image_path}: {e}")
        return ""

def ocr_pdf(pdf_path, dpi=250):
    """OCR một PDF scan: render từng trang → OCR."""
    texts = []
    try:
        pages = convert_from_path(pdf_path, dpi=dpi)
    except Exception as e:
        warnings.warn(f"pdf2image render failed for {pdf_path}: {e}")
        return ""
    for i, img in enumerate(pages):
        try:
            t = pytesseract.image_to_string(img, lang=OCR_LANG)
        except Exception as e:
            warnings.warn(f"OCR failed at page {i} for {pdf_path}: {e}")
            t = ""
        if t and len(t.strip()) >= MIN_CHARS:
            texts.append(t)
    return "\n\n".join(texts)

def load_pptx(path):
    """Trích text đơn giản từ PPTX (python-pptx)."""
    if not HAS_PPTX:
        warnings.warn("python-pptx chưa cài; bỏ qua PPTX")
        return ""
    try:
        pres = pptx.Presentation(path)
        chunks = []
        for slide in pres.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text:
                    chunks.append(shape.text)
        return "\n\n".join(chunks)
    except Exception as e:
        warnings.warn(f"PPTX load failed for {path}: {e}")
        return ""

def load_html(path):
    """Đọc HTML thành text thô (nếu có bs4)."""
    if not HAS_BS4:
        warnings.warn("beautifulsoup4 chưa cài; bỏ qua HTML")
        return ""
    try:
        raw = safe_read_text(path)  # dùng charset_normalizer
        soup = BeautifulSoup(raw, "html.parser")
        for t in soup(["script", "style", "noscript"]):
            t.decompose()
        text = soup.get_text(separator="\n")
        lines = [ln.strip() for ln in text.splitlines() if len(ln.strip()) >= 1]
        return "\n".join(lines)
    except Exception as e:
        warnings.warn(f"HTML load failed for {path}: {e}")
        return ""

# ================= Nạp tài liệu =================
def load_docs():
    docs = []
    for path in glob.glob(DATA_GLOB, recursive=True):
        p = Path(path)
        if not p.is_file():
            continue
        lower = p.suffix.lower()

        try:
            if lower == ".pdf":
                texts = []
                try:
                    pages = PyPDFLoader(str(p)).load()
                    for page in pages:
                        if page.page_content and len(page.page_content.strip()) >= MIN_CHARS:
                            texts.append(page.page_content)
                except Exception as e:
                    warnings.warn(f"PDF text extract failed for {p}: {e}")

                if not texts:  # PDF có thể là scan → OCR fallback
                    ocr_txt = ocr_pdf(str(p))
                    if ocr_txt:
                        texts = [ocr_txt]

                for t in texts:
                    docs.append({"text": t, "meta": {"source": str(p)}})

            elif lower in {".txt", ".md"}:
                t = safe_read_text(str(p))
                if t and len(t.strip()) >= MIN_CHARS:
                    docs.append({"text": t, "meta": {"source": str(p)}})

            elif lower == ".docx":
                t = read_docx(str(p))
                if t and len(t.strip()) >= MIN_CHARS:
                    docs.append({"text": t, "meta": {"source": str(p)}})

            elif lower == ".csv":
                try:
                    df = pd.read_csv(str(p))
                    text = df.to_string(index=False)
                    if text and len(text.strip()) >= MIN_CHARS:
                        docs.append({"text": text, "meta": {"source": str(p)}})
                except Exception as e:
                    warnings.warn(f"CSV load failed for {p}: {e}")

            elif lower == ".json":
                try:
                    with open(str(p), "r", encoding="utf-8") as f:
                        data = json.load(f)
                    text = json.dumps(data, ensure_ascii=False, indent=2)
                    if text and len(text.strip()) >= MIN_CHARS:
                        docs.append({"text": text, "meta": {"source": str(p)}})
                except Exception as e:
                    warnings.warn(f"JSON load failed for {p}: {e}")

            elif lower in {".png", ".jpg", ".jpeg", ".webp"}:
                t = ocr_image(str(p))
                if t and len(t.strip()) >= MIN_CHARS:
                    docs.append({"text": t, "meta": {"source": str(p), "type": "image-ocr"}})

            elif lower in {".pptx"}:
                t = load_pptx(str(p))
                if t and len(t.strip()) >= MIN_CHARS:
                    docs.append({"text": t, "meta": {"source": str(p)}})

            elif lower in {".html", ".htm"}:
                t = load_html(str(p))
                if t and len(t.strip()) >= MIN_CHARS:
                    docs.append({"text": t, "meta": {"source": str(p)}})

        except Exception as e:
            warnings.warn(f"Failed to load {p}: {e}")

    return docs

# ================ Chunking, Embed, Index ================
def main():
    raw_docs = load_docs()

    if not raw_docs:
        print("Không có tài liệu nào đọc được trong data/.")
        return

    splitter = RecursiveCharacterTextSplitter(
        chunk_size=CHUNK_SIZE,
        chunk_overlap=CHUNK_OVERLAP
    )

    chunks = []
    for d in raw_docs:
        txt = d["text"]
        if not txt or len(txt.strip()) < MIN_CHARS:
            continue
        for c in splitter.split_text(txt):
            c = c.strip()
            if len(c) < MIN_CHARS:
                continue
            chunks.append({"id": str(uuid.uuid4()), "text": c, "meta": d["meta"]})

    if not chunks:
        print("Không có chunk nào để index. Kiểm tra dữ liệu trong thư mục data/.")
        return

    emb = model.encode([c["text"] for c in chunks], normalize_embeddings=True)
    index = faiss.IndexFlatIP(emb.shape[1])
    index.add(np.array(emb, dtype="float32"))
    faiss.write_index(index, "index.faiss")
    with open("chunks.pkl", "wb") as f:
        pickle.dump(chunks, f)
    print(f"Indexed {len(chunks)} chunks from {len(raw_docs)} documents.")
    print(f"Model: {EMBED_MODEL} | CHUNK_SIZE={CHUNK_SIZE} | OVERLAP={CHUNK_OVERLAP}")

if __name__ == "__main__":
    main()
