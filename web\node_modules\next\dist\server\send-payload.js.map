{"version": 3, "sources": ["../../src/server/send-payload.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type RenderResult from './render-result'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { isResSent } from '../shared/lib/utils'\nimport { generateETag } from './lib/etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { HTML_CONTENT_TYPE_HEADER } from '../lib/constants'\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  result,\n  generateEtags,\n  poweredByHeader,\n  cacheControl,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  result: RenderResult\n  generateEtags: boolean\n  poweredByHeader: boolean\n  cacheControl: CacheControl | undefined\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && result.contentType === HTML_CONTENT_TYPE_HEADER) {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  // If cache control is already set on the response we don't\n  // override it to allow users to customize it via next.config\n  if (cacheControl && !res.getHeader('Cache-Control')) {\n    res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n  }\n\n  const payload = result.isDynamic ? null : result.toUnchunkedString()\n\n  if (generateEtags && payload !== null) {\n    const etag = generateETag(payload)\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type') && result.contentType) {\n    res.setHeader('Content-Type', result.contentType)\n  }\n\n  if (payload) {\n    res.setHeader('Content-Length', Buffer.byteLength(payload))\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n    return\n  }\n\n  if (payload !== null) {\n    res.end(payload)\n    return\n  }\n\n  // Pipe the render result to the response after we get a writer for it.\n  await result.pipeToNodeResponse(res)\n}\n"], "names": ["sendEtagResponse", "sendRenderResult", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "fresh", "headers", "statusCode", "end", "result", "generateEtags", "poweredByHeader", "cacheControl", "isResSent", "contentType", "HTML_CONTENT_TYPE_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "getCacheControlHeader", "payload", "isDynamic", "toUnchunkedString", "generateETag", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;;;;;;;;;;;;IAUgBA,gBAAgB;eAAhBA;;IAwBMC,gBAAgB;eAAhBA;;;uBA9BI;sBACG;8DACX;8BACoB;2BACG;;;;;;AAElC,SAASD,iBACdE,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIE,IAAAA,cAAK,EAACJ,IAAIK,OAAO,EAAE;QAAEH;IAAK,IAAI;QAChCD,IAAIK,UAAU,GAAG;QACjBL,IAAIM,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeR,iBAAiB,EACrCC,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,aAAa,EACbC,eAAe,EACfC,YAAY,EAQb;IACC,IAAIC,IAAAA,gBAAS,EAACX,MAAM;QAClB;IACF;IAEA,IAAIS,mBAAmBF,OAAOK,WAAW,KAAKC,mCAAwB,EAAE;QACtEb,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAIQ,gBAAgB,CAACV,IAAIc,SAAS,CAAC,kBAAkB;QACnDd,IAAIE,SAAS,CAAC,iBAAiBa,IAAAA,mCAAqB,EAACL;IACvD;IAEA,MAAMM,UAAUT,OAAOU,SAAS,GAAG,OAAOV,OAAOW,iBAAiB;IAElE,IAAIV,iBAAiBQ,YAAY,MAAM;QACrC,MAAMf,OAAOkB,IAAAA,kBAAY,EAACH;QAC1B,IAAInB,iBAAiBE,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIc,SAAS,CAAC,mBAAmBP,OAAOK,WAAW,EAAE;QACxDZ,IAAIE,SAAS,CAAC,gBAAgBK,OAAOK,WAAW;IAClD;IAEA,IAAII,SAAS;QACXhB,IAAIE,SAAS,CAAC,kBAAkBkB,OAAOC,UAAU,CAACL;IACpD;IAEA,IAAIjB,IAAIuB,MAAM,KAAK,QAAQ;QACzBtB,IAAIM,GAAG,CAAC;QACR;IACF;IAEA,IAAIU,YAAY,MAAM;QACpBhB,IAAIM,GAAG,CAACU;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMT,OAAOgB,kBAAkB,CAACvB;AAClC", "ignoreList": [0]}