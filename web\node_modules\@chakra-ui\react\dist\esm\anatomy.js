"use strict";
import { comboboxAnatomy as comboboxAnatomy$1 } from '@ark-ui/react';
import { accordionAnatomy as accordionAnatomy$1 } from '@ark-ui/react/accordion';
import { createAnatomy } from '@ark-ui/react/anatomy';
import { clipboardAnatomy as clipboardAnatomy$1 } from '@ark-ui/react/clipboard';
import { colorPickerAnatomy as colorPickerAnatomy$1 } from '@ark-ui/react/color-picker';
import { dialogAnatomy as dialogAnatomy$1 } from '@ark-ui/react/dialog';
import { editableAnatomy as editableAnatomy$1 } from '@ark-ui/react/editable';
import { fieldAnatomy as fieldAnatomy$1 } from '@ark-ui/react/field';
import { fieldsetAnatomy as fieldsetAnatomy$1 } from '@ark-ui/react/fieldset';
import { fileUploadAnatomy as fileUploadAnatomy$1 } from '@ark-ui/react/file-upload';
import { menuAnatomy as menuAnatomy$1 } from '@ark-ui/react/menu';
import { popoverAnatomy as popoverAnatomy$1 } from '@ark-ui/react/popover';
import { radioGroupAnatomy as radioGroupAnatomy$1 } from '@ark-ui/react/radio-group';
import { ratingGroupAnatomy as ratingGroupAnatomy$1 } from '@ark-ui/react/rating-group';
import { selectAnatomy as selectAnatomy$1 } from '@ark-ui/react/select';
import { sliderAnatomy as sliderAnatomy$1 } from '@ark-ui/react/slider';
import { switchAnatomy as switchAnatomy$1 } from '@ark-ui/react/switch';
export { treeViewAnatomy } from '@ark-ui/react/tree-view';
export { avatarAnatomy } from '@ark-ui/react/avatar';
export { checkboxAnatomy } from '@ark-ui/react/checkbox';
export { collapsibleAnatomy } from '@ark-ui/react/collapsible';
export { hoverCardAnatomy } from '@ark-ui/react/hover-card';
export { numberInputAnatomy } from '@ark-ui/react/number-input';
export { pinInputAnatomy } from '@ark-ui/react/pin-input';
export { progressAnatomy } from '@ark-ui/react/progress';
export { qrCodeAnatomy } from '@ark-ui/react/qr-code';
export { scrollAreaAnatomy } from '@ark-ui/react/scroll-area';
export { segmentGroupAnatomy } from '@ark-ui/react/segment-group';
export { tooltipAnatomy } from '@ark-ui/react/tooltip';

const accordionAnatomy = accordionAnatomy$1.extendWith("itemBody");
const actionBarAnatomy = createAnatomy("action-bar").parts(
  "positioner",
  "content",
  "separator",
  "selectionTrigger",
  "closeTrigger"
);
const alertAnatomy = createAnatomy("alert").parts(
  "title",
  "description",
  "root",
  "indicator",
  "content"
);
const breadcrumbAnatomy = createAnatomy("breadcrumb").parts(
  "link",
  "currentLink",
  "item",
  "list",
  "root",
  "ellipsis",
  "separator"
);
const blockquoteAnatomy = createAnatomy("blockquote").parts(
  "root",
  "icon",
  "content",
  "caption"
);
const cardAnatomy = createAnatomy("card").parts(
  "root",
  "header",
  "body",
  "footer",
  "title",
  "description"
);
const checkboxCardAnatomy = createAnatomy("checkbox-card", [
  "root",
  "control",
  "label",
  "description",
  "addon",
  "indicator",
  "content"
]);
const dataListAnatomy = createAnatomy("data-list").parts(
  "root",
  "item",
  "itemLabel",
  "itemValue"
);
const dialogAnatomy = dialogAnatomy$1.extendWith(
  "header",
  "body",
  "footer",
  "backdrop"
);
const drawerAnatomy = dialogAnatomy$1.extendWith(
  "header",
  "body",
  "footer",
  "backdrop"
);
const editableAnatomy = editableAnatomy$1.extendWith("textarea");
const emptyStateAnatomy = createAnatomy("empty-state", [
  "root",
  "content",
  "indicator",
  "title",
  "description"
]);
const fieldAnatomy = fieldAnatomy$1.extendWith("requiredIndicator");
const fieldsetAnatomy = fieldsetAnatomy$1.extendWith("content");
const fileUploadAnatomy = fileUploadAnatomy$1.extendWith(
  "itemContent",
  "dropzoneContent",
  "fileText"
);
const listAnatomy = createAnatomy("list").parts(
  "root",
  "item",
  "indicator"
);
const menuAnatomy = menuAnatomy$1.extendWith("itemCommand");
const nativeSelectAnatomy = createAnatomy("select").parts(
  "root",
  "field",
  "indicator"
);
const popoverAnatomy = popoverAnatomy$1.extendWith(
  "header",
  "body",
  "footer"
);
const radioGroupAnatomy = radioGroupAnatomy$1.extendWith(
  "itemAddon",
  "itemIndicator"
);
const radioCardAnatomy = radioGroupAnatomy.extendWith(
  "itemContent",
  "itemDescription"
);
const ratingGroupAnatomy = ratingGroupAnatomy$1.extendWith("itemIndicator");
const selectAnatomy = selectAnatomy$1.extendWith("indicatorGroup");
const comboboxAnatomy = comboboxAnatomy$1.extendWith(
  "indicatorGroup",
  "empty"
);
const sliderAnatomy = sliderAnatomy$1.extendWith("markerIndicator");
const statAnatomy = createAnatomy("stat").parts(
  "root",
  "label",
  "helpText",
  "valueText",
  "valueUnit",
  "indicator"
);
const statusAnatomy = createAnatomy("status").parts("root", "indicator");
const stepsAnatomy = createAnatomy("steps", [
  "root",
  "list",
  "item",
  "trigger",
  "indicator",
  "separator",
  "content",
  "title",
  "description",
  "nextTrigger",
  "prevTrigger",
  "progress"
]);
const switchAnatomy = switchAnatomy$1.extendWith("indicator");
const tableAnatomy = createAnatomy("table").parts(
  "root",
  "header",
  "body",
  "row",
  "columnHeader",
  "cell",
  "footer",
  "caption"
);
const toastAnatomy = createAnatomy("toast").parts(
  "root",
  "title",
  "description",
  "indicator",
  "closeTrigger",
  "actionTrigger"
);
const tabsAnatomy = createAnatomy("tabs").parts(
  "root",
  "trigger",
  "list",
  "content",
  "contentGroup",
  "indicator"
);
const tagAnatomy = createAnatomy("tag").parts(
  "root",
  "label",
  "closeTrigger",
  "startElement",
  "endElement"
);
const timelineAnatomy = createAnatomy("timeline").parts(
  "root",
  "item",
  "content",
  "separator",
  "indicator",
  "connector",
  "title",
  "description"
);
const colorPickerAnatomy = colorPickerAnatomy$1.extendWith("channelText");
const codeBlockAnatomy = createAnatomy("code-block", [
  "root",
  "content",
  "title",
  "header",
  "footer",
  "control",
  "overlay",
  "code",
  "codeText",
  "copyTrigger",
  "copyIndicator",
  "collapseTrigger",
  "collapseIndicator",
  "collapseText"
]);
const clipboardAnatomy = clipboardAnatomy$1.extendWith("valueText");

export { accordionAnatomy, actionBarAnatomy, alertAnatomy, blockquoteAnatomy, breadcrumbAnatomy, cardAnatomy, checkboxCardAnatomy, clipboardAnatomy, codeBlockAnatomy, colorPickerAnatomy, comboboxAnatomy, dataListAnatomy, dialogAnatomy, drawerAnatomy, editableAnatomy, emptyStateAnatomy, fieldAnatomy, fieldsetAnatomy, fileUploadAnatomy, listAnatomy, menuAnatomy, nativeSelectAnatomy, popoverAnatomy, radioCardAnatomy, radioGroupAnatomy, ratingGroupAnatomy, selectAnatomy, sliderAnatomy, statAnatomy, statusAnatomy, stepsAnatomy, switchAnatomy, tableAnatomy, tabsAnatomy, tagAnatomy, timelineAnatomy, toastAnatomy };
