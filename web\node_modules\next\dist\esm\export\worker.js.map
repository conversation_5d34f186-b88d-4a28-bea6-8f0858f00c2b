{"version": 3, "sources": ["../../src/export/worker.ts"], "sourcesContent": ["import type {\n  ExportPagesInput,\n  ExportPageInput,\n  ExportPageResult,\n  ExportRouteResult,\n  WorkerRenderOpts,\n  ExportPagesResult,\n  ExportPathEntry,\n} from './types'\n\nimport '../server/node-environment'\n\nprocess.env.NEXT_IS_EXPORT_WORKER = 'true'\n\nimport { extname, join, dirname, sep } from 'path'\nimport fs from 'fs/promises'\nimport { loadComponents } from '../server/load-components'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport { addRequestMeta } from '../server/request-meta'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { createRequestResponseMocks } from '../server/lib/mock-request'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { hasNextSupport } from '../server/ci-info'\nimport { exportAppRoute } from './routes/app-route'\nimport { exportAppPage } from './routes/app-page'\nimport { exportPagesPage } from './routes/pages'\nimport { getParams } from './helpers/get-params'\nimport { createIncrementalCache } from './helpers/create-incremental-cache'\nimport { isPostpone } from '../server/lib/router-utils/is-postpone'\nimport { isDynamicUsageError } from './helpers/is-dynamic-usage-error'\nimport { isBailoutToCSRError } from '../shared/lib/lazy-dynamic/bailout-to-csr'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n} from '../build/turborepo-access-trace'\nimport type { Params } from '../server/request/params'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../server/request/fallback-params'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport type { AppRouteRouteModule } from '../server/route-modules/app-route/module.compiled'\nimport { isStaticGenBailoutError } from '../client/components/static-generation-bailout'\nimport type { PagesRenderContext, PagesSharedContext } from '../server/render'\nimport type { AppSharedContext } from '../server/app-render/app-render'\nimport { MultiFileWriter } from '../lib/multi-file-writer'\nimport { createRenderResumeDataCache } from '../server/resume-data-cache/resume-data-cache'\n\nconst envConfig =\n  require('../shared/lib/runtime-config.external') as typeof import('../shared/lib/runtime-config.external')\n\n;(globalThis as any).__NEXT_DATA__ = {\n  nextExport: true,\n}\n\nclass TimeoutError extends Error {\n  code = 'NEXT_EXPORT_TIMEOUT_ERROR'\n}\n\nclass ExportPageError extends Error {\n  code = 'NEXT_EXPORT_PAGE_ERROR'\n}\n\nasync function exportPageImpl(\n  input: ExportPageInput,\n  fileWriter: MultiFileWriter\n): Promise<ExportRouteResult | undefined> {\n  const {\n    exportPath,\n    distDir,\n    pagesDataDir,\n    buildExport = false,\n    serverRuntimeConfig,\n    subFolders = false,\n    optimizeCss,\n    disableOptimizedLoading,\n    debugOutput = false,\n    enableExperimentalReact,\n    ampValidatorPath,\n    trailingSlash,\n    sriEnabled,\n    renderOpts: commonRenderOpts,\n    outDir: commonOutDir,\n    buildId,\n    renderResumeDataCache,\n  } = input\n\n  if (enableExperimentalReact) {\n    process.env.__NEXT_EXPERIMENTAL_REACT = 'true'\n  }\n\n  const {\n    path,\n    page,\n\n    // The parameters that are currently unknown.\n    _fallbackRouteParams = [],\n\n    // Check if this is an `app/` page.\n    _isAppDir: isAppDir = false,\n\n    // Check if this should error when dynamic usage is detected.\n    _isDynamicError: isDynamicError = false,\n\n    // If this page supports partial prerendering, then we need to pass that to\n    // the renderOpts.\n    _isRoutePPREnabled: isRoutePPREnabled,\n\n    // Configure the rendering of the page to allow that an empty static shell\n    // is generated while rendering using PPR and Cache Components.\n    _allowEmptyStaticShell: allowEmptyStaticShell = false,\n\n    // Pull the original query out.\n    query: originalQuery = {},\n  } = exportPath\n\n  const fallbackRouteParams: FallbackRouteParams | null =\n    getFallbackRouteParams(_fallbackRouteParams)\n\n  let query = { ...originalQuery }\n  const pathname = normalizeAppPath(page)\n  const isDynamic = isDynamicRoute(page)\n  const outDir = isAppDir ? join(distDir, 'server/app') : commonOutDir\n\n  const filePath = normalizePagePath(path)\n  const ampPath = `${filePath}.amp`\n  let renderAmpPath = ampPath\n\n  let updatedPath = exportPath._ssgPath || path\n  let locale = exportPath._locale || commonRenderOpts.locale\n\n  if (commonRenderOpts.locale) {\n    const localePathResult = normalizeLocalePath(path, commonRenderOpts.locales)\n\n    if (localePathResult.detectedLocale) {\n      updatedPath = localePathResult.pathname\n      locale = localePathResult.detectedLocale\n\n      if (locale === commonRenderOpts.defaultLocale) {\n        renderAmpPath = `${normalizePagePath(updatedPath)}.amp`\n      }\n    }\n  }\n\n  // We need to show a warning if they try to provide query values\n  // for an auto-exported page since they won't be available\n  const hasOrigQueryValues = Object.keys(originalQuery).length > 0\n\n  // Check if the page is a specified dynamic route\n  const { pathname: nonLocalizedPath } = normalizeLocalePath(\n    path,\n    commonRenderOpts.locales\n  )\n\n  let params: Params | undefined\n\n  if (isDynamic && page !== nonLocalizedPath) {\n    const normalizedPage = isAppDir ? normalizeAppPath(page) : page\n\n    params = getParams(normalizedPage, updatedPath)\n  }\n\n  const { req, res } = createRequestResponseMocks({ url: updatedPath })\n\n  // If this is a status code page, then set the response code.\n  for (const statusCode of [404, 500]) {\n    if (\n      [\n        `/${statusCode}`,\n        `/${statusCode}.html`,\n        `/${statusCode}/index.html`,\n      ].some((p) => p === updatedPath || `/${locale}${p}` === updatedPath)\n    ) {\n      res.statusCode = statusCode\n    }\n  }\n\n  // Ensure that the URL has a trailing slash if it's configured.\n  if (trailingSlash && !req.url?.endsWith('/')) {\n    req.url += '/'\n  }\n\n  if (\n    locale &&\n    buildExport &&\n    commonRenderOpts.domainLocales &&\n    commonRenderOpts.domainLocales.some(\n      (dl) => dl.defaultLocale === locale || dl.locales?.includes(locale || '')\n    )\n  ) {\n    addRequestMeta(req, 'isLocaleDomain', true)\n  }\n\n  envConfig.setConfig({\n    serverRuntimeConfig,\n    publicRuntimeConfig: commonRenderOpts.runtimeConfig,\n  })\n\n  const getHtmlFilename = (p: string) =>\n    subFolders ? `${p}${sep}index.html` : `${p}.html`\n\n  let htmlFilename = getHtmlFilename(filePath)\n\n  // dynamic routes can provide invalid extensions e.g. /blog/[...slug] returns an\n  // extension of `.slug]`\n  const pageExt = isDynamic || isAppDir ? '' : extname(page)\n  const pathExt = isDynamic || isAppDir ? '' : extname(path)\n\n  // force output 404.html for backwards compat\n  if (path === '/404.html') {\n    htmlFilename = path\n  }\n  // Make sure page isn't a folder with a dot in the name e.g. `v1.2`\n  else if (pageExt !== pathExt && pathExt !== '') {\n    const isBuiltinPaths = ['/500', '/404'].some(\n      (p) => p === path || p === path + '.html'\n    )\n    // If the ssg path has .html extension, and it's not builtin paths, use it directly\n    // Otherwise, use that as the filename instead\n    const isHtmlExtPath = !isBuiltinPaths && path.endsWith('.html')\n    htmlFilename = isHtmlExtPath ? getHtmlFilename(path) : path\n  } else if (path === '/') {\n    // If the path is the root, just use index.html\n    htmlFilename = 'index.html'\n  }\n\n  const baseDir = join(outDir, dirname(htmlFilename))\n  let htmlFilepath = join(outDir, htmlFilename)\n\n  await fs.mkdir(baseDir, { recursive: true })\n\n  const components = await loadComponents({\n    distDir,\n    page,\n    isAppPath: isAppDir,\n    isDev: false,\n    sriEnabled,\n  })\n\n  // Handle App Routes.\n  if (isAppDir && isAppRouteRoute(page)) {\n    return exportAppRoute(\n      req,\n      res,\n      params,\n      page,\n      components.routeModule as AppRouteRouteModule,\n      commonRenderOpts.incrementalCache,\n      commonRenderOpts.cacheLifeProfiles,\n      htmlFilepath,\n      fileWriter,\n      commonRenderOpts.experimental,\n      buildId\n    )\n  }\n\n  const renderOpts: WorkerRenderOpts = {\n    ...components,\n    ...commonRenderOpts,\n    ampPath: renderAmpPath,\n    params,\n    optimizeCss,\n    disableOptimizedLoading,\n    locale,\n    supportsDynamicResponse: false,\n    // During the export phase in next build, we always enable the streaming metadata since if there's\n    // any dynamic access in metadata we can determine it in the build phase.\n    // If it's static, then it won't affect anything.\n    // If it's dynamic, then it can be handled when request hits the route.\n    serveStreamingMetadata: true,\n    allowEmptyStaticShell,\n    experimental: {\n      ...commonRenderOpts.experimental,\n      isRoutePPREnabled,\n    },\n    renderResumeDataCache,\n  }\n\n  if (hasNextSupport) {\n    renderOpts.isRevalidate = true\n  }\n\n  // Handle App Pages\n  if (isAppDir) {\n    const sharedContext: AppSharedContext = { buildId }\n\n    return exportAppPage(\n      req,\n      res,\n      page,\n      path,\n      pathname,\n      query,\n      fallbackRouteParams,\n      renderOpts,\n      htmlFilepath,\n      debugOutput,\n      isDynamicError,\n      fileWriter,\n      sharedContext\n    )\n  }\n\n  const sharedContext: PagesSharedContext = {\n    buildId,\n    deploymentId: commonRenderOpts.deploymentId,\n    customServer: undefined,\n  }\n\n  const renderContext: PagesRenderContext = {\n    isFallback: exportPath._pagesFallback ?? false,\n    isDraftMode: false,\n    developmentNotFoundSourcePage: undefined,\n  }\n\n  return exportPagesPage(\n    req,\n    res,\n    path,\n    page,\n    query,\n    params,\n    htmlFilepath,\n    htmlFilename,\n    ampPath,\n    subFolders,\n    outDir,\n    ampValidatorPath,\n    pagesDataDir,\n    buildExport,\n    isDynamic,\n    sharedContext,\n    renderContext,\n    hasOrigQueryValues,\n    renderOpts,\n    components,\n    fileWriter\n  )\n}\n\nexport async function exportPages(\n  input: ExportPagesInput\n): Promise<ExportPagesResult> {\n  const {\n    exportPaths,\n    dir,\n    distDir,\n    outDir,\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    pagesDataDir,\n    renderOpts,\n    nextConfig,\n    options,\n    renderResumeDataCachesByPage = {},\n  } = input\n\n  if (nextConfig.experimental.enablePrerenderSourceMaps) {\n    try {\n      // Same as `next dev`\n      // Limiting the stack trace to a useful amount of frames is handled by ignore-listing.\n      // TODO: How high can we go without severely impacting CPU/memory?\n      Error.stackTraceLimit = 50\n    } catch {}\n  }\n\n  // If the fetch cache was enabled, we need to create an incremental\n  // cache instance for this page.\n  const incrementalCache = await createIncrementalCache({\n    cacheHandler,\n    cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    distDir,\n    dir,\n    // skip writing to disk in minimal mode for now, pending some\n    // changes to better support it\n    flushToDisk: !hasNextSupport,\n    cacheHandlers: nextConfig.experimental.cacheHandlers,\n  })\n\n  renderOpts.incrementalCache = incrementalCache\n\n  const maxConcurrency =\n    nextConfig.experimental.staticGenerationMaxConcurrency ?? 8\n  const results: ExportPagesResult = []\n\n  const exportPageWithRetry = async (\n    exportPath: ExportPathEntry,\n    maxAttempts: number\n  ) => {\n    const { page, path } = exportPath\n    const pageKey = page !== path ? `${page}: ${path}` : path\n    let attempt = 0\n    let result\n\n    const hasDebuggerAttached =\n      // Also tests for `inspect-brk`\n      process.env.NODE_OPTIONS?.includes('--inspect')\n\n    const renderResumeDataCache = renderResumeDataCachesByPage[page]\n      ? createRenderResumeDataCache(renderResumeDataCachesByPage[page])\n      : undefined\n\n    while (attempt < maxAttempts) {\n      try {\n        result = await Promise.race<ExportPageResult | undefined>([\n          exportPage({\n            exportPath,\n            distDir,\n            outDir,\n            pagesDataDir,\n            renderOpts,\n            ampValidatorPath:\n              nextConfig.experimental.amp?.validator || undefined,\n            trailingSlash: nextConfig.trailingSlash,\n            serverRuntimeConfig: nextConfig.serverRuntimeConfig,\n            subFolders: nextConfig.trailingSlash && !options.buildExport,\n            buildExport: options.buildExport,\n            optimizeCss: nextConfig.experimental.optimizeCss,\n            disableOptimizedLoading:\n              nextConfig.experimental.disableOptimizedLoading,\n            parentSpanId: input.parentSpanId,\n            httpAgentOptions: nextConfig.httpAgentOptions,\n            debugOutput: options.debugOutput,\n            enableExperimentalReact: needsExperimentalReact(nextConfig),\n            sriEnabled: Boolean(nextConfig.experimental.sri?.algorithm),\n            buildId: input.buildId,\n            renderResumeDataCache,\n          }),\n          hasDebuggerAttached\n            ? // With a debugger attached, exporting can take infinitely if we paused script execution.\n              new Promise(() => {})\n            : // If exporting the page takes longer than the timeout, reject the promise.\n              new Promise((_, reject) => {\n                setTimeout(() => {\n                  reject(new TimeoutError())\n                }, nextConfig.staticPageGenerationTimeout * 1000)\n              }),\n        ])\n\n        // If there was an error in the export, throw it immediately. In the catch block, we might retry the export,\n        // or immediately fail the build, depending on user configuration. We might also continue on and attempt other pages.\n        if (result && 'error' in result) {\n          throw new ExportPageError()\n        }\n\n        // If the export succeeds, break out of the retry loop\n        break\n      } catch (err) {\n        // The only error that should be caught here is an ExportError, as `exportPage` doesn't throw and instead returns an object with an `error` property.\n        // This is an overly cautious check to ensure that we don't accidentally catch an unexpected error.\n        if (!(err instanceof ExportPageError || err instanceof TimeoutError)) {\n          throw err\n        }\n\n        if (err instanceof TimeoutError) {\n          // If the export times out, we will restart the worker up to 3 times.\n          maxAttempts = 3\n        }\n\n        // We've reached the maximum number of attempts\n        if (attempt >= maxAttempts - 1) {\n          // Log a message if we've reached the maximum number of attempts.\n          // We only care to do this if maxAttempts was configured.\n          if (maxAttempts > 1) {\n            console.info(\n              `Failed to build ${pageKey} after ${maxAttempts} attempts.`\n            )\n          }\n          // If prerenderEarlyExit is enabled, we'll exit the build immediately.\n          if (nextConfig.experimental.prerenderEarlyExit) {\n            console.error(\n              `Export encountered an error on ${pageKey}, exiting the build.`\n            )\n            process.exit(1)\n          } else {\n            // Otherwise, this is a no-op. The build will continue, and a summary of failed pages will be displayed at the end.\n          }\n        } else {\n          // Otherwise, we have more attempts to make. Wait before retrying\n          if (err instanceof TimeoutError) {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}) because it took more than ${nextConfig.staticPageGenerationTimeout} seconds. Retrying again shortly.`\n            )\n          } else {\n            console.info(\n              `Failed to build ${pageKey} (attempt ${attempt + 1} of ${maxAttempts}). Retrying again shortly.`\n            )\n          }\n\n          // Exponential backoff with random jitter to avoid thundering herd on retries\n          const baseDelay = 500 // 500ms\n          const maxDelay = 2000 // 2 seconds\n          const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay)\n          const jitter = Math.random() * 0.3 * delay // Add up to 30% random jitter\n          await new Promise((r) => setTimeout(r, delay + jitter))\n        }\n      }\n\n      attempt++\n    }\n\n    return { result, path, page, pageKey }\n  }\n\n  for (let i = 0; i < exportPaths.length; i += maxConcurrency) {\n    const subset = exportPaths.slice(i, i + maxConcurrency)\n\n    const subsetResults = await Promise.all(\n      subset.map((exportPath) =>\n        exportPageWithRetry(\n          exportPath,\n          nextConfig.experimental.staticGenerationRetryCount ?? 1\n        )\n      )\n    )\n\n    results.push(...subsetResults)\n  }\n\n  return results\n}\n\nasync function exportPage(\n  input: ExportPageInput\n): Promise<ExportPageResult | undefined> {\n  trace('export-page', input.parentSpanId).setAttribute(\n    'path',\n    input.exportPath.path\n  )\n\n  // Configure the http agent.\n  setHttpClientAndAgentOptions({\n    httpAgentOptions: input.httpAgentOptions,\n  })\n\n  const fileWriter = new MultiFileWriter({\n    writeFile: (filePath, data) => fs.writeFile(filePath, data),\n    mkdir: (dir) => fs.mkdir(dir, { recursive: true }),\n  })\n\n  const exportPageSpan = trace('export-page-worker', input.parentSpanId)\n\n  const start = Date.now()\n\n  const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n\n  // Export the page.\n  let result: ExportRouteResult | undefined\n  try {\n    result = await exportPageSpan.traceAsyncFn(() =>\n      turborepoTraceAccess(\n        () => exportPageImpl(input, fileWriter),\n        turborepoAccessTraceResult\n      )\n    )\n\n    // Wait for all the files to flush to disk.\n    await fileWriter.wait()\n\n    // If there was no result, then we can exit early.\n    if (!result) return\n\n    // If there was an error, then we can exit early.\n    if ('error' in result) {\n      return { error: result.error, duration: Date.now() - start }\n    }\n  } catch (err) {\n    console.error(\n      `Error occurred prerendering page \"${input.exportPath.path}\". Read more: https://nextjs.org/docs/messages/prerender-error`\n    )\n\n    // bailoutToCSRError errors should not leak to the user as they are not actionable; they're\n    // a framework signal\n    if (!isBailoutToCSRError(err)) {\n      // A static generation bailout error is a framework signal to fail static generation but\n      // and will encode a reason in the error message. If there is a message, we'll print it.\n      // Otherwise there's nothing to show as we don't want to leak an error internal error stack to the user.\n      // TODO: Always log the full error. ignore-listing will take care of hiding internal stacks.\n      if (isStaticGenBailoutError(err)) {\n        if (err.message) {\n          console.error(`Error: ${err.message}`)\n        }\n      } else {\n        console.error(err)\n      }\n    }\n\n    return { error: true, duration: Date.now() - start }\n  }\n\n  // Notify the parent process that we processed a page (used by the progress activity indicator)\n  process.send?.([3, { type: 'activity' }])\n\n  // Otherwise we can return the result.\n  return {\n    ...result,\n    duration: Date.now() - start,\n    turborepoAccessTraceResult: turborepoAccessTraceResult.serialize(),\n  }\n}\n\nprocess.on('unhandledRejection', (err: unknown) => {\n  // if it's a postpone error, it'll be handled later\n  // when the postponed promise is actually awaited.\n  if (isPostpone(err)) {\n    return\n  }\n\n  // we don't want to log these errors\n  if (isDynamicUsageError(err)) {\n    return\n  }\n\n  console.error(err)\n})\n\nprocess.on('rejectionHandled', () => {\n  // It is ok to await a Promise late in Next.js as it allows for better\n  // prefetching patterns to avoid waterfalls. We ignore logging these.\n  // We should've already errored in anyway unhandledRejection.\n})\n\nconst FATAL_UNHANDLED_NEXT_API_EXIT_CODE = 78\n\nprocess.on('uncaughtException', (err) => {\n  if (isDynamicUsageError(err)) {\n    console.error(\n      'A Next.js API that uses exceptions to signal framework behavior was uncaught. This suggests improper usage of a Next.js API. The original error is printed below and the build will now exit.'\n    )\n    console.error(err)\n    process.exit(FATAL_UNHANDLED_NEXT_API_EXIT_CODE)\n  } else {\n    console.error(err)\n  }\n})\n"], "names": ["process", "env", "NEXT_IS_EXPORT_WORKER", "extname", "join", "dirname", "sep", "fs", "loadComponents", "isDynamicRoute", "normalizePagePath", "normalizeLocalePath", "trace", "setHttpClientAndAgentOptions", "addRequestMeta", "normalizeAppPath", "createRequestResponseMocks", "isAppRouteRoute", "hasNextSupport", "exportAppRoute", "exportAppPage", "exportPagesPage", "getParams", "createIncrementalCache", "isPostpone", "isDynamicUsageError", "isBailoutToCSRError", "turborepoTraceAccess", "TurborepoAccessTraceResult", "getFallbackRouteParams", "needsExperimentalReact", "isStaticGenBailoutError", "MultiFileWriter", "createRenderResumeDataCache", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "TimeoutError", "Error", "code", "ExportPageError", "exportPageImpl", "input", "fileWriter", "req", "exportPath", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeCss", "disableOptimizedLoading", "debugOutput", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "sriEnabled", "renderOpts", "commonRenderOpts", "outDir", "commonOutDir", "buildId", "renderResumeDataCache", "__NEXT_EXPERIMENTAL_REACT", "path", "page", "_fallbackRouteParams", "_isAppDir", "isAppDir", "_isDynamicError", "isDynamicError", "_isRoutePPREnabled", "isRoutePPREnabled", "_allowEmptyStaticShell", "allowEmptyStaticShell", "query", "originalQuery", "fallbackRouteParams", "pathname", "isDynamic", "filePath", "ampPath", "renderAmpPath", "updatedPath", "_ssgPath", "locale", "_locale", "localePathResult", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "params", "normalizedPage", "res", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "htmlFilename", "pageExt", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "htmlFilepath", "mkdir", "recursive", "components", "isAppPath", "isDev", "routeModule", "incrementalCache", "cacheLifeProfiles", "experimental", "supportsDynamicResponse", "serveStreamingMetadata", "isRevalidate", "sharedContext", "deploymentId", "customServer", "undefined", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "_pagesFallback", "isDraftMode", "developmentNotFoundSourcePage", "exportPages", "exportPaths", "dir", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "nextConfig", "options", "renderResumeDataCachesByPage", "enablePrerenderSourceMaps", "stackTraceLimit", "flushToDisk", "cacheHandlers", "maxConcurrency", "staticGenerationMaxConcurrency", "results", "exportPageWithRetry", "maxAttempts", "page<PERSON><PERSON>", "attempt", "result", "hasDebuggerAttached", "NODE_OPTIONS", "Promise", "race", "exportPage", "amp", "validator", "parentSpanId", "httpAgentOptions", "Boolean", "sri", "algorithm", "_", "reject", "setTimeout", "staticPageGenerationTimeout", "err", "console", "info", "prerenderEarlyExit", "error", "exit", "baseDelay", "max<PERSON><PERSON><PERSON>", "delay", "Math", "min", "pow", "jitter", "random", "r", "i", "subset", "slice", "subsetResults", "all", "map", "staticGenerationRetryCount", "push", "setAttribute", "writeFile", "data", "exportPageSpan", "start", "Date", "now", "turborepoAccessTraceResult", "traceAsyncFn", "wait", "duration", "message", "send", "type", "serialize", "on", "FATAL_UNHANDLED_NEXT_API_EXIT_CODE"], "mappings": "AAUA,OAAO,6BAA4B;AAEnCA,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAEpC,SAASC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,GAAG,QAAQ,OAAM;AAClD,OAAOC,QAAQ,cAAa;AAC5B,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,KAAK,QAAQ,WAAU;AAChC,SAASC,4BAA4B,QAAQ,iCAAgC;AAC7E,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,gBAAgB,QAAQ,uCAAsC;AAEvE,SAASC,0BAA0B,QAAQ,6BAA4B;AACvE,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,sBAAsB,QAAQ,qCAAoC;AAC3E,SAASC,UAAU,QAAQ,yCAAwC;AACnE,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SACEC,oBAAoB,EACpBC,0BAA0B,QACrB,kCAAiC;AAExC,SACEC,sBAAsB,QAEjB,oCAAmC;AAC1C,SAASC,sBAAsB,QAAQ,kCAAiC;AAExE,SAASC,uBAAuB,QAAQ,iDAAgD;AAGxF,SAASC,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,2BAA2B,QAAQ,gDAA+C;AAE3F,MAAMC,YACJC,QAAQ;AAERC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,MAAMC,qBAAqBC;;QAA3B,qBACEC,OAAO;;AACT;AAEA,MAAMC,wBAAwBF;;QAA9B,qBACEC,OAAO;;AACT;AAEA,eAAeE,eACbC,KAAsB,EACtBC,UAA2B;QAiHLC;IA/GtB,MAAM,EACJC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,UAAU,EACVC,YAAYC,gBAAgB,EAC5BC,QAAQC,YAAY,EACpBC,OAAO,EACPC,qBAAqB,EACtB,GAAGrB;IAEJ,IAAIY,yBAAyB;QAC3BxD,QAAQC,GAAG,CAACiE,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EACJC,IAAI,EAEJ,6CAA6C;IAC7CC,uBAAuB,EAAE,EAEzB,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,2EAA2E;IAC3E,kBAAkB;IAClBC,oBAAoBC,iBAAiB,EAErC,0EAA0E;IAC1E,+DAA+D;IAC/DC,wBAAwBC,wBAAwB,KAAK,EAErD,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAGhC;IAEJ,MAAMiC,sBACJnD,uBAAuBwC;IAEzB,IAAIS,QAAQ;QAAE,GAAGC,aAAa;IAAC;IAC/B,MAAME,WAAWlE,iBAAiBqD;IAClC,MAAMc,YAAYzE,eAAe2D;IACjC,MAAMN,SAASS,WAAWnE,KAAK4C,SAAS,gBAAgBe;IAExD,MAAMoB,WAAWzE,kBAAkByD;IACnC,MAAMiB,UAAU,GAAGD,SAAS,IAAI,CAAC;IACjC,IAAIE,gBAAgBD;IAEpB,IAAIE,cAAcvC,WAAWwC,QAAQ,IAAIpB;IACzC,IAAIqB,SAASzC,WAAW0C,OAAO,IAAI5B,iBAAiB2B,MAAM;IAE1D,IAAI3B,iBAAiB2B,MAAM,EAAE;QAC3B,MAAME,mBAAmB/E,oBAAoBwD,MAAMN,iBAAiB8B,OAAO;QAE3E,IAAID,iBAAiBE,cAAc,EAAE;YACnCN,cAAcI,iBAAiBT,QAAQ;YACvCO,SAASE,iBAAiBE,cAAc;YAExC,IAAIJ,WAAW3B,iBAAiBgC,aAAa,EAAE;gBAC7CR,gBAAgB,GAAG3E,kBAAkB4E,aAAa,IAAI,CAAC;YACzD;QACF;IACF;IAEA,gEAAgE;IAChE,0DAA0D;IAC1D,MAAMQ,qBAAqBC,OAAOC,IAAI,CAACjB,eAAekB,MAAM,GAAG;IAE/D,iDAAiD;IACjD,MAAM,EAAEhB,UAAUiB,gBAAgB,EAAE,GAAGvF,oBACrCwD,MACAN,iBAAiB8B,OAAO;IAG1B,IAAIQ;IAEJ,IAAIjB,aAAad,SAAS8B,kBAAkB;QAC1C,MAAME,iBAAiB7B,WAAWxD,iBAAiBqD,QAAQA;QAE3D+B,SAAS7E,UAAU8E,gBAAgBd;IACrC;IAEA,MAAM,EAAExC,GAAG,EAAEuD,GAAG,EAAE,GAAGrF,2BAA2B;QAAEsF,KAAKhB;IAAY;IAEnE,6DAA6D;IAC7D,KAAK,MAAMiB,cAAc;QAAC;QAAK;KAAI,CAAE;QACnC,IACE;YACE,CAAC,CAAC,EAAEA,YAAY;YAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;YACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;SAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMnB,eAAe,CAAC,CAAC,EAAEE,SAASiB,GAAG,KAAKnB,cACxD;YACAe,IAAIE,UAAU,GAAGA;QACnB;IACF;IAEA,+DAA+D;IAC/D,IAAI7C,iBAAiB,GAACZ,WAAAA,IAAIwD,GAAG,qBAAPxD,SAAS4D,QAAQ,CAAC,OAAM;QAC5C5D,IAAIwD,GAAG,IAAI;IACb;IAEA,IACEd,UACAtC,eACAW,iBAAiB8C,aAAa,IAC9B9C,iBAAiB8C,aAAa,CAACH,IAAI,CACjC,CAACI;YAAsCA;eAA/BA,GAAGf,aAAa,KAAKL,YAAUoB,cAAAA,GAAGjB,OAAO,qBAAViB,YAAYC,QAAQ,CAACrB,UAAU;QAExE;QACA1E,eAAegC,KAAK,kBAAkB;IACxC;IAEAZ,UAAU4E,SAAS,CAAC;QAClB3D;QACA4D,qBAAqBlD,iBAAiBmD,aAAa;IACrD;IAEA,MAAMC,kBAAkB,CAACR,IACvBrD,aAAa,GAAGqD,IAAInG,IAAI,UAAU,CAAC,GAAG,GAAGmG,EAAE,KAAK,CAAC;IAEnD,IAAIS,eAAeD,gBAAgB9B;IAEnC,gFAAgF;IAChF,wBAAwB;IACxB,MAAMgC,UAAUjC,aAAaX,WAAW,KAAKpE,QAAQiE;IACrD,MAAMgD,UAAUlC,aAAaX,WAAW,KAAKpE,QAAQgE;IAErD,6CAA6C;IAC7C,IAAIA,SAAS,aAAa;QACxB+C,eAAe/C;IACjB,OAEK,IAAIgD,YAAYC,WAAWA,YAAY,IAAI;QAC9C,MAAMC,iBAAiB;YAAC;YAAQ;SAAO,CAACb,IAAI,CAC1C,CAACC,IAAMA,MAAMtC,QAAQsC,MAAMtC,OAAO;QAEpC,mFAAmF;QACnF,8CAA8C;QAC9C,MAAMmD,gBAAgB,CAACD,kBAAkBlD,KAAKuC,QAAQ,CAAC;QACvDQ,eAAeI,gBAAgBL,gBAAgB9C,QAAQA;IACzD,OAAO,IAAIA,SAAS,KAAK;QACvB,+CAA+C;QAC/C+C,eAAe;IACjB;IAEA,MAAMK,UAAUnH,KAAK0D,QAAQzD,QAAQ6G;IACrC,IAAIM,eAAepH,KAAK0D,QAAQoD;IAEhC,MAAM3G,GAAGkH,KAAK,CAACF,SAAS;QAAEG,WAAW;IAAK;IAE1C,MAAMC,aAAa,MAAMnH,eAAe;QACtCwC;QACAoB;QACAwD,WAAWrD;QACXsD,OAAO;QACPlE;IACF;IAEA,qBAAqB;IACrB,IAAIY,YAAYtD,gBAAgBmD,OAAO;QACrC,OAAOjD,eACL2B,KACAuD,KACAF,QACA/B,MACAuD,WAAWG,WAAW,EACtBjE,iBAAiBkE,gBAAgB,EACjClE,iBAAiBmE,iBAAiB,EAClCR,cACA3E,YACAgB,iBAAiBoE,YAAY,EAC7BjE;IAEJ;IAEA,MAAMJ,aAA+B;QACnC,GAAG+D,UAAU;QACb,GAAG9D,gBAAgB;QACnBuB,SAASC;QACTc;QACA9C;QACAC;QACAkC;QACA0C,yBAAyB;QACzB,kGAAkG;QAClG,yEAAyE;QACzE,iDAAiD;QACjD,uEAAuE;QACvEC,wBAAwB;QACxBtD;QACAoD,cAAc;YACZ,GAAGpE,iBAAiBoE,YAAY;YAChCtD;QACF;QACAV;IACF;IAEA,IAAI/C,gBAAgB;QAClB0C,WAAWwE,YAAY,GAAG;IAC5B;IAEA,mBAAmB;IACnB,IAAI7D,UAAU;QACZ,MAAM8D,gBAAkC;YAAErE;QAAQ;QAElD,OAAO5C,cACL0B,KACAuD,KACAjC,MACAD,MACAc,UACAH,OACAE,qBACApB,YACA4D,cACAjE,aACAkB,gBACA5B,YACAwF;IAEJ;IAEA,MAAMA,gBAAoC;QACxCrE;QACAsE,cAAczE,iBAAiByE,YAAY;QAC3CC,cAAcC;IAChB;IAEA,MAAMC,gBAAoC;QACxCC,YAAY3F,WAAW4F,cAAc,IAAI;QACzCC,aAAa;QACbC,+BAA+BL;IACjC;IAEA,OAAOnH,gBACLyB,KACAuD,KACAlC,MACAC,MACAU,OACAqB,QACAqB,cACAN,cACA9B,SACAhC,YACAU,QACAL,kBACAR,cACAC,aACAgC,WACAmD,eACAI,eACA3C,oBACAlC,YACA+D,YACA9E;AAEJ;AAEA,OAAO,eAAeiG,YACpBlG,KAAuB;IAEvB,MAAM,EACJmG,WAAW,EACXC,GAAG,EACHhG,OAAO,EACPc,MAAM,EACNmF,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBlG,YAAY,EACZW,UAAU,EACVwF,UAAU,EACVC,OAAO,EACPC,+BAA+B,CAAC,CAAC,EAClC,GAAG1G;IAEJ,IAAIwG,WAAWnB,YAAY,CAACsB,yBAAyB,EAAE;QACrD,IAAI;YACF,qBAAqB;YACrB,sFAAsF;YACtF,kEAAkE;YAClE/G,MAAMgH,eAAe,GAAG;QAC1B,EAAE,OAAM,CAAC;IACX;IAEA,mEAAmE;IACnE,gCAAgC;IAChC,MAAMzB,mBAAmB,MAAMxG,uBAAuB;QACpD0H;QACAC;QACAC;QACAnG;QACAgG;QACA,6DAA6D;QAC7D,+BAA+B;QAC/BS,aAAa,CAACvI;QACdwI,eAAeN,WAAWnB,YAAY,CAACyB,aAAa;IACtD;IAEA9F,WAAWmE,gBAAgB,GAAGA;IAE9B,MAAM4B,iBACJP,WAAWnB,YAAY,CAAC2B,8BAA8B,IAAI;IAC5D,MAAMC,UAA6B,EAAE;IAErC,MAAMC,sBAAsB,OAC1B/G,YACAgH;YAQE,+BAA+B;QAC/B/J;QAPF,MAAM,EAAEoE,IAAI,EAAED,IAAI,EAAE,GAAGpB;QACvB,MAAMiH,UAAU5F,SAASD,OAAO,GAAGC,KAAK,EAAE,EAAED,MAAM,GAAGA;QACrD,IAAI8F,UAAU;QACd,IAAIC;QAEJ,MAAMC,uBAEJnK,4BAAAA,QAAQC,GAAG,CAACmK,YAAY,qBAAxBpK,0BAA0B6G,QAAQ,CAAC;QAErC,MAAM5C,wBAAwBqF,4BAA4B,CAAClF,KAAK,GAC5DnC,4BAA4BqH,4BAA4B,CAAClF,KAAK,IAC9DoE;QAEJ,MAAOyB,UAAUF,YAAa;YAC5B,IAAI;oBASIX,8BAYkBA;gBApBxBc,SAAS,MAAMG,QAAQC,IAAI,CAA+B;oBACxDC,WAAW;wBACTxH;wBACAC;wBACAc;wBACAb;wBACAW;wBACAH,kBACE2F,EAAAA,+BAAAA,WAAWnB,YAAY,CAACuC,GAAG,qBAA3BpB,6BAA6BqB,SAAS,KAAIjC;wBAC5C9E,eAAe0F,WAAW1F,aAAa;wBACvCP,qBAAqBiG,WAAWjG,mBAAmB;wBACnDC,YAAYgG,WAAW1F,aAAa,IAAI,CAAC2F,QAAQnG,WAAW;wBAC5DA,aAAamG,QAAQnG,WAAW;wBAChCG,aAAa+F,WAAWnB,YAAY,CAAC5E,WAAW;wBAChDC,yBACE8F,WAAWnB,YAAY,CAAC3E,uBAAuB;wBACjDoH,cAAc9H,MAAM8H,YAAY;wBAChCC,kBAAkBvB,WAAWuB,gBAAgB;wBAC7CpH,aAAa8F,QAAQ9F,WAAW;wBAChCC,yBAAyB1B,uBAAuBsH;wBAChDzF,YAAYiH,SAAQxB,+BAAAA,WAAWnB,YAAY,CAAC4C,GAAG,qBAA3BzB,6BAA6B0B,SAAS;wBAC1D9G,SAASpB,MAAMoB,OAAO;wBACtBC;oBACF;oBACAkG,sBAEI,IAAIE,QAAQ,KAAO,KAEnB,IAAIA,QAAQ,CAACU,GAAGC;wBACdC,WAAW;4BACTD,OAAO,IAAIzI;wBACb,GAAG6G,WAAW8B,2BAA2B,GAAG;oBAC9C;iBACL;gBAED,4GAA4G;gBAC5G,qHAAqH;gBACrH,IAAIhB,UAAU,WAAWA,QAAQ;oBAC/B,MAAM,IAAIxH;gBACZ;gBAGA;YACF,EAAE,OAAOyI,KAAK;gBACZ,qJAAqJ;gBACrJ,mGAAmG;gBACnG,IAAI,CAAEA,CAAAA,eAAezI,mBAAmByI,eAAe5I,YAAW,GAAI;oBACpE,MAAM4I;gBACR;gBAEA,IAAIA,eAAe5I,cAAc;oBAC/B,qEAAqE;oBACrEwH,cAAc;gBAChB;gBAEA,+CAA+C;gBAC/C,IAAIE,WAAWF,cAAc,GAAG;oBAC9B,iEAAiE;oBACjE,yDAAyD;oBACzD,IAAIA,cAAc,GAAG;wBACnBqB,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAErB,QAAQ,OAAO,EAAED,YAAY,UAAU,CAAC;oBAE/D;oBACA,sEAAsE;oBACtE,IAAIX,WAAWnB,YAAY,CAACqD,kBAAkB,EAAE;wBAC9CF,QAAQG,KAAK,CACX,CAAC,+BAA+B,EAAEvB,QAAQ,oBAAoB,CAAC;wBAEjEhK,QAAQwL,IAAI,CAAC;oBACf,OAAO;oBACL,mHAAmH;oBACrH;gBACF,OAAO;oBACL,iEAAiE;oBACjE,IAAIL,eAAe5I,cAAc;wBAC/B6I,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAErB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,4BAA4B,EAAEX,WAAW8B,2BAA2B,CAAC,iCAAiC,CAAC;oBAEhL,OAAO;wBACLE,QAAQC,IAAI,CACV,CAAC,gBAAgB,EAAErB,QAAQ,UAAU,EAAEC,UAAU,EAAE,IAAI,EAAEF,YAAY,0BAA0B,CAAC;oBAEpG;oBAEA,6EAA6E;oBAC7E,MAAM0B,YAAY,IAAI,QAAQ;;oBAC9B,MAAMC,WAAW,KAAK,YAAY;;oBAClC,MAAMC,QAAQC,KAAKC,GAAG,CAACJ,YAAYG,KAAKE,GAAG,CAAC,GAAG7B,UAAUyB;oBACzD,MAAMK,SAASH,KAAKI,MAAM,KAAK,MAAML,MAAM,8BAA8B;;oBACzE,MAAM,IAAItB,QAAQ,CAAC4B,IAAMhB,WAAWgB,GAAGN,QAAQI;gBACjD;YACF;YAEA9B;QACF;QAEA,OAAO;YAAEC;YAAQ/F;YAAMC;YAAM4F;QAAQ;IACvC;IAEA,IAAK,IAAIkC,IAAI,GAAGA,IAAInD,YAAY9C,MAAM,EAAEiG,KAAKvC,eAAgB;QAC3D,MAAMwC,SAASpD,YAAYqD,KAAK,CAACF,GAAGA,IAAIvC;QAExC,MAAM0C,gBAAgB,MAAMhC,QAAQiC,GAAG,CACrCH,OAAOI,GAAG,CAAC,CAACxJ,aACV+G,oBACE/G,YACAqG,WAAWnB,YAAY,CAACuE,0BAA0B,IAAI;QAK5D3C,QAAQ4C,IAAI,IAAIJ;IAClB;IAEA,OAAOxC;AACT;AAEA,eAAeU,WACb3H,KAAsB;IAEtBhC,MAAM,eAAegC,MAAM8H,YAAY,EAAEgC,YAAY,CACnD,QACA9J,MAAMG,UAAU,CAACoB,IAAI;IAGvB,4BAA4B;IAC5BtD,6BAA6B;QAC3B8J,kBAAkB/H,MAAM+H,gBAAgB;IAC1C;IAEA,MAAM9H,aAAa,IAAIb,gBAAgB;QACrC2K,WAAW,CAACxH,UAAUyH,OAASrM,GAAGoM,SAAS,CAACxH,UAAUyH;QACtDnF,OAAO,CAACuB,MAAQzI,GAAGkH,KAAK,CAACuB,KAAK;gBAAEtB,WAAW;YAAK;IAClD;IAEA,MAAMmF,iBAAiBjM,MAAM,sBAAsBgC,MAAM8H,YAAY;IAErE,MAAMoC,QAAQC,KAAKC,GAAG;IAEtB,MAAMC,6BAA6B,IAAIrL;IAEvC,mBAAmB;IACnB,IAAIsI;IACJ,IAAI;QACFA,SAAS,MAAM2C,eAAeK,YAAY,CAAC,IACzCvL,qBACE,IAAMgB,eAAeC,OAAOC,aAC5BoK;QAIJ,2CAA2C;QAC3C,MAAMpK,WAAWsK,IAAI;QAErB,kDAAkD;QAClD,IAAI,CAACjD,QAAQ;QAEb,iDAAiD;QACjD,IAAI,WAAWA,QAAQ;YACrB,OAAO;gBAAEqB,OAAOrB,OAAOqB,KAAK;gBAAE6B,UAAUL,KAAKC,GAAG,KAAKF;YAAM;QAC7D;IACF,EAAE,OAAO3B,KAAK;QACZC,QAAQG,KAAK,CACX,CAAC,kCAAkC,EAAE3I,MAAMG,UAAU,CAACoB,IAAI,CAAC,8DAA8D,CAAC;QAG5H,2FAA2F;QAC3F,qBAAqB;QACrB,IAAI,CAACzC,oBAAoByJ,MAAM;YAC7B,wFAAwF;YACxF,wFAAwF;YACxF,wGAAwG;YACxG,4FAA4F;YAC5F,IAAIpJ,wBAAwBoJ,MAAM;gBAChC,IAAIA,IAAIkC,OAAO,EAAE;oBACfjC,QAAQG,KAAK,CAAC,CAAC,OAAO,EAAEJ,IAAIkC,OAAO,EAAE;gBACvC;YACF,OAAO;gBACLjC,QAAQG,KAAK,CAACJ;YAChB;QACF;QAEA,OAAO;YAAEI,OAAO;YAAM6B,UAAUL,KAAKC,GAAG,KAAKF;QAAM;IACrD;IAEA,+FAA+F;IAC/F9M,QAAQsN,IAAI,oBAAZtN,QAAQsN,IAAI,MAAZtN,SAAe;QAAC;QAAG;YAAEuN,MAAM;QAAW;KAAE;IAExC,sCAAsC;IACtC,OAAO;QACL,GAAGrD,MAAM;QACTkD,UAAUL,KAAKC,GAAG,KAAKF;QACvBG,4BAA4BA,2BAA2BO,SAAS;IAClE;AACF;AAEAxN,QAAQyN,EAAE,CAAC,sBAAsB,CAACtC;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAI3J,WAAW2J,MAAM;QACnB;IACF;IAEA,oCAAoC;IACpC,IAAI1J,oBAAoB0J,MAAM;QAC5B;IACF;IAEAC,QAAQG,KAAK,CAACJ;AAChB;AAEAnL,QAAQyN,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,qEAAqE;AACrE,6DAA6D;AAC/D;AAEA,MAAMC,qCAAqC;AAE3C1N,QAAQyN,EAAE,CAAC,qBAAqB,CAACtC;IAC/B,IAAI1J,oBAAoB0J,MAAM;QAC5BC,QAAQG,KAAK,CACX;QAEFH,QAAQG,KAAK,CAACJ;QACdnL,QAAQwL,IAAI,CAACkC;IACf,OAAO;QACLtC,QAAQG,KAAK,CAACJ;IAChB;AACF", "ignoreList": [0]}