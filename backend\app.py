from dotenv import load_dotenv
load_dotenv()
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import faiss, pickle, numpy as np, os, requests
from sentence_transformers import SentenceTransformer


LLM_API = os.getenv("LLM_API_ENDPOINT") # Endpoint OpenAI‑compatible hoặc nội bộ
LLM_KEY = os.getenv("LLM_API_KEY")


index = faiss.read_index("index.faiss")
chunks = pickle.load(open("chunks.pkl", "rb"))
embed_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")


app = FastAPI()
# CORS nếu gọi từ frontend (Next.js ở port 3000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # hoặc ["http://localhost:3000"]
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"status": "ok", "message": "FastAPI RAG backend is running", "try": ["/docs", "/chat"]}

@app.get("/health")
def health():
    return {"ok": True}

def retrieve(query: str, k: int = 5):
    qemb = embed_model.encode([query], normalize_embeddings=True).astype("float32")
    D, I = index.search(qemb, k)
    return [chunks[i] for i in I[0]]


def call_llm(messages):
    headers = {"Authorization": f"Bearer {LLM_KEY}"}
    payload = {"model": "gpt-4.1-mini", "messages": messages, "temperature": 0.2}
    r = requests.post(LLM_API, json=payload, headers=headers, timeout=60)
    r.raise_for_status()
    return r.json()["choices"][0]["message"]["content"]


class ChatReq(BaseModel):
    query: str


@app.post("/chat")
def chat(req: ChatReq):
    ctx = retrieve(req.query, k=5)
    sys = (
            "Bạn là trợ lý trả lời bằng tiếng Việt, trung thực, trích dẫn nguồn. "
            "Nếu không đủ dữ liệu trong nguồn tham chiếu thì nói không biết."
            )
    context = "\n\n".join([f"[{i+1}] {c['text']}" for i, c in enumerate(ctx)])
    user = f"Câu hỏi: {req.query}\n\nNguồn tham chiếu:\n{context}"
    msgs = [{"role": "system", "content": sys}, {"role": "user", "content": user}]
    answer = call_llm(msgs)
    sources = [c["meta"]["source"] for c in ctx]
    return {"answer": answer, "sources": sources}