# backend/app.py
import os, pickle, logging, traceback
from typing import List, Dict
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import faiss, numpy as np
import requests
from sentence_transformers import SentenceTransformer

load_dotenv()

LLM_API = os.getenv("LLM_API_ENDPOINT", "").strip()
LLM_KEY = os.getenv("LLM_API_KEY", "").strip()
LLM_MODEL = os.getenv("LLM_MODEL", "gpt-4o-mini").strip()

# Log cấu hình (ẩn key)
print(f"[BOOT] LLM_API_ENDPOINT = {LLM_API or '(missing)'}")
print(f"[BOOT] LLM_API_KEY      = {'(set)' if LLM_KEY else '(empty)'}")
print(f"[BOOT] LLM_MODEL        = {LLM_MODEL}")

# Nạp index + model
INDEX_PATH = os.getenv("INDEX_PATH", "index.faiss")
CHUNKS_PATH = os.getenv("CHUNKS_PATH", "chunks.pkl")
if not os.path.exists(INDEX_PATH) or not os.path.exists(CHUNKS_PATH):
    raise RuntimeError("Thiếu index/chunks. Hãy chạy: python build_index.py")

index = faiss.read_index(INDEX_PATH)
chunks = pickle.load(open(CHUNKS_PATH, "rb"))
embed_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

# Kiểm tra kích thước embedding khớp index
dim_faiss = index.d
dim_model = embed_model.get_sentence_embedding_dimension()
if dim_faiss != dim_model:
    raise RuntimeError(
        f"Embedding dim mismatch (FAISS={dim_faiss} vs model={dim_model}). "
        "Bạn cần xóa index.faiss/chunks.pkl và chạy lại build_index.py."
    )

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"],
)

class ChatReq(BaseModel):
    query: str

@app.get("/health")
def health():
    return {"ok": True}

def retrieve(query: str, k: int = 5) -> List[Dict]:
    qemb = embed_model.encode([query], normalize_embeddings=True).astype("float32")
    D, I = index.search(qemb, k)
    return [chunks[i] for i in I[0]]

def call_llm(messages):
    if not LLM_API:
        raise RuntimeError("Thiếu LLM_API_ENDPOINT trong .env")
    headers = {"Content-Type": "application/json"}
    if LLM_KEY:
        headers["Authorization"] = f"Bearer {LLM_KEY}"
    payload = {"model": LLM_MODEL, "messages": messages, "temperature": 0.2}
    r = requests.post(LLM_API, json=payload, headers=headers, timeout=90)
    # Ghi log lỗi chi tiết nếu có
    try:
        j = r.json()
    except Exception:
        j = None
    if r.status_code >= 400:
        raise RuntimeError(f"LLM {r.status_code}: {j or r.text}")
    try:
        return j["choices"][0]["message"]["content"]
    except Exception:
        raise RuntimeError(f"Phản hồi LLM lạ: {j}")

@app.post("/chat")
def chat(req: ChatReq):
    try:
        ctx = retrieve(req.query, k=5)
        context = "\n\n".join([f"[{i+1}] {c['text']}" for i, c in enumerate(ctx)])
        sys = (
            "Bạn là trợ lý trả lời tiếng Việt, trung thực, có trích dẫn nguồn. "
            "Nếu không chắc, nói không biết."
        )
        user = f"Câu hỏi: {req.query}\n\nNguồn tham chiếu:\n{context}"
        msgs = [{"role": "system", "content": sys}, {"role": "user", "content": user}]
        answer = call_llm(msgs)
        sources = [c["meta"]["source"] for c in ctx]
        return {"answer": answer, "sources": sources}
    except Exception as e:
        logging.exception("Chat error")
        return JSONResponse({"error": str(e), "trace": traceback.format_exc()}, status_code=500)
