{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "sourcesContent": ["import type { CacheNodeSeedData, PreloadCallbacks } from './types'\nimport React from 'react'\nimport {\n  isClientReference,\n  isUseCacheFunction,\n} from '../../lib/client-and-server-references'\nimport { getLayoutOrPageModule } from '../lib/app-dir-module'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport { interopDefault } from './interop-default'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport type { AppRenderContext, GetDynamicParamFromSegment } from './app-render'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { getLayerAssets } from './get-layer-assets'\nimport { hasLoadingComponentInTree } from './has-loading-component-in-tree'\nimport { validateRevalidate } from '../lib/patch-fetch'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../client/components/builtin/default'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NextNodeServerSpan } from '../lib/trace/constants'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { Params } from '../request/params'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { OUTLET_BOUNDARY_NAME } from '../../lib/framework/boundary-constants'\nimport type {\n  UseCacheLayoutComponentProps,\n  UseCachePageComponentProps,\n} from '../use-cache/use-cache-wrapper'\nimport { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport {\n  BOUNDARY_PREFIX,\n  BOUNDARY_SUFFIX,\n  BUILTIN_PREFIX,\n  getConventionPathByType,\n  isNextjsBuiltinFilePath,\n} from './segment-explorer-path'\n\n/**\n * Use the provided loader tree to create the React Component tree.\n */\nexport function createComponentTree(props: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType | null\n}): Promise<CacheNodeSeedData> {\n  return getTracer().trace(\n    NextNodeServerSpan.createComponentTree,\n    {\n      spanName: 'build component tree',\n    },\n    () => createComponentTreeInternal(props, true)\n  )\n}\n\nfunction errorMissingDefaultExport(\n  pagePath: string,\n  convention: string\n): never {\n  const normalizedPagePath = pagePath === '/' ? '' : pagePath\n  throw new Error(\n    `The default export is not a React Component in \"${normalizedPagePath}/${convention}\"`\n  )\n}\n\nconst cacheNodeKey = 'c'\n\nasync function createComponentTreeInternal(\n  {\n    loaderTree: tree,\n    parentParams,\n    rootLayoutIncluded,\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    getViewportReady,\n    getMetadataReady,\n    ctx,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts,\n    StreamingMetadataOutlet,\n  }: {\n    loaderTree: LoaderTree\n    parentParams: Params\n    rootLayoutIncluded: boolean\n    injectedCSS: Set<string>\n    injectedJS: Set<string>\n    injectedFontPreloadTags: Set<string>\n    getViewportReady: () => Promise<void>\n    getMetadataReady: () => Promise<void>\n    ctx: AppRenderContext\n    missingSlots?: Set<string>\n    preloadCallbacks: PreloadCallbacks\n    authInterrupts: boolean\n    StreamingMetadataOutlet: React.ComponentType | null\n  },\n  isRoot: boolean\n): Promise<CacheNodeSeedData> {\n  const {\n    renderOpts: { nextConfigOutput, experimental },\n    workStore,\n    componentMod: {\n      SegmentViewNode,\n      HTTPAccessFallbackBoundary,\n      LayoutRouter,\n      RenderFromTemplateContext,\n      OutletBoundary,\n      ClientPageRoot,\n      ClientSegmentRoot,\n      createServerSearchParamsForServerPage,\n      createPrerenderSearchParamsForClientPage,\n      createServerParamsForServerSegment,\n      createPrerenderParamsForClientSegment,\n      serverHooks: { DynamicServerError },\n      Postpone,\n    },\n    pagePath,\n    getDynamicParamFromSegment,\n    isPrefetch,\n    query,\n  } = ctx\n\n  const { page, conventionPath, segment, modules, parallelRoutes } =\n    parseLoaderTree(tree)\n\n  const {\n    layout,\n    template,\n    error,\n    loading,\n    'not-found': notFound,\n    forbidden,\n    unauthorized,\n  } = modules\n\n  const injectedCSSWithCurrentLayout = new Set(injectedCSS)\n  const injectedJSWithCurrentLayout = new Set(injectedJS)\n  const injectedFontPreloadTagsWithCurrentLayout = new Set(\n    injectedFontPreloadTags\n  )\n\n  const layerAssets = getLayerAssets({\n    preloadCallbacks,\n    ctx,\n    layoutOrPagePath: conventionPath,\n    injectedCSS: injectedCSSWithCurrentLayout,\n    injectedJS: injectedJSWithCurrentLayout,\n    injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  })\n\n  const [Template, templateStyles, templateScripts] = template\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: template[1],\n        getComponent: template[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : [React.Fragment]\n\n  const [ErrorComponent, errorStyles, errorScripts] = error\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: error[1],\n        getComponent: error[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Loading, loadingStyles, loadingScripts] = loading\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: loading[1],\n        getComponent: loading[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const { mod: layoutOrPageMod, modType } = await getTracer().trace(\n    NextNodeServerSpan.getLayoutOrPageModule,\n    {\n      hideSpan: !(isLayout || isPage),\n      spanName: 'resolve segment modules',\n      attributes: {\n        'next.segment': segment,\n      },\n    },\n    () => getLayoutOrPageModule(tree)\n  )\n\n  /**\n   * Checks if the current segment is a root layout.\n   */\n  const rootLayoutAtThisLevel = isLayout && !rootLayoutIncluded\n  /**\n   * Checks if the current segment or any level above it has a root layout.\n   */\n  const rootLayoutIncludedAtThisLevelOrAbove =\n    rootLayoutIncluded || rootLayoutAtThisLevel\n\n  const [NotFound, notFoundStyles] = notFound\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: notFound[1],\n        getComponent: notFound[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Forbidden, forbiddenStyles] =\n    authInterrupts && forbidden\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: forbidden[1],\n          getComponent: forbidden[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  const [Unauthorized, unauthorizedStyles] =\n    authInterrupts && unauthorized\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: unauthorized[1],\n          getComponent: unauthorized[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  let dynamic = layoutOrPageMod?.dynamic\n\n  if (nextConfigOutput === 'export') {\n    if (!dynamic || dynamic === 'auto') {\n      dynamic = 'error'\n    } else if (dynamic === 'force-dynamic') {\n      // force-dynamic is always incompatible with 'export'. We must interrupt the build\n      throw new StaticGenBailoutError(\n        `Page with \\`dynamic = \"force-dynamic\"\\` couldn't be exported. \\`output: \"export\"\\` requires all pages be renderable statically because there is no runtime server to dynamically render routes in this output format. Learn more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports`\n      )\n    }\n  }\n\n  if (typeof dynamic === 'string') {\n    // the nested most config wins so we only force-static\n    // if it's configured above any parent that configured\n    // otherwise\n    if (dynamic === 'error') {\n      workStore.dynamicShouldError = true\n    } else if (dynamic === 'force-dynamic') {\n      workStore.forceDynamic = true\n\n      // TODO: (PPR) remove this bailout once PPR is the default\n      if (workStore.isStaticGeneration && !experimental.isRoutePPREnabled) {\n        // If the postpone API isn't available, we can't postpone the render and\n        // therefore we can't use the dynamic API.\n        const err = new DynamicServerError(\n          `Page with \\`dynamic = \"force-dynamic\"\\` won't be rendered statically.`\n        )\n        workStore.dynamicUsageDescription = err.message\n        workStore.dynamicUsageStack = err.stack\n        throw err\n      }\n    } else {\n      workStore.dynamicShouldError = false\n      workStore.forceStatic = dynamic === 'force-static'\n    }\n  }\n\n  if (typeof layoutOrPageMod?.fetchCache === 'string') {\n    workStore.fetchCache = layoutOrPageMod?.fetchCache\n  }\n\n  if (typeof layoutOrPageMod?.revalidate !== 'undefined') {\n    validateRevalidate(layoutOrPageMod?.revalidate, workStore.route)\n  }\n\n  if (typeof layoutOrPageMod?.revalidate === 'number') {\n    const defaultRevalidate = layoutOrPageMod.revalidate as number\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-runtime':\n        case 'prerender-legacy':\n        case 'prerender-ppr':\n          if (workUnitStore.revalidate > defaultRevalidate) {\n            workUnitStore.revalidate = defaultRevalidate\n          }\n          break\n        case 'request':\n          // A request store doesn't have a revalidate property.\n          break\n        // createComponentTree is not called for these stores:\n        case 'cache':\n        case 'private-cache':\n        case 'prerender-client':\n        case 'unstable-cache':\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    if (\n      !workStore.forceStatic &&\n      workStore.isStaticGeneration &&\n      defaultRevalidate === 0 &&\n      // If the postpone API isn't available, we can't postpone the render and\n      // therefore we can't use the dynamic API.\n      !experimental.isRoutePPREnabled\n    ) {\n      const dynamicUsageDescription = `revalidate: 0 configured ${segment}`\n      workStore.dynamicUsageDescription = dynamicUsageDescription\n\n      throw new DynamicServerError(dynamicUsageDescription)\n    }\n  }\n\n  const isStaticGeneration = workStore.isStaticGeneration\n\n  // Assume the segment we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // It's OK for this to be `true` when the data is actually fully static, but\n  // it's not OK for this to be `false` when the data possibly contains holes.\n  // Although the value here is overly pessimistic, for prefetches, it will be\n  // replaced by a more specific value when the data is later processed into\n  // per-segment responses (see collect-segment-data.tsx)\n  //\n  // For dynamic requests, this must always be `false` because dynamic responses\n  // are never partial.\n  const isPossiblyPartialResponse =\n    isStaticGeneration && experimental.isRoutePPREnabled === true\n\n  const LayoutOrPage: React.ComponentType<any> | undefined = layoutOrPageMod\n    ? interopDefault(layoutOrPageMod)\n    : undefined\n\n  /**\n   * The React Component to render.\n   */\n  let MaybeComponent = LayoutOrPage\n\n  if (process.env.NODE_ENV === 'development') {\n    const { isValidElementType } =\n      require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n    if (\n      typeof MaybeComponent !== 'undefined' &&\n      !isValidElementType(MaybeComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, modType ?? 'page')\n    }\n\n    if (\n      typeof ErrorComponent !== 'undefined' &&\n      !isValidElementType(ErrorComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, 'error')\n    }\n\n    if (typeof Loading !== 'undefined' && !isValidElementType(Loading)) {\n      errorMissingDefaultExport(pagePath, 'loading')\n    }\n\n    if (typeof NotFound !== 'undefined' && !isValidElementType(NotFound)) {\n      errorMissingDefaultExport(pagePath, 'not-found')\n    }\n\n    if (typeof Forbidden !== 'undefined' && !isValidElementType(Forbidden)) {\n      errorMissingDefaultExport(pagePath, 'forbidden')\n    }\n\n    if (\n      typeof Unauthorized !== 'undefined' &&\n      !isValidElementType(Unauthorized)\n    ) {\n      errorMissingDefaultExport(pagePath, 'unauthorized')\n    }\n  }\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  // Create object holding the parent params and current params\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  // Resolve the segment param\n  const actualSegment = segmentParam ? segmentParam.treeSegment : segment\n  const isSegmentViewEnabled =\n    process.env.NODE_ENV === 'development' &&\n    ctx.renderOpts.devtoolSegmentExplorer\n  const dir =\n    (process.env.NEXT_RUNTIME === 'edge'\n      ? process.env.__NEXT_EDGE_PROJECT_DIR\n      : ctx.renderOpts.dir) || ''\n\n  // Use the same condition to render metadataOutlet as metadata\n  const metadataOutlet = StreamingMetadataOutlet ? (\n    <StreamingMetadataOutlet />\n  ) : (\n    <MetadataOutlet ready={getMetadataReady} />\n  )\n\n  const [notFoundElement, notFoundFilePath] =\n    await createBoundaryConventionElement({\n      ctx,\n      conventionName: 'not-found',\n      Component: NotFound,\n      styles: notFoundStyles,\n      tree,\n    })\n\n  const [forbiddenElement] = await createBoundaryConventionElement({\n    ctx,\n    conventionName: 'forbidden',\n    Component: Forbidden,\n    styles: forbiddenStyles,\n    tree,\n  })\n\n  const [unauthorizedElement] = await createBoundaryConventionElement({\n    ctx,\n    conventionName: 'unauthorized',\n    Component: Unauthorized,\n    styles: unauthorizedStyles,\n    tree,\n  })\n\n  // TODO: Combine this `map` traversal with the loop below that turns the array\n  // into an object.\n  const parallelRouteMap = await Promise.all(\n    Object.keys(parallelRoutes).map(\n      async (\n        parallelRouteKey\n      ): Promise<[string, React.ReactNode, CacheNodeSeedData | null]> => {\n        const isChildrenRouteKey = parallelRouteKey === 'children'\n        const parallelRoute = parallelRoutes[parallelRouteKey]\n\n        const notFoundComponent = isChildrenRouteKey\n          ? notFoundElement\n          : undefined\n\n        const forbiddenComponent = isChildrenRouteKey\n          ? forbiddenElement\n          : undefined\n\n        const unauthorizedComponent = isChildrenRouteKey\n          ? unauthorizedElement\n          : undefined\n\n        // if we're prefetching and that there's a Loading component, we bail out\n        // otherwise we keep rendering for the prefetch.\n        // We also want to bail out if there's no Loading component in the tree.\n        let childCacheNodeSeedData: CacheNodeSeedData | null = null\n\n        if (\n          // Before PPR, the way instant navigations work in Next.js is we\n          // prefetch everything up to the first route segment that defines a\n          // loading.tsx boundary. (We do the same if there's no loading\n          // boundary in the entire tree, because we don't want to prefetch too\n          // much) The rest of the tree is deferred until the actual navigation.\n          // It does not take into account whether the data is dynamic — even if\n          // the tree is completely static, it will still defer everything\n          // inside the loading boundary.\n          //\n          // This behavior predates PPR and is only relevant if the\n          // PPR flag is not enabled.\n          isPrefetch &&\n          (Loading || !hasLoadingComponentInTree(parallelRoute)) &&\n          // The approach with PPR is different — loading.tsx behaves like a\n          // regular Suspense boundary and has no special behavior.\n          //\n          // With PPR, we prefetch as deeply as possible, and only defer when\n          // dynamic data is accessed. If so, we only defer the nearest parent\n          // Suspense boundary of the dynamic data access, regardless of whether\n          // the boundary is defined by loading.tsx or a normal <Suspense>\n          // component in userspace.\n          //\n          // NOTE: In practice this usually means we'll end up prefetching more\n          // than we were before PPR, which may or may not be considered a\n          // performance regression by some apps. The plan is to address this\n          // before General Availability of PPR by introducing granular\n          // per-segment fetching, so we can reuse as much of the tree as\n          // possible during both prefetches and dynamic navigations. But during\n          // the beta period, we should be clear about this trade off in our\n          // communications.\n          !experimental.isRoutePPREnabled\n        ) {\n          // Don't prefetch this child. This will trigger a lazy fetch by the\n          // client router.\n        } else {\n          // Create the child component\n\n          if (process.env.NODE_ENV === 'development' && missingSlots) {\n            // When we detect the default fallback (which triggers a 404), we collect the missing slots\n            // to provide more helpful debug information during development mode.\n            const parsedTree = parseLoaderTree(parallelRoute)\n            if (\n              parsedTree.conventionPath?.endsWith(PARALLEL_ROUTE_DEFAULT_PATH)\n            ) {\n              missingSlots.add(parallelRouteKey)\n            }\n          }\n\n          const seedData = await createComponentTreeInternal(\n            {\n              loaderTree: parallelRoute,\n              parentParams: currentParams,\n              rootLayoutIncluded: rootLayoutIncludedAtThisLevelOrAbove,\n              injectedCSS: injectedCSSWithCurrentLayout,\n              injectedJS: injectedJSWithCurrentLayout,\n              injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n              // `getMetadataReady` and `getViewportReady` are used to conditionally throw. In the case of parallel routes we will have more than one page\n              // but we only want to throw on the first one.\n              getMetadataReady: isChildrenRouteKey\n                ? getMetadataReady\n                : () => Promise.resolve(),\n              getViewportReady: isChildrenRouteKey\n                ? getViewportReady\n                : () => Promise.resolve(),\n              ctx,\n              missingSlots,\n              preloadCallbacks,\n              authInterrupts,\n              // `StreamingMetadataOutlet` is used to conditionally throw. In the case of parallel routes we will have more than one page\n              // but we only want to throw on the first one.\n              StreamingMetadataOutlet: isChildrenRouteKey\n                ? StreamingMetadataOutlet\n                : null,\n            },\n            false\n          )\n\n          childCacheNodeSeedData = seedData\n        }\n\n        const templateNode = (\n          <Template>\n            <RenderFromTemplateContext />\n          </Template>\n        )\n\n        const templateFilePath = getConventionPathByType(tree, dir, 'template')\n        const errorFilePath = getConventionPathByType(tree, dir, 'error')\n        const loadingFilePath = getConventionPathByType(tree, dir, 'loading')\n        const globalErrorFilePath = isRoot\n          ? getConventionPathByType(tree, dir, 'global-error')\n          : undefined\n\n        const wrappedErrorStyles =\n          isSegmentViewEnabled && errorFilePath ? (\n            <SegmentViewNode type=\"error\" pagePath={errorFilePath}>\n              {errorStyles}\n            </SegmentViewNode>\n          ) : (\n            errorStyles\n          )\n\n        // Add a suffix to avoid conflict with the segment view node representing rendered file.\n        // existence: not-found.tsx@boundary\n        // rendered: not-found.tsx\n        const fileNameSuffix = BOUNDARY_SUFFIX\n        const segmentViewBoundaries = isSegmentViewEnabled ? (\n          <>\n            {notFoundFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}not-found`}\n                pagePath={notFoundFilePath + fileNameSuffix}\n              />\n            )}\n            {loadingFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}loading`}\n                pagePath={loadingFilePath + fileNameSuffix}\n              />\n            )}\n            {errorFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}error`}\n                pagePath={errorFilePath + fileNameSuffix}\n              />\n            )}\n            {/* Only show global-error when it's the builtin one */}\n            {globalErrorFilePath && (\n              <SegmentViewNode\n                type={`${BOUNDARY_PREFIX}global-error`}\n                pagePath={\n                  isNextjsBuiltinFilePath(globalErrorFilePath)\n                    ? `${BUILTIN_PREFIX}global-error.js${fileNameSuffix}`\n                    : globalErrorFilePath\n                }\n              />\n            )}\n            {/* do not surface forbidden and unauthorized boundaries yet as they're unstable */}\n          </>\n        ) : null\n\n        return [\n          parallelRouteKey,\n          <LayoutRouter\n            parallelRouterKey={parallelRouteKey}\n            // TODO-APP: Add test for loading returning `undefined`. This currently can't be tested as the `webdriver()` tab will wait for the full page to load before returning.\n            error={ErrorComponent}\n            errorStyles={wrappedErrorStyles}\n            errorScripts={errorScripts}\n            template={\n              // Only render SegmentViewNode when there's an actual template\n              isSegmentViewEnabled && templateFilePath ? (\n                <SegmentViewNode type=\"template\" pagePath={templateFilePath}>\n                  {templateNode}\n                </SegmentViewNode>\n              ) : (\n                templateNode\n              )\n            }\n            templateStyles={templateStyles}\n            templateScripts={templateScripts}\n            notFound={notFoundComponent}\n            forbidden={forbiddenComponent}\n            unauthorized={unauthorizedComponent}\n            {...(isSegmentViewEnabled && { segmentViewBoundaries })}\n          />,\n          childCacheNodeSeedData,\n        ]\n      }\n    )\n  )\n\n  // Convert the parallel route map into an object after all promises have been resolved.\n  let parallelRouteProps: { [key: string]: React.ReactNode } = {}\n  let parallelRouteCacheNodeSeedData: {\n    [key: string]: CacheNodeSeedData | null\n  } = {}\n  for (const parallelRoute of parallelRouteMap) {\n    const [parallelRouteKey, parallelRouteProp, flightData] = parallelRoute\n    parallelRouteProps[parallelRouteKey] = parallelRouteProp\n    parallelRouteCacheNodeSeedData[parallelRouteKey] = flightData\n  }\n\n  let loadingElement = Loading ? <Loading key=\"l\" /> : null\n  const loadingFilePath = getConventionPathByType(tree, dir, 'loading')\n  if (isSegmentViewEnabled && loadingElement) {\n    if (loadingFilePath) {\n      loadingElement = (\n        <SegmentViewNode\n          key={cacheNodeKey + '-loading'}\n          type=\"loading\"\n          pagePath={loadingFilePath}\n        >\n          {loadingElement}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  const loadingData: LoadingModuleData = loadingElement\n    ? [loadingElement, loadingStyles, loadingScripts]\n    : null\n\n  // When the segment does not have a layout or page we still have to add the layout router to ensure the path holds the loading component\n  if (!MaybeComponent) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {layerAssets}\n        {parallelRouteProps.children}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n\n  const Component = MaybeComponent\n  // If force-dynamic is used and the current render supports postponing, we\n  // replace it with a node that will postpone the render. This ensures that the\n  // postpone is invoked during the react render phase and not during the next\n  // render phase.\n  // @TODO this does not actually do what it seems like it would or should do. The idea is that\n  // if we are rendering in a force-dynamic mode and we can postpone we should only make the segments\n  // that ask for force-dynamic to be dynamic, allowing other segments to still prerender. However\n  // because this comes after the children traversal and the static generation store is mutated every segment\n  // along the parent path of a force-dynamic segment will hit this condition effectively making the entire\n  // render force-dynamic. We should refactor this function so that we can correctly track which segments\n  // need to be dynamic\n  if (\n    workStore.isStaticGeneration &&\n    workStore.forceDynamic &&\n    experimental.isRoutePPREnabled\n  ) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        <Postpone\n          reason='dynamic = \"force-dynamic\" was used'\n          route={workStore.route}\n        />\n        {layerAssets}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      true,\n    ]\n  }\n\n  const isClientComponent = isClientReference(layoutOrPageMod)\n\n  if (\n    process.env.NODE_ENV === 'development' &&\n    'params' in parallelRouteProps\n  ) {\n    // @TODO consider making this an error and running the check in build as well\n    console.error(\n      `\"params\" is a reserved prop in Layouts and Pages and cannot be used as the name of a parallel route in ${segment}`\n    )\n  }\n\n  if (isPage) {\n    const PageComponent = Component\n\n    // Assign searchParams to props if this is a page\n    let pageElement: React.ReactNode\n    if (isClientComponent) {\n      if (isStaticGeneration) {\n        const promiseOfParams =\n          createPrerenderParamsForClientSegment(currentParams)\n        const promiseOfSearchParams =\n          createPrerenderSearchParamsForClientPage(workStore)\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n            promises={[promiseOfSearchParams, promiseOfParams]}\n          />\n        )\n      } else {\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n          />\n        )\n      }\n    } else {\n      // If we are passing params to a server component Page we need to track\n      // their usage in case the current render mode tracks dynamic API usage.\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      // If we are passing searchParams to a server component Page we need to\n      // track their usage in case the current render mode tracks dynamic API\n      // usage.\n      let searchParams = createServerSearchParamsForServerPage(query, workStore)\n\n      if (isUseCacheFunction(PageComponent)) {\n        const UseCachePageComponent: React.ComponentType<UseCachePageComponentProps> =\n          PageComponent\n\n        pageElement = (\n          <UseCachePageComponent\n            params={params}\n            searchParams={searchParams}\n            $$isPageComponent\n          />\n        )\n      } else {\n        pageElement = (\n          <PageComponent params={params} searchParams={searchParams} />\n        )\n      }\n    }\n\n    const isDefaultSegment = segment === DEFAULT_SEGMENT_KEY\n    const pageFilePath =\n      getConventionPathByType(tree, dir, 'page') ??\n      getConventionPathByType(tree, dir, 'defaultPage')\n    const segmentType = isDefaultSegment ? 'default' : 'page'\n    const wrappedPageElement =\n      isSegmentViewEnabled && pageFilePath ? (\n        <SegmentViewNode\n          key={cacheNodeKey + '-' + segmentType}\n          type={segmentType}\n          pagePath={pageFilePath}\n        >\n          {pageElement}\n        </SegmentViewNode>\n      ) : (\n        pageElement\n      )\n\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {wrappedPageElement}\n        {layerAssets}\n        <OutletBoundary>\n          <MetadataOutlet ready={getViewportReady} />\n          {metadataOutlet}\n        </OutletBoundary>\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  } else {\n    const SegmentComponent = Component\n    const isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot =\n      rootLayoutAtThisLevel &&\n      'children' in parallelRoutes &&\n      Object.keys(parallelRoutes).length > 1\n\n    let segmentNode: React.ReactNode\n\n    if (isClientComponent) {\n      let clientSegment: React.ReactNode\n\n      if (isStaticGeneration) {\n        const promiseOfParams =\n          createPrerenderParamsForClientSegment(currentParams)\n\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n            promise={promiseOfParams}\n          />\n        )\n      } else {\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n          />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        let notfoundClientSegment: React.ReactNode\n        let forbiddenClientSegment: React.ReactNode\n        let unauthorizedClientSegment: React.ReactNode\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        notfoundClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: NotFound,\n          errorElement: notFoundElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        forbiddenClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Forbidden,\n          errorElement: forbiddenElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        unauthorizedClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Unauthorized,\n          errorElement: unauthorizedElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        if (\n          notfoundClientSegment ||\n          forbiddenClientSegment ||\n          unauthorizedClientSegment\n        ) {\n          segmentNode = (\n            <HTTPAccessFallbackBoundary\n              key={cacheNodeKey}\n              notFound={notfoundClientSegment}\n              forbidden={forbiddenClientSegment}\n              unauthorized={unauthorizedClientSegment}\n            >\n              {layerAssets}\n              {clientSegment}\n            </HTTPAccessFallbackBoundary>\n          )\n        } else {\n          segmentNode = (\n            <React.Fragment key={cacheNodeKey}>\n              {layerAssets}\n              {clientSegment}\n            </React.Fragment>\n          )\n        }\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {clientSegment}\n          </React.Fragment>\n        )\n      }\n    } else {\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      let serverSegment: React.ReactNode\n\n      if (isUseCacheFunction(SegmentComponent)) {\n        const UseCacheLayoutComponent: React.ComponentType<UseCacheLayoutComponentProps> =\n          SegmentComponent\n\n        serverSegment = (\n          <UseCacheLayoutComponent\n            {...parallelRouteProps}\n            params={params}\n            $$isLayoutComponent\n          />\n        )\n      } else {\n        serverSegment = (\n          <SegmentComponent {...parallelRouteProps} params={params} />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        segmentNode = (\n          <HTTPAccessFallbackBoundary\n            key={cacheNodeKey}\n            notFound={\n              notFoundElement ? (\n                <>\n                  {layerAssets}\n                  <SegmentComponent params={params}>\n                    {notFoundStyles}\n                    {notFoundElement}\n                  </SegmentComponent>\n                </>\n              ) : undefined\n            }\n          >\n            {layerAssets}\n            {serverSegment}\n          </HTTPAccessFallbackBoundary>\n        )\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {serverSegment}\n          </React.Fragment>\n        )\n      }\n    }\n\n    const layoutFilePath = getConventionPathByType(tree, dir, 'layout')\n    const wrappedSegmentNode =\n      isSegmentViewEnabled && layoutFilePath ? (\n        <SegmentViewNode key=\"layout\" type=\"layout\" pagePath={layoutFilePath}>\n          {segmentNode}\n        </SegmentViewNode>\n      ) : (\n        segmentNode\n      )\n\n    // For layouts we just render the component\n    return [\n      actualSegment,\n      wrappedSegmentNode,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n}\n\nasync function MetadataOutlet({\n  ready,\n}: {\n  ready: () => Promise<void> & { status?: string; value?: unknown }\n}) {\n  const r = ready()\n  // We can avoid a extra microtask by unwrapping the instrumented promise directly if available.\n  if (r.status === 'rejected') {\n    throw r.value\n  } else if (r.status !== 'fulfilled') {\n    await r\n  }\n  return null\n}\nMetadataOutlet.displayName = OUTLET_BOUNDARY_NAME\n\nfunction createErrorBoundaryClientSegmentRoot({\n  ErrorBoundaryComponent,\n  errorElement,\n  ClientSegmentRoot,\n  layerAssets,\n  SegmentComponent,\n  currentParams,\n}: {\n  ErrorBoundaryComponent: React.ComponentType<any> | undefined\n  errorElement: React.ReactNode\n  ClientSegmentRoot: React.ComponentType<any>\n  layerAssets: React.ReactNode\n  SegmentComponent: React.ComponentType<any>\n  currentParams: Params\n}) {\n  if (ErrorBoundaryComponent) {\n    const notFoundParallelRouteProps = {\n      children: errorElement,\n    }\n    return (\n      <>\n        {layerAssets}\n        <ClientSegmentRoot\n          Component={SegmentComponent}\n          slots={notFoundParallelRouteProps}\n          params={currentParams}\n        />\n      </>\n    )\n  }\n  return null\n}\n\nexport function getRootParams(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  return getRootParamsImpl({}, loaderTree, getDynamicParamFromSegment)\n}\n\nfunction getRootParamsImpl(\n  parentParams: Params,\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  const {\n    segment,\n    modules: { layout },\n    parallelRoutes,\n  } = parseLoaderTree(loaderTree)\n\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const isRootLayout = typeof layout !== 'undefined'\n\n  if (isRootLayout) {\n    return currentParams\n  } else if (!parallelRoutes.children) {\n    // This should really be an error but there are bugs in Turbopack that cause\n    // the _not-found LoaderTree to not have any layouts. For rootParams sake\n    // this is somewhat irrelevant when you are not customizing the 404 page.\n    // If you are customizing 404\n    // TODO update rootParams to make all params optional if `/app/not-found.tsx` is defined\n    return currentParams\n  } else {\n    return getRootParamsImpl(\n      currentParams,\n      // We stop looking for root params as soon as we hit the first layout\n      // and it is not possible to use parallel route children above the root layout\n      // so every parallelRoutes object that this function can visit will necessarily\n      // have a single `children` prop and no others.\n      parallelRoutes.children,\n      getDynamicParamFromSegment\n    )\n  }\n}\n\nasync function createBoundaryConventionElement({\n  ctx,\n  conventionName,\n  Component,\n  styles,\n  tree,\n}: {\n  ctx: AppRenderContext\n  conventionName:\n    | 'not-found'\n    | 'error'\n    | 'loading'\n    | 'forbidden'\n    | 'unauthorized'\n  Component: React.ComponentType<any> | undefined\n  styles: React.ReactNode | undefined\n  tree: LoaderTree\n}) {\n  const isSegmentViewEnabled =\n    process.env.NODE_ENV === 'development' &&\n    ctx.renderOpts.devtoolSegmentExplorer\n  const dir =\n    (process.env.NEXT_RUNTIME === 'edge'\n      ? process.env.__NEXT_EDGE_PROJECT_DIR\n      : ctx.renderOpts.dir) || ''\n  const { SegmentViewNode } = ctx.componentMod\n  const element = Component ? (\n    <>\n      <Component />\n      {styles}\n    </>\n  ) : undefined\n\n  const pagePath = getConventionPathByType(tree, dir, conventionName)\n\n  const wrappedElement =\n    isSegmentViewEnabled && element ? (\n      <SegmentViewNode\n        key={cacheNodeKey + '-' + conventionName}\n        type={conventionName}\n        pagePath={pagePath!}\n      >\n        {element}\n      </SegmentViewNode>\n    ) : (\n      element\n    )\n\n  return [wrappedElement, pagePath] as const\n}\n"], "names": ["createComponentTree", "getRootParams", "props", "getTracer", "trace", "NextNodeServerSpan", "spanName", "createComponentTreeInternal", "errorMissingDefaultExport", "pagePath", "convention", "normalizedPagePath", "Error", "cacheNodeKey", "loaderTree", "tree", "parentParams", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "getViewportReady", "getMetadataReady", "ctx", "missingSlots", "preloadCallbacks", "authInterrupts", "StreamingMetadataOutlet", "isRoot", "renderOpts", "nextConfigOutput", "experimental", "workStore", "componentMod", "SegmentViewNode", "HTTPAccessFallbackBoundary", "LayoutRouter", "RenderFromTemplateContext", "OutletBoundary", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createPrerenderParamsForClientSegment", "serverHooks", "DynamicServerError", "Postpone", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "conventionPath", "segment", "modules", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "forbidden", "unauthorized", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "layoutOrPagePath", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "mod", "layoutOrPageMod", "modType", "getLayoutOrPageModule", "hideSpan", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "Forbidden", "forbiddenStyles", "Unauthorized", "unauthorizedStyles", "dynamic", "StaticGenBailoutError", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "isRoutePPREnabled", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "validateRevalidate", "route", "defaultRevalidate", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "isPossiblyPartialResponse", "LayoutOrPage", "interopDefault", "undefined", "MaybeComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "isSegmentViewEnabled", "devtoolSegmentExplorer", "dir", "NEXT_RUNTIME", "__NEXT_EDGE_PROJECT_DIR", "metadataOutlet", "MetadataOutlet", "ready", "notFoundElement", "notFoundFilePath", "createBoundaryConventionElement", "conventionName", "Component", "styles", "forbiddenElement", "unauthorizedElement", "parallelRouteMap", "Promise", "all", "Object", "keys", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "parallelRoute", "notFoundComponent", "forbiddenComponent", "unauthorizedComponent", "childCacheNodeSeedData", "hasLoadingComponentInTree", "parsedTree", "endsWith", "PARALLEL_ROUTE_DEFAULT_PATH", "add", "seedData", "resolve", "templateNode", "templateFilePath", "getConventionPathByType", "errorFilePath", "loadingFilePath", "globalErrorFilePath", "wrappedErrorStyles", "fileNameSuffix", "BOUNDARY_SUFFIX", "segmentViewBoundaries", "BOUNDARY_PREFIX", "isNextjsBuiltinFilePath", "BUILTIN_PREFIX", "parallel<PERSON><PERSON>er<PERSON>ey", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingElement", "loadingData", "children", "reason", "isClientComponent", "isClientReference", "console", "PageComponent", "pageElement", "promiseOfParams", "promiseOfSearchParams", "searchParams", "params", "promises", "isUseCacheFunction", "UseCachePageComponent", "$$isPageComponent", "isDefaultSegment", "DEFAULT_SEGMENT_KEY", "pageFilePath", "segmentType", "wrappedPageElement", "SegmentComponent", "isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot", "length", "segmentNode", "clientSegment", "slots", "promise", "notfoundClientSegment", "forbiddenClientSegment", "unauthorizedClientSegment", "createErrorBoundaryClientSegmentRoot", "ErrorBoundaryComponent", "errorElement", "serverSegment", "UseCacheLayoutComponent", "$$isLayoutComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wrappedSegmentNode", "r", "status", "displayName", "OUTLET_BOUNDARY_NAME", "notFoundParallelRouteProps", "getRootParamsImpl", "isRootLayout", "element", "wrappedElement"], "mappings": ";;;;;;;;;;;;;;;IAuCgBA,mBAAmB;eAAnBA;;IAggCAC,aAAa;eAAbA;;;;8DAtiCE;2CAIX;8BAC+B;gCAEP;iCACC;iDAEgB;gCACjB;2CACW;4BACP;yBACS;wBAClB;2BACS;yCACG;8CAGD;mCACA;yBAKD;qCAO7B;;;;;;AAKA,SAASD,oBAAoBE,KAcnC;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,6BAAkB,CAACL,mBAAmB,EACtC;QACEM,UAAU;IACZ,GACA,IAAMC,4BAA4BL,OAAO;AAE7C;AAEA,SAASM,0BACPC,QAAgB,EAChBC,UAAkB;IAElB,MAAMC,qBAAqBF,aAAa,MAAM,KAAKA;IACnD,MAAM,qBAEL,CAFK,IAAIG,MACR,CAAC,gDAAgD,EAAED,mBAAmB,CAAC,EAAED,WAAW,CAAC,CAAC,GADlF,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMG,eAAe;AAErB,eAAeN,4BACb,EACEO,YAAYC,IAAI,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,GAAG,EACHC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EAexB,EACDC,MAAe;IAEf,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,SAAS,EACTC,cAAc,EACZC,eAAe,EACfC,0BAA0B,EAC1BC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,qCAAqC,EACrCC,wCAAwC,EACxCC,kCAAkC,EAClCC,qCAAqC,EACrCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDtC,QAAQ,EACRuC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAG3B;IAEJ,MAAM,EAAE4B,IAAI,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAE,GAC9DC,IAAAA,gCAAe,EAACzC;IAElB,MAAM,EACJ0C,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACP,aAAaC,QAAQ,EACrBC,SAAS,EACTC,YAAY,EACb,GAAGT;IAEJ,MAAMU,+BAA+B,IAAIC,IAAI/C;IAC7C,MAAMgD,8BAA8B,IAAID,IAAI9C;IAC5C,MAAMgD,2CAA2C,IAAIF,IACnD7C;IAGF,MAAMgD,cAAcC,IAAAA,8BAAc,EAAC;QACjC5C;QACAF;QACA+C,kBAAkBlB;QAClBlC,aAAa8C;QACb7C,YAAY+C;QACZ9C,yBAAyB+C;IAC3B;IAEA,MAAM,CAACI,UAAUC,gBAAgBC,gBAAgB,GAAGf,WAChD,MAAMgB,IAAAA,gEAA+B,EAAC;QACpCnD;QACAoD,UAAUjB,QAAQ,CAAC,EAAE;QACrBkB,cAAclB,QAAQ,CAAC,EAAE;QACzBxC,aAAa8C;QACb7C,YAAY+C;IACd,KACA;QAACW,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGtB,QAChD,MAAMe,IAAAA,gEAA+B,EAAC;QACpCnD;QACAoD,UAAUhB,KAAK,CAAC,EAAE;QAClBiB,cAAcjB,KAAK,CAAC,EAAE;QACtBzC,aAAa8C;QACb7C,YAAY+C;IACd,KACA,EAAE;IAEN,MAAM,CAACgB,SAASC,eAAeC,eAAe,GAAGxB,UAC7C,MAAMc,IAAAA,gEAA+B,EAAC;QACpCnD;QACAoD,UAAUf,OAAO,CAAC,EAAE;QACpBgB,cAAchB,OAAO,CAAC,EAAE;QACxB1C,aAAa8C;QACb7C,YAAY+C;IACd,KACA,EAAE;IAEN,MAAMmB,WAAW,OAAO5B,WAAW;IACnC,MAAM6B,SAAS,OAAOnC,SAAS;IAC/B,MAAM,EAAEoC,KAAKC,eAAe,EAAEC,OAAO,EAAE,GAAG,MAAMtF,IAAAA,iBAAS,IAAGC,KAAK,CAC/DC,6BAAkB,CAACqF,qBAAqB,EACxC;QACEC,UAAU,CAAEN,CAAAA,YAAYC,MAAK;QAC7BhF,UAAU;QACVsF,YAAY;YACV,gBAAgBvC;QAClB;IACF,GACA,IAAMqC,IAAAA,mCAAqB,EAAC3E;IAG9B;;GAEC,GACD,MAAM8E,wBAAwBR,YAAY,CAACpE;IAC3C;;GAEC,GACD,MAAM6E,uCACJ7E,sBAAsB4E;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGnC,WAC/B,MAAMa,IAAAA,gEAA+B,EAAC;QACpCnD;QACAoD,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzB3C,aAAa8C;QACb7C,YAAY+C;IACd,KACA,EAAE;IAEN,MAAM,CAAC+B,WAAWC,gBAAgB,GAChCxE,kBAAkBoC,YACd,MAAMY,IAAAA,gEAA+B,EAAC;QACpCnD;QACAoD,UAAUb,SAAS,CAAC,EAAE;QACtBc,cAAcd,SAAS,CAAC,EAAE;QAC1B5C,aAAa8C;QACb7C,YAAY+C;IACd,KACA,EAAE;IAER,MAAM,CAACiC,cAAcC,mBAAmB,GACtC1E,kBAAkBqC,eACd,MAAMW,IAAAA,gEAA+B,EAAC;QACpCnD;QACAoD,UAAUZ,YAAY,CAAC,EAAE;QACzBa,cAAcb,YAAY,CAAC,EAAE;QAC7B7C,aAAa8C;QACb7C,YAAY+C;IACd,KACA,EAAE;IAER,IAAImC,UAAUb,mCAAAA,gBAAiBa,OAAO;IAEtC,IAAIvE,qBAAqB,UAAU;QACjC,IAAI,CAACuE,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,gTAAgT,CAAC,GAD9S,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI,OAAOD,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBrE,UAAUuE,kBAAkB,GAAG;QACjC,OAAO,IAAIF,YAAY,iBAAiB;YACtCrE,UAAUwE,YAAY,GAAG;YAEzB,0DAA0D;YAC1D,IAAIxE,UAAUyE,kBAAkB,IAAI,CAAC1E,aAAa2E,iBAAiB,EAAE;gBACnE,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,qBAEX,CAFW,IAAI7D,mBACd,CAAC,qEAAqE,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAd,UAAU4E,uBAAuB,GAAGD,IAAIE,OAAO;gBAC/C7E,UAAU8E,iBAAiB,GAAGH,IAAII,KAAK;gBACvC,MAAMJ;YACR;QACF,OAAO;YACL3E,UAAUuE,kBAAkB,GAAG;YAC/BvE,UAAUgF,WAAW,GAAGX,YAAY;QACtC;IACF;IAEA,IAAI,QAAOb,mCAAAA,gBAAiByB,UAAU,MAAK,UAAU;QACnDjF,UAAUiF,UAAU,GAAGzB,mCAAAA,gBAAiByB,UAAU;IACpD;IAEA,IAAI,QAAOzB,mCAAAA,gBAAiB0B,UAAU,MAAK,aAAa;QACtDC,IAAAA,8BAAkB,EAAC3B,mCAAAA,gBAAiB0B,UAAU,EAAElF,UAAUoF,KAAK;IACjE;IAEA,IAAI,QAAO5B,mCAAAA,gBAAiB0B,UAAU,MAAK,UAAU;QACnD,MAAMG,oBAAoB7B,gBAAgB0B,UAAU;QAEpD,MAAMI,gBAAgBC,kDAAoB,CAACC,QAAQ;QAEnD,IAAIF,eAAe;YACjB,OAAQA,cAAcG,IAAI;gBACxB,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,IAAIH,cAAcJ,UAAU,GAAGG,mBAAmB;wBAChDC,cAAcJ,UAAU,GAAGG;oBAC7B;oBACA;gBACF,KAAK;oBAEH;gBACF,sDAAsD;gBACtD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF;oBACEC;YACJ;QACF;QAEA,IACE,CAACtF,UAAUgF,WAAW,IACtBhF,UAAUyE,kBAAkB,IAC5BY,sBAAsB,KACtB,wEAAwE;QACxE,0CAA0C;QAC1C,CAACtF,aAAa2E,iBAAiB,EAC/B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAEvD,SAAS;YACrErB,UAAU4E,uBAAuB,GAAGA;YAEpC,MAAM,qBAA+C,CAA/C,IAAI9D,mBAAmB8D,0BAAvB,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;IACF;IAEA,MAAMH,qBAAqBzE,UAAUyE,kBAAkB;IAEvD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0EAA0E;IAC1E,uDAAuD;IACvD,EAAE;IACF,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMiB,4BACJjB,sBAAsB1E,aAAa2E,iBAAiB,KAAK;IAE3D,MAAMiB,eAAqDnC,kBACvDoC,IAAAA,8BAAc,EAACpC,mBACfqC;IAEJ;;GAEC,GACD,IAAIC,iBAAiBH;IAErB,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAC1BC,QAAQ;QACV,IACE,OAAOL,mBAAmB,eAC1B,CAACI,mBAAmBJ,iBACpB;YACAtH,0BAA0BC,UAAUgF,WAAW;QACjD;QAEA,IACE,OAAOV,mBAAmB,eAC1B,CAACmD,mBAAmBnD,iBACpB;YACAvE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOyE,YAAY,eAAe,CAACgD,mBAAmBhD,UAAU;YAClE1E,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOsF,aAAa,eAAe,CAACmC,mBAAmBnC,WAAW;YACpEvF,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOwF,cAAc,eAAe,CAACiC,mBAAmBjC,YAAY;YACtEzF,0BAA0BC,UAAU;QACtC;QAEA,IACE,OAAO0F,iBAAiB,eACxB,CAAC+B,mBAAmB/B,eACpB;YACA3F,0BAA0BC,UAAU;QACtC;IACF;IAEA,iCAAiC;IACjC,MAAM2H,eAAepF,2BAA2BK;IAEhD,6DAA6D;IAC7D,IAAIgF,gBAAwBrH;IAC5B,IAAIoH,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGrH,YAAY;YACf,CAACoH,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,4BAA4B;IAC5B,MAAME,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGpF;IAChE,MAAMqF,uBACJX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB1G,IAAIM,UAAU,CAAC8G,sBAAsB;IACvC,MAAMC,MACJ,AAACb,CAAAA,QAAQC,GAAG,CAACa,YAAY,KAAK,SAC1Bd,QAAQC,GAAG,CAACc,uBAAuB,GACnCvH,IAAIM,UAAU,CAAC+G,GAAG,AAAD,KAAM;IAE7B,8DAA8D;IAC9D,MAAMG,iBAAiBpH,wCACrB,qBAACA,6CAED,qBAACqH;QAAeC,OAAO3H;;IAGzB,MAAM,CAAC4H,iBAAiBC,iBAAiB,GACvC,MAAMC,gCAAgC;QACpC7H;QACA8H,gBAAgB;QAChBC,WAAWvD;QACXwD,QAAQvD;QACRjF;IACF;IAEF,MAAM,CAACyI,iBAAiB,GAAG,MAAMJ,gCAAgC;QAC/D7H;QACA8H,gBAAgB;QAChBC,WAAWrD;QACXsD,QAAQrD;QACRnF;IACF;IAEA,MAAM,CAAC0I,oBAAoB,GAAG,MAAML,gCAAgC;QAClE7H;QACA8H,gBAAgB;QAChBC,WAAWnD;QACXoD,QAAQnD;QACRrF;IACF;IAEA,8EAA8E;IAC9E,kBAAkB;IAClB,MAAM2I,mBAAmB,MAAMC,QAAQC,GAAG,CACxCC,OAAOC,IAAI,CAACvG,gBAAgBwG,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,gBAAgB3G,cAAc,CAACyG,iBAAiB;QAEtD,MAAMG,oBAAoBF,qBACtBf,kBACArB;QAEJ,MAAMuC,qBAAqBH,qBACvBT,mBACA3B;QAEJ,MAAMwC,wBAAwBJ,qBAC1BR,sBACA5B;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIyC,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3BrH,cACCiC,CAAAA,WAAW,CAACqF,IAAAA,oDAAyB,EAACL,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAACnI,aAAa2E,iBAAiB,EAC/B;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIqB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBzG,cAAc;oBAKxDgJ;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAahH,IAAAA,gCAAe,EAAC0G;gBACnC,KACEM,6BAAAA,WAAWpH,cAAc,qBAAzBoH,2BAA2BC,QAAQ,CAACC,oCAA2B,GAC/D;oBACAlJ,aAAamJ,GAAG,CAACX;gBACnB;YACF;YAEA,MAAMY,WAAW,MAAMrK,4BACrB;gBACEO,YAAYoJ;gBACZlJ,cAAcqH;gBACdpH,oBAAoB6E;gBACpB5E,aAAa8C;gBACb7C,YAAY+C;gBACZ9C,yBAAyB+C;gBACzB,4IAA4I;gBAC5I,8CAA8C;gBAC9C7C,kBAAkB2I,qBACd3I,mBACA,IAAMqI,QAAQkB,OAAO;gBACzBxJ,kBAAkB4I,qBACd5I,mBACA,IAAMsI,QAAQkB,OAAO;gBACzBtJ;gBACAC;gBACAC;gBACAC;gBACA,2HAA2H;gBAC3H,8CAA8C;gBAC9CC,yBAAyBsI,qBACrBtI,0BACA;YACN,GACA;YAGF2I,yBAAyBM;QAC3B;QAEA,MAAME,6BACJ,qBAACvG;sBACC,cAAA,qBAAClC;;QAIL,MAAM0I,mBAAmBC,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK;QAC5D,MAAMqC,gBAAgBD,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK;QACzD,MAAMsC,kBAAkBF,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK;QAC3D,MAAMuC,sBAAsBvJ,SACxBoJ,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK,kBACnCf;QAEJ,MAAMuD,qBACJ1C,wBAAwBuC,8BACtB,qBAAC/I;YAAgBuF,MAAK;YAAQhH,UAAUwK;sBACrCjG;aAGHA;QAGJ,wFAAwF;QACxF,oCAAoC;QACpC,0BAA0B;QAC1B,MAAMqG,iBAAiBC,oCAAe;QACtC,MAAMC,wBAAwB7C,qCAC5B;;gBACGS,kCACC,qBAACjH;oBACCuF,MAAM,GAAG+D,oCAAe,CAAC,SAAS,CAAC;oBACnC/K,UAAU0I,mBAAmBkC;;gBAGhCH,iCACC,qBAAChJ;oBACCuF,MAAM,GAAG+D,oCAAe,CAAC,OAAO,CAAC;oBACjC/K,UAAUyK,kBAAkBG;;gBAG/BJ,+BACC,qBAAC/I;oBACCuF,MAAM,GAAG+D,oCAAe,CAAC,KAAK,CAAC;oBAC/B/K,UAAUwK,gBAAgBI;;gBAI7BF,qCACC,qBAACjJ;oBACCuF,MAAM,GAAG+D,oCAAe,CAAC,YAAY,CAAC;oBACtC/K,UACEgL,IAAAA,4CAAuB,EAACN,uBACpB,GAAGO,mCAAc,CAAC,eAAe,EAAEL,gBAAgB,GACnDF;;;aAMV;QAEJ,OAAO;YACLnB;0BACA,qBAAC5H;gBACCuJ,mBAAmB3B;gBACnB,sKAAsK;gBACtKrG,OAAOoB;gBACPC,aAAaoG;gBACbnG,cAAcA;gBACdvB,UACE,8DAA8D;gBAC9DgF,wBAAwBqC,iCACtB,qBAAC7I;oBAAgBuF,MAAK;oBAAWhH,UAAUsK;8BACxCD;qBAGHA;gBAGJtG,gBAAgBA;gBAChBC,iBAAiBA;gBACjBZ,UAAUsG;gBACVrG,WAAWsG;gBACXrG,cAAcsG;gBACb,GAAI3B,wBAAwB;oBAAE6C;gBAAsB,CAAC;;YAExDjB;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIsB,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAM3B,iBAAiBR,iBAAkB;QAC5C,MAAM,CAACM,kBAAkB8B,mBAAmBC,WAAW,GAAG7B;QAC1D0B,kBAAkB,CAAC5B,iBAAiB,GAAG8B;QACvCD,8BAA8B,CAAC7B,iBAAiB,GAAG+B;IACrD;IAEA,IAAIC,iBAAiB9G,wBAAU,qBAACA,aAAY,OAAS;IACrD,MAAMgG,kBAAkBF,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK;IAC3D,IAAIF,wBAAwBsD,gBAAgB;QAC1C,IAAId,iBAAiB;YACnBc,+BACE,qBAAC9J;gBAECuF,MAAK;gBACLhH,UAAUyK;0BAETc;eAJInL,eAAe;QAO1B;IACF;IAEA,MAAMoL,cAAiCD,iBACnC;QAACA;QAAgB7G;QAAeC;KAAe,GAC/C;IAEJ,wIAAwI;IACxI,IAAI,CAAC0C,gBAAgB;QACnB,OAAO;YACLU;0BACA,sBAAC3D,cAAK,CAACC,QAAQ;;oBACZV;oBACAwH,mBAAmBM,QAAQ;;eAFTrL;YAIrBgL;YACAI;YACAvE;SACD;IACH;IAEA,MAAM4B,YAAYxB;IAClB,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACE9F,UAAUyE,kBAAkB,IAC5BzE,UAAUwE,YAAY,IACtBzE,aAAa2E,iBAAiB,EAC9B;QACA,OAAO;YACL8B;0BACA,sBAAC3D,cAAK,CAACC,QAAQ;;kCACb,qBAAC/B;wBACCoJ,QAAO;wBACP/E,OAAOpF,UAAUoF,KAAK;;oBAEvBhD;;eALkBvD;YAOrBgL;YACAI;YACA;SACD;IACH;IAEA,MAAMG,oBAAoBC,IAAAA,4CAAiB,EAAC7G;IAE5C,IACEuC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAY2D,oBACZ;QACA,6EAA6E;QAC7EU,QAAQ3I,KAAK,CACX,CAAC,uGAAuG,EAAEN,SAAS;IAEvH;IAEA,IAAIiC,QAAQ;QACV,MAAMiH,gBAAgBjD;QAEtB,iDAAiD;QACjD,IAAIkD;QACJ,IAAIJ,mBAAmB;YACrB,IAAI3F,oBAAoB;gBACtB,MAAMgG,kBACJ7J,sCAAsCyF;gBACxC,MAAMqE,wBACJhK,yCAAyCV;gBAC3CwK,4BACE,qBAACjK;oBACC+G,WAAWiD;oBACXI,cAAczJ;oBACd0J,QAAQvE;oBACRwE,UAAU;wBAACH;wBAAuBD;qBAAgB;;YAGxD,OAAO;gBACLD,4BACE,qBAACjK;oBACC+G,WAAWiD;oBACXI,cAAczJ;oBACd0J,QAAQvE;;YAGd;QACF,OAAO;YACL,uEAAuE;YACvE,wEAAwE;YACxE,MAAMuE,SAASjK,mCACb0F,eACArG;YAGF,uEAAuE;YACvE,uEAAuE;YACvE,SAAS;YACT,IAAI2K,eAAelK,sCAAsCS,OAAOlB;YAEhE,IAAI8K,IAAAA,6CAAkB,EAACP,gBAAgB;gBACrC,MAAMQ,wBACJR;gBAEFC,4BACE,qBAACO;oBACCH,QAAQA;oBACRD,cAAcA;oBACdK,iBAAiB;;YAGvB,OAAO;gBACLR,4BACE,qBAACD;oBAAcK,QAAQA;oBAAQD,cAAcA;;YAEjD;QACF;QAEA,MAAMM,mBAAmB5J,YAAY6J,4BAAmB;QACxD,MAAMC,eACJnC,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK,WACnCoC,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK;QACrC,MAAMwE,cAAcH,mBAAmB,YAAY;QACnD,MAAMI,qBACJ3E,wBAAwByE,6BACtB,qBAACjL;YAECuF,MAAM2F;YACN3M,UAAU0M;sBAETX;WAJI3L,eAAe,MAAMuM,eAO5BZ;QAGJ,OAAO;YACLhE;0BACA,sBAAC3D,cAAK,CAACC,QAAQ;;oBACZuI;oBACAjJ;kCACD,sBAAC9B;;0CACC,qBAAC0G;gCAAeC,OAAO5H;;4BACtB0H;;;;eALgBlI;YAQrBgL;YACAI;YACAvE;SACD;IACH,OAAO;QACL,MAAM4F,mBAAmBhE;QACzB,MAAMiE,oDACJ1H,yBACA,cAActC,kBACdsG,OAAOC,IAAI,CAACvG,gBAAgBiK,MAAM,GAAG;QAEvC,IAAIC;QAEJ,IAAIrB,mBAAmB;YACrB,IAAIsB;YAEJ,IAAIjH,oBAAoB;gBACtB,MAAMgG,kBACJ7J,sCAAsCyF;gBAExCqF,8BACE,qBAAClL;oBACC8G,WAAWgE;oBACXK,OAAO/B;oBACPgB,QAAQvE;oBACRuF,SAASnB;;YAGf,OAAO;gBACLiB,8BACE,qBAAClL;oBACC8G,WAAWgE;oBACXK,OAAO/B;oBACPgB,QAAQvE;;YAGd;YAEA,IAAIkF,mDAAmD;gBACrD,IAAIM;gBACJ,IAAIC;gBACJ,IAAIC;gBACJ,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCF,wBAAwBG,qCAAqC;oBAC3DC,wBAAwBlI;oBACxBmI,cAAchF;oBACd1G;oBACA4B;oBACAkJ;oBACAjF;gBACF;gBACAyF,yBAAyBE,qCAAqC;oBAC5DC,wBAAwBhI;oBACxBiI,cAAc1E;oBACdhH;oBACA4B;oBACAkJ;oBACAjF;gBACF;gBACA0F,4BAA4BC,qCAAqC;oBAC/DC,wBAAwB9H;oBACxB+H,cAAczE;oBACdjH;oBACA4B;oBACAkJ;oBACAjF;gBACF;gBACA,IACEwF,yBACAC,0BACAC,2BACA;oBACAN,4BACE,sBAACtL;wBAEC0B,UAAUgK;wBACV/J,WAAWgK;wBACX/J,cAAcgK;;4BAEb3J;4BACAsJ;;uBANI7M;gBASX,OAAO;oBACL4M,4BACE,sBAAC5I,cAAK,CAACC,QAAQ;;4BACZV;4BACAsJ;;uBAFkB7M;gBAKzB;YACF,OAAO;gBACL4M,4BACE,sBAAC5I,cAAK,CAACC,QAAQ;;wBACZV;wBACAsJ;;mBAFkB7M;YAKzB;QACF,OAAO;YACL,MAAM+L,SAASjK,mCACb0F,eACArG;YAGF,IAAImM;YAEJ,IAAIrB,IAAAA,6CAAkB,EAACQ,mBAAmB;gBACxC,MAAMc,0BACJd;gBAEFa,8BACE,qBAACC;oBACE,GAAGxC,kBAAkB;oBACtBgB,QAAQA;oBACRyB,mBAAmB;;YAGzB,OAAO;gBACLF,8BACE,qBAACb;oBAAkB,GAAG1B,kBAAkB;oBAAEgB,QAAQA;;YAEtD;YAEA,IAAIW,mDAAmD;gBACrD,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCE,4BACE,sBAACtL;oBAEC0B,UACEqF,gCACE;;4BACG9E;0CACD,sBAACkJ;gCAAiBV,QAAQA;;oCACvB5G;oCACAkD;;;;yBAGHrB;;wBAGLzD;wBACA+J;;mBAdItN;YAiBX,OAAO;gBACL4M,4BACE,sBAAC5I,cAAK,CAACC,QAAQ;;wBACZV;wBACA+J;;mBAFkBtN;YAKzB;QACF;QAEA,MAAMyN,iBAAiBtD,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAK;QAC1D,MAAM2F,qBACJ7F,wBAAwB4F,+BACtB,qBAACpM;YAA6BuF,MAAK;YAAShH,UAAU6N;sBACnDb;WADkB,YAIrBA;QAGJ,2CAA2C;QAC3C,OAAO;YACLjF;YACA+F;YACA1C;YACAI;YACAvE;SACD;IACH;AACF;AAEA,eAAesB,eAAe,EAC5BC,KAAK,EAGN;IACC,MAAMuF,IAAIvF;IACV,+FAA+F;IAC/F,IAAIuF,EAAEC,MAAM,KAAK,YAAY;QAC3B,MAAMD,EAAElG,KAAK;IACf,OAAO,IAAIkG,EAAEC,MAAM,KAAK,aAAa;QACnC,MAAMD;IACR;IACA,OAAO;AACT;AACAxF,eAAe0F,WAAW,GAAGC,uCAAoB;AAEjD,SAASX,qCAAqC,EAC5CC,sBAAsB,EACtBC,YAAY,EACZ1L,iBAAiB,EACjB4B,WAAW,EACXkJ,gBAAgB,EAChBjF,aAAa,EAQd;IACC,IAAI4F,wBAAwB;QAC1B,MAAMW,6BAA6B;YACjC1C,UAAUgC;QACZ;QACA,qBACE;;gBACG9J;8BACD,qBAAC5B;oBACC8G,WAAWgE;oBACXK,OAAOiB;oBACPhC,QAAQvE;;;;IAIhB;IACA,OAAO;AACT;AAEO,SAASpI,cACda,UAAsB,EACtBkC,0BAAsD;IAEtD,OAAO6L,kBAAkB,CAAC,GAAG/N,YAAYkC;AAC3C;AAEA,SAAS6L,kBACP7N,YAAoB,EACpBF,UAAsB,EACtBkC,0BAAsD;IAEtD,MAAM,EACJK,OAAO,EACPC,SAAS,EAAEG,MAAM,EAAE,EACnBF,cAAc,EACf,GAAGC,IAAAA,gCAAe,EAAC1C;IAEpB,MAAMsH,eAAepF,2BAA2BK;IAEhD,IAAIgF,gBAAwBrH;IAC5B,IAAIoH,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGrH,YAAY;YACf,CAACoH,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAMwG,eAAe,OAAOrL,WAAW;IAEvC,IAAIqL,cAAc;QAChB,OAAOzG;IACT,OAAO,IAAI,CAAC9E,eAAe2I,QAAQ,EAAE;QACnC,4EAA4E;QAC5E,yEAAyE;QACzE,yEAAyE;QACzE,6BAA6B;QAC7B,wFAAwF;QACxF,OAAO7D;IACT,OAAO;QACL,OAAOwG,kBACLxG,eACA,qEAAqE;QACrE,8EAA8E;QAC9E,+EAA+E;QAC/E,+CAA+C;QAC/C9E,eAAe2I,QAAQ,EACvBlJ;IAEJ;AACF;AAEA,eAAeoG,gCAAgC,EAC7C7H,GAAG,EACH8H,cAAc,EACdC,SAAS,EACTC,MAAM,EACNxI,IAAI,EAYL;IACC,MAAM2H,uBACJX,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB1G,IAAIM,UAAU,CAAC8G,sBAAsB;IACvC,MAAMC,MACJ,AAACb,CAAAA,QAAQC,GAAG,CAACa,YAAY,KAAK,SAC1Bd,QAAQC,GAAG,CAACc,uBAAuB,GACnCvH,IAAIM,UAAU,CAAC+G,GAAG,AAAD,KAAM;IAC7B,MAAM,EAAE1G,eAAe,EAAE,GAAGX,IAAIU,YAAY;IAC5C,MAAM8M,UAAUzF,0BACd;;0BACE,qBAACA;YACAC;;SAED1B;IAEJ,MAAMpH,WAAWuK,IAAAA,4CAAuB,EAACjK,MAAM6H,KAAKS;IAEpD,MAAM2F,iBACJtG,wBAAwBqG,wBACtB,qBAAC7M;QAECuF,MAAM4B;QACN5I,UAAUA;kBAETsO;OAJIlO,eAAe,MAAMwI,kBAO5B0F;IAGJ,OAAO;QAACC;QAAgBvO;KAAS;AACnC", "ignoreList": [0]}