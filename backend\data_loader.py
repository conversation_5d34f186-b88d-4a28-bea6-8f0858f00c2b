import os, glob, json, logging
from pathlib import Path
from typing import Dict, List, Tuple
import pandas as pd
from docx import Document

# Optional libraries
try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False

try:
    import pptx
    HAS_PPTX = True
except ImportError:
    HAS_PPTX = False

try:
    import openpyxl
    import xlrd
    HAS_EXCEL_ENGINES = True
except ImportError:
    HAS_EXCEL_ENGINES = False

try:
    from PIL import Image
    import pytesseract
    from pdf2image import convert_from_path
    HAS_OCR = True
except ImportError:
    HAS_OCR = False

try:
    from charset_normalizer import from_path
    HAS_CHARSET_NORMALIZER = True
except ImportError:
    HAS_CHARSET_NORMALIZER = False

logger = logging.getLogger(__name__)

# --- TXT/MD ---
def safe_read_text(path: str) -> str:
    try:
        if HAS_CHARSET_NORMALIZER:
            encoding = from_path(path).best().encoding or "utf-8"
        else:
            encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
            for enc in encodings:
                try:
                    with open(path, 'r', encoding=enc) as f:
                        content = f.read()
                    return content
                except UnicodeDecodeError:
                    continue
            encoding = 'utf-8'
        with open(path, 'r', encoding=encoding) as f:
            return f.read()
    except Exception as e:
        logger.warning(f"Text read failed for {path}: {e}")
        return ""

# --- DOCX ---
def read_docx_enhanced(path: str) -> str:
    try:
        doc = Document(path)
        parts = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
        return "\n".join(parts)
    except Exception as e:
        logger.warning(f"DOCX load failed for {path}: {e}")
        return ""

# --- XLSX ---
def read_excel_robust(path: str) -> str:
    if not HAS_EXCEL_ENGINES:
        logger.warning("Excel engines not available, skipping Excel files")
        return ""
    try:
        df = pd.read_excel(path)
        return df.to_string(index=False)
    except Exception as e:
        logger.warning(f"Excel read failed for {path}: {e}")
        return ""

# --- CSV ---
def read_csv(path: str) -> str:
    try:
        df = pd.read_csv(path, encoding='utf-8', dtype=str, na_filter=False)
        return df.to_string(index=False)
    except Exception as e:
        logger.warning(f"CSV read failed for {path}: {e}")
        return ""

# --- JSON ---
def read_json(path: str) -> str:
    try:
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return json.dumps(data, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.warning(f"JSON read failed for {path}: {e}")
        return ""

# --- JSONL ---
def load_jsonl_enhanced(path: str) -> List[Dict]:
    docs = []
    try:
        with open(path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    data = json.loads(line)
                    text = data.get("text") or data.get("content") or data.get("body")
                    if text:
                        docs.append({"text": str(text).strip(), "meta": {"source": path, "line_number": line_num, "original_data": data}})
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON at line {line_num} in {path}: {e}")
                    continue
    except Exception as e:
        logger.error(f"JSONL load failed for {path}: {e}")
    return docs

# --- Image OCR ---
def ocr_image_enhanced(image_path: str) -> str:
    if not HAS_OCR:
        logger.warning("OCR libraries not available")
        return ""
    try:
        img = Image.open(image_path)
        img = img.convert('RGB')
        text = pytesseract.image_to_string(img)
        return text.strip()
    except Exception as e:
        logger.warning(f"OCR failed for image {image_path}: {e}")
        return ""

# --- PDF OCR ---
def ocr_pdf_enhanced(pdf_path: str, dpi: int = 300) -> str:
    if not HAS_OCR:
        return ""
    try:
        pages = convert_from_path(pdf_path, dpi=dpi)
        texts = [pytesseract.image_to_string(img) for img in pages]
        return "\n\n--- PAGE BREAK ---\n\n".join(texts)
    except Exception as e:
        logger.error(f"pdf2image render failed for {pdf_path}: {e}")
        return ""
