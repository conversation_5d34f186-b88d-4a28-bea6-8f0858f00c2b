'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const createContext = require('../../utils/create-context.cjs');

const [TreeViewNodeStateProvider, useTreeViewNodeContext] = createContext.createContext({
  name: "TreeViewNodeContext",
  hookName: "useTreeViewNodeContext",
  providerName: "<TreeViewNodeProvider />"
});

exports.TreeViewNodeStateProvider = TreeViewNodeStateProvider;
exports.useTreeViewNodeContext = useTreeViewNodeContext;
