import numpy as np
from sentence_transformers import SentenceTransformer

def get_model(model_name):
    return SentenceTransformer(model_name)

def encode_chunks(model, chunks, batch_size=32):
    all_embeddings = []
    for i in range(0, len(chunks), batch_size):
        batch_texts = [c["text"] for c in chunks[i:i+batch_size]]
        batch_embeddings = model.encode(
            batch_texts,
            normalize_embeddings=True,
            show_progress_bar=True if i == 0 else False
        )
        all_embeddings.extend(batch_embeddings)
    return np.array(all_embeddings, dtype="float32")
