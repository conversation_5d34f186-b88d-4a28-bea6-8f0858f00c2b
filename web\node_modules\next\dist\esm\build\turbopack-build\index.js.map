{"version": 3, "sources": ["../../../src/build/turbopack-build/index.ts"], "sourcesContent": ["import path from 'path'\n\nimport { Worker } from '../../lib/worker'\nimport { NextBuildContext } from '../build-context'\n\nasync function turbopackBuildWithWorker() {\n  try {\n    const worker = new Worker(path.join(__dirname, 'impl.js'), {\n      exposedMethods: ['workerMain', 'waitForShutdown'],\n      debuggerPortOffset: -1,\n      isolatedMemory: false,\n      numWorkers: 1,\n      maxRetries: 0,\n      forkOptions: {\n        env: {\n          NEXT_PRIVATE_BUILD_WORKER: '1',\n        },\n      },\n    }) as Worker & typeof import('./impl')\n    const { nextBuildSpan, ...prunedBuildContext } = NextBuildContext\n    const result = await worker.workerMain({\n      buildContext: prunedBuildContext,\n    })\n\n    // destroy worker when Turbo<PERSON> has shutdown so it's not sticking around using memory\n    // We need to wait for shutdown to make sure persistent cache is flushed\n    result.shutdownPromise = worker.waitForShutdown().then(() => {\n      worker.end()\n    })\n\n    return result\n  } catch (err: any) {\n    // When the error is a serialized `Error` object we need to recreate the `Error` instance\n    // in order to keep the consistent error reporting behavior.\n    if (err.type === 'Error') {\n      const error = new Error(err.message)\n      if (err.name) {\n        error.name = err.name\n      }\n      if (err.cause) {\n        error.cause = err.cause\n      }\n      error.message = err.message\n      error.stack = err.stack\n      throw error\n    }\n    throw err\n  }\n}\n\nexport function turbopackBuild(\n  withWorker: boolean\n): ReturnType<typeof import('./impl').turbopackBuild> {\n  const nextBuildSpan = NextBuildContext.nextBuildSpan!\n  return nextBuildSpan\n    .traceChild('run-turbopack-compiler')\n    .traceAsyncFn(async () => {\n      if (withWorker) {\n        return await turbopackBuildWithWorker()\n      } else {\n        const build = (require('./impl') as typeof import('./impl'))\n          .turbopackBuild\n        return await build()\n      }\n    })\n}\n"], "names": ["path", "Worker", "NextBuildContext", "turbopackBuildWithWorker", "worker", "join", "__dirname", "exposedMethods", "debuggerPortOffset", "isolated<PERSON><PERSON><PERSON>", "numWorkers", "maxRetries", "forkOptions", "env", "NEXT_PRIVATE_BUILD_WORKER", "nextBuildSpan", "prunedBuildContext", "result", "worker<PERSON>ain", "buildContext", "shutdownPromise", "waitForShutdown", "then", "end", "err", "type", "error", "Error", "message", "name", "cause", "stack", "turbopackBuild", "with<PERSON><PERSON>ker", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "build", "require"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAEvB,SAASC,MAAM,QAAQ,mBAAkB;AACzC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,eAAeC;IACb,IAAI;QACF,MAAMC,SAAS,IAAIH,OAAOD,KAAKK,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;gBAAc;aAAkB;YACjDC,oBAAoB,CAAC;YACrBC,gBAAgB;YAChBC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACHC,2BAA2B;gBAC7B;YACF;QACF;QACA,MAAM,EAAEC,aAAa,EAAE,GAAGC,oBAAoB,GAAGd;QACjD,MAAMe,SAAS,MAAMb,OAAOc,UAAU,CAAC;YACrCC,cAAcH;QAChB;QAEA,sFAAsF;QACtF,wEAAwE;QACxEC,OAAOG,eAAe,GAAGhB,OAAOiB,eAAe,GAAGC,IAAI,CAAC;YACrDlB,OAAOmB,GAAG;QACZ;QAEA,OAAON;IACT,EAAE,OAAOO,KAAU;QACjB,yFAAyF;QACzF,4DAA4D;QAC5D,IAAIA,IAAIC,IAAI,KAAK,SAAS;YACxB,MAAMC,QAAQ,qBAAsB,CAAtB,IAAIC,MAAMH,IAAII,OAAO,GAArB,qBAAA;uBAAA;4BAAA;8BAAA;YAAqB;YACnC,IAAIJ,IAAIK,IAAI,EAAE;gBACZH,MAAMG,IAAI,GAAGL,IAAIK,IAAI;YACvB;YACA,IAAIL,IAAIM,KAAK,EAAE;gBACbJ,MAAMI,KAAK,GAAGN,IAAIM,KAAK;YACzB;YACAJ,MAAME,OAAO,GAAGJ,IAAII,OAAO;YAC3BF,MAAMK,KAAK,GAAGP,IAAIO,KAAK;YACvB,MAAML;QACR;QACA,MAAMF;IACR;AACF;AAEA,OAAO,SAASQ,eACdC,UAAmB;IAEnB,MAAMlB,gBAAgBb,iBAAiBa,aAAa;IACpD,OAAOA,cACJmB,UAAU,CAAC,0BACXC,YAAY,CAAC;QACZ,IAAIF,YAAY;YACd,OAAO,MAAM9B;QACf,OAAO;YACL,MAAMiC,QAAQ,AAACC,QAAQ,UACpBL,cAAc;YACjB,OAAO,MAAMI;QACf;IACF;AACJ", "ignoreList": [0]}