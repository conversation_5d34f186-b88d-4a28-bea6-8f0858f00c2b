{"version": 3, "sources": ["../../../src/build/swc/types.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../server/config-shared'\nimport type { __ApiPreviewProps } from '../../server/api-utils'\nimport type {\n  ExternalObject,\n  RefCell,\n  NapiTurboEngineOptions,\n  NapiSourceDiagnostic,\n} from './generated-native'\n\nexport type { NapiTurboEngineOptions as TurboEngineOptions }\n\nexport interface Binding {\n  isWasm: boolean\n  turbo: {\n    createProject(\n      options: ProjectOptions,\n      turboEngineOptions?: NapiTurboEngineOptions\n    ): Promise<Project>\n    startTurbopackTraceServer(traceFilePath: string): void\n\n    nextBuild?: any\n  }\n  mdx: {\n    compile(src: string, options: any): any\n    compileSync(src: string, options: any): any\n  }\n  minify(src: string, options: any): Promise<any>\n  minifySync(src: string, options: any): any\n  transform(src: string, options: any): Promise<any>\n  transformSync(src: string, options: any): any\n  parse(src: string, options: any): Promise<string>\n\n  getTargetTriple(): string | undefined\n\n  initCustomTraceSubscriber?(traceOutFilePath?: string): ExternalObject<RefCell>\n  teardownTraceSubscriber?(guardExternal: ExternalObject<RefCell>): void\n  css: {\n    lightning: {\n      transform(transformOptions: any): Promise<any>\n      transformStyleAttr(transformAttrOptions: any): Promise<any>\n    }\n  }\n\n  reactCompiler: {\n    isReactCompilerRequired(filename: string): Promise<boolean>\n  }\n\n  rspack: {\n    getModuleNamedExports(resourcePath: string): Promise<string[]>\n    warnForEdgeRuntime(\n      source: string,\n      isProduction: boolean\n    ): Promise<NapiSourceDiagnostic[]>\n  }\n  expandNextJsTemplate(\n    content: Buffer,\n    templatePath: string,\n    nextPackageDirPath: string,\n    replacements: Record<`VAR_${string}`, string>,\n    injections: Record<string, string>,\n    imports: Record<string, string | null>\n  ): string\n}\n\nexport type StyledString =\n  | {\n      type: 'text'\n      value: string\n    }\n  | {\n      type: 'code'\n      value: string\n    }\n  | {\n      type: 'strong'\n      value: string\n    }\n  | {\n      type: 'stack'\n      value: StyledString[]\n    }\n  | {\n      type: 'line'\n      value: StyledString[]\n    }\n\nexport interface Issue {\n  severity: string\n  stage: string\n  filePath: string\n  title: StyledString\n  description?: StyledString\n  detail?: StyledString\n  source?: {\n    source: {\n      ident: string\n      content?: string\n    }\n    range?: {\n      start: {\n        // 0-indexed\n        line: number\n        // 0-indexed\n        column: number\n      }\n      end: {\n        // 0-indexed\n        line: number\n        // 0-indexed\n        column: number\n      }\n    }\n  }\n  documentationLink: string\n  importTraces?: PlainTraceItem[][]\n}\nexport interface PlainTraceItem {\n  fsName: string\n  path: string\n  rootPath: string\n  layer?: string\n}\n\nexport interface Diagnostics {\n  category: string\n  name: string\n  payload: unknown\n}\n\nexport type TurbopackResult<T = {}> = T & {\n  issues: Issue[]\n  diagnostics: Diagnostics[]\n}\n\nexport interface Middleware {\n  endpoint: Endpoint\n}\n\nexport interface Instrumentation {\n  nodeJs: Endpoint\n  edge: Endpoint\n}\n\nexport interface RawEntrypoints {\n  routes: Map<string, Route>\n  middleware?: Middleware\n  instrumentation?: Instrumentation\n  pagesDocumentEndpoint: Endpoint\n  pagesAppEndpoint: Endpoint\n  pagesErrorEndpoint: Endpoint\n}\n\ninterface BaseUpdate {\n  resource: {\n    headers: unknown\n    path: string\n  }\n  diagnostics: unknown[]\n  issues: Issue[]\n}\n\ninterface IssuesUpdate extends BaseUpdate {\n  type: 'issues'\n}\n\ninterface EcmascriptMergedUpdate {\n  type: 'EcmascriptMergedUpdate'\n  chunks: { [moduleName: string]: { type: 'partial' } }\n  entries: { [moduleName: string]: { code: string; map: string; url: string } }\n}\n\ninterface PartialUpdate extends BaseUpdate {\n  type: 'partial'\n  instruction: {\n    type: 'ChunkListUpdate'\n    merged: EcmascriptMergedUpdate[] | undefined\n  }\n}\n\nexport type Update = IssuesUpdate | PartialUpdate\n\nexport interface HmrIdentifiers {\n  identifiers: string[]\n}\n\n/** @see https://github.com/vercel/next.js/blob/415cd74b9a220b6f50da64da68c13043e9b02995/packages/next-swc/crates/napi/src/next_api/project.rs#L824-L833 */\nexport interface TurbopackStackFrame {\n  isServer: boolean\n  isInternal?: boolean\n  file: string\n  originalFile?: string\n  /** 1-indexed, unlike source map tokens */\n  line?: number\n  /** 1-indexed, unlike source map tokens */\n  column?: number\n  methodName?: string\n}\n\nexport type UpdateMessage =\n  | {\n      updateType: 'start'\n    }\n  | {\n      updateType: 'end'\n      value: UpdateInfo\n    }\n\nexport type CompilationEvent = {\n  typeName: string\n  message: string\n  severity: string\n  eventData: any\n}\n\nexport interface UpdateInfo {\n  duration: number\n  tasks: number\n}\n\nexport interface Project {\n  update(options: Partial<ProjectOptions>): Promise<void>\n\n  writeAllEntrypointsToDisk(\n    appDirOnly: boolean\n  ): Promise<TurbopackResult<RawEntrypoints>>\n\n  entrypointsSubscribe(): AsyncIterableIterator<TurbopackResult<RawEntrypoints>>\n\n  hmrEvents(identifier: string): AsyncIterableIterator<TurbopackResult<Update>>\n\n  hmrIdentifiersSubscribe(): AsyncIterableIterator<\n    TurbopackResult<HmrIdentifiers>\n  >\n\n  getSourceForAsset(filePath: string): Promise<string | null>\n\n  getSourceMap(filePath: string): Promise<string | null>\n  getSourceMapSync(filePath: string): string | null\n\n  traceSource(\n    stackFrame: TurbopackStackFrame,\n    currentDirectoryFileUrl: string\n  ): Promise<TurbopackStackFrame | null>\n\n  updateInfoSubscribe(\n    aggregationMs: number\n  ): AsyncIterableIterator<TurbopackResult<UpdateMessage>>\n\n  compilationEventsSubscribe(\n    eventTypes?: string[]\n  ): AsyncIterableIterator<TurbopackResult<CompilationEvent>>\n\n  invalidatePersistentCache(): Promise<void>\n\n  shutdown(): Promise<void>\n\n  onExit(): Promise<void>\n}\n\nexport type Route =\n  | {\n      type: 'conflict'\n    }\n  | {\n      type: 'app-page'\n      pages: {\n        originalName: string\n        htmlEndpoint: Endpoint\n        rscEndpoint: Endpoint\n      }[]\n    }\n  | {\n      type: 'app-route'\n      originalName: string\n      endpoint: Endpoint\n    }\n  | {\n      type: 'page'\n      htmlEndpoint: Endpoint\n      dataEndpoint: Endpoint\n    }\n  | {\n      type: 'page-api'\n      endpoint: Endpoint\n    }\n\nexport interface Endpoint {\n  /** Write files for the endpoint to disk. */\n  writeToDisk(): Promise<TurbopackResult<WrittenEndpoint>>\n\n  /**\n   * Listen to client-side changes to the endpoint.\n   * After clientChanged() has been awaited it will listen to changes.\n   * The async iterator will yield for each change.\n   */\n  clientChanged(): Promise<AsyncIterableIterator<TurbopackResult>>\n\n  /**\n   * Listen to server-side changes to the endpoint.\n   * After serverChanged() has been awaited it will listen to changes.\n   * The async iterator will yield for each change.\n   */\n  serverChanged(\n    includeIssues: boolean\n  ): Promise<AsyncIterableIterator<TurbopackResult>>\n}\n\ninterface EndpointConfig {\n  dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static'\n  dynamicParams?: boolean\n  revalidate?: 'never' | 'force-cache' | number\n  fetchCache?:\n    | 'auto'\n    | 'default-cache'\n    | 'only-cache'\n    | 'force-cache'\n    | 'default-no-store'\n    | 'only-no-store'\n    | 'force-no-store'\n  runtime?: 'nodejs' | 'edge'\n  preferredRegion?: string\n}\n\nexport type ServerPath = {\n  path: string\n  contentHash: string\n}\n\nexport type WrittenEndpoint =\n  | {\n      type: 'nodejs'\n      /** The entry path for the endpoint. */\n      entryPath: string\n      /** All client paths that have been written for the endpoint. */\n      clientPaths: string[]\n      /** All server paths that have been written for the endpoint. */\n      serverPaths: ServerPath[]\n      config: EndpointConfig\n    }\n  | {\n      type: 'edge'\n      /** All client paths that have been written for the endpoint. */\n      clientPaths: string[]\n      /** All server paths that have been written for the endpoint. */\n      serverPaths: ServerPath[]\n      config: EndpointConfig\n    }\n  | {\n      type: 'none'\n      clientPaths: []\n      serverPaths: []\n      config: EndpointConfig\n    }\n\nexport interface ProjectOptions {\n  /**\n   * An absolute root path (Unix or Windows path) from which all files must be nested under. Trying\n   * to access a file outside this root will fail, so think of this as a chroot.\n   * E.g. `/home/<USER>/projects/my-repo`.\n   */\n  rootPath: string\n\n  /**\n   * A path which contains the app/pages directories, relative to `root_path`, always a Unix path.\n   * E.g. `apps/my-app`\n   */\n  projectPath: string\n\n  /**\n   * A path where to emit the build outputs, relative to [`Project::project_path`], always a Unix\n   * path. Corresponds to next.config.js's `distDir`.\n   * E.g. `.next`\n   */\n  distDir: string\n\n  /**\n   * The next.config.js contents.\n   */\n  nextConfig: NextConfigComplete\n\n  /**\n   * Jsconfig, or tsconfig contents.\n   *\n   * Next.js implicitly requires to read it to support few options\n   * https://nextjs.org/docs/architecture/nextjs-compiler#legacy-decorators\n   * https://nextjs.org/docs/architecture/nextjs-compiler#importsource\n   */\n  jsConfig: {\n    compilerOptions: object\n  }\n\n  /**\n   * A map of environment variables to use when compiling code.\n   */\n  env: Record<string, string>\n\n  defineEnv: DefineEnv\n\n  /**\n   * Whether to watch the filesystem for file changes.\n   */\n  watch: {\n    enable: boolean\n    pollIntervalMs?: number\n  }\n\n  /**\n   * The mode in which Next.js is running.\n   */\n  dev: boolean\n\n  /**\n   * The server actions encryption key.\n   */\n  encryptionKey: string\n\n  /**\n   * The build id.\n   */\n  buildId: string\n\n  /**\n   * Options for draft mode.\n   */\n  previewProps: __ApiPreviewProps\n\n  /**\n   * The browserslist query to use for targeting browsers.\n   */\n  browserslistQuery: string\n\n  /**\n   * When the code is minified, this opts out of the default mangling of local\n   * names for variables, functions etc., which can be useful for\n   * debugging/profiling purposes.\n   */\n  noMangling: boolean\n\n  /**\n   * The version of Node.js that is available/currently running.\n   */\n  currentNodeJsVersion: string\n}\n\nexport interface DefineEnv {\n  client: RustifiedOptionalEnv\n  edge: RustifiedOptionalEnv\n  nodejs: RustifiedOptionalEnv\n}\n\nexport type RustifiedEnv = { name: string; value: string }[]\nexport type RustifiedOptionalEnv = { name: string; value: string | undefined }[]\n\nexport interface GlobalEntrypoints {\n  app: Endpoint | undefined\n  document: Endpoint | undefined\n  error: Endpoint | undefined\n  middleware: Middleware | undefined\n  instrumentation: Instrumentation | undefined\n}\n\nexport type PageRoute =\n  | {\n      type: 'page'\n      htmlEndpoint: Endpoint\n      dataEndpoint: Endpoint\n    }\n  | {\n      type: 'page-api'\n      endpoint: Endpoint\n    }\n\nexport type AppRoute =\n  | {\n      type: 'app-page'\n      htmlEndpoint: Endpoint\n      rscEndpoint: Endpoint\n    }\n  | {\n      type: 'app-route'\n      endpoint: Endpoint\n    }\n\n// pathname -> route\nexport type PageEntrypoints = Map<string, PageRoute>\n\n// originalName / page -> route\nexport type AppEntrypoints = Map<string, AppRoute>\n\nexport type Entrypoints = {\n  global: GlobalEntrypoints\n  page: PageEntrypoints\n  app: AppEntrypoints\n}\n"], "names": [], "mappings": "AAyeA,WAIC", "ignoreList": [0]}