{"version": 3, "sources": ["../../src/server/config.ts"], "sourcesContent": ["import { existsSync } from 'fs'\nimport { basename, extname, join, relative, isAbsolute, resolve } from 'path'\nimport { pathToFileURL } from 'url'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  CONFIG_FILES,\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_EXPORT,\n  PHASE_PRODUCTION_BUILD,\n  PHASE_PRODUCTION_SERVER,\n} from '../shared/lib/constants'\nimport { defaultConfig, normalizeConfig } from './config-shared'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n  NextConfig,\n  TurbopackLoaderItem,\n  NextAdapter,\n} from './config-shared'\n\nimport { loadWebpackHook } from './config-utils'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport type { ImageConfig } from '../shared/lib/image-config'\nimport { loadEnvConfig, updateInitialEnv } from '@next/env'\nimport { flushAndExit } from '../telemetry/flush-and-exit'\nimport { findRootDir } from '../lib/find-root'\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\nimport { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\nimport { matchRemotePattern } from '../shared/lib/match-remote-pattern'\n\nimport type { ZodError } from 'next/dist/compiled/zod'\nimport { hasNextSupport } from '../server/ci-info'\nimport { transpileConfig } from '../build/next-config-ts/transpile-config'\nimport { dset } from '../shared/lib/dset'\nimport { normalizeZodErrors } from '../shared/lib/zod'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport { findDir } from '../lib/find-pages-dir'\nimport { CanaryOnlyError, isStableBuild } from '../shared/lib/canary-only'\nimport { interopDefault } from '../lib/interop-default'\nimport { djb2Hash } from '../shared/lib/hash'\n\nexport { normalizeConfig } from './config-shared'\nexport type { DomainLocale, NextConfig } from './config-shared'\n\nfunction normalizeNextConfigZodErrors(\n  error: ZodError<NextConfig>\n): [errorMessages: string[], shouldExit: boolean] {\n  let shouldExit = false\n  const issues = normalizeZodErrors(error)\n  return [\n    issues.flatMap(({ issue, message }) => {\n      if (issue.path[0] === 'images') {\n        // We exit the build when encountering an error in the images config\n        shouldExit = true\n      }\n\n      return message\n    }),\n    shouldExit,\n  ]\n}\n\nexport function warnOptionHasBeenDeprecated(\n  config: NextConfig,\n  nestedPropertyKey: string,\n  reason: string,\n  silent: boolean\n): boolean {\n  let hasWarned = false\n  if (!silent) {\n    let current = config\n    let found = true\n    const nestedPropertyKeys = nestedPropertyKey.split('.')\n    for (const key of nestedPropertyKeys) {\n      if (current[key] !== undefined) {\n        current = current[key]\n      } else {\n        found = false\n        break\n      }\n    }\n    if (found) {\n      Log.warnOnce(reason)\n      hasWarned = true\n    }\n  }\n  return hasWarned\n}\n\nfunction checkDeprecations(\n  userConfig: NextConfig,\n  configFileName: string,\n  silent: boolean,\n  dir: string\n) {\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'amp',\n    `Built-in amp support is deprecated and the \\`amp\\` configuration option will be removed in Next.js 16.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'experimental.amp',\n    `Built-in amp support is deprecated and the \\`experimental.amp\\` configuration option will be removed in Next.js 16.`,\n    silent\n  )\n\n  if (userConfig.experimental?.dynamicIO !== undefined) {\n    warnOptionHasBeenDeprecated(\n      userConfig,\n      'experimental.dynamicIO',\n      `\\`experimental.dynamicIO\\` has been renamed to \\`experimental.cacheComponents\\`. Please update your ${configFileName} file accordingly.`,\n      silent\n    )\n  }\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'experimental.instrumentationHook',\n    `\\`experimental.instrumentationHook\\` is no longer needed, because \\`instrumentation.js\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'experimental.after',\n    `\\`experimental.after\\` is no longer needed, because \\`after\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'devIndicators.appIsrStatus',\n    `\\`devIndicators.appIsrStatus\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'devIndicators.buildActivity',\n    `\\`devIndicators.buildActivity\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    userConfig,\n    'devIndicators.buildActivityPosition',\n    `\\`devIndicators.buildActivityPosition\\` has been renamed to \\`devIndicators.position\\`. Please update your ${configFileName} file accordingly.`,\n    silent\n  )\n\n  // i18n deprecation for App Router\n  if (userConfig.i18n) {\n    const hasAppDir = Boolean(findDir(dir, 'app'))\n    if (hasAppDir) {\n      warnOptionHasBeenDeprecated(\n        userConfig,\n        'i18n',\n        `i18n configuration in ${configFileName} is unsupported in App Router.\\nLearn more about internationalization in App Router: https://nextjs.org/docs/app/building-your-application/routing/internationalization`,\n        silent\n      )\n    }\n  }\n}\n\nexport function warnOptionHasBeenMovedOutOfExperimental(\n  config: NextConfig,\n  oldExperimentalKey: string,\n  newKey: string,\n  configFileName: string,\n  silent: boolean\n) {\n  if (config.experimental && oldExperimentalKey in config.experimental) {\n    if (!silent) {\n      Log.warn(\n        `\\`experimental.${oldExperimentalKey}\\` has been moved to \\`${newKey}\\`. ` +\n          `Please update your ${configFileName} file accordingly.`\n      )\n    }\n\n    let current = config\n    const newKeys = newKey.split('.')\n    while (newKeys.length > 1) {\n      const key = newKeys.shift()!\n      current[key] = current[key] || {}\n      current = current[key]\n    }\n    current[newKeys.shift()!] = (config.experimental as any)[oldExperimentalKey]\n  }\n\n  return config\n}\n\nfunction warnCustomizedOption(\n  config: NextConfig,\n  key: string,\n  defaultValue: any,\n  customMessage: string,\n  configFileName: string,\n  silent: boolean\n) {\n  const segs = key.split('.')\n  let current = config\n\n  while (segs.length >= 1) {\n    const seg = segs.shift()!\n    if (!(seg in current)) {\n      return\n    }\n    current = current[seg]\n  }\n\n  if (!silent && current !== defaultValue) {\n    Log.warn(\n      `The \"${key}\" option has been modified. ${customMessage ? customMessage + '. ' : ''}It should be removed from your ${configFileName}.`\n    )\n  }\n}\n\nfunction assignDefaults(\n  dir: string,\n  userConfig: NextConfig & { configFileName: string },\n  silent: boolean\n): NextConfigComplete {\n  const configFileName = userConfig.configFileName\n  if (typeof userConfig.exportTrailingSlash !== 'undefined') {\n    if (!silent) {\n      Log.warn(\n        `The \"exportTrailingSlash\" option has been renamed to \"trailingSlash\". Please update your ${configFileName}.`\n      )\n    }\n    if (typeof userConfig.trailingSlash === 'undefined') {\n      userConfig.trailingSlash = userConfig.exportTrailingSlash\n    }\n    delete userConfig.exportTrailingSlash\n  }\n\n  // Handle migration of experimental.dynamicIO to experimental.cacheComponents\n  if (userConfig.experimental?.dynamicIO !== undefined) {\n    // If cacheComponents was not explicitly set by the user (i.e., it's still the default value),\n    // use the dynamicIO value. We check against the user config, not the merged result.\n    if (userConfig.experimental?.cacheComponents === undefined) {\n      userConfig.experimental.cacheComponents =\n        userConfig.experimental.dynamicIO\n    }\n\n    // Remove the deprecated property\n    delete userConfig.experimental.dynamicIO\n  }\n\n  const config = Object.keys(userConfig).reduce<{ [key: string]: any }>(\n    (currentConfig, key) => {\n      const value = userConfig[key]\n\n      if (value === undefined || value === null) {\n        return currentConfig\n      }\n\n      if (key === 'distDir') {\n        if (typeof value !== 'string') {\n          throw new Error(\n            `Specified distDir is not a string, found type \"${typeof value}\"`\n          )\n        }\n        const userDistDir = value.trim()\n\n        // don't allow public as the distDir as this is a reserved folder for\n        // public files\n        if (userDistDir === 'public') {\n          throw new Error(\n            `The 'public' directory is reserved in Next.js and can not be set as the 'distDir'. https://nextjs.org/docs/messages/can-not-output-to-public`\n          )\n        }\n        // make sure distDir isn't an empty string as it can result in the provided\n        // directory being deleted in development mode\n        if (userDistDir.length === 0) {\n          throw new Error(\n            `Invalid distDir provided, distDir can not be an empty string. Please remove this config or set it to undefined`\n          )\n        }\n      }\n\n      if (key === 'pageExtensions') {\n        if (!Array.isArray(value)) {\n          throw new Error(\n            `Specified pageExtensions is not an array of strings, found \"${value}\". Please update this config or remove it.`\n          )\n        }\n\n        if (!value.length) {\n          throw new Error(\n            `Specified pageExtensions is an empty array. Please update it with the relevant extensions or remove it.`\n          )\n        }\n\n        value.forEach((ext) => {\n          if (typeof ext !== 'string') {\n            throw new Error(\n              `Specified pageExtensions is not an array of strings, found \"${ext}\" of type \"${typeof ext}\". Please update this config or remove it.`\n            )\n          }\n        })\n      }\n\n      const defaultValue = (defaultConfig as Record<string, unknown>)[key]\n\n      if (\n        !!value &&\n        value.constructor === Object &&\n        typeof defaultValue === 'object'\n      ) {\n        currentConfig[key] = {\n          ...defaultValue,\n          ...Object.keys(value).reduce<any>((c, k) => {\n            const v = value[k]\n            if (v !== undefined && v !== null) {\n              c[k] = v\n            }\n            return c\n          }, {}),\n        }\n      } else {\n        currentConfig[key] = value\n      }\n\n      return currentConfig\n    },\n    {}\n  ) as NextConfig & { configFileName: string }\n\n  const result = {\n    ...defaultConfig,\n    ...config,\n    experimental: {\n      ...defaultConfig.experimental,\n      ...config.experimental,\n    },\n  }\n\n  // ensure correct default is set for api-resolver revalidate handling\n  if (!result.experimental?.trustHostHeader && ciEnvironment.hasNextSupport) {\n    result.experimental.trustHostHeader = true\n  }\n\n  if (\n    result.experimental?.allowDevelopmentBuild &&\n    process.env.NODE_ENV !== 'development'\n  ) {\n    throw new Error(\n      `The experimental.allowDevelopmentBuild option requires NODE_ENV to be explicitly set to 'development'.`\n    )\n  }\n\n  if (isStableBuild()) {\n    // Prevents usage of certain experimental features outside of canary\n    if (result.experimental?.ppr) {\n      throw new CanaryOnlyError({ feature: 'experimental.ppr' })\n    } else if (result.experimental?.cacheComponents) {\n      throw new CanaryOnlyError({ feature: 'experimental.cacheComponents' })\n    } else if (result.experimental?.turbopackPersistentCaching) {\n      throw new CanaryOnlyError({\n        feature: 'experimental.turbopackPersistentCaching',\n      })\n    }\n  }\n\n  if (result.output === 'export') {\n    if (result.i18n) {\n      throw new Error(\n        'Specified \"i18n\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-i18n'\n      )\n    }\n\n    if (!hasNextSupport) {\n      if (result.rewrites) {\n        Log.warn(\n          'Specified \"rewrites\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.redirects) {\n        Log.warn(\n          'Specified \"redirects\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.headers) {\n        Log.warn(\n          'Specified \"headers\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n    }\n  }\n\n  if (typeof result.assetPrefix !== 'string') {\n    throw new Error(\n      `Specified assetPrefix is not a string, found type \"${typeof result.assetPrefix}\" https://nextjs.org/docs/messages/invalid-assetprefix`\n    )\n  }\n\n  if (typeof result.basePath !== 'string') {\n    throw new Error(\n      `Specified basePath is not a string, found type \"${typeof result.basePath}\"`\n    )\n  }\n\n  if (result.basePath !== '') {\n    if (result.basePath === '/') {\n      throw new Error(\n        `Specified basePath /. basePath has to be either an empty string or a path prefix\"`\n      )\n    }\n\n    if (!result.basePath.startsWith('/')) {\n      throw new Error(\n        `Specified basePath has to start with a /, found \"${result.basePath}\"`\n      )\n    }\n\n    if (result.basePath !== '/') {\n      if (result.basePath.endsWith('/')) {\n        throw new Error(\n          `Specified basePath should not end with /, found \"${result.basePath}\"`\n        )\n      }\n\n      if (result.assetPrefix === '') {\n        result.assetPrefix = result.basePath\n      }\n\n      if (result.amp?.canonicalBase === '') {\n        result.amp.canonicalBase = result.basePath\n      }\n    }\n  }\n\n  if (result?.images) {\n    const images: ImageConfig = result.images\n\n    if (typeof images !== 'object') {\n      throw new Error(\n        `Specified images should be an object received ${typeof images}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n      )\n    }\n\n    if (images.localPatterns) {\n      if (!Array.isArray(images.localPatterns)) {\n        throw new Error(\n          `Specified images.localPatterns should be an Array received ${typeof images.localPatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n      // avoid double-pushing the same pattern if it already exists\n      const hasMatch = images.localPatterns.some(\n        (pattern) =>\n          pattern.pathname === '/_next/static/media/**' && pattern.search === ''\n      )\n      if (!hasMatch) {\n        // static import images are automatically allowed\n        images.localPatterns.push({\n          pathname: '/_next/static/media/**',\n          search: '',\n        })\n      }\n    }\n\n    if (images.remotePatterns) {\n      if (!Array.isArray(images.remotePatterns)) {\n        throw new Error(\n          `Specified images.remotePatterns should be an Array received ${typeof images.remotePatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n\n      // We must convert URL to RemotePattern since URL has a colon in the protocol\n      // and also has additional properties we want to filter out. Also, new URL()\n      // accepts any protocol so we need manual validation here.\n      images.remotePatterns = images.remotePatterns.map(\n        ({ protocol, hostname, port, pathname, search }) => {\n          const proto = protocol?.replace(/:$/, '')\n          if (!['http', 'https', undefined].includes(proto)) {\n            throw new Error(\n              `Specified images.remotePatterns must have protocol \"http\" or \"https\" received \"${proto}\".`\n            )\n          }\n          return {\n            protocol: proto as 'http' | 'https' | undefined,\n            hostname,\n            port,\n            pathname,\n            search,\n          }\n        }\n      )\n\n      // static images are automatically prefixed with assetPrefix\n      // so we need to ensure _next/image allows downloading from\n      // this resource\n      if (config.assetPrefix?.startsWith('http')) {\n        try {\n          const url = new URL(config.assetPrefix)\n          const hasMatchForAssetPrefix = images.remotePatterns.some((pattern) =>\n            matchRemotePattern(pattern, url)\n          )\n\n          // avoid double-pushing the same pattern if it already can be matched\n          if (!hasMatchForAssetPrefix) {\n            images.remotePatterns.push({\n              hostname: url.hostname,\n              protocol: url.protocol.replace(/:$/, '') as 'http' | 'https',\n              port: url.port,\n            })\n          }\n        } catch (error) {\n          throw new Error(\n            `Invalid assetPrefix provided. Original error: ${error}`\n          )\n        }\n      }\n    }\n\n    if (images.domains) {\n      if (!Array.isArray(images.domains)) {\n        throw new Error(\n          `Specified images.domains should be an Array received ${typeof images.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n    }\n\n    if (!images.loader) {\n      images.loader = 'default'\n    }\n\n    if (\n      images.loader !== 'default' &&\n      images.loader !== 'custom' &&\n      images.path === imageConfigDefault.path\n    ) {\n      throw new Error(\n        `Specified images.loader property (${images.loader}) also requires images.path property to be assigned to a URL prefix.\\nSee more info here: https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration`\n      )\n    }\n\n    if (\n      images.path === imageConfigDefault.path &&\n      result.basePath &&\n      !pathHasPrefix(images.path, result.basePath)\n    ) {\n      images.path = `${result.basePath}${images.path}`\n    }\n\n    // Append trailing slash for non-default loaders and when trailingSlash is set\n    if (\n      images.path &&\n      !images.path.endsWith('/') &&\n      (images.loader !== 'default' || result.trailingSlash)\n    ) {\n      images.path += '/'\n    }\n\n    if (images.loaderFile) {\n      if (images.loader !== 'default' && images.loader !== 'custom') {\n        throw new Error(\n          `Specified images.loader property (${images.loader}) cannot be used with images.loaderFile property. Please set images.loader to \"custom\".`\n        )\n      }\n      const absolutePath = join(dir, images.loaderFile)\n      if (!existsSync(absolutePath)) {\n        throw new Error(\n          `Specified images.loaderFile does not exist at \"${absolutePath}\".`\n        )\n      }\n      images.loaderFile = absolutePath\n    }\n  }\n\n  warnCustomizedOption(\n    result,\n    'experimental.esmExternals',\n    true,\n    'experimental.esmExternals is not recommended to be modified as it may disrupt module resolution',\n    configFileName,\n    silent\n  )\n\n  // Handle buildActivityPosition migration (needs to be done after merging with defaults)\n  if (\n    result.devIndicators &&\n    typeof result.devIndicators === 'object' &&\n    'buildActivityPosition' in result.devIndicators &&\n    result.devIndicators.buildActivityPosition !== result.devIndicators.position\n  ) {\n    if (!silent) {\n      Log.warnOnce(\n        `The \\`devIndicators\\` option \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") conflicts with \\`position\\` (\"${result.devIndicators.position}\"). Using \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") for backward compatibility.`\n      )\n    }\n    result.devIndicators.position = result.devIndicators.buildActivityPosition\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'bundlePagesExternals',\n    'bundlePagesRouterDependencies',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'serverComponentsExternalPackages',\n    'serverExternalPackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'relay',\n    'compiler.relay',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'styledComponents',\n    'compiler.styledComponents',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'emotion',\n    'compiler.emotion',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'reactRemoveProperties',\n    'compiler.reactRemoveProperties',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'removeConsole',\n    'compiler.removeConsole',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'swrDelta',\n    'expireTime',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'typedRoutes',\n    'typedRoutes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingRoot',\n    'outputFileTracingRoot',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingIncludes',\n    'outputFileTracingIncludes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingExcludes',\n    'outputFileTracingExcludes',\n    configFileName,\n    silent\n  )\n\n  if ((result.experimental as any).outputStandalone) {\n    if (!silent) {\n      Log.warn(\n        `experimental.outputStandalone has been renamed to \"output: 'standalone'\", please move the config.`\n      )\n    }\n    result.output = 'standalone'\n  }\n\n  if (\n    typeof result.experimental?.serverActions?.bodySizeLimit !== 'undefined'\n  ) {\n    const value = parseInt(\n      result.experimental.serverActions?.bodySizeLimit.toString()\n    )\n    if (isNaN(value) || value < 1) {\n      throw new Error(\n        'Server Actions Size Limit must be a valid number or filesize format larger than 1MB: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit'\n      )\n    }\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'transpilePackages',\n    'transpilePackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipMiddlewareUrlNormalize',\n    'skipMiddlewareUrlNormalize',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipTrailingSlashRedirect',\n    'skipTrailingSlashRedirect',\n    configFileName,\n    silent\n  )\n\n  if (\n    result?.outputFileTracingRoot &&\n    !isAbsolute(result.outputFileTracingRoot)\n  ) {\n    result.outputFileTracingRoot = resolve(result.outputFileTracingRoot)\n    if (!silent) {\n      Log.warn(\n        `outputFileTracingRoot should be absolute, using: ${result.outputFileTracingRoot}`\n      )\n    }\n  }\n\n  if (result?.turbopack?.root && !isAbsolute(result.turbopack.root)) {\n    result.turbopack.root = resolve(result.turbopack.root)\n    if (!silent) {\n      Log.warn(\n        `turbopack.root should be absolute, using: ${result.turbopack.root}`\n      )\n    }\n  }\n\n  // only leverage deploymentId\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    result.deploymentId = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  const tracingRoot = result?.outputFileTracingRoot\n  const turbopackRoot = result?.turbopack?.root\n\n  // If both provided, validate they match. If not, use outputFileTracingRoot.\n  if (tracingRoot && turbopackRoot && tracingRoot !== turbopackRoot) {\n    Log.warn(\n      `Both \\`outputFileTracingRoot\\` and \\`turbopack.root\\` are set, but they must have the same value.\\n` +\n        `Using \\`outputFileTracingRoot\\` value: ${tracingRoot}.`\n    )\n  }\n\n  const rootDir = tracingRoot || turbopackRoot || findRootDir(dir)\n\n  if (!rootDir) {\n    throw new Error(\n      'Failed to find the root directory of the project. This is a bug in Next.js.'\n    )\n  }\n\n  // Ensure both properties are set to the same value\n  result.outputFileTracingRoot = rootDir\n  dset(result, ['turbopack', 'root'], rootDir)\n\n  setHttpClientAndAgentOptions(result || defaultConfig)\n\n  if (result.i18n) {\n    const { i18n } = result\n    const i18nType = typeof i18n\n\n    if (i18nType !== 'object') {\n      throw new Error(\n        `Specified i18n should be an object received ${i18nType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales should be an Array received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.locales.length > 100 && !silent) {\n      Log.warn(\n        `Received ${i18n.locales.length} i18n.locales items which exceeds the recommended max of 100.\\nSee more info here: https://nextjs.org/docs/advanced-features/i18n-routing#how-does-this-work-with-static-generation`\n      )\n    }\n\n    const defaultLocaleType = typeof i18n.defaultLocale\n\n    if (!i18n.defaultLocale || defaultLocaleType !== 'string') {\n      throw new Error(\n        `Specified i18n.defaultLocale should be a string.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (typeof i18n.domains !== 'undefined' && !Array.isArray(i18n.domains)) {\n      throw new Error(\n        `Specified i18n.domains must be an array of domain objects e.g. [ { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] } ] received ${typeof i18n.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.domains) {\n      const invalidDomainItems = i18n.domains.filter((item) => {\n        if (!item || typeof item !== 'object') return true\n        if (!item.defaultLocale) return true\n        if (!item.domain || typeof item.domain !== 'string') return true\n\n        if (item.domain.includes(':')) {\n          console.warn(\n            `i18n domain: \"${item.domain}\" is invalid it should be a valid domain without protocol (https://) or port (:3000) e.g. example.vercel.sh`\n          )\n          return true\n        }\n\n        const defaultLocaleDuplicate = i18n.domains?.find(\n          (altItem) =>\n            altItem.defaultLocale === item.defaultLocale &&\n            altItem.domain !== item.domain\n        )\n\n        if (!silent && defaultLocaleDuplicate) {\n          console.warn(\n            `Both ${item.domain} and ${defaultLocaleDuplicate.domain} configured the defaultLocale ${item.defaultLocale} but only one can. Change one item's default locale to continue`\n          )\n          return true\n        }\n\n        let hasInvalidLocale = false\n\n        if (Array.isArray(item.locales)) {\n          for (const locale of item.locales) {\n            if (typeof locale !== 'string') hasInvalidLocale = true\n\n            for (const domainItem of i18n.domains || []) {\n              if (domainItem === item) continue\n              if (domainItem.locales && domainItem.locales.includes(locale)) {\n                console.warn(\n                  `Both ${item.domain} and ${domainItem.domain} configured the locale (${locale}) but only one can. Remove it from one i18n.domains config to continue`\n                )\n                hasInvalidLocale = true\n                break\n              }\n            }\n          }\n        }\n\n        return hasInvalidLocale\n      })\n\n      if (invalidDomainItems.length > 0) {\n        throw new Error(\n          `Invalid i18n.domains values:\\n${invalidDomainItems\n            .map((item: any) => JSON.stringify(item))\n            .join(\n              '\\n'\n            )}\\n\\ndomains value must follow format { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] }.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n        )\n      }\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales must be an array of locale strings e.g. [\"en-US\", \"nl-NL\"] received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const invalidLocales = i18n.locales.filter(\n      (locale: any) => typeof locale !== 'string'\n    )\n\n    if (invalidLocales.length > 0) {\n      throw new Error(\n        `Specified i18n.locales contains invalid values (${invalidLocales\n          .map(String)\n          .join(\n            ', '\n          )}), locales must be valid locale tags provided as strings e.g. \"en-US\".\\n` +\n          `See here for list of valid language sub-tags: http://www.iana.org/assignments/language-subtag-registry/language-subtag-registry`\n      )\n    }\n\n    if (!i18n.locales.includes(i18n.defaultLocale)) {\n      throw new Error(\n        `Specified i18n.defaultLocale should be included in i18n.locales.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const normalizedLocales = new Set()\n    const duplicateLocales = new Set()\n\n    i18n.locales.forEach((locale) => {\n      const localeLower = locale.toLowerCase()\n      if (normalizedLocales.has(localeLower)) {\n        duplicateLocales.add(locale)\n      }\n      normalizedLocales.add(localeLower)\n    })\n\n    if (duplicateLocales.size > 0) {\n      throw new Error(\n        `Specified i18n.locales contains the following duplicate locales:\\n` +\n          `${[...duplicateLocales].join(', ')}\\n` +\n          `Each locale should be listed only once.\\n` +\n          `See more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    // make sure default Locale is at the front\n    i18n.locales = [\n      i18n.defaultLocale,\n      ...i18n.locales.filter((locale) => locale !== i18n.defaultLocale),\n    ]\n\n    const localeDetectionType = typeof i18n.localeDetection\n\n    if (\n      localeDetectionType !== 'boolean' &&\n      localeDetectionType !== 'undefined'\n    ) {\n      throw new Error(\n        `Specified i18n.localeDetection should be undefined or a boolean received ${localeDetectionType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n  }\n\n  if (result.devIndicators !== false && result.devIndicators?.position) {\n    const { position } = result.devIndicators\n    const allowedValues = [\n      'top-left',\n      'top-right',\n      'bottom-left',\n      'bottom-right',\n    ]\n\n    if (!allowedValues.includes(position)) {\n      throw new Error(\n        `Invalid \"devIndicator.position\" provided, expected one of ${allowedValues.join(\n          ', '\n        )}, received ${position}`\n      )\n    }\n  }\n\n  if (result.experimental) {\n    result.experimental.cacheLife = {\n      ...defaultConfig.experimental?.cacheLife,\n      ...result.experimental.cacheLife,\n    }\n    const defaultDefault = defaultConfig.experimental?.cacheLife?.['default']\n    if (\n      !defaultDefault ||\n      defaultDefault.revalidate === undefined ||\n      defaultDefault.expire === undefined ||\n      !defaultConfig.experimental?.staleTimes?.static\n    ) {\n      throw new Error('No default cacheLife profile.')\n    }\n    const defaultCacheLifeProfile = result.experimental.cacheLife['default']\n    if (!defaultCacheLifeProfile) {\n      result.experimental.cacheLife['default'] = defaultDefault\n    } else {\n      if (defaultCacheLifeProfile.stale === undefined) {\n        const staticStaleTime = result.experimental.staleTimes?.static\n        defaultCacheLifeProfile.stale =\n          staticStaleTime ?? defaultConfig.experimental?.staleTimes?.static\n      }\n      if (defaultCacheLifeProfile.revalidate === undefined) {\n        defaultCacheLifeProfile.revalidate = defaultDefault.revalidate\n      }\n      if (defaultCacheLifeProfile.expire === undefined) {\n        defaultCacheLifeProfile.expire =\n          result.expireTime ?? defaultDefault.expire\n      }\n    }\n  }\n\n  if (result.experimental?.cacheHandlers) {\n    const allowedHandlerNameRegex = /[a-z-]/\n\n    if (typeof result.experimental.cacheHandlers !== 'object') {\n      throw new Error(\n        `Invalid \"experimental.cacheHandlers\" provided, expected an object e.g. { default: '/my-handler.js' }, received ${JSON.stringify(result.experimental.cacheHandlers)}`\n      )\n    }\n\n    const handlerKeys = Object.keys(result.experimental.cacheHandlers)\n    const invalidHandlerItems: Array<{ key: string; reason: string }> = []\n\n    for (const key of handlerKeys) {\n      if (key === 'private') {\n        invalidHandlerItems.push({\n          key,\n          reason:\n            'The cache handler for \"use cache: private\" cannot be customized.',\n        })\n      } else if (!allowedHandlerNameRegex.test(key)) {\n        invalidHandlerItems.push({\n          key,\n          reason: 'key must only use characters a-z and -',\n        })\n      } else {\n        const handlerPath = (\n          result.experimental.cacheHandlers as {\n            [handlerName: string]: string | undefined\n          }\n        )[key]\n\n        if (handlerPath && !existsSync(handlerPath)) {\n          invalidHandlerItems.push({\n            key,\n            reason: `cache handler path provided does not exist, received ${handlerPath}`,\n          })\n        }\n      }\n      if (invalidHandlerItems.length) {\n        throw new Error(\n          `Invalid handler fields configured for \"experimental.cacheHandler\":\\n${invalidHandlerItems.map((item) => `${key}: ${item.reason}`).join('\\n')}`\n        )\n      }\n    }\n  }\n\n  const userProvidedModularizeImports = result.modularizeImports\n  // Unfortunately these packages end up re-exporting 10600 modules, for example: https://unpkg.com/browse/@mui/icons-material@5.11.16/esm/index.js.\n  // Leveraging modularizeImports tremendously reduces compile times for these.\n  result.modularizeImports = {\n    ...(userProvidedModularizeImports || {}),\n    // This is intentionally added after the user-provided modularizeImports config.\n    '@mui/icons-material': {\n      transform: '@mui/icons-material/{{member}}',\n    },\n    lodash: {\n      transform: 'lodash/{{member}}',\n    },\n  }\n\n  const userProvidedOptimizePackageImports =\n    result.experimental?.optimizePackageImports || []\n\n  result.experimental.optimizePackageImports = [\n    ...new Set([\n      ...userProvidedOptimizePackageImports,\n      'lucide-react',\n      'date-fns',\n      'lodash-es',\n      'ramda',\n      'antd',\n      'react-bootstrap',\n      'ahooks',\n      '@ant-design/icons',\n      '@headlessui/react',\n      '@headlessui-float/react',\n      '@heroicons/react/20/solid',\n      '@heroicons/react/24/solid',\n      '@heroicons/react/24/outline',\n      '@visx/visx',\n      '@tremor/react',\n      'rxjs',\n      '@mui/material',\n      '@mui/icons-material',\n      'recharts',\n      'react-use',\n      'effect',\n      '@effect/schema',\n      '@effect/platform',\n      '@effect/platform-node',\n      '@effect/platform-browser',\n      '@effect/platform-bun',\n      '@effect/sql',\n      '@effect/sql-mssql',\n      '@effect/sql-mysql2',\n      '@effect/sql-pg',\n      '@effect/sql-sqlite-node',\n      '@effect/sql-sqlite-bun',\n      '@effect/sql-sqlite-wasm',\n      '@effect/sql-sqlite-react-native',\n      '@effect/rpc',\n      '@effect/rpc-http',\n      '@effect/typeclass',\n      '@effect/experimental',\n      '@effect/opentelemetry',\n      '@material-ui/core',\n      '@material-ui/icons',\n      '@tabler/icons-react',\n      'mui-core',\n      // We don't support wildcard imports for these configs, e.g. `react-icons/*`\n      // so we need to add them manually.\n      // In the future, we should consider automatically detecting packages that\n      // need to be optimized.\n      'react-icons/ai',\n      'react-icons/bi',\n      'react-icons/bs',\n      'react-icons/cg',\n      'react-icons/ci',\n      'react-icons/di',\n      'react-icons/fa',\n      'react-icons/fa6',\n      'react-icons/fc',\n      'react-icons/fi',\n      'react-icons/gi',\n      'react-icons/go',\n      'react-icons/gr',\n      'react-icons/hi',\n      'react-icons/hi2',\n      'react-icons/im',\n      'react-icons/io',\n      'react-icons/io5',\n      'react-icons/lia',\n      'react-icons/lib',\n      'react-icons/lu',\n      'react-icons/md',\n      'react-icons/pi',\n      'react-icons/ri',\n      'react-icons/rx',\n      'react-icons/si',\n      'react-icons/sl',\n      'react-icons/tb',\n      'react-icons/tfi',\n      'react-icons/ti',\n      'react-icons/vsc',\n      'react-icons/wi',\n    ]),\n  ]\n\n  if (!result.htmlLimitedBots) {\n    // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n    result.htmlLimitedBots = HTML_LIMITED_BOT_UA_RE_STRING\n  }\n\n  // \"use cache\" was originally implicitly enabled with the cacheComponents flag, so\n  // we transfer the value for cacheComponents to the explicit useCache flag to ensure\n  // backwards compatibility.\n  if (result.experimental.useCache === undefined) {\n    result.experimental.useCache = result.experimental.cacheComponents\n  }\n\n  // If cacheComponents is enabled, we also enable PPR.\n  if (result.experimental.cacheComponents) {\n    if (\n      userConfig.experimental?.ppr === false ||\n      userConfig.experimental?.ppr === 'incremental'\n    ) {\n      throw new Error(\n        `\\`experimental.ppr\\` can not be \\`${JSON.stringify(userConfig.experimental?.ppr)}\\` when \\`experimental.cacheComponents\\` is \\`true\\`. PPR is implicitly enabled when Cache Components is enabled.`\n      )\n    }\n\n    result.experimental.ppr = true\n  }\n\n  return result as NextConfigComplete\n}\n\nasync function applyModifyConfig(\n  config: NextConfigComplete,\n  phase: string,\n  silent: boolean\n): Promise<NextConfigComplete> {\n  if (\n    // TODO: should this be called for server start as\n    // adapters shouldn't be relying on \"next start\"\n    [PHASE_PRODUCTION_BUILD, PHASE_PRODUCTION_SERVER].includes(phase) &&\n    config.experimental?.adapterPath\n  ) {\n    const adapterMod = interopDefault(\n      await import(\n        pathToFileURL(require.resolve(config.experimental.adapterPath)).href\n      )\n    ) as NextAdapter\n\n    if (typeof adapterMod.modifyConfig === 'function') {\n      if (!silent) {\n        Log.info(`Applying modifyConfig from ${adapterMod.name}`)\n      }\n      config = await adapterMod.modifyConfig(config)\n    }\n  }\n  return config\n}\n\n// Cache config with keys to handle multiple configurations (e.g., multi-zone)\nconst configCache = new Map<\n  string,\n  {\n    rawConfig: any\n    config: NextConfigComplete\n    configuredExperimentalFeatures: ConfiguredExperimentalFeature[]\n  }\n>()\n\n// Generate cache key based on parameters that affect config output\n// We need a unique key for cache because there can be multiple values\nfunction getCacheKey(\n  phase: string,\n  dir: string,\n  customConfig?: object | null,\n  reactProductionProfiling?: boolean,\n  debugPrerender?: boolean\n): string {\n  // The next.config.js is unique per project, so we can use the dir as the major key\n  // to generate the unique config key.\n  const keyData = JSON.stringify({\n    dir,\n    phase,\n    hasCustomConfig: Boolean(customConfig),\n    reactProductionProfiling: Boolean(reactProductionProfiling),\n    debugPrerender: Boolean(debugPrerender),\n  })\n\n  return djb2Hash(keyData).toString(36)\n}\n\nexport default async function loadConfig(\n  phase: string,\n  dir: string,\n  {\n    customConfig,\n    rawConfig,\n    silent = true,\n    reportExperimentalFeatures,\n    reactProductionProfiling,\n    debugPrerender,\n  }: {\n    customConfig?: object | null\n    rawConfig?: boolean\n    silent?: boolean\n    reportExperimentalFeatures?: (\n      configuredExperimentalFeatures: ConfiguredExperimentalFeature[]\n    ) => void\n    reactProductionProfiling?: boolean\n    debugPrerender?: boolean\n  } = {}\n): Promise<NextConfigComplete> {\n  // Generate cache key based on parameters that affect config output\n  const cacheKey = getCacheKey(\n    phase,\n    dir,\n    customConfig,\n    reactProductionProfiling,\n    debugPrerender\n  )\n\n  // Check if we have a cached result\n  const cachedResult = configCache.get(cacheKey)\n  if (cachedResult) {\n    // Call the experimental features callback if provided\n    if (reportExperimentalFeatures) {\n      reportExperimentalFeatures(cachedResult.configuredExperimentalFeatures)\n    }\n\n    // Return raw config if requested and available\n    if (rawConfig && cachedResult.rawConfig) {\n      return cachedResult.rawConfig\n    }\n\n    return cachedResult.config\n  }\n\n  // Original implementation continues below...\n  if (!process.env.__NEXT_PRIVATE_RENDER_WORKER) {\n    try {\n      loadWebpackHook()\n    } catch (err) {\n      // this can fail in standalone mode as the files\n      // aren't traced/included\n      if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n        throw err\n      }\n    }\n  }\n\n  if (process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n    // we don't apply assignDefaults or modifyConfig here as it\n    // has already been applied\n    const standaloneConfig = JSON.parse(\n      process.env.__NEXT_PRIVATE_STANDALONE_CONFIG\n    )\n\n    // Cache the standalone config\n    configCache.set(cacheKey, {\n      config: standaloneConfig,\n      rawConfig: standaloneConfig,\n      configuredExperimentalFeatures: [],\n    })\n\n    return standaloneConfig\n  }\n\n  const curLog = silent\n    ? {\n        warn: () => {},\n        info: () => {},\n        error: () => {},\n      }\n    : Log\n\n  loadEnvConfig(dir, phase === PHASE_DEVELOPMENT_SERVER, curLog)\n\n  let configFileName = 'next.config.js'\n  const configuredExperimentalFeatures: ConfiguredExperimentalFeature[] = []\n\n  if (customConfig) {\n    // Check deprecation warnings on the custom config before merging with defaults\n    checkDeprecations(customConfig as NextConfig, configFileName, silent, dir)\n\n    const config = await applyModifyConfig(\n      assignDefaults(\n        dir,\n        {\n          configOrigin: 'server',\n          configFileName,\n          ...customConfig,\n        },\n        silent\n      ) as NextConfigComplete,\n      phase,\n      silent\n    )\n\n    // Cache the custom config result\n    configCache.set(cacheKey, {\n      config,\n      rawConfig: customConfig,\n      configuredExperimentalFeatures,\n    })\n\n    reportExperimentalFeatures?.(configuredExperimentalFeatures)\n\n    return config\n  }\n\n  const path = await findUp(CONFIG_FILES, { cwd: dir })\n\n  // If config file was found\n  if (path?.length) {\n    configFileName = basename(path)\n\n    let userConfigModule: any\n    try {\n      const envBefore = Object.assign({}, process.env)\n\n      // `import()` expects url-encoded strings, so the path must be properly\n      // escaped and (especially on Windows) absolute paths must pe prefixed\n      // with the `file://` protocol\n      if (process.env.__NEXT_TEST_MODE === 'jest') {\n        // dynamic import does not currently work inside of vm which\n        // jest relies on so we fall back to require for this case\n        // https://github.com/nodejs/node/issues/35889\n        userConfigModule = require(path)\n      } else if (configFileName === 'next.config.ts') {\n        userConfigModule = await transpileConfig({\n          nextConfigPath: path,\n          configFileName,\n          cwd: dir,\n        })\n      } else {\n        userConfigModule = await import(pathToFileURL(path).href)\n      }\n      const newEnv: typeof process.env = {} as any\n\n      for (const key of Object.keys(process.env)) {\n        if (envBefore[key] !== process.env[key]) {\n          newEnv[key] = process.env[key]\n        }\n      }\n      updateInitialEnv(newEnv)\n\n      if (rawConfig) {\n        // Cache the raw config\n        configCache.set(cacheKey, {\n          config: userConfigModule as NextConfigComplete,\n          rawConfig: userConfigModule,\n          configuredExperimentalFeatures,\n        })\n\n        reportExperimentalFeatures?.(configuredExperimentalFeatures)\n\n        return userConfigModule\n      }\n    } catch (err) {\n      // TODO: Modify docs to add cases of failing next.config.ts transformation\n      curLog.error(\n        `Failed to load ${configFileName}, see more info here https://nextjs.org/docs/messages/next-config-error`\n      )\n      throw err\n    }\n\n    const loadedConfig = Object.freeze(\n      (await normalizeConfig(\n        phase,\n        interopDefault(userConfigModule)\n      )) as NextConfig\n    )\n\n    if (loadedConfig.experimental) {\n      for (const name of Object.keys(\n        loadedConfig.experimental\n      ) as (keyof ExperimentalConfig)[]) {\n        const value = loadedConfig.experimental[name]\n\n        if (name === 'turbo' && !process.env.TURBOPACK) {\n          // Ignore any Turbopack config if Turbopack is not enabled\n          continue\n        }\n\n        addConfiguredExperimentalFeature(\n          configuredExperimentalFeatures,\n          name,\n          value\n        )\n      }\n    }\n\n    // Clone a new userConfig each time to avoid mutating the original\n    const userConfig = cloneObject(loadedConfig) as NextConfig\n\n    // Check deprecation warnings on the actual user config before merging with defaults\n    checkDeprecations(userConfig, configFileName, silent, dir)\n\n    // Always validate the config against schema in non minimal mode.\n    // Only validate once in the root Next.js process, not in forked processes.\n    const isRootProcess = typeof process.send !== 'function'\n    if (!process.env.NEXT_MINIMAL && isRootProcess) {\n      // We only validate the config against schema in non minimal mode\n      const { configSchema } =\n        require('./config-schema') as typeof import('./config-schema')\n      const state = configSchema.safeParse(userConfig)\n\n      if (!state.success) {\n        // error message header\n        const messages = [`Invalid ${configFileName} options detected: `]\n\n        const [errorMessages, shouldExit] = normalizeNextConfigZodErrors(\n          state.error\n        )\n        // ident list item\n        for (const error of errorMessages) {\n          messages.push(`    ${error}`)\n        }\n\n        // error message footer\n        messages.push(\n          'See more info here: https://nextjs.org/docs/messages/invalid-next-config'\n        )\n\n        if (shouldExit) {\n          for (const message of messages) {\n            console.error(message)\n          }\n          await flushAndExit(1)\n        } else {\n          for (const message of messages) {\n            curLog.warn(message)\n          }\n        }\n      }\n    }\n\n    if (userConfig.target && userConfig.target !== 'server') {\n      throw new Error(\n        `The \"target\" property is no longer supported in ${configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/deprecated-target-config'\n      )\n    }\n\n    if (userConfig.amp?.canonicalBase) {\n      const { canonicalBase } = userConfig.amp || ({} as any)\n      userConfig.amp = userConfig.amp || {}\n      userConfig.amp.canonicalBase =\n        (canonicalBase?.endsWith('/')\n          ? canonicalBase.slice(0, -1)\n          : canonicalBase) || ''\n    }\n\n    if (reactProductionProfiling) {\n      userConfig.reactProductionProfiling = reactProductionProfiling\n    }\n\n    if (\n      userConfig.experimental?.turbo?.loaders &&\n      !userConfig.experimental?.turbo?.rules\n    ) {\n      curLog.warn(\n        'experimental.turbo.loaders is now deprecated. Please update next.config.js to use experimental.turbo.rules as soon as possible.\\n' +\n          'The new option is similar, but the key should be a glob instead of an extension.\\n' +\n          'Example: loaders: { \".mdx\": [\"mdx-loader\"] } -> rules: { \"*.mdx\": [\"mdx-loader\"] }\" }\\n' +\n          'See more info here https://nextjs.org/docs/app/api-reference/next-config-js/turbo'\n      )\n\n      const rules: Record<string, TurbopackLoaderItem[]> = {}\n      for (const [ext, loaders] of Object.entries(\n        userConfig.experimental.turbo.loaders\n      )) {\n        rules['*' + ext] = loaders as TurbopackLoaderItem[]\n      }\n\n      userConfig.experimental.turbo.rules = rules\n    }\n\n    if (userConfig.experimental?.turbo) {\n      curLog.warn(\n        'The config property `experimental.turbo` is deprecated. Move this setting to `config.turbopack` or run `npx @next/codemod@latest next-experimental-turbo-to-turbopack .`'\n      )\n\n      // Merge the two configs, preferring values in `config.turbopack`.\n      userConfig.turbopack = {\n        ...userConfig.experimental.turbo,\n        ...userConfig.turbopack,\n      }\n      userConfig.experimental.turbopackMemoryLimit ??=\n        userConfig.experimental.turbo.memoryLimit\n      userConfig.experimental.turbopackMinify ??=\n        userConfig.experimental.turbo.minify\n      userConfig.experimental.turbopackTreeShaking ??=\n        userConfig.experimental.turbo.treeShaking\n      userConfig.experimental.turbopackSourceMaps ??=\n        userConfig.experimental.turbo.sourceMaps\n    }\n\n    if (userConfig.experimental?.useLightningcss) {\n      const { loadBindings } =\n        require('../build/swc') as typeof import('../build/swc')\n      const isLightningSupported = (await loadBindings())?.css?.lightning\n\n      if (!isLightningSupported) {\n        curLog.warn(\n          `experimental.useLightningcss is set, but the setting is disabled because next-swc/wasm does not support it yet.`\n        )\n        userConfig.experimental.useLightningcss = false\n      }\n    }\n\n    // serialize the regex config into string\n    if (userConfig?.htmlLimitedBots instanceof RegExp) {\n      // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n      userConfig.htmlLimitedBots = userConfig.htmlLimitedBots.source\n    }\n\n    enforceExperimentalFeatures(userConfig, {\n      isDefaultConfig: false,\n      configuredExperimentalFeatures,\n      debugPrerender,\n      phase,\n    })\n\n    const completeConfig = assignDefaults(\n      dir,\n      {\n        configOrigin: relative(dir, path),\n        configFile: path,\n        configFileName,\n        ...userConfig,\n      },\n      silent\n    ) as NextConfigComplete\n\n    const finalConfig = await applyModifyConfig(completeConfig, phase, silent)\n\n    // Cache the final result\n    configCache.set(cacheKey, {\n      config: finalConfig,\n      rawConfig: userConfigModule, // Store the original user config module\n      configuredExperimentalFeatures,\n    })\n\n    if (reportExperimentalFeatures) {\n      reportExperimentalFeatures(configuredExperimentalFeatures)\n    }\n\n    return finalConfig\n  } else {\n    const configBaseName = basename(CONFIG_FILES[0], extname(CONFIG_FILES[0]))\n    const unsupportedConfig = findUp.sync(\n      [\n        `${configBaseName}.cjs`,\n        `${configBaseName}.cts`,\n        `${configBaseName}.mts`,\n        `${configBaseName}.json`,\n        `${configBaseName}.jsx`,\n        `${configBaseName}.tsx`,\n      ],\n      { cwd: dir }\n    )\n    if (unsupportedConfig?.length) {\n      throw new Error(\n        `Configuring Next.js via '${basename(\n          unsupportedConfig\n        )}' is not supported. Please replace the file with 'next.config.js', 'next.config.mjs', or 'next.config.ts'.`\n      )\n    }\n  }\n\n  const clonedDefaultConfig = cloneObject(defaultConfig) as NextConfig\n\n  enforceExperimentalFeatures(clonedDefaultConfig, {\n    isDefaultConfig: true,\n    configuredExperimentalFeatures,\n    debugPrerender,\n    phase,\n  })\n\n  // always call assignDefaults to ensure settings like\n  // reactRoot can be updated correctly even with no next.config.js\n  const completeConfig = assignDefaults(\n    dir,\n    { ...clonedDefaultConfig, configFileName },\n    silent\n  ) as NextConfigComplete\n\n  setHttpClientAndAgentOptions(completeConfig)\n\n  const finalConfig = await applyModifyConfig(completeConfig, phase, silent)\n\n  // Cache the default config result\n  configCache.set(cacheKey, {\n    config: finalConfig,\n    rawConfig: clonedDefaultConfig,\n    configuredExperimentalFeatures,\n  })\n\n  if (reportExperimentalFeatures) {\n    reportExperimentalFeatures(configuredExperimentalFeatures)\n  }\n\n  return finalConfig\n}\n\nexport type ConfiguredExperimentalFeature = {\n  key: keyof ExperimentalConfig\n  value: ExperimentalConfig[keyof ExperimentalConfig]\n  reason?: string\n}\n\nfunction enforceExperimentalFeatures(\n  config: NextConfig,\n  options: {\n    isDefaultConfig: boolean\n    configuredExperimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n    debugPrerender: boolean | undefined\n    phase: string\n  }\n) {\n  const {\n    configuredExperimentalFeatures,\n    debugPrerender,\n    isDefaultConfig,\n    phase,\n  } = options\n\n  config.experimental ??= {}\n\n  if (\n    debugPrerender &&\n    (phase === PHASE_PRODUCTION_BUILD || phase === PHASE_EXPORT)\n  ) {\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      'serverSourceMaps',\n      true,\n      configuredExperimentalFeatures\n    )\n\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      process.env.TURBOPACK ? 'turbopackMinify' : 'serverMinification',\n      false,\n      configuredExperimentalFeatures\n    )\n\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      'enablePrerenderSourceMaps',\n      true,\n      configuredExperimentalFeatures\n    )\n\n    setExperimentalFeatureForDebugPrerender(\n      config.experimental,\n      'prerenderEarlyExit',\n      false,\n      configuredExperimentalFeatures\n    )\n  }\n\n  // TODO: Remove this once we've made Cache Components the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_CACHE_COMPONENTS === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.ppr === undefined ||\n      (isDefaultConfig && !config.experimental.ppr))\n  ) {\n    config.experimental.ppr = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'ppr',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_CACHE_COMPONENTS`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Cache Components the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_PPR === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.ppr === undefined ||\n      (isDefaultConfig && !config.experimental.ppr))\n  ) {\n    config.experimental.ppr = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'ppr',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_PPR`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Client Segment Cache the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_PPR === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.clientSegmentCache === undefined ||\n      (isDefaultConfig && !config.experimental.clientSegmentCache))\n  ) {\n    config.experimental.clientSegmentCache = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'clientSegmentCache',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_PPR`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Client Param Parsing the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_PPR === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.clientParamParsing === undefined ||\n      (isDefaultConfig && !config.experimental.clientParamParsing))\n  ) {\n    config.experimental.clientParamParsing = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'clientParamParsing',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_PPR`'\n      )\n    }\n  }\n\n  // TODO: Remove this once we've made Cache Components the default.\n  if (\n    process.env.__NEXT_EXPERIMENTAL_CACHE_COMPONENTS === 'true' &&\n    // We do respect an explicit value in the user config.\n    (config.experimental.cacheComponents === undefined ||\n      (isDefaultConfig && !config.experimental.cacheComponents))\n  ) {\n    config.experimental.cacheComponents = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'cacheComponents',\n        true,\n        'enabled by `__NEXT_EXPERIMENTAL_CACHE_COMPONENTS`'\n      )\n    }\n  }\n\n  if (\n    config.experimental.enablePrerenderSourceMaps === undefined &&\n    config.experimental.cacheComponents === true\n  ) {\n    config.experimental.enablePrerenderSourceMaps = true\n\n    if (configuredExperimentalFeatures) {\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        'enablePrerenderSourceMaps',\n        true,\n        'enabled by `experimental.cacheComponents`'\n      )\n    }\n  }\n}\n\nfunction addConfiguredExperimentalFeature<\n  KeyType extends keyof ExperimentalConfig,\n>(\n  configuredExperimentalFeatures: ConfiguredExperimentalFeature[],\n  key: KeyType,\n  value: ExperimentalConfig[KeyType],\n  reason?: string\n) {\n  if (value !== (defaultConfig.experimental as Record<string, unknown>)[key]) {\n    configuredExperimentalFeatures.push({ key, value, reason })\n  }\n}\n\nfunction setExperimentalFeatureForDebugPrerender<\n  KeyType extends keyof ExperimentalConfig,\n>(\n  experimentalConfig: ExperimentalConfig,\n  key: KeyType,\n  value: ExperimentalConfig[KeyType],\n  configuredExperimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n) {\n  if (experimentalConfig[key] !== value) {\n    experimentalConfig[key] = value\n\n    if (configuredExperimentalFeatures) {\n      const action =\n        value === true ? 'enabled' : value === false ? 'disabled' : 'set'\n\n      const reason = `${action} by \\`--debug-prerender\\``\n\n      addConfiguredExperimentalFeature(\n        configuredExperimentalFeatures,\n        key,\n        value,\n        reason\n      )\n    }\n  }\n}\n\nfunction cloneObject(obj: any): any {\n  // Primitives & null\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n\n  // RegExp → clone via constructor\n  if (obj instanceof RegExp) {\n    return new RegExp(obj.source, obj.flags)\n  }\n\n  // Function → just reuse the function reference\n  if (typeof obj === 'function') {\n    return obj\n  }\n\n  // Arrays → map each element\n  if (Array.isArray(obj)) {\n    return obj.map(cloneObject)\n  }\n\n  // Detect non‑plain objects (class instances)\n  const proto = Object.getPrototypeOf(obj)\n  const isPlainObject = proto === Object.prototype || proto === null\n\n  // If it's not a plain object, just return the original\n  if (!isPlainObject) {\n    return obj\n  }\n\n  // Plain object → create a new object with the same prototype\n  // and copy all properties, cloning data properties and keeping\n  // accessor properties (getters/setters) as‑is.\n  const result = Object.create(proto)\n  for (const key of Reflect.ownKeys(obj)) {\n    const descriptor = Object.getOwnPropertyDescriptor(obj, key)\n\n    if (descriptor && (descriptor.get || descriptor.set)) {\n      // Accessor property → copy descriptor as‑is (get/set functions)\n      Object.defineProperty(result, key, descriptor)\n    } else {\n      // Data property → clone the value\n      result[key] = cloneObject(obj[key])\n    }\n  }\n\n  return result\n}\n"], "names": ["existsSync", "basename", "extname", "join", "relative", "isAbsolute", "resolve", "pathToFileURL", "findUp", "Log", "ciEnvironment", "CONFIG_FILES", "PHASE_DEVELOPMENT_SERVER", "PHASE_EXPORT", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "defaultConfig", "normalizeConfig", "loadWebpackHook", "imageConfigDefault", "loadEnvConfig", "updateInitialEnv", "flushAndExit", "findRootDir", "setHttpClientAndAgentOptions", "pathHasPrefix", "matchRemotePattern", "hasNextSupport", "transpileConfig", "dset", "normalizeZodErrors", "HTML_LIMITED_BOT_UA_RE_STRING", "findDir", "CanaryOnlyError", "isStableBuild", "interopDefault", "djb2Hash", "normalizeNextConfigZodErrors", "error", "shouldExit", "issues", "flatMap", "issue", "message", "path", "warnOptionHasBeenDeprecated", "config", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "reason", "silent", "hasWarned", "current", "found", "nestedPropertyKeys", "split", "key", "undefined", "warnOnce", "checkDeprecations", "userConfig", "configFileName", "dir", "experimental", "dynamicIO", "i18n", "hasAppDir", "Boolean", "warnOptionHasBeenMovedOutOfExperimental", "oldExperimentalKey", "new<PERSON>ey", "warn", "newKeys", "length", "shift", "warnCustomizedOption", "defaultValue", "customMessage", "segs", "seg", "assignDefaults", "result", "exportTrailingSlash", "trailingSlash", "cacheComponents", "Object", "keys", "reduce", "currentConfig", "value", "Error", "userDistDir", "trim", "Array", "isArray", "for<PERSON>ach", "ext", "constructor", "c", "k", "v", "trustHostHeader", "allowDevelopmentBuild", "process", "env", "NODE_ENV", "ppr", "feature", "turbopackPersistentCaching", "output", "rewrites", "redirects", "headers", "assetPrefix", "basePath", "startsWith", "endsWith", "amp", "canonicalBase", "images", "localPatterns", "hasMatch", "some", "pattern", "pathname", "search", "push", "remotePatterns", "map", "protocol", "hostname", "port", "proto", "replace", "includes", "url", "URL", "hasMatchForAssetPrefix", "domains", "loader", "loaderFile", "absolutePath", "devIndicators", "buildActivityPosition", "position", "outputStandalone", "serverActions", "bodySizeLimit", "parseInt", "toString", "isNaN", "outputFileTracingRoot", "turbopack", "root", "NEXT_DEPLOYMENT_ID", "deploymentId", "tracingRoot", "turbopackRoot", "rootDir", "i18nType", "locales", "defaultLocaleType", "defaultLocale", "invalidDomainItems", "filter", "item", "domain", "console", "defaultLocaleDuplicate", "find", "altItem", "hasInvalidLocale", "locale", "domainItem", "JSON", "stringify", "invalidLocales", "String", "normalizedLocales", "Set", "duplicateLocales", "localeLower", "toLowerCase", "has", "add", "size", "localeDetectionType", "localeDetection", "<PERSON><PERSON><PERSON><PERSON>", "cacheLife", "defaultDefault", "revalidate", "expire", "staleTimes", "static", "defaultCacheLifeProfile", "stale", "staticStaleTime", "expireTime", "cacheHandlers", "allowedHandlerNameRegex", "handler<PERSON>eys", "invalidHandlerItems", "test", "handler<PERSON><PERSON>", "userProvidedModularizeImports", "modularizeImports", "transform", "lodash", "userProvidedOptimizePackageImports", "optimizePackageImports", "htmlLimitedBots", "useCache", "applyModifyConfig", "phase", "adapterPath", "adapterMod", "require", "href", "modifyConfig", "info", "name", "config<PERSON><PERSON>", "Map", "get<PERSON><PERSON><PERSON><PERSON>", "customConfig", "reactProductionProfiling", "debugPrerender", "keyData", "hasCustomConfig", "loadConfig", "rawConfig", "reportExperimentalFeatures", "cache<PERSON>ey", "cachedResult", "get", "configuredExperimentalFeatures", "__NEXT_PRIVATE_RENDER_WORKER", "err", "__NEXT_PRIVATE_STANDALONE_CONFIG", "standaloneConfig", "parse", "set", "curLog", "config<PERSON><PERSON><PERSON>", "cwd", "userConfigModule", "envBefore", "assign", "__NEXT_TEST_MODE", "nextConfigPath", "newEnv", "loadedConfig", "freeze", "TURBOPACK", "addConfiguredExperimentalFeature", "cloneObject", "isRootProcess", "send", "NEXT_MINIMAL", "configSchema", "state", "safeParse", "success", "messages", "errorMessages", "target", "slice", "turbo", "loaders", "rules", "entries", "turbopackMemoryLimit", "memoryLimit", "turbopackMinify", "minify", "turbopackTreeShaking", "treeShaking", "turbopackSourceMaps", "sourceMaps", "useLightningcss", "loadBindings", "isLightningSupported", "css", "lightning", "RegExp", "source", "enforceExperimentalFeatures", "isDefaultConfig", "completeConfig", "configFile", "finalConfig", "configBaseName", "unsupportedConfig", "sync", "clonedDefaultConfig", "options", "setExperimentalFeatureForDebugPrerender", "__NEXT_EXPERIMENTAL_CACHE_COMPONENTS", "__NEXT_EXPERIMENTAL_PPR", "clientSegmentCache", "clientParamParsing", "enablePrerenderSourceMaps", "experimentalConfig", "action", "obj", "flags", "getPrototypeOf", "isPlainObject", "prototype", "create", "Reflect", "ownKeys", "descriptor", "getOwnPropertyDescriptor", "defineProperty"], "mappings": "AAAA,SAASA,UAAU,QAAQ,KAAI;AAC/B,SAASC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAC7E,SAASC,aAAa,QAAQ,MAAK;AACnC,OAAOC,YAAY,6BAA4B;AAC/C,YAAYC,SAAS,sBAAqB;AAC1C,YAAYC,mBAAmB,oBAAmB;AAClD,SACEC,YAAY,EACZC,wBAAwB,EACxBC,YAAY,EACZC,sBAAsB,EACtBC,uBAAuB,QAClB,0BAAyB;AAChC,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAiB;AAShE,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,kBAAkB,QAAQ,6BAA4B;AAE/D,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAW;AAC3D,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,4BAA4B,QAAQ,yBAAwB;AACrE,SAASC,aAAa,QAAQ,6CAA4C;AAC1E,SAASC,kBAAkB,QAAQ,qCAAoC;AAGvE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,IAAI,QAAQ,qBAAoB;AACzC,SAASC,kBAAkB,QAAQ,oBAAmB;AACtD,SAASC,6BAA6B,QAAQ,oCAAmC;AACjF,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,eAAe,EAAEC,aAAa,QAAQ,4BAA2B;AAC1E,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,QAAQ,QAAQ,qBAAoB;AAE7C,SAASnB,eAAe,QAAQ,kBAAiB;AAGjD,SAASoB,6BACPC,KAA2B;IAE3B,IAAIC,aAAa;IACjB,MAAMC,SAASV,mBAAmBQ;IAClC,OAAO;QACLE,OAAOC,OAAO,CAAC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;YAChC,IAAID,MAAME,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9B,oEAAoE;gBACpEL,aAAa;YACf;YAEA,OAAOI;QACT;QACAJ;KACD;AACH;AAEA,OAAO,SAASM,4BACdC,MAAkB,EAClBC,iBAAyB,EACzBC,MAAc,EACdC,MAAe;IAEf,IAAIC,YAAY;IAChB,IAAI,CAACD,QAAQ;QACX,IAAIE,UAAUL;QACd,IAAIM,QAAQ;QACZ,MAAMC,qBAAqBN,kBAAkBO,KAAK,CAAC;QACnD,KAAK,MAAMC,OAAOF,mBAAoB;YACpC,IAAIF,OAAO,CAACI,IAAI,KAAKC,WAAW;gBAC9BL,UAAUA,OAAO,CAACI,IAAI;YACxB,OAAO;gBACLH,QAAQ;gBACR;YACF;QACF;QACA,IAAIA,OAAO;YACT3C,IAAIgD,QAAQ,CAACT;YACbE,YAAY;QACd;IACF;IACA,OAAOA;AACT;AAEA,SAASQ,kBACPC,UAAsB,EACtBC,cAAsB,EACtBX,MAAe,EACfY,GAAW;QAgBPF;IAdJd,4BACEc,YACA,OACA,CAAC,sGAAsG,CAAC,EACxGV;IAGFJ,4BACEc,YACA,oBACA,CAAC,mHAAmH,CAAC,EACrHV;IAGF,IAAIU,EAAAA,2BAAAA,WAAWG,YAAY,qBAAvBH,yBAAyBI,SAAS,MAAKP,WAAW;QACpDX,4BACEc,YACA,0BACA,CAAC,oGAAoG,EAAEC,eAAe,kBAAkB,CAAC,EACzIX;IAEJ;IAEAJ,4BACEc,YACA,oCACA,CAAC,yIAAyI,EAAEC,eAAe,CAAC,CAAC,EAC7JX;IAGFJ,4BACEc,YACA,sBACA,CAAC,8GAA8G,EAAEC,eAAe,CAAC,CAAC,EAClIX;IAGFJ,4BACEc,YACA,8BACA,CAAC,+FAA+F,EAAEC,eAAe,CAAC,CAAC,EACnHX;IAGFJ,4BACEc,YACA,+BACA,CAAC,gGAAgG,EAAEC,eAAe,CAAC,CAAC,EACpHX;IAGFJ,4BACEc,YACA,uCACA,CAAC,2GAA2G,EAAEC,eAAe,kBAAkB,CAAC,EAChJX;IAGF,kCAAkC;IAClC,IAAIU,WAAWK,IAAI,EAAE;QACnB,MAAMC,YAAYC,QAAQlC,QAAQ6B,KAAK;QACvC,IAAII,WAAW;YACbpB,4BACEc,YACA,QACA,CAAC,sBAAsB,EAAEC,eAAe,uKAAuK,CAAC,EAChNX;QAEJ;IACF;AACF;AAEA,OAAO,SAASkB,wCACdrB,MAAkB,EAClBsB,kBAA0B,EAC1BC,MAAc,EACdT,cAAsB,EACtBX,MAAe;IAEf,IAAIH,OAAOgB,YAAY,IAAIM,sBAAsBtB,OAAOgB,YAAY,EAAE;QACpE,IAAI,CAACb,QAAQ;YACXxC,IAAI6D,IAAI,CACN,CAAC,eAAe,EAAEF,mBAAmB,uBAAuB,EAAEC,OAAO,IAAI,CAAC,GACxE,CAAC,mBAAmB,EAAET,eAAe,kBAAkB,CAAC;QAE9D;QAEA,IAAIT,UAAUL;QACd,MAAMyB,UAAUF,OAAOf,KAAK,CAAC;QAC7B,MAAOiB,QAAQC,MAAM,GAAG,EAAG;YACzB,MAAMjB,MAAMgB,QAAQE,KAAK;YACzBtB,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,CAAC;YAChCJ,UAAUA,OAAO,CAACI,IAAI;QACxB;QACAJ,OAAO,CAACoB,QAAQE,KAAK,GAAI,GAAG,AAAC3B,OAAOgB,YAAY,AAAQ,CAACM,mBAAmB;IAC9E;IAEA,OAAOtB;AACT;AAEA,SAAS4B,qBACP5B,MAAkB,EAClBS,GAAW,EACXoB,YAAiB,EACjBC,aAAqB,EACrBhB,cAAsB,EACtBX,MAAe;IAEf,MAAM4B,OAAOtB,IAAID,KAAK,CAAC;IACvB,IAAIH,UAAUL;IAEd,MAAO+B,KAAKL,MAAM,IAAI,EAAG;QACvB,MAAMM,MAAMD,KAAKJ,KAAK;QACtB,IAAI,CAAEK,CAAAA,OAAO3B,OAAM,GAAI;YACrB;QACF;QACAA,UAAUA,OAAO,CAAC2B,IAAI;IACxB;IAEA,IAAI,CAAC7B,UAAUE,YAAYwB,cAAc;QACvClE,IAAI6D,IAAI,CACN,CAAC,KAAK,EAAEf,IAAI,4BAA4B,EAAEqB,gBAAgBA,gBAAgB,OAAO,GAAG,+BAA+B,EAAEhB,eAAe,CAAC,CAAC;IAE1I;AACF;AAEA,SAASmB,eACPlB,GAAW,EACXF,UAAmD,EACnDV,MAAe;QAgBXU,0BAsGCqB,sBAKHA,uBA0VOA,oCAAAA,uBA8CLA,mBAekBA,oBAwLgBA,uBAmDlCA,uBA6DFA;IAxzBF,MAAMpB,iBAAiBD,WAAWC,cAAc;IAChD,IAAI,OAAOD,WAAWsB,mBAAmB,KAAK,aAAa;QACzD,IAAI,CAAChC,QAAQ;YACXxC,IAAI6D,IAAI,CACN,CAAC,yFAAyF,EAAEV,eAAe,CAAC,CAAC;QAEjH;QACA,IAAI,OAAOD,WAAWuB,aAAa,KAAK,aAAa;YACnDvB,WAAWuB,aAAa,GAAGvB,WAAWsB,mBAAmB;QAC3D;QACA,OAAOtB,WAAWsB,mBAAmB;IACvC;IAEA,6EAA6E;IAC7E,IAAItB,EAAAA,2BAAAA,WAAWG,YAAY,qBAAvBH,yBAAyBI,SAAS,MAAKP,WAAW;YAGhDG;QAFJ,8FAA8F;QAC9F,oFAAoF;QACpF,IAAIA,EAAAA,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyBwB,eAAe,MAAK3B,WAAW;YAC1DG,WAAWG,YAAY,CAACqB,eAAe,GACrCxB,WAAWG,YAAY,CAACC,SAAS;QACrC;QAEA,iCAAiC;QACjC,OAAOJ,WAAWG,YAAY,CAACC,SAAS;IAC1C;IAEA,MAAMjB,SAASsC,OAAOC,IAAI,CAAC1B,YAAY2B,MAAM,CAC3C,CAACC,eAAehC;QACd,MAAMiC,QAAQ7B,UAAU,CAACJ,IAAI;QAE7B,IAAIiC,UAAUhC,aAAagC,UAAU,MAAM;YACzC,OAAOD;QACT;QAEA,IAAIhC,QAAQ,WAAW;YACrB,IAAI,OAAOiC,UAAU,UAAU;gBAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,+CAA+C,EAAE,OAAOD,MAAM,CAAC,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,cAAcF,MAAMG,IAAI;YAE9B,qEAAqE;YACrE,eAAe;YACf,IAAID,gBAAgB,UAAU;gBAC5B,MAAM,qBAEL,CAFK,IAAID,MACR,CAAC,4IAA4I,CAAC,GAD1I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,2EAA2E;YAC3E,8CAA8C;YAC9C,IAAIC,YAAYlB,MAAM,KAAK,GAAG;gBAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,8GAA8G,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAIlC,QAAQ,kBAAkB;YAC5B,IAAI,CAACqC,MAAMC,OAAO,CAACL,QAAQ;gBACzB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,4DAA4D,EAAED,MAAM,0CAA0C,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACA,MAAMhB,MAAM,EAAE;gBACjB,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,uGAAuG,CAAC,GADrG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAD,MAAMM,OAAO,CAAC,CAACC;gBACb,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,4DAA4D,EAAEM,IAAI,WAAW,EAAE,OAAOA,IAAI,0CAA0C,CAAC,GADlI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,MAAMpB,eAAe,AAAC3D,aAAyC,CAACuC,IAAI;QAEpE,IACE,CAAC,CAACiC,SACFA,MAAMQ,WAAW,KAAKZ,UACtB,OAAOT,iBAAiB,UACxB;YACAY,aAAa,CAAChC,IAAI,GAAG;gBACnB,GAAGoB,YAAY;gBACf,GAAGS,OAAOC,IAAI,CAACG,OAAOF,MAAM,CAAM,CAACW,GAAGC;oBACpC,MAAMC,IAAIX,KAAK,CAACU,EAAE;oBAClB,IAAIC,MAAM3C,aAAa2C,MAAM,MAAM;wBACjCF,CAAC,CAACC,EAAE,GAAGC;oBACT;oBACA,OAAOF;gBACT,GAAG,CAAC,EAAE;YACR;QACF,OAAO;YACLV,aAAa,CAAChC,IAAI,GAAGiC;QACvB;QAEA,OAAOD;IACT,GACA,CAAC;IAGH,MAAMP,SAAS;QACb,GAAGhE,aAAa;QAChB,GAAG8B,MAAM;QACTgB,cAAc;YACZ,GAAG9C,cAAc8C,YAAY;YAC7B,GAAGhB,OAAOgB,YAAY;QACxB;IACF;IAEA,qEAAqE;IACrE,IAAI,GAACkB,uBAAAA,OAAOlB,YAAY,qBAAnBkB,qBAAqBoB,eAAe,KAAI1F,cAAciB,cAAc,EAAE;QACzEqD,OAAOlB,YAAY,CAACsC,eAAe,GAAG;IACxC;IAEA,IACEpB,EAAAA,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBqB,qBAAqB,KAC1CC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;QACA,MAAM,qBAEL,CAFK,IAAIf,MACR,CAAC,sGAAsG,CAAC,GADpG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIvD,iBAAiB;YAEf8C,uBAEOA,uBAEAA;QALX,oEAAoE;QACpE,KAAIA,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqByB,GAAG,EAAE;YAC5B,MAAM,qBAAoD,CAApD,IAAIxE,gBAAgB;gBAAEyE,SAAS;YAAmB,IAAlD,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D,OAAO,KAAI1B,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBG,eAAe,EAAE;YAC/C,MAAM,qBAAgE,CAAhE,IAAIlD,gBAAgB;gBAAEyE,SAAS;YAA+B,IAA9D,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE,OAAO,KAAI1B,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqB2B,0BAA0B,EAAE;YAC1D,MAAM,qBAEJ,CAFI,IAAI1E,gBAAgB;gBACxByE,SAAS;YACX,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;IACF;IAEA,IAAI1B,OAAO4B,MAAM,KAAK,UAAU;QAC9B,IAAI5B,OAAOhB,IAAI,EAAE;YACf,MAAM,qBAEL,CAFK,IAAIyB,MACR,+HADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAAC9D,gBAAgB;YACnB,IAAIqD,OAAO6B,QAAQ,EAAE;gBACnBpG,IAAI6D,IAAI,CACN;YAEJ;YACA,IAAIU,OAAO8B,SAAS,EAAE;gBACpBrG,IAAI6D,IAAI,CACN;YAEJ;YACA,IAAIU,OAAO+B,OAAO,EAAE;gBAClBtG,IAAI6D,IAAI,CACN;YAEJ;QACF;IACF;IAEA,IAAI,OAAOU,OAAOgC,WAAW,KAAK,UAAU;QAC1C,MAAM,qBAEL,CAFK,IAAIvB,MACR,CAAC,mDAAmD,EAAE,OAAOT,OAAOgC,WAAW,CAAC,sDAAsD,CAAC,GADnI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOhC,OAAOiC,QAAQ,KAAK,UAAU;QACvC,MAAM,qBAEL,CAFK,IAAIxB,MACR,CAAC,gDAAgD,EAAE,OAAOT,OAAOiC,QAAQ,CAAC,CAAC,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIjC,OAAOiC,QAAQ,KAAK,IAAI;QAC1B,IAAIjC,OAAOiC,QAAQ,KAAK,KAAK;YAC3B,MAAM,qBAEL,CAFK,IAAIxB,MACR,CAAC,iFAAiF,CAAC,GAD/E,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACT,OAAOiC,QAAQ,CAACC,UAAU,CAAC,MAAM;YACpC,MAAM,qBAEL,CAFK,IAAIzB,MACR,CAAC,iDAAiD,EAAET,OAAOiC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIjC,OAAOiC,QAAQ,KAAK,KAAK;gBAWvBjC;YAVJ,IAAIA,OAAOiC,QAAQ,CAACE,QAAQ,CAAC,MAAM;gBACjC,MAAM,qBAEL,CAFK,IAAI1B,MACR,CAAC,iDAAiD,EAAET,OAAOiC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIjC,OAAOgC,WAAW,KAAK,IAAI;gBAC7BhC,OAAOgC,WAAW,GAAGhC,OAAOiC,QAAQ;YACtC;YAEA,IAAIjC,EAAAA,cAAAA,OAAOoC,GAAG,qBAAVpC,YAAYqC,aAAa,MAAK,IAAI;gBACpCrC,OAAOoC,GAAG,CAACC,aAAa,GAAGrC,OAAOiC,QAAQ;YAC5C;QACF;IACF;IAEA,IAAIjC,0BAAAA,OAAQsC,MAAM,EAAE;QAClB,MAAMA,SAAsBtC,OAAOsC,MAAM;QAEzC,IAAI,OAAOA,WAAW,UAAU;YAC9B,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,8CAA8C,EAAE,OAAO6B,OAAO,6EAA6E,CAAC,GADzI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,OAAOC,aAAa,EAAE;YACxB,IAAI,CAAC3B,MAAMC,OAAO,CAACyB,OAAOC,aAAa,GAAG;gBACxC,MAAM,qBAEL,CAFK,IAAI9B,MACR,CAAC,2DAA2D,EAAE,OAAO6B,OAAOC,aAAa,CAAC,6EAA6E,CAAC,GADpK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,6DAA6D;YAC7D,MAAMC,WAAWF,OAAOC,aAAa,CAACE,IAAI,CACxC,CAACC,UACCA,QAAQC,QAAQ,KAAK,4BAA4BD,QAAQE,MAAM,KAAK;YAExE,IAAI,CAACJ,UAAU;gBACb,iDAAiD;gBACjDF,OAAOC,aAAa,CAACM,IAAI,CAAC;oBACxBF,UAAU;oBACVC,QAAQ;gBACV;YACF;QACF;QAEA,IAAIN,OAAOQ,cAAc,EAAE;gBA+BrBhF;YA9BJ,IAAI,CAAC8C,MAAMC,OAAO,CAACyB,OAAOQ,cAAc,GAAG;gBACzC,MAAM,qBAEL,CAFK,IAAIrC,MACR,CAAC,4DAA4D,EAAE,OAAO6B,OAAOQ,cAAc,CAAC,6EAA6E,CAAC,GADtK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,6EAA6E;YAC7E,4EAA4E;YAC5E,0DAA0D;YAC1DR,OAAOQ,cAAc,GAAGR,OAAOQ,cAAc,CAACC,GAAG,CAC/C,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEP,QAAQ,EAAEC,MAAM,EAAE;gBAC7C,MAAMO,QAAQH,4BAAAA,SAAUI,OAAO,CAAC,MAAM;gBACtC,IAAI,CAAC;oBAAC;oBAAQ;oBAAS5E;iBAAU,CAAC6E,QAAQ,CAACF,QAAQ;oBACjD,MAAM,qBAEL,CAFK,IAAI1C,MACR,CAAC,+EAA+E,EAAE0C,MAAM,EAAE,CAAC,GADvF,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,OAAO;oBACLH,UAAUG;oBACVF;oBACAC;oBACAP;oBACAC;gBACF;YACF;YAGF,4DAA4D;YAC5D,2DAA2D;YAC3D,gBAAgB;YAChB,KAAI9E,sBAAAA,OAAOkE,WAAW,qBAAlBlE,oBAAoBoE,UAAU,CAAC,SAAS;gBAC1C,IAAI;oBACF,MAAMoB,MAAM,IAAIC,IAAIzF,OAAOkE,WAAW;oBACtC,MAAMwB,yBAAyBlB,OAAOQ,cAAc,CAACL,IAAI,CAAC,CAACC,UACzDhG,mBAAmBgG,SAASY;oBAG9B,qEAAqE;oBACrE,IAAI,CAACE,wBAAwB;wBAC3BlB,OAAOQ,cAAc,CAACD,IAAI,CAAC;4BACzBI,UAAUK,IAAIL,QAAQ;4BACtBD,UAAUM,IAAIN,QAAQ,CAACI,OAAO,CAAC,MAAM;4BACrCF,MAAMI,IAAIJ,IAAI;wBAChB;oBACF;gBACF,EAAE,OAAO5F,OAAO;oBACd,MAAM,qBAEL,CAFK,IAAImD,MACR,CAAC,8CAA8C,EAAEnD,OAAO,GADpD,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAIgF,OAAOmB,OAAO,EAAE;YAClB,IAAI,CAAC7C,MAAMC,OAAO,CAACyB,OAAOmB,OAAO,GAAG;gBAClC,MAAM,qBAEL,CAFK,IAAIhD,MACR,CAAC,qDAAqD,EAAE,OAAO6B,OAAOmB,OAAO,CAAC,6EAA6E,CAAC,GADxJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI,CAACnB,OAAOoB,MAAM,EAAE;YAClBpB,OAAOoB,MAAM,GAAG;QAClB;QAEA,IACEpB,OAAOoB,MAAM,KAAK,aAClBpB,OAAOoB,MAAM,KAAK,YAClBpB,OAAO1E,IAAI,KAAKzB,mBAAmByB,IAAI,EACvC;YACA,MAAM,qBAEL,CAFK,IAAI6C,MACR,CAAC,kCAAkC,EAAE6B,OAAOoB,MAAM,CAAC,sKAAsK,CAAC,GADtN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IACEpB,OAAO1E,IAAI,KAAKzB,mBAAmByB,IAAI,IACvCoC,OAAOiC,QAAQ,IACf,CAACxF,cAAc6F,OAAO1E,IAAI,EAAEoC,OAAOiC,QAAQ,GAC3C;YACAK,OAAO1E,IAAI,GAAG,GAAGoC,OAAOiC,QAAQ,GAAGK,OAAO1E,IAAI,EAAE;QAClD;QAEA,8EAA8E;QAC9E,IACE0E,OAAO1E,IAAI,IACX,CAAC0E,OAAO1E,IAAI,CAACuE,QAAQ,CAAC,QACrBG,CAAAA,OAAOoB,MAAM,KAAK,aAAa1D,OAAOE,aAAa,AAAD,GACnD;YACAoC,OAAO1E,IAAI,IAAI;QACjB;QAEA,IAAI0E,OAAOqB,UAAU,EAAE;YACrB,IAAIrB,OAAOoB,MAAM,KAAK,aAAapB,OAAOoB,MAAM,KAAK,UAAU;gBAC7D,MAAM,qBAEL,CAFK,IAAIjD,MACR,CAAC,kCAAkC,EAAE6B,OAAOoB,MAAM,CAAC,uFAAuF,CAAC,GADvI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,eAAezI,KAAK0D,KAAKyD,OAAOqB,UAAU;YAChD,IAAI,CAAC3I,WAAW4I,eAAe;gBAC7B,MAAM,qBAEL,CAFK,IAAInD,MACR,CAAC,+CAA+C,EAAEmD,aAAa,EAAE,CAAC,GAD9D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAtB,OAAOqB,UAAU,GAAGC;QACtB;IACF;IAEAlE,qBACEM,QACA,6BACA,MACA,mGACApB,gBACAX;IAGF,wFAAwF;IACxF,IACE+B,OAAO6D,aAAa,IACpB,OAAO7D,OAAO6D,aAAa,KAAK,YAChC,2BAA2B7D,OAAO6D,aAAa,IAC/C7D,OAAO6D,aAAa,CAACC,qBAAqB,KAAK9D,OAAO6D,aAAa,CAACE,QAAQ,EAC5E;QACA,IAAI,CAAC9F,QAAQ;YACXxC,IAAIgD,QAAQ,CACV,CAAC,yDAAyD,EAAEuB,OAAO6D,aAAa,CAACC,qBAAqB,CAAC,iCAAiC,EAAE9D,OAAO6D,aAAa,CAACE,QAAQ,CAAC,sCAAsC,EAAE/D,OAAO6D,aAAa,CAACC,qBAAqB,CAAC,8BAA8B,CAAC;QAE9R;QACA9D,OAAO6D,aAAa,CAACE,QAAQ,GAAG/D,OAAO6D,aAAa,CAACC,qBAAqB;IAC5E;IAEA3E,wCACEa,QACA,wBACA,iCACApB,gBACAX;IAEFkB,wCACEa,QACA,oCACA,0BACApB,gBACAX;IAEFkB,wCACEa,QACA,SACA,kBACApB,gBACAX;IAEFkB,wCACEa,QACA,oBACA,6BACApB,gBACAX;IAEFkB,wCACEa,QACA,WACA,oBACApB,gBACAX;IAEFkB,wCACEa,QACA,yBACA,kCACApB,gBACAX;IAEFkB,wCACEa,QACA,iBACA,0BACApB,gBACAX;IAEFkB,wCACEa,QACA,YACA,cACApB,gBACAX;IAEFkB,wCACEa,QACA,eACA,eACApB,gBACAX;IAEFkB,wCACEa,QACA,yBACA,yBACApB,gBACAX;IAEFkB,wCACEa,QACA,6BACA,6BACApB,gBACAX;IAEFkB,wCACEa,QACA,6BACA,6BACApB,gBACAX;IAGF,IAAI,AAAC+B,OAAOlB,YAAY,CAASkF,gBAAgB,EAAE;QACjD,IAAI,CAAC/F,QAAQ;YACXxC,IAAI6D,IAAI,CACN,CAAC,iGAAiG,CAAC;QAEvG;QACAU,OAAO4B,MAAM,GAAG;IAClB;IAEA,IACE,SAAO5B,wBAAAA,OAAOlB,YAAY,sBAAnBkB,qCAAAA,sBAAqBiE,aAAa,qBAAlCjE,mCAAoCkE,aAAa,MAAK,aAC7D;YAEElE;QADF,MAAMQ,QAAQ2D,UACZnE,sCAAAA,OAAOlB,YAAY,CAACmF,aAAa,qBAAjCjE,oCAAmCkE,aAAa,CAACE,QAAQ;QAE3D,IAAIC,MAAM7D,UAAUA,QAAQ,GAAG;YAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,8KADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEAtB,wCACEa,QACA,qBACA,qBACApB,gBACAX;IAEFkB,wCACEa,QACA,8BACA,8BACApB,gBACAX;IAEFkB,wCACEa,QACA,6BACA,6BACApB,gBACAX;IAGF,IACE+B,CAAAA,0BAAAA,OAAQsE,qBAAqB,KAC7B,CAACjJ,WAAW2E,OAAOsE,qBAAqB,GACxC;QACAtE,OAAOsE,qBAAqB,GAAGhJ,QAAQ0E,OAAOsE,qBAAqB;QACnE,IAAI,CAACrG,QAAQ;YACXxC,IAAI6D,IAAI,CACN,CAAC,iDAAiD,EAAEU,OAAOsE,qBAAqB,EAAE;QAEtF;IACF;IAEA,IAAItE,CAAAA,2BAAAA,oBAAAA,OAAQuE,SAAS,qBAAjBvE,kBAAmBwE,IAAI,KAAI,CAACnJ,WAAW2E,OAAOuE,SAAS,CAACC,IAAI,GAAG;QACjExE,OAAOuE,SAAS,CAACC,IAAI,GAAGlJ,QAAQ0E,OAAOuE,SAAS,CAACC,IAAI;QACrD,IAAI,CAACvG,QAAQ;YACXxC,IAAI6D,IAAI,CACN,CAAC,0CAA0C,EAAEU,OAAOuE,SAAS,CAACC,IAAI,EAAE;QAExE;IACF;IAEA,6BAA6B;IAC7B,IAAIlD,QAAQC,GAAG,CAACkD,kBAAkB,EAAE;QAClCzE,OAAO0E,YAAY,GAAGpD,QAAQC,GAAG,CAACkD,kBAAkB;IACtD;IAEA,MAAME,cAAc3E,0BAAAA,OAAQsE,qBAAqB;IACjD,MAAMM,gBAAgB5E,2BAAAA,qBAAAA,OAAQuE,SAAS,qBAAjBvE,mBAAmBwE,IAAI;IAE7C,4EAA4E;IAC5E,IAAIG,eAAeC,iBAAiBD,gBAAgBC,eAAe;QACjEnJ,IAAI6D,IAAI,CACN,CAAC,mGAAmG,CAAC,GACnG,CAAC,uCAAuC,EAAEqF,YAAY,CAAC,CAAC;IAE9D;IAEA,MAAME,UAAUF,eAAeC,iBAAiBrI,YAAYsC;IAE5D,IAAI,CAACgG,SAAS;QACZ,MAAM,qBAEL,CAFK,IAAIpE,MACR,gFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,mDAAmD;IACnDT,OAAOsE,qBAAqB,GAAGO;IAC/BhI,KAAKmD,QAAQ;QAAC;QAAa;KAAO,EAAE6E;IAEpCrI,6BAA6BwD,UAAUhE;IAEvC,IAAIgE,OAAOhB,IAAI,EAAE;QACf,MAAM,EAAEA,IAAI,EAAE,GAAGgB;QACjB,MAAM8E,WAAW,OAAO9F;QAExB,IAAI8F,aAAa,UAAU;YACzB,MAAM,qBAEL,CAFK,IAAIrE,MACR,CAAC,4CAA4C,EAAEqE,SAAS,2EAA2E,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAAClE,MAAMC,OAAO,CAAC7B,KAAK+F,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAItE,MACR,CAAC,mDAAmD,EAAE,OAAOzB,KAAK+F,OAAO,CAAC,2EAA2E,CAAC,GADlJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI/F,KAAK+F,OAAO,CAACvF,MAAM,GAAG,OAAO,CAACvB,QAAQ;YACxCxC,IAAI6D,IAAI,CACN,CAAC,SAAS,EAAEN,KAAK+F,OAAO,CAACvF,MAAM,CAAC,mLAAmL,CAAC;QAExN;QAEA,MAAMwF,oBAAoB,OAAOhG,KAAKiG,aAAa;QAEnD,IAAI,CAACjG,KAAKiG,aAAa,IAAID,sBAAsB,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAIvE,MACR,CAAC,0HAA0H,CAAC,GADxH,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,OAAOzB,KAAKyE,OAAO,KAAK,eAAe,CAAC7C,MAAMC,OAAO,CAAC7B,KAAKyE,OAAO,GAAG;YACvE,MAAM,qBAEL,CAFK,IAAIhD,MACR,CAAC,2IAA2I,EAAE,OAAOzB,KAAKyE,OAAO,CAAC,2EAA2E,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIzE,KAAKyE,OAAO,EAAE;YAChB,MAAMyB,qBAAqBlG,KAAKyE,OAAO,CAAC0B,MAAM,CAAC,CAACC;oBAYfpG;gBAX/B,IAAI,CAACoG,QAAQ,OAAOA,SAAS,UAAU,OAAO;gBAC9C,IAAI,CAACA,KAAKH,aAAa,EAAE,OAAO;gBAChC,IAAI,CAACG,KAAKC,MAAM,IAAI,OAAOD,KAAKC,MAAM,KAAK,UAAU,OAAO;gBAE5D,IAAID,KAAKC,MAAM,CAAChC,QAAQ,CAAC,MAAM;oBAC7BiC,QAAQhG,IAAI,CACV,CAAC,cAAc,EAAE8F,KAAKC,MAAM,CAAC,2GAA2G,CAAC;oBAE3I,OAAO;gBACT;gBAEA,MAAME,0BAAyBvG,gBAAAA,KAAKyE,OAAO,qBAAZzE,cAAcwG,IAAI,CAC/C,CAACC,UACCA,QAAQR,aAAa,KAAKG,KAAKH,aAAa,IAC5CQ,QAAQJ,MAAM,KAAKD,KAAKC,MAAM;gBAGlC,IAAI,CAACpH,UAAUsH,wBAAwB;oBACrCD,QAAQhG,IAAI,CACV,CAAC,KAAK,EAAE8F,KAAKC,MAAM,CAAC,KAAK,EAAEE,uBAAuBF,MAAM,CAAC,8BAA8B,EAAED,KAAKH,aAAa,CAAC,+DAA+D,CAAC;oBAE9K,OAAO;gBACT;gBAEA,IAAIS,mBAAmB;gBAEvB,IAAI9E,MAAMC,OAAO,CAACuE,KAAKL,OAAO,GAAG;oBAC/B,KAAK,MAAMY,UAAUP,KAAKL,OAAO,CAAE;wBACjC,IAAI,OAAOY,WAAW,UAAUD,mBAAmB;wBAEnD,KAAK,MAAME,cAAc5G,KAAKyE,OAAO,IAAI,EAAE,CAAE;4BAC3C,IAAImC,eAAeR,MAAM;4BACzB,IAAIQ,WAAWb,OAAO,IAAIa,WAAWb,OAAO,CAAC1B,QAAQ,CAACsC,SAAS;gCAC7DL,QAAQhG,IAAI,CACV,CAAC,KAAK,EAAE8F,KAAKC,MAAM,CAAC,KAAK,EAAEO,WAAWP,MAAM,CAAC,wBAAwB,EAAEM,OAAO,sEAAsE,CAAC;gCAEvJD,mBAAmB;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,IAAIR,mBAAmB1F,MAAM,GAAG,GAAG;gBACjC,MAAM,qBAML,CANK,IAAIiB,MACR,CAAC,8BAA8B,EAAEyE,mBAC9BnC,GAAG,CAAC,CAACqC,OAAcS,KAAKC,SAAS,CAACV,OAClCjK,IAAI,CACH,MACA,8KAA8K,CAAC,GAL/K,qBAAA;2BAAA;gCAAA;kCAAA;gBAMN;YACF;QACF;QAEA,IAAI,CAACyF,MAAMC,OAAO,CAAC7B,KAAK+F,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAItE,MACR,CAAC,2FAA2F,EAAE,OAAOzB,KAAK+F,OAAO,CAAC,2EAA2E,CAAC,GAD1L,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMgB,iBAAiB/G,KAAK+F,OAAO,CAACI,MAAM,CACxC,CAACQ,SAAgB,OAAOA,WAAW;QAGrC,IAAII,eAAevG,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAOL,CAPK,IAAIiB,MACR,CAAC,gDAAgD,EAAEsF,eAChDhD,GAAG,CAACiD,QACJ7K,IAAI,CACH,MACA,wEAAwE,CAAC,GAC3E,CAAC,+HAA+H,CAAC,GAN/H,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QAEA,IAAI,CAAC6D,KAAK+F,OAAO,CAAC1B,QAAQ,CAACrE,KAAKiG,aAAa,GAAG;YAC9C,MAAM,qBAEL,CAFK,IAAIxE,MACR,CAAC,0IAA0I,CAAC,GADxI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMwF,oBAAoB,IAAIC;QAC9B,MAAMC,mBAAmB,IAAID;QAE7BlH,KAAK+F,OAAO,CAACjE,OAAO,CAAC,CAAC6E;YACpB,MAAMS,cAAcT,OAAOU,WAAW;YACtC,IAAIJ,kBAAkBK,GAAG,CAACF,cAAc;gBACtCD,iBAAiBI,GAAG,CAACZ;YACvB;YACAM,kBAAkBM,GAAG,CAACH;QACxB;QAEA,IAAID,iBAAiBK,IAAI,GAAG,GAAG;YAC7B,MAAM,qBAKL,CALK,IAAI/F,MACR,CAAC,kEAAkE,CAAC,GAClE,GAAG;mBAAI0F;aAAiB,CAAChL,IAAI,CAAC,MAAM,EAAE,CAAC,GACvC,CAAC,yCAAyC,CAAC,GAC3C,CAAC,wEAAwE,CAAC,GAJxE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,2CAA2C;QAC3C6D,KAAK+F,OAAO,GAAG;YACb/F,KAAKiG,aAAa;eACfjG,KAAK+F,OAAO,CAACI,MAAM,CAAC,CAACQ,SAAWA,WAAW3G,KAAKiG,aAAa;SACjE;QAED,MAAMwB,sBAAsB,OAAOzH,KAAK0H,eAAe;QAEvD,IACED,wBAAwB,aACxBA,wBAAwB,aACxB;YACA,MAAM,qBAEL,CAFK,IAAIhG,MACR,CAAC,yEAAyE,EAAEgG,oBAAoB,2EAA2E,CAAC,GADxK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAIzG,OAAO6D,aAAa,KAAK,WAAS7D,wBAAAA,OAAO6D,aAAa,qBAApB7D,sBAAsB+D,QAAQ,GAAE;QACpE,MAAM,EAAEA,QAAQ,EAAE,GAAG/D,OAAO6D,aAAa;QACzC,MAAM8C,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,IAAI,CAACA,cAActD,QAAQ,CAACU,WAAW;YACrC,MAAM,qBAIL,CAJK,IAAItD,MACR,CAAC,0DAA0D,EAAEkG,cAAcxL,IAAI,CAC7E,MACA,WAAW,EAAE4I,UAAU,GAHrB,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,IAAI/D,OAAOlB,YAAY,EAAE;YAElB9C,6BAGkBA,uCAAAA,8BAKpBA,wCAAAA;QATHgE,OAAOlB,YAAY,CAAC8H,SAAS,GAAG;gBAC3B5K,8BAAAA,cAAc8C,YAAY,qBAA1B9C,4BAA4B4K,SAAS,AAAxC;YACA,GAAG5G,OAAOlB,YAAY,CAAC8H,SAAS;QAClC;QACA,MAAMC,kBAAiB7K,+BAAAA,cAAc8C,YAAY,sBAA1B9C,wCAAAA,6BAA4B4K,SAAS,qBAArC5K,qCAAuC,CAAC,UAAU;QACzE,IACE,CAAC6K,kBACDA,eAAeC,UAAU,KAAKtI,aAC9BqI,eAAeE,MAAM,KAAKvI,aAC1B,GAACxC,+BAAAA,cAAc8C,YAAY,sBAA1B9C,yCAAAA,6BAA4BgL,UAAU,qBAAtChL,uCAAwCiL,MAAM,GAC/C;YACA,MAAM,qBAA0C,CAA1C,IAAIxG,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QACA,MAAMyG,0BAA0BlH,OAAOlB,YAAY,CAAC8H,SAAS,CAAC,UAAU;QACxE,IAAI,CAACM,yBAAyB;YAC5BlH,OAAOlB,YAAY,CAAC8H,SAAS,CAAC,UAAU,GAAGC;QAC7C,OAAO;YACL,IAAIK,wBAAwBC,KAAK,KAAK3I,WAAW;oBACvBwB,iCAEHhE,yCAAAA;gBAFrB,MAAMoL,mBAAkBpH,kCAAAA,OAAOlB,YAAY,CAACkI,UAAU,qBAA9BhH,gCAAgCiH,MAAM;gBAC9DC,wBAAwBC,KAAK,GAC3BC,qBAAmBpL,+BAAAA,cAAc8C,YAAY,sBAA1B9C,0CAAAA,6BAA4BgL,UAAU,qBAAtChL,wCAAwCiL,MAAM;YACrE;YACA,IAAIC,wBAAwBJ,UAAU,KAAKtI,WAAW;gBACpD0I,wBAAwBJ,UAAU,GAAGD,eAAeC,UAAU;YAChE;YACA,IAAII,wBAAwBH,MAAM,KAAKvI,WAAW;gBAChD0I,wBAAwBH,MAAM,GAC5B/G,OAAOqH,UAAU,IAAIR,eAAeE,MAAM;YAC9C;QACF;IACF;IAEA,KAAI/G,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBsH,aAAa,EAAE;QACtC,MAAMC,0BAA0B;QAEhC,IAAI,OAAOvH,OAAOlB,YAAY,CAACwI,aAAa,KAAK,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAI7G,MACR,CAAC,+GAA+G,EAAEoF,KAAKC,SAAS,CAAC9F,OAAOlB,YAAY,CAACwI,aAAa,GAAG,GADjK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAME,cAAcpH,OAAOC,IAAI,CAACL,OAAOlB,YAAY,CAACwI,aAAa;QACjE,MAAMG,sBAA8D,EAAE;QAEtE,KAAK,MAAMlJ,OAAOiJ,YAAa;YAC7B,IAAIjJ,QAAQ,WAAW;gBACrBkJ,oBAAoB5E,IAAI,CAAC;oBACvBtE;oBACAP,QACE;gBACJ;YACF,OAAO,IAAI,CAACuJ,wBAAwBG,IAAI,CAACnJ,MAAM;gBAC7CkJ,oBAAoB5E,IAAI,CAAC;oBACvBtE;oBACAP,QAAQ;gBACV;YACF,OAAO;gBACL,MAAM2J,cAAc,AAClB3H,OAAOlB,YAAY,CAACwI,aAAa,AAGlC,CAAC/I,IAAI;gBAEN,IAAIoJ,eAAe,CAAC3M,WAAW2M,cAAc;oBAC3CF,oBAAoB5E,IAAI,CAAC;wBACvBtE;wBACAP,QAAQ,CAAC,qDAAqD,EAAE2J,aAAa;oBAC/E;gBACF;YACF;YACA,IAAIF,oBAAoBjI,MAAM,EAAE;gBAC9B,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,oEAAoE,EAAEgH,oBAAoB1E,GAAG,CAAC,CAACqC,OAAS,GAAG7G,IAAI,EAAE,EAAE6G,KAAKpH,MAAM,EAAE,EAAE7C,IAAI,CAAC,OAAO,GAD3I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMyM,gCAAgC5H,OAAO6H,iBAAiB;IAC9D,kJAAkJ;IAClJ,6EAA6E;IAC7E7H,OAAO6H,iBAAiB,GAAG;QACzB,GAAID,iCAAiC,CAAC,CAAC;QACvC,gFAAgF;QAChF,uBAAuB;YACrBE,WAAW;QACb;QACAC,QAAQ;YACND,WAAW;QACb;IACF;IAEA,MAAME,qCACJhI,EAAAA,wBAAAA,OAAOlB,YAAY,qBAAnBkB,sBAAqBiI,sBAAsB,KAAI,EAAE;IAEnDjI,OAAOlB,YAAY,CAACmJ,sBAAsB,GAAG;WACxC,IAAI/B,IAAI;eACN8B;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,4EAA4E;YAC5E,mCAAmC;YACnC,0EAA0E;YAC1E,wBAAwB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;KACF;IAED,IAAI,CAAChI,OAAOkI,eAAe,EAAE;QAC3B,oGAAoG;QACpGlI,OAAOkI,eAAe,GAAGnL;IAC3B;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,2BAA2B;IAC3B,IAAIiD,OAAOlB,YAAY,CAACqJ,QAAQ,KAAK3J,WAAW;QAC9CwB,OAAOlB,YAAY,CAACqJ,QAAQ,GAAGnI,OAAOlB,YAAY,CAACqB,eAAe;IACpE;IAEA,qDAAqD;IACrD,IAAIH,OAAOlB,YAAY,CAACqB,eAAe,EAAE;YAErCxB,2BACAA;QAFF,IACEA,EAAAA,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyB8C,GAAG,MAAK,SACjC9C,EAAAA,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyB8C,GAAG,MAAK,eACjC;gBAEsD9C;YADtD,MAAM,qBAEL,CAFK,IAAI8B,MACR,CAAC,kCAAkC,EAAEoF,KAAKC,SAAS,EAACnH,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyB8C,GAAG,EAAE,iHAAiH,CAAC,GADhM,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAzB,OAAOlB,YAAY,CAAC2C,GAAG,GAAG;IAC5B;IAEA,OAAOzB;AACT;AAEA,eAAeoI,kBACbtK,MAA0B,EAC1BuK,KAAa,EACbpK,MAAe;QAMbH;IAJF,IACE,kDAAkD;IAClD,gDAAgD;IAChD;QAAChC;QAAwBC;KAAwB,CAACsH,QAAQ,CAACgF,YAC3DvK,uBAAAA,OAAOgB,YAAY,qBAAnBhB,qBAAqBwK,WAAW,GAChC;QACA,MAAMC,aAAapL,eACjB,MAAM,MAAM,CACV5B,cAAciN,QAAQlN,OAAO,CAACwC,OAAOgB,YAAY,CAACwJ,WAAW,GAAGG,IAAI;QAIxE,IAAI,OAAOF,WAAWG,YAAY,KAAK,YAAY;YACjD,IAAI,CAACzK,QAAQ;gBACXxC,IAAIkN,IAAI,CAAC,CAAC,2BAA2B,EAAEJ,WAAWK,IAAI,EAAE;YAC1D;YACA9K,SAAS,MAAMyK,WAAWG,YAAY,CAAC5K;QACzC;IACF;IACA,OAAOA;AACT;AAEA,8EAA8E;AAC9E,MAAM+K,cAAc,IAAIC;AASxB,mEAAmE;AACnE,sEAAsE;AACtE,SAASC,YACPV,KAAa,EACbxJ,GAAW,EACXmK,YAA4B,EAC5BC,wBAAkC,EAClCC,cAAwB;IAExB,mFAAmF;IACnF,qCAAqC;IACrC,MAAMC,UAAUtD,KAAKC,SAAS,CAAC;QAC7BjH;QACAwJ;QACAe,iBAAiBlK,QAAQ8J;QACzBC,0BAA0B/J,QAAQ+J;QAClCC,gBAAgBhK,QAAQgK;IAC1B;IAEA,OAAO9L,SAAS+L,SAAS/E,QAAQ,CAAC;AACpC;AAEA,eAAe,eAAeiF,WAC5BhB,KAAa,EACbxJ,GAAW,EACX,EACEmK,YAAY,EACZM,SAAS,EACTrL,SAAS,IAAI,EACbsL,0BAA0B,EAC1BN,wBAAwB,EACxBC,cAAc,EAUf,GAAG,CAAC,CAAC;IAEN,mEAAmE;IACnE,MAAMM,WAAWT,YACfV,OACAxJ,KACAmK,cACAC,0BACAC;IAGF,mCAAmC;IACnC,MAAMO,eAAeZ,YAAYa,GAAG,CAACF;IACrC,IAAIC,cAAc;QAChB,sDAAsD;QACtD,IAAIF,4BAA4B;YAC9BA,2BAA2BE,aAAaE,8BAA8B;QACxE;QAEA,+CAA+C;QAC/C,IAAIL,aAAaG,aAAaH,SAAS,EAAE;YACvC,OAAOG,aAAaH,SAAS;QAC/B;QAEA,OAAOG,aAAa3L,MAAM;IAC5B;IAEA,6CAA6C;IAC7C,IAAI,CAACwD,QAAQC,GAAG,CAACqI,4BAA4B,EAAE;QAC7C,IAAI;YACF1N;QACF,EAAE,OAAO2N,KAAK;YACZ,gDAAgD;YAChD,yBAAyB;YACzB,IAAI,CAACvI,QAAQC,GAAG,CAACuI,gCAAgC,EAAE;gBACjD,MAAMD;YACR;QACF;IACF;IAEA,IAAIvI,QAAQC,GAAG,CAACuI,gCAAgC,EAAE;QAChD,2DAA2D;QAC3D,2BAA2B;QAC3B,MAAMC,mBAAmBlE,KAAKmE,KAAK,CACjC1I,QAAQC,GAAG,CAACuI,gCAAgC;QAG9C,8BAA8B;QAC9BjB,YAAYoB,GAAG,CAACT,UAAU;YACxB1L,QAAQiM;YACRT,WAAWS;YACXJ,gCAAgC,EAAE;QACpC;QAEA,OAAOI;IACT;IAEA,MAAMG,SAASjM,SACX;QACEqB,MAAM,KAAO;QACbqJ,MAAM,KAAO;QACbrL,OAAO,KAAO;IAChB,IACA7B;IAEJW,cAAcyC,KAAKwJ,UAAUzM,0BAA0BsO;IAEvD,IAAItL,iBAAiB;IACrB,MAAM+K,iCAAkE,EAAE;IAE1E,IAAIX,cAAc;QAChB,+EAA+E;QAC/EtK,kBAAkBsK,cAA4BpK,gBAAgBX,QAAQY;QAEtE,MAAMf,SAAS,MAAMsK,kBACnBrI,eACElB,KACA;YACEsL,cAAc;YACdvL;YACA,GAAGoK,YAAY;QACjB,GACA/K,SAEFoK,OACApK;QAGF,iCAAiC;QACjC4K,YAAYoB,GAAG,CAACT,UAAU;YACxB1L;YACAwL,WAAWN;YACXW;QACF;QAEAJ,8CAAAA,2BAA6BI;QAE7B,OAAO7L;IACT;IAEA,MAAMF,OAAO,MAAMpC,OAAOG,cAAc;QAAEyO,KAAKvL;IAAI;IAEnD,2BAA2B;IAC3B,IAAIjB,wBAAAA,KAAM4B,MAAM,EAAE;YAmIZb,iBAcFA,gCAAAA,0BACCA,iCAAAA,2BAmBCA,2BAoBAA;QAxLJC,iBAAiB3D,SAAS2C;QAE1B,IAAIyM;QACJ,IAAI;YACF,MAAMC,YAAYlK,OAAOmK,MAAM,CAAC,CAAC,GAAGjJ,QAAQC,GAAG;YAE/C,uEAAuE;YACvE,sEAAsE;YACtE,8BAA8B;YAC9B,IAAID,QAAQC,GAAG,CAACiJ,gBAAgB,KAAK,QAAQ;gBAC3C,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8CAA8C;gBAC9CH,mBAAmB7B,QAAQ5K;YAC7B,OAAO,IAAIgB,mBAAmB,kBAAkB;gBAC9CyL,mBAAmB,MAAMzN,gBAAgB;oBACvC6N,gBAAgB7M;oBAChBgB;oBACAwL,KAAKvL;gBACP;YACF,OAAO;gBACLwL,mBAAmB,MAAM,MAAM,CAAC9O,cAAcqC,MAAM6K,IAAI;YAC1D;YACA,MAAMiC,SAA6B,CAAC;YAEpC,KAAK,MAAMnM,OAAO6B,OAAOC,IAAI,CAACiB,QAAQC,GAAG,EAAG;gBAC1C,IAAI+I,SAAS,CAAC/L,IAAI,KAAK+C,QAAQC,GAAG,CAAChD,IAAI,EAAE;oBACvCmM,MAAM,CAACnM,IAAI,GAAG+C,QAAQC,GAAG,CAAChD,IAAI;gBAChC;YACF;YACAlC,iBAAiBqO;YAEjB,IAAIpB,WAAW;gBACb,uBAAuB;gBACvBT,YAAYoB,GAAG,CAACT,UAAU;oBACxB1L,QAAQuM;oBACRf,WAAWe;oBACXV;gBACF;gBAEAJ,8CAAAA,2BAA6BI;gBAE7B,OAAOU;YACT;QACF,EAAE,OAAOR,KAAK;YACZ,0EAA0E;YAC1EK,OAAO5M,KAAK,CACV,CAAC,eAAe,EAAEsB,eAAe,uEAAuE,CAAC;YAE3G,MAAMiL;QACR;QAEA,MAAMc,eAAevK,OAAOwK,MAAM,CAC/B,MAAM3O,gBACLoM,OACAlL,eAAekN;QAInB,IAAIM,aAAa7L,YAAY,EAAE;YAC7B,KAAK,MAAM8J,QAAQxI,OAAOC,IAAI,CAC5BsK,aAAa7L,YAAY,EACQ;gBACjC,MAAM0B,QAAQmK,aAAa7L,YAAY,CAAC8J,KAAK;gBAE7C,IAAIA,SAAS,WAAW,CAACtH,QAAQC,GAAG,CAACsJ,SAAS,EAAE;oBAE9C;gBACF;gBAEAC,iCACEnB,gCACAf,MACApI;YAEJ;QACF;QAEA,kEAAkE;QAClE,MAAM7B,aAAaoM,YAAYJ;QAE/B,oFAAoF;QACpFjM,kBAAkBC,YAAYC,gBAAgBX,QAAQY;QAEtD,iEAAiE;QACjE,2EAA2E;QAC3E,MAAMmM,gBAAgB,OAAO1J,QAAQ2J,IAAI,KAAK;QAC9C,IAAI,CAAC3J,QAAQC,GAAG,CAAC2J,YAAY,IAAIF,eAAe;YAC9C,iEAAiE;YACjE,MAAM,EAAEG,YAAY,EAAE,GACpB3C,QAAQ;YACV,MAAM4C,QAAQD,aAAaE,SAAS,CAAC1M;YAErC,IAAI,CAACyM,MAAME,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,MAAMC,WAAW;oBAAC,CAAC,QAAQ,EAAE3M,eAAe,mBAAmB,CAAC;iBAAC;gBAEjE,MAAM,CAAC4M,eAAejO,WAAW,GAAGF,6BAClC+N,MAAM9N,KAAK;gBAEb,kBAAkB;gBAClB,KAAK,MAAMA,SAASkO,cAAe;oBACjCD,SAAS1I,IAAI,CAAC,CAAC,IAAI,EAAEvF,OAAO;gBAC9B;gBAEA,uBAAuB;gBACvBiO,SAAS1I,IAAI,CACX;gBAGF,IAAItF,YAAY;oBACd,KAAK,MAAMI,WAAW4N,SAAU;wBAC9BjG,QAAQhI,KAAK,CAACK;oBAChB;oBACA,MAAMrB,aAAa;gBACrB,OAAO;oBACL,KAAK,MAAMqB,WAAW4N,SAAU;wBAC9BrB,OAAO5K,IAAI,CAAC3B;oBACd;gBACF;YACF;QACF;QAEA,IAAIgB,WAAW8M,MAAM,IAAI9M,WAAW8M,MAAM,KAAK,UAAU;YACvD,MAAM,qBAGL,CAHK,IAAIhL,MACR,CAAC,gDAAgD,EAAE7B,eAAe,GAAG,CAAC,GACpE,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,KAAID,kBAAAA,WAAWyD,GAAG,qBAAdzD,gBAAgB0D,aAAa,EAAE;YACjC,MAAM,EAAEA,aAAa,EAAE,GAAG1D,WAAWyD,GAAG,IAAK,CAAC;YAC9CzD,WAAWyD,GAAG,GAAGzD,WAAWyD,GAAG,IAAI,CAAC;YACpCzD,WAAWyD,GAAG,CAACC,aAAa,GAC1B,AAACA,CAAAA,CAAAA,iCAAAA,cAAeF,QAAQ,CAAC,QACrBE,cAAcqJ,KAAK,CAAC,GAAG,CAAC,KACxBrJ,aAAY,KAAM;QAC1B;QAEA,IAAI4G,0BAA0B;YAC5BtK,WAAWsK,wBAAwB,GAAGA;QACxC;QAEA,IACEtK,EAAAA,2BAAAA,WAAWG,YAAY,sBAAvBH,iCAAAA,yBAAyBgN,KAAK,qBAA9BhN,+BAAgCiN,OAAO,KACvC,GAACjN,4BAAAA,WAAWG,YAAY,sBAAvBH,kCAAAA,0BAAyBgN,KAAK,qBAA9BhN,gCAAgCkN,KAAK,GACtC;YACA3B,OAAO5K,IAAI,CACT,sIACE,uFACA,4FACA;YAGJ,MAAMuM,QAA+C,CAAC;YACtD,KAAK,MAAM,CAAC9K,KAAK6K,QAAQ,IAAIxL,OAAO0L,OAAO,CACzCnN,WAAWG,YAAY,CAAC6M,KAAK,CAACC,OAAO,EACpC;gBACDC,KAAK,CAAC,MAAM9K,IAAI,GAAG6K;YACrB;YAEAjN,WAAWG,YAAY,CAAC6M,KAAK,CAACE,KAAK,GAAGA;QACxC;QAEA,KAAIlN,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyBgN,KAAK,EAAE;YAClCzB,OAAO5K,IAAI,CACT;YAGF,kEAAkE;YAClEX,WAAW4F,SAAS,GAAG;gBACrB,GAAG5F,WAAWG,YAAY,CAAC6M,KAAK;gBAChC,GAAGhN,WAAW4F,SAAS;YACzB;YACA5F,WAAWG,YAAY,CAACiN,oBAAoB,KAC1CpN,WAAWG,YAAY,CAAC6M,KAAK,CAACK,WAAW;YAC3CrN,WAAWG,YAAY,CAACmN,eAAe,KACrCtN,WAAWG,YAAY,CAAC6M,KAAK,CAACO,MAAM;YACtCvN,WAAWG,YAAY,CAACqN,oBAAoB,KAC1CxN,WAAWG,YAAY,CAAC6M,KAAK,CAACS,WAAW;YAC3CzN,WAAWG,YAAY,CAACuN,mBAAmB,KACzC1N,WAAWG,YAAY,CAAC6M,KAAK,CAACW,UAAU;QAC5C;QAEA,KAAI3N,4BAAAA,WAAWG,YAAY,qBAAvBH,0BAAyB4N,eAAe,EAAE;gBAGf,MAAC;YAF9B,MAAM,EAAEC,YAAY,EAAE,GACpBhE,QAAQ;YACV,MAAMiE,wBAAwB,QAAA,MAAMD,oCAAP,OAAA,AAAC,MAAuBE,GAAG,qBAA3B,KAA6BC,SAAS;YAEnE,IAAI,CAACF,sBAAsB;gBACzBvC,OAAO5K,IAAI,CACT,CAAC,+GAA+G,CAAC;gBAEnHX,WAAWG,YAAY,CAACyN,eAAe,GAAG;YAC5C;QACF;QAEA,yCAAyC;QACzC,IAAI5N,CAAAA,8BAAAA,WAAYuJ,eAAe,aAAY0E,QAAQ;YACjD,oGAAoG;YACpGjO,WAAWuJ,eAAe,GAAGvJ,WAAWuJ,eAAe,CAAC2E,MAAM;QAChE;QAEAC,4BAA4BnO,YAAY;YACtCoO,iBAAiB;YACjBpD;YACAT;YACAb;QACF;QAEA,MAAM2E,iBAAiBjN,eACrBlB,KACA;YACEsL,cAAc/O,SAASyD,KAAKjB;YAC5BqP,YAAYrP;YACZgB;YACA,GAAGD,UAAU;QACf,GACAV;QAGF,MAAMiP,cAAc,MAAM9E,kBAAkB4E,gBAAgB3E,OAAOpK;QAEnE,yBAAyB;QACzB4K,YAAYoB,GAAG,CAACT,UAAU;YACxB1L,QAAQoP;YACR5D,WAAWe;YACXV;QACF;QAEA,IAAIJ,4BAA4B;YAC9BA,2BAA2BI;QAC7B;QAEA,OAAOuD;IACT,OAAO;QACL,MAAMC,iBAAiBlS,SAASU,YAAY,CAAC,EAAE,EAAET,QAAQS,YAAY,CAAC,EAAE;QACxE,MAAMyR,oBAAoB5R,OAAO6R,IAAI,CACnC;YACE,GAAGF,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,KAAK,CAAC;YACxB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;SACxB,EACD;YAAE/C,KAAKvL;QAAI;QAEb,IAAIuO,qCAAAA,kBAAmB5N,MAAM,EAAE;YAC7B,MAAM,qBAIL,CAJK,IAAIiB,MACR,CAAC,yBAAyB,EAAExF,SAC1BmS,mBACA,0GAA0G,CAAC,GAHzG,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,MAAME,sBAAsBvC,YAAY/O;IAExC8Q,4BAA4BQ,qBAAqB;QAC/CP,iBAAiB;QACjBpD;QACAT;QACAb;IACF;IAEA,qDAAqD;IACrD,iEAAiE;IACjE,MAAM2E,iBAAiBjN,eACrBlB,KACA;QAAE,GAAGyO,mBAAmB;QAAE1O;IAAe,GACzCX;IAGFzB,6BAA6BwQ;IAE7B,MAAME,cAAc,MAAM9E,kBAAkB4E,gBAAgB3E,OAAOpK;IAEnE,kCAAkC;IAClC4K,YAAYoB,GAAG,CAACT,UAAU;QACxB1L,QAAQoP;QACR5D,WAAWgE;QACX3D;IACF;IAEA,IAAIJ,4BAA4B;QAC9BA,2BAA2BI;IAC7B;IAEA,OAAOuD;AACT;AAQA,SAASJ,4BACPhP,MAAkB,EAClByP,OAKC;IAED,MAAM,EACJ5D,8BAA8B,EAC9BT,cAAc,EACd6D,eAAe,EACf1E,KAAK,EACN,GAAGkF;IAEJzP,OAAOgB,YAAY,KAAK,CAAC;IAEzB,IACEoK,kBACCb,CAAAA,UAAUvM,0BAA0BuM,UAAUxM,YAAW,GAC1D;QACA2R,wCACE1P,OAAOgB,YAAY,EACnB,oBACA,MACA6K;QAGF6D,wCACE1P,OAAOgB,YAAY,EACnBwC,QAAQC,GAAG,CAACsJ,SAAS,GAAG,oBAAoB,sBAC5C,OACAlB;QAGF6D,wCACE1P,OAAOgB,YAAY,EACnB,6BACA,MACA6K;QAGF6D,wCACE1P,OAAOgB,YAAY,EACnB,sBACA,OACA6K;IAEJ;IAEA,kEAAkE;IAClE,IACErI,QAAQC,GAAG,CAACkM,oCAAoC,KAAK,UACrD,sDAAsD;IACrD3P,CAAAA,OAAOgB,YAAY,CAAC2C,GAAG,KAAKjD,aAC1BuO,mBAAmB,CAACjP,OAAOgB,YAAY,CAAC2C,GAAG,GAC9C;QACA3D,OAAOgB,YAAY,CAAC2C,GAAG,GAAG;QAE1B,IAAIkI,gCAAgC;YAClCmB,iCACEnB,gCACA,OACA,MACA;QAEJ;IACF;IAEA,kEAAkE;IAClE,IACErI,QAAQC,GAAG,CAACmM,uBAAuB,KAAK,UACxC,sDAAsD;IACrD5P,CAAAA,OAAOgB,YAAY,CAAC2C,GAAG,KAAKjD,aAC1BuO,mBAAmB,CAACjP,OAAOgB,YAAY,CAAC2C,GAAG,GAC9C;QACA3D,OAAOgB,YAAY,CAAC2C,GAAG,GAAG;QAE1B,IAAIkI,gCAAgC;YAClCmB,iCACEnB,gCACA,OACA,MACA;QAEJ;IACF;IAEA,sEAAsE;IACtE,IACErI,QAAQC,GAAG,CAACmM,uBAAuB,KAAK,UACxC,sDAAsD;IACrD5P,CAAAA,OAAOgB,YAAY,CAAC6O,kBAAkB,KAAKnP,aACzCuO,mBAAmB,CAACjP,OAAOgB,YAAY,CAAC6O,kBAAkB,GAC7D;QACA7P,OAAOgB,YAAY,CAAC6O,kBAAkB,GAAG;QAEzC,IAAIhE,gCAAgC;YAClCmB,iCACEnB,gCACA,sBACA,MACA;QAEJ;IACF;IAEA,sEAAsE;IACtE,IACErI,QAAQC,GAAG,CAACmM,uBAAuB,KAAK,UACxC,sDAAsD;IACrD5P,CAAAA,OAAOgB,YAAY,CAAC8O,kBAAkB,KAAKpP,aACzCuO,mBAAmB,CAACjP,OAAOgB,YAAY,CAAC8O,kBAAkB,GAC7D;QACA9P,OAAOgB,YAAY,CAAC8O,kBAAkB,GAAG;QAEzC,IAAIjE,gCAAgC;YAClCmB,iCACEnB,gCACA,sBACA,MACA;QAEJ;IACF;IAEA,kEAAkE;IAClE,IACErI,QAAQC,GAAG,CAACkM,oCAAoC,KAAK,UACrD,sDAAsD;IACrD3P,CAAAA,OAAOgB,YAAY,CAACqB,eAAe,KAAK3B,aACtCuO,mBAAmB,CAACjP,OAAOgB,YAAY,CAACqB,eAAe,GAC1D;QACArC,OAAOgB,YAAY,CAACqB,eAAe,GAAG;QAEtC,IAAIwJ,gCAAgC;YAClCmB,iCACEnB,gCACA,mBACA,MACA;QAEJ;IACF;IAEA,IACE7L,OAAOgB,YAAY,CAAC+O,yBAAyB,KAAKrP,aAClDV,OAAOgB,YAAY,CAACqB,eAAe,KAAK,MACxC;QACArC,OAAOgB,YAAY,CAAC+O,yBAAyB,GAAG;QAEhD,IAAIlE,gCAAgC;YAClCmB,iCACEnB,gCACA,6BACA,MACA;QAEJ;IACF;AACF;AAEA,SAASmB,iCAGPnB,8BAA+D,EAC/DpL,GAAY,EACZiC,KAAkC,EAClCxC,MAAe;IAEf,IAAIwC,UAAU,AAACxE,cAAc8C,YAAY,AAA4B,CAACP,IAAI,EAAE;QAC1EoL,+BAA+B9G,IAAI,CAAC;YAAEtE;YAAKiC;YAAOxC;QAAO;IAC3D;AACF;AAEA,SAASwP,wCAGPM,kBAAsC,EACtCvP,GAAY,EACZiC,KAAkC,EAClCmJ,8BAA2E;IAE3E,IAAImE,kBAAkB,CAACvP,IAAI,KAAKiC,OAAO;QACrCsN,kBAAkB,CAACvP,IAAI,GAAGiC;QAE1B,IAAImJ,gCAAgC;YAClC,MAAMoE,SACJvN,UAAU,OAAO,YAAYA,UAAU,QAAQ,aAAa;YAE9D,MAAMxC,SAAS,GAAG+P,OAAO,yBAAyB,CAAC;YAEnDjD,iCACEnB,gCACApL,KACAiC,OACAxC;QAEJ;IACF;AACF;AAEA,SAAS+M,YAAYiD,GAAQ;IAC3B,oBAAoB;IACpB,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;QAC3C,OAAOA;IACT;IAEA,iCAAiC;IACjC,IAAIA,eAAepB,QAAQ;QACzB,OAAO,IAAIA,OAAOoB,IAAInB,MAAM,EAAEmB,IAAIC,KAAK;IACzC;IAEA,+CAA+C;IAC/C,IAAI,OAAOD,QAAQ,YAAY;QAC7B,OAAOA;IACT;IAEA,4BAA4B;IAC5B,IAAIpN,MAAMC,OAAO,CAACmN,MAAM;QACtB,OAAOA,IAAIjL,GAAG,CAACgI;IACjB;IAEA,6CAA6C;IAC7C,MAAM5H,QAAQ/C,OAAO8N,cAAc,CAACF;IACpC,MAAMG,gBAAgBhL,UAAU/C,OAAOgO,SAAS,IAAIjL,UAAU;IAE9D,uDAAuD;IACvD,IAAI,CAACgL,eAAe;QAClB,OAAOH;IACT;IAEA,6DAA6D;IAC7D,+DAA+D;IAC/D,+CAA+C;IAC/C,MAAMhO,SAASI,OAAOiO,MAAM,CAAClL;IAC7B,KAAK,MAAM5E,OAAO+P,QAAQC,OAAO,CAACP,KAAM;QACtC,MAAMQ,aAAapO,OAAOqO,wBAAwB,CAACT,KAAKzP;QAExD,IAAIiQ,cAAeA,CAAAA,WAAW9E,GAAG,IAAI8E,WAAWvE,GAAG,AAAD,GAAI;YACpD,gEAAgE;YAChE7J,OAAOsO,cAAc,CAAC1O,QAAQzB,KAAKiQ;QACrC,OAAO;YACL,kCAAkC;YAClCxO,MAAM,CAACzB,IAAI,GAAGwM,YAAYiD,GAAG,CAACzP,IAAI;QACpC;IACF;IAEA,OAAOyB;AACT", "ignoreList": [0]}