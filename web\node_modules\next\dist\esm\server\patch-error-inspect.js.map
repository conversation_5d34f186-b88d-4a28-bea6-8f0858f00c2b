{"version": 3, "sources": ["../../src/server/patch-error-inspect.ts"], "sourcesContent": ["import { findSourceMap as nativeFindSourceMap } from 'module'\nimport * as path from 'path'\nimport * as url from 'url'\nimport type * as util from 'util'\nimport { SourceMapConsumer as SyncSourceMapConsumer } from 'next/dist/compiled/source-map'\nimport {\n  type ModernSourceMapPayload,\n  findApplicableSourceMapPayload,\n  ignoreListAnonymousStackFramesIfSandwiched as ignoreListAnonymousStackFramesIfSandwichedGeneric,\n  sourceMapIgnoreListsEverything,\n} from './lib/source-maps'\nimport { parseStack, type StackFrame } from './lib/parse-stack'\nimport { getOriginalCodeFrame } from '../next-devtools/server/shared'\nimport { workUnitAsyncStorage } from './app-render/work-unit-async-storage.external'\nimport { dim } from '../lib/picocolors'\n\ntype FindSourceMapPayload = (\n  sourceURL: string\n) => ModernSourceMapPayload | undefined\n// Find a source map using the bundler's API.\n// This is only a fallback for when Node.js fails to due to bugs e.g. https://github.com/nodejs/node/issues/52102\n// TODO: Remove once all supported Node.js versions are fixed.\n// TODO(veil): Set from Webpack as well\nlet bundlerFindSourceMapPayload: FindSourceMapPayload = () => undefined\n\nexport function setBundlerFindSourceMapImplementation(\n  findSourceMapImplementation: FindSourceMapPayload\n): void {\n  bundlerFindSourceMapPayload = findSourceMapImplementation\n}\n\ninterface IgnorableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\ntype SourceMapCache = Map<\n  string,\n  null | { map: SyncSourceMapConsumer; payload: ModernSourceMapPayload }\n>\n\nfunction frameToString(\n  methodName: string | null,\n  sourceURL: string | null,\n  line1: number | null,\n  column1: number | null\n): string {\n  let sourceLocation = line1 !== null ? `:${line1}` : ''\n  if (column1 !== null && sourceLocation !== '') {\n    sourceLocation += `:${column1}`\n  }\n\n  let fileLocation: string | null\n  if (\n    sourceURL !== null &&\n    sourceURL.startsWith('file://') &&\n    URL.canParse(sourceURL)\n  ) {\n    // If not relative to CWD, the path is ambiguous to IDEs and clicking will prompt to select the file first.\n    // In a multi-app repo, this leads to potentially larger file names but will make clicking snappy.\n    // There's no tradeoff for the cases where `dir` in `next dev [dir]` is omitted\n    // since relative to cwd is both the shortest and snappiest.\n    fileLocation = path.relative(process.cwd(), url.fileURLToPath(sourceURL))\n  } else if (sourceURL !== null && sourceURL.startsWith('/')) {\n    fileLocation = path.relative(process.cwd(), sourceURL)\n  } else {\n    fileLocation = sourceURL\n  }\n\n  return methodName\n    ? `    at ${methodName} (${fileLocation}${sourceLocation})`\n    : `    at ${fileLocation}${sourceLocation}`\n}\n\nfunction computeErrorName(error: Error): string {\n  // TODO: Node.js seems to use a different algorithm\n  // class ReadonlyRequestCookiesError extends Error {}` would read `ReadonlyRequestCookiesError: [...]`\n  // in the stack i.e. seems like under certain conditions it favors the constructor name.\n  return error.name || 'Error'\n}\n\nfunction prepareUnsourcemappedStackTrace(\n  error: Error,\n  structuredStackTrace: any[]\n): string {\n  const name = computeErrorName(error)\n  const message = error.message || ''\n  let stack = name + ': ' + message\n  for (let i = 0; i < structuredStackTrace.length; i++) {\n    stack += '\\n    at ' + structuredStackTrace[i].toString()\n  }\n  return stack\n}\n\nfunction shouldIgnoreListGeneratedFrame(file: string): boolean {\n  return file.startsWith('node:') || file.includes('node_modules')\n}\n\nfunction shouldIgnoreListOriginalFrame(file: string): boolean {\n  return file.includes('node_modules')\n}\n\ninterface SourcemappableStackFrame extends StackFrame {\n  file: NonNullable<StackFrame['file']>\n}\n\ninterface SourceMappedFrame {\n  stack: IgnorableStackFrame\n  // DEV only\n  code: string | null\n}\n\nfunction createUnsourcemappedFrame(\n  frame: SourcemappableStackFrame\n): SourceMappedFrame {\n  return {\n    stack: {\n      file: frame.file,\n      line1: frame.line1,\n      column1: frame.column1,\n      methodName: frame.methodName,\n      arguments: frame.arguments,\n      ignored: shouldIgnoreListGeneratedFrame(frame.file),\n    },\n    code: null,\n  }\n}\n\nfunction ignoreListAnonymousStackFramesIfSandwiched(\n  sourceMappedFrames: Array<{\n    stack: IgnorableStackFrame\n    code: string | null\n  }>\n) {\n  return ignoreListAnonymousStackFramesIfSandwichedGeneric(\n    sourceMappedFrames,\n    (frame) => frame.stack.file === '<anonymous>',\n    (frame) => frame.stack.ignored,\n    (frame) => frame.stack.methodName,\n    (frame) => {\n      frame.stack.ignored = true\n    }\n  )\n}\n\n/**\n * @param frame\n * @param sourceMapCache\n * @returns The original frame if not sourcemapped.\n */\nfunction getSourcemappedFrameIfPossible(\n  frame: SourcemappableStackFrame,\n  sourceMapCache: SourceMapCache,\n  inspectOptions: util.InspectOptions\n): {\n  stack: IgnorableStackFrame\n  code: string | null\n} {\n  const sourceMapCacheEntry = sourceMapCache.get(frame.file)\n  let sourceMapConsumer: SyncSourceMapConsumer\n  let sourceMapPayload: ModernSourceMapPayload\n  if (sourceMapCacheEntry === undefined) {\n    let sourceURL = frame.file\n    // e.g. \"/APP/.next/server/chunks/ssr/[root-of-the-server]__2934a0._.js\"\n    // will be keyed by Node.js as \"file:///APP/.next/server/chunks/ssr/[root-of-the-server]__2934a0._.js\".\n    // This is likely caused by `callsite.toString()` in `Error.prepareStackTrace converting file URLs to paths.\n    if (sourceURL.startsWith('/')) {\n      sourceURL = url.pathToFileURL(frame.file).toString()\n    }\n    let maybeSourceMapPayload: ModernSourceMapPayload | undefined\n    try {\n      const sourceMap = nativeFindSourceMap(sourceURL)\n      maybeSourceMapPayload = sourceMap?.payload\n    } catch (cause) {\n      // We should not log an actual error instance here because that will re-enter\n      // this codepath during error inspection and could lead to infinite recursion.\n      console.error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n      // If loading fails once, it'll fail every time.\n      // So set the cache to avoid duplicate errors.\n      sourceMapCache.set(frame.file, null)\n      // Don't even fall back to the bundler because it might be not as strict\n      // with regards to parsing and then we fail later once we consume the\n      // source map payload.\n      // This essentially avoids a redundant error where we fail here and then\n      // later on consumption because the bundler just handed back an invalid\n      // source map.\n      return createUnsourcemappedFrame(frame)\n    }\n    if (maybeSourceMapPayload === undefined) {\n      maybeSourceMapPayload = bundlerFindSourceMapPayload(sourceURL)\n    }\n\n    if (maybeSourceMapPayload === undefined) {\n      return createUnsourcemappedFrame(frame)\n    }\n    sourceMapPayload = maybeSourceMapPayload\n    try {\n      sourceMapConsumer = new SyncSourceMapConsumer(\n        // @ts-expect-error -- Module.SourceMap['version'] is number but SyncSourceMapConsumer wants a string\n        sourceMapPayload\n      )\n    } catch (cause) {\n      // We should not log an actual error instance here because that will re-enter\n      // this codepath during error inspection and could lead to infinite recursion.\n      console.error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n      // If creating the consumer fails once, it'll fail every time.\n      // So set the cache to avoid duplicate errors.\n      sourceMapCache.set(frame.file, null)\n      return createUnsourcemappedFrame(frame)\n    }\n    sourceMapCache.set(frame.file, {\n      map: sourceMapConsumer,\n      payload: sourceMapPayload,\n    })\n  } else if (sourceMapCacheEntry === null) {\n    // We failed earlier getting the payload or consumer.\n    // Just return an unsourcemapped frame.\n    // Errors will already be logged.\n    return createUnsourcemappedFrame(frame)\n  } else {\n    sourceMapConsumer = sourceMapCacheEntry.map\n    sourceMapPayload = sourceMapCacheEntry.payload\n  }\n\n  const sourcePosition = sourceMapConsumer.originalPositionFor({\n    column: (frame.column1 ?? 1) - 1,\n    line: frame.line1 ?? 1,\n  })\n\n  const applicableSourceMap = findApplicableSourceMapPayload(\n    (frame.line1 ?? 1) - 1,\n    (frame.column1 ?? 1) - 1,\n    sourceMapPayload\n  )\n  let ignored =\n    applicableSourceMap !== undefined &&\n    sourceMapIgnoreListsEverything(applicableSourceMap)\n  if (sourcePosition.source === null) {\n    return {\n      stack: {\n        arguments: frame.arguments,\n        file: frame.file,\n        line1: frame.line1,\n        column1: frame.column1,\n        methodName: frame.methodName,\n        ignored: ignored || shouldIgnoreListGeneratedFrame(frame.file),\n      },\n      code: null,\n    }\n  }\n\n  // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n  if (applicableSourceMap === undefined) {\n    console.error('No applicable source map found in sections for frame', frame)\n  } else if (!ignored && shouldIgnoreListOriginalFrame(sourcePosition.source)) {\n    // Externals may be libraries that don't ship ignoreLists.\n    // This is really taking control away from libraries.\n    // They should still ship `ignoreList` so that attached debuggers ignore-list their frames.\n    // TODO: Maybe only ignore library sourcemaps if `ignoreList` is absent?\n    // Though keep in mind that Turbopack omits empty `ignoreList`.\n    // So if we establish this convention, we should communicate it to the ecosystem.\n    ignored = true\n  } else if (!ignored) {\n    // TODO: O(n^2). Consider moving `ignoreList` into a Set\n    const sourceIndex = applicableSourceMap.sources.indexOf(\n      sourcePosition.source\n    )\n    ignored = applicableSourceMap.ignoreList?.includes(sourceIndex) ?? false\n  }\n\n  const originalFrame: IgnorableStackFrame = {\n    // We ignore the sourcemapped name since it won't be the correct name.\n    // The callsite will point to the column of the variable name instead of the\n    // name of the enclosing function.\n    // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n    methodName: frame.methodName\n      ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n      ?.replace('__webpack_exports__.', ''),\n    file: sourcePosition.source,\n    line1: sourcePosition.line,\n    column1: sourcePosition.column + 1,\n    // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n    arguments: [],\n    ignored,\n  }\n\n  /** undefined = not yet computed*/\n  let codeFrame: string | null | undefined\n\n  return Object.defineProperty(\n    {\n      stack: originalFrame,\n      code: null,\n    },\n    'code',\n    {\n      get: () => {\n        if (codeFrame === undefined) {\n          const sourceContent: string | null =\n            sourceMapConsumer.sourceContentFor(\n              sourcePosition.source,\n              /* returnNullOnMissing */ true\n            ) ?? null\n          codeFrame = getOriginalCodeFrame(\n            originalFrame,\n            sourceContent,\n            inspectOptions.colors\n          )\n        }\n        return codeFrame\n      },\n    }\n  )\n}\n\nfunction parseAndSourceMap(\n  error: Error,\n  inspectOptions: util.InspectOptions\n): string {\n  // TODO(veil): Expose as CLI arg or config option. Useful for local debugging.\n  const showIgnoreListed = false\n  // We overwrote Error.prepareStackTrace earlier so error.stack is not sourcemapped.\n  let unparsedStack = String(error.stack)\n  // We could just read it from `error.stack`.\n  // This works around cases where a 3rd party `Error.prepareStackTrace` implementation\n  // doesn't implement the name computation correctly.\n  const errorName = computeErrorName(error)\n\n  let idx = unparsedStack.indexOf('react_stack_bottom_frame')\n  if (idx !== -1) {\n    idx = unparsedStack.lastIndexOf('\\n', idx)\n  } else {\n    idx = unparsedStack.indexOf('react-stack-bottom-frame')\n    if (idx !== -1) {\n      idx = unparsedStack.lastIndexOf('\\n', idx)\n    }\n  }\n  if (idx !== -1 && !showIgnoreListed) {\n    // Cut off everything after the bottom frame since it'll be React internals.\n    unparsedStack = unparsedStack.slice(0, idx)\n  }\n\n  const unsourcemappedStack = parseStack(unparsedStack)\n  const sourceMapCache: SourceMapCache = new Map()\n\n  const sourceMappedFrames: Array<{\n    stack: IgnorableStackFrame\n    code: string | null\n  }> = []\n  let sourceFrame: null | string = null\n  for (const frame of unsourcemappedStack) {\n    if (frame.file === null) {\n      sourceMappedFrames.push({\n        code: null,\n        stack: {\n          file: frame.file,\n          line1: frame.line1,\n          column1: frame.column1,\n          methodName: frame.methodName,\n          arguments: frame.arguments,\n          ignored: false,\n        },\n      })\n    } else {\n      const sourcemappedFrame = getSourcemappedFrameIfPossible(\n        // We narrowed this earlier by bailing if `frame.file` is null.\n        frame as SourcemappableStackFrame,\n        sourceMapCache,\n        inspectOptions\n      )\n      sourceMappedFrames.push(sourcemappedFrame)\n\n      // We can determine the sourceframe here.\n      // anonymous frames won't have a sourceframe so we don't need to scan\n      // all stacks again to check if they are sandwiched between ignored frames.\n      if (\n        sourceFrame === null &&\n        // TODO: Is this the right choice?\n        !sourcemappedFrame.stack.ignored &&\n        sourcemappedFrame.code !== null\n      ) {\n        sourceFrame = sourcemappedFrame.code\n      }\n    }\n  }\n\n  ignoreListAnonymousStackFramesIfSandwiched(sourceMappedFrames)\n\n  let sourceMappedStack = ''\n  for (let i = 0; i < sourceMappedFrames.length; i++) {\n    const frame = sourceMappedFrames[i]\n\n    if (!frame.stack.ignored) {\n      sourceMappedStack +=\n        '\\n' +\n        frameToString(\n          frame.stack.methodName,\n          frame.stack.file,\n          frame.stack.line1,\n          frame.stack.column1\n        )\n    } else if (showIgnoreListed) {\n      sourceMappedStack +=\n        '\\n' +\n        dim(\n          frameToString(\n            frame.stack.methodName,\n            frame.stack.file,\n            frame.stack.line1,\n            frame.stack.column1\n          )\n        )\n    }\n  }\n\n  return (\n    errorName +\n    ': ' +\n    error.message +\n    sourceMappedStack +\n    (sourceFrame !== null ? '\\n' + sourceFrame : '')\n  )\n}\n\nfunction sourceMapError(\n  this: void,\n  error: Error,\n  inspectOptions: util.InspectOptions\n): Error {\n  // Create a new Error object with the source mapping applied and then use native\n  // Node.js formatting on the result.\n  const newError =\n    error.cause !== undefined\n      ? // Setting an undefined `cause` would print `[cause]: undefined`\n        new Error(error.message, { cause: error.cause })\n      : new Error(error.message)\n\n  // TODO: Ensure `class MyError extends Error {}` prints `MyError` as the name\n  newError.stack = parseAndSourceMap(error, inspectOptions)\n\n  for (const key in error) {\n    if (!Object.prototype.hasOwnProperty.call(newError, key)) {\n      // @ts-expect-error -- We're copying all enumerable properties.\n      // So they definitely exist on `this` and obviously have no type on `newError` (yet)\n      newError[key] = error[key]\n    }\n  }\n\n  return newError\n}\n\nexport function patchErrorInspectNodeJS(\n  errorConstructor: ErrorConstructor\n): void {\n  const inspectSymbol = Symbol.for('nodejs.util.inspect.custom')\n\n  errorConstructor.prepareStackTrace = prepareUnsourcemappedStackTrace\n\n  // @ts-expect-error -- TODO upstream types\n  // eslint-disable-next-line no-extend-native -- We're not extending but overriding.\n  errorConstructor.prototype[inspectSymbol] = function (\n    depth: number,\n    inspectOptions: util.InspectOptions,\n    inspect: typeof util.inspect\n  ): string {\n    // avoid false-positive dynamic i/o warnings e.g. due to usage of `Math.random` in `source-map`.\n    return workUnitAsyncStorage.exit(() => {\n      const newError = sourceMapError(this, inspectOptions)\n\n      const originalCustomInspect = (newError as any)[inspectSymbol]\n      // Prevent infinite recursion.\n      // { customInspect: false } would result in `error.cause` not using our inspect.\n      Object.defineProperty(newError, inspectSymbol, {\n        value: undefined,\n        enumerable: false,\n        writable: true,\n      })\n      try {\n        return inspect(newError, {\n          ...inspectOptions,\n          depth:\n            (inspectOptions.depth ??\n              // Default in Node.js\n              2) - depth,\n        })\n      } finally {\n        ;(newError as any)[inspectSymbol] = originalCustomInspect\n      }\n    })\n  }\n}\n\nexport function patchErrorInspectEdgeLite(\n  errorConstructor: ErrorConstructor\n): void {\n  const inspectSymbol = Symbol.for('edge-runtime.inspect.custom')\n\n  errorConstructor.prepareStackTrace = prepareUnsourcemappedStackTrace\n\n  // @ts-expect-error -- TODO upstream types\n  // eslint-disable-next-line no-extend-native -- We're not extending but overriding.\n  errorConstructor.prototype[inspectSymbol] = function ({\n    format,\n  }: {\n    format: (...args: unknown[]) => string\n  }): string {\n    // avoid false-positive dynamic i/o warnings e.g. due to usage of `Math.random` in `source-map`.\n    return workUnitAsyncStorage.exit(() => {\n      const newError = sourceMapError(this, {})\n\n      const originalCustomInspect = (newError as any)[inspectSymbol]\n      // Prevent infinite recursion.\n      Object.defineProperty(newError, inspectSymbol, {\n        value: undefined,\n        enumerable: false,\n        writable: true,\n      })\n      try {\n        return format(newError)\n      } finally {\n        ;(newError as any)[inspectSymbol] = originalCustomInspect\n      }\n    })\n  }\n}\n"], "names": ["findSourceMap", "nativeFindSourceMap", "path", "url", "SourceMapConsumer", "SyncSourceMapConsumer", "findApplicableSourceMapPayload", "ignoreListAnonymousStackFramesIfSandwiched", "ignoreListAnonymousStackFramesIfSandwichedGeneric", "sourceMapIgnoreListsEverything", "parseStack", "getOriginalCodeFrame", "workUnitAsyncStorage", "dim", "bundlerFindSourceMapPayload", "undefined", "setBundlerFindSourceMapImplementation", "findSourceMapImplementation", "frameToString", "methodName", "sourceURL", "line1", "column1", "sourceLocation", "fileLocation", "startsWith", "URL", "canParse", "relative", "process", "cwd", "fileURLToPath", "computeErrorName", "error", "name", "prepareUnsourcemappedStackTrace", "structuredStackTrace", "message", "stack", "i", "length", "toString", "shouldIgnoreListGeneratedFrame", "file", "includes", "shouldIgnoreListOriginalFrame", "createUnsourcemappedFrame", "frame", "arguments", "ignored", "code", "sourceMappedFrames", "getSourcemappedFrameIfPossible", "sourceMapCache", "inspectOptions", "sourceMapCacheEntry", "get", "sourceMapConsumer", "sourceMapPayload", "pathToFileURL", "maybeSourceMapPayload", "sourceMap", "payload", "cause", "console", "set", "map", "sourcePosition", "originalPositionFor", "column", "line", "applicableSourceMap", "source", "sourceIndex", "sources", "indexOf", "ignoreList", "originalFrame", "replace", "codeFrame", "Object", "defineProperty", "sourceContent", "sourceContentFor", "colors", "parseAndSourceMap", "showIgnoreListed", "unparsedStack", "String", "errorName", "idx", "lastIndexOf", "slice", "unsourcemappedStack", "Map", "sourceFrame", "push", "sourcemappedFrame", "sourceMappedStack", "sourceMapError", "newError", "Error", "key", "prototype", "hasOwnProperty", "call", "patchErrorInspectNodeJS", "errorConstructor", "inspectSymbol", "Symbol", "for", "prepareStackTrace", "depth", "inspect", "exit", "originalCustomInspect", "value", "enumerable", "writable", "patchErrorInspectEdgeLite", "format"], "mappings": "AAAA,SAASA,iBAAiBC,mBAAmB,QAAQ,SAAQ;AAC7D,YAAYC,UAAU,OAAM;AAC5B,YAAYC,SAAS,MAAK;AAE1B,SAASC,qBAAqBC,qBAAqB,QAAQ,gCAA+B;AAC1F,SAEEC,8BAA8B,EAC9BC,8CAA8CC,iDAAiD,EAC/FC,8BAA8B,QACzB,oBAAmB;AAC1B,SAASC,UAAU,QAAyB,oBAAmB;AAC/D,SAASC,oBAAoB,QAAQ,iCAAgC;AACrE,SAASC,oBAAoB,QAAQ,gDAA+C;AACpF,SAASC,GAAG,QAAQ,oBAAmB;AAKvC,6CAA6C;AAC7C,iHAAiH;AACjH,8DAA8D;AAC9D,uCAAuC;AACvC,IAAIC,8BAAoD,IAAMC;AAE9D,OAAO,SAASC,sCACdC,2BAAiD;IAEjDH,8BAA8BG;AAChC;AAWA,SAASC,cACPC,UAAyB,EACzBC,SAAwB,EACxBC,KAAoB,EACpBC,OAAsB;IAEtB,IAAIC,iBAAiBF,UAAU,OAAO,CAAC,CAAC,EAAEA,OAAO,GAAG;IACpD,IAAIC,YAAY,QAAQC,mBAAmB,IAAI;QAC7CA,kBAAkB,CAAC,CAAC,EAAED,SAAS;IACjC;IAEA,IAAIE;IACJ,IACEJ,cAAc,QACdA,UAAUK,UAAU,CAAC,cACrBC,IAAIC,QAAQ,CAACP,YACb;QACA,2GAA2G;QAC3G,kGAAkG;QAClG,+EAA+E;QAC/E,4DAA4D;QAC5DI,eAAetB,KAAK0B,QAAQ,CAACC,QAAQC,GAAG,IAAI3B,IAAI4B,aAAa,CAACX;IAChE,OAAO,IAAIA,cAAc,QAAQA,UAAUK,UAAU,CAAC,MAAM;QAC1DD,eAAetB,KAAK0B,QAAQ,CAACC,QAAQC,GAAG,IAAIV;IAC9C,OAAO;QACLI,eAAeJ;IACjB;IAEA,OAAOD,aACH,CAAC,OAAO,EAAEA,WAAW,EAAE,EAAEK,eAAeD,eAAe,CAAC,CAAC,GACzD,CAAC,OAAO,EAAEC,eAAeD,gBAAgB;AAC/C;AAEA,SAASS,iBAAiBC,KAAY;IACpC,mDAAmD;IACnD,sGAAsG;IACtG,wFAAwF;IACxF,OAAOA,MAAMC,IAAI,IAAI;AACvB;AAEA,SAASC,gCACPF,KAAY,EACZG,oBAA2B;IAE3B,MAAMF,OAAOF,iBAAiBC;IAC9B,MAAMI,UAAUJ,MAAMI,OAAO,IAAI;IACjC,IAAIC,QAAQJ,OAAO,OAAOG;IAC1B,IAAK,IAAIE,IAAI,GAAGA,IAAIH,qBAAqBI,MAAM,EAAED,IAAK;QACpDD,SAAS,cAAcF,oBAAoB,CAACG,EAAE,CAACE,QAAQ;IACzD;IACA,OAAOH;AACT;AAEA,SAASI,+BAA+BC,IAAY;IAClD,OAAOA,KAAKlB,UAAU,CAAC,YAAYkB,KAAKC,QAAQ,CAAC;AACnD;AAEA,SAASC,8BAA8BF,IAAY;IACjD,OAAOA,KAAKC,QAAQ,CAAC;AACvB;AAYA,SAASE,0BACPC,KAA+B;IAE/B,OAAO;QACLT,OAAO;YACLK,MAAMI,MAAMJ,IAAI;YAChBtB,OAAO0B,MAAM1B,KAAK;YAClBC,SAASyB,MAAMzB,OAAO;YACtBH,YAAY4B,MAAM5B,UAAU;YAC5B6B,WAAWD,MAAMC,SAAS;YAC1BC,SAASP,+BAA+BK,MAAMJ,IAAI;QACpD;QACAO,MAAM;IACR;AACF;AAEA,SAAS3C,2CACP4C,kBAGE;IAEF,OAAO3C,kDACL2C,oBACA,CAACJ,QAAUA,MAAMT,KAAK,CAACK,IAAI,KAAK,eAChC,CAACI,QAAUA,MAAMT,KAAK,CAACW,OAAO,EAC9B,CAACF,QAAUA,MAAMT,KAAK,CAACnB,UAAU,EACjC,CAAC4B;QACCA,MAAMT,KAAK,CAACW,OAAO,GAAG;IACxB;AAEJ;AAEA;;;;CAIC,GACD,SAASG,+BACPL,KAA+B,EAC/BM,cAA8B,EAC9BC,cAAmC;QA8HrBP,2BAAAA;IAzHd,MAAMQ,sBAAsBF,eAAeG,GAAG,CAACT,MAAMJ,IAAI;IACzD,IAAIc;IACJ,IAAIC;IACJ,IAAIH,wBAAwBxC,WAAW;QACrC,IAAIK,YAAY2B,MAAMJ,IAAI;QAC1B,wEAAwE;QACxE,uGAAuG;QACvG,4GAA4G;QAC5G,IAAIvB,UAAUK,UAAU,CAAC,MAAM;YAC7BL,YAAYjB,IAAIwD,aAAa,CAACZ,MAAMJ,IAAI,EAAEF,QAAQ;QACpD;QACA,IAAImB;QACJ,IAAI;YACF,MAAMC,YAAY5D,oBAAoBmB;YACtCwC,wBAAwBC,6BAAAA,UAAWC,OAAO;QAC5C,EAAE,OAAOC,OAAO;YACd,6EAA6E;YAC7E,8EAA8E;YAC9EC,QAAQ/B,KAAK,CACX,GAAGb,UAAU,gGAAgG,EAAE2C,OAAO;YAExH,gDAAgD;YAChD,8CAA8C;YAC9CV,eAAeY,GAAG,CAAClB,MAAMJ,IAAI,EAAE;YAC/B,wEAAwE;YACxE,qEAAqE;YACrE,sBAAsB;YACtB,wEAAwE;YACxE,uEAAuE;YACvE,cAAc;YACd,OAAOG,0BAA0BC;QACnC;QACA,IAAIa,0BAA0B7C,WAAW;YACvC6C,wBAAwB9C,4BAA4BM;QACtD;QAEA,IAAIwC,0BAA0B7C,WAAW;YACvC,OAAO+B,0BAA0BC;QACnC;QACAW,mBAAmBE;QACnB,IAAI;YACFH,oBAAoB,IAAIpD,sBACtB,qGAAqG;YACrGqD;QAEJ,EAAE,OAAOK,OAAO;YACd,6EAA6E;YAC7E,8EAA8E;YAC9EC,QAAQ/B,KAAK,CACX,GAAGb,UAAU,gGAAgG,EAAE2C,OAAO;YAExH,8DAA8D;YAC9D,8CAA8C;YAC9CV,eAAeY,GAAG,CAAClB,MAAMJ,IAAI,EAAE;YAC/B,OAAOG,0BAA0BC;QACnC;QACAM,eAAeY,GAAG,CAAClB,MAAMJ,IAAI,EAAE;YAC7BuB,KAAKT;YACLK,SAASJ;QACX;IACF,OAAO,IAAIH,wBAAwB,MAAM;QACvC,qDAAqD;QACrD,uCAAuC;QACvC,iCAAiC;QACjC,OAAOT,0BAA0BC;IACnC,OAAO;QACLU,oBAAoBF,oBAAoBW,GAAG;QAC3CR,mBAAmBH,oBAAoBO,OAAO;IAChD;IAEA,MAAMK,iBAAiBV,kBAAkBW,mBAAmB,CAAC;QAC3DC,QAAQ,AAACtB,CAAAA,MAAMzB,OAAO,IAAI,CAAA,IAAK;QAC/BgD,MAAMvB,MAAM1B,KAAK,IAAI;IACvB;IAEA,MAAMkD,sBAAsBjE,+BAC1B,AAACyC,CAAAA,MAAM1B,KAAK,IAAI,CAAA,IAAK,GACrB,AAAC0B,CAAAA,MAAMzB,OAAO,IAAI,CAAA,IAAK,GACvBoC;IAEF,IAAIT,UACFsB,wBAAwBxD,aACxBN,+BAA+B8D;IACjC,IAAIJ,eAAeK,MAAM,KAAK,MAAM;QAClC,OAAO;YACLlC,OAAO;gBACLU,WAAWD,MAAMC,SAAS;gBAC1BL,MAAMI,MAAMJ,IAAI;gBAChBtB,OAAO0B,MAAM1B,KAAK;gBAClBC,SAASyB,MAAMzB,OAAO;gBACtBH,YAAY4B,MAAM5B,UAAU;gBAC5B8B,SAASA,WAAWP,+BAA+BK,MAAMJ,IAAI;YAC/D;YACAO,MAAM;QACR;IACF;IAEA,0GAA0G;IAC1G,IAAIqB,wBAAwBxD,WAAW;QACrCiD,QAAQ/B,KAAK,CAAC,wDAAwDc;IACxE,OAAO,IAAI,CAACE,WAAWJ,8BAA8BsB,eAAeK,MAAM,GAAG;QAC3E,0DAA0D;QAC1D,qDAAqD;QACrD,2FAA2F;QAC3F,wEAAwE;QACxE,+DAA+D;QAC/D,iFAAiF;QACjFvB,UAAU;IACZ,OAAO,IAAI,CAACA,SAAS;YAKTsB;QAJV,wDAAwD;QACxD,MAAME,cAAcF,oBAAoBG,OAAO,CAACC,OAAO,CACrDR,eAAeK,MAAM;QAEvBvB,UAAUsB,EAAAA,kCAAAA,oBAAoBK,UAAU,qBAA9BL,gCAAgC3B,QAAQ,CAAC6B,iBAAgB;IACrE;IAEA,MAAMI,gBAAqC;QACzC,sEAAsE;QACtE,4EAA4E;QAC5E,kCAAkC;QAClC,oGAAoG;QACpG1D,UAAU,GAAE4B,oBAAAA,MAAM5B,UAAU,sBAAhB4B,4BAAAA,kBACR+B,OAAO,CAAC,8BAA8B,+BAD9B/B,0BAER+B,OAAO,CAAC,wBAAwB;QACpCnC,MAAMwB,eAAeK,MAAM;QAC3BnD,OAAO8C,eAAeG,IAAI;QAC1BhD,SAAS6C,eAAeE,MAAM,GAAG;QACjC,6EAA6E;QAC7ErB,WAAW,EAAE;QACbC;IACF;IAEA,gCAAgC,GAChC,IAAI8B;IAEJ,OAAOC,OAAOC,cAAc,CAC1B;QACE3C,OAAOuC;QACP3B,MAAM;IACR,GACA,QACA;QACEM,KAAK;YACH,IAAIuB,cAAchE,WAAW;gBAC3B,MAAMmE,gBACJzB,kBAAkB0B,gBAAgB,CAChChB,eAAeK,MAAM,EACrB,uBAAuB,GAAG,SACvB;gBACPO,YAAYpE,qBACVkE,eACAK,eACA5B,eAAe8B,MAAM;YAEzB;YACA,OAAOL;QACT;IACF;AAEJ;AAEA,SAASM,kBACPpD,KAAY,EACZqB,cAAmC;IAEnC,8EAA8E;IAC9E,MAAMgC,mBAAmB;IACzB,mFAAmF;IACnF,IAAIC,gBAAgBC,OAAOvD,MAAMK,KAAK;IACtC,4CAA4C;IAC5C,qFAAqF;IACrF,oDAAoD;IACpD,MAAMmD,YAAYzD,iBAAiBC;IAEnC,IAAIyD,MAAMH,cAAcZ,OAAO,CAAC;IAChC,IAAIe,QAAQ,CAAC,GAAG;QACdA,MAAMH,cAAcI,WAAW,CAAC,MAAMD;IACxC,OAAO;QACLA,MAAMH,cAAcZ,OAAO,CAAC;QAC5B,IAAIe,QAAQ,CAAC,GAAG;YACdA,MAAMH,cAAcI,WAAW,CAAC,MAAMD;QACxC;IACF;IACA,IAAIA,QAAQ,CAAC,KAAK,CAACJ,kBAAkB;QACnC,4EAA4E;QAC5EC,gBAAgBA,cAAcK,KAAK,CAAC,GAAGF;IACzC;IAEA,MAAMG,sBAAsBnF,WAAW6E;IACvC,MAAMlC,iBAAiC,IAAIyC;IAE3C,MAAM3C,qBAGD,EAAE;IACP,IAAI4C,cAA6B;IACjC,KAAK,MAAMhD,SAAS8C,oBAAqB;QACvC,IAAI9C,MAAMJ,IAAI,KAAK,MAAM;YACvBQ,mBAAmB6C,IAAI,CAAC;gBACtB9C,MAAM;gBACNZ,OAAO;oBACLK,MAAMI,MAAMJ,IAAI;oBAChBtB,OAAO0B,MAAM1B,KAAK;oBAClBC,SAASyB,MAAMzB,OAAO;oBACtBH,YAAY4B,MAAM5B,UAAU;oBAC5B6B,WAAWD,MAAMC,SAAS;oBAC1BC,SAAS;gBACX;YACF;QACF,OAAO;YACL,MAAMgD,oBAAoB7C,+BACxB,+DAA+D;YAC/DL,OACAM,gBACAC;YAEFH,mBAAmB6C,IAAI,CAACC;YAExB,yCAAyC;YACzC,qEAAqE;YACrE,2EAA2E;YAC3E,IACEF,gBAAgB,QAChB,kCAAkC;YAClC,CAACE,kBAAkB3D,KAAK,CAACW,OAAO,IAChCgD,kBAAkB/C,IAAI,KAAK,MAC3B;gBACA6C,cAAcE,kBAAkB/C,IAAI;YACtC;QACF;IACF;IAEA3C,2CAA2C4C;IAE3C,IAAI+C,oBAAoB;IACxB,IAAK,IAAI3D,IAAI,GAAGA,IAAIY,mBAAmBX,MAAM,EAAED,IAAK;QAClD,MAAMQ,QAAQI,kBAAkB,CAACZ,EAAE;QAEnC,IAAI,CAACQ,MAAMT,KAAK,CAACW,OAAO,EAAE;YACxBiD,qBACE,OACAhF,cACE6B,MAAMT,KAAK,CAACnB,UAAU,EACtB4B,MAAMT,KAAK,CAACK,IAAI,EAChBI,MAAMT,KAAK,CAACjB,KAAK,EACjB0B,MAAMT,KAAK,CAAChB,OAAO;QAEzB,OAAO,IAAIgE,kBAAkB;YAC3BY,qBACE,OACArF,IACEK,cACE6B,MAAMT,KAAK,CAACnB,UAAU,EACtB4B,MAAMT,KAAK,CAACK,IAAI,EAChBI,MAAMT,KAAK,CAACjB,KAAK,EACjB0B,MAAMT,KAAK,CAAChB,OAAO;QAG3B;IACF;IAEA,OACEmE,YACA,OACAxD,MAAMI,OAAO,GACb6D,oBACCH,CAAAA,gBAAgB,OAAO,OAAOA,cAAc,EAAC;AAElD;AAEA,SAASI,eAEPlE,KAAY,EACZqB,cAAmC;IAEnC,gFAAgF;IAChF,oCAAoC;IACpC,MAAM8C,WACJnE,MAAM8B,KAAK,KAAKhD,YAEZ,qBAAgD,CAAhD,IAAIsF,MAAMpE,MAAMI,OAAO,EAAE;QAAE0B,OAAO9B,MAAM8B,KAAK;IAAC,IAA9C,qBAAA;eAAA;oBAAA;sBAAA;IAA+C,KAC/C,qBAAwB,CAAxB,IAAIsC,MAAMpE,MAAMI,OAAO,GAAvB,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;IAE7B,6EAA6E;IAC7E+D,SAAS9D,KAAK,GAAG+C,kBAAkBpD,OAAOqB;IAE1C,IAAK,MAAMgD,OAAOrE,MAAO;QACvB,IAAI,CAAC+C,OAAOuB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,UAAUE,MAAM;YACxD,+DAA+D;YAC/D,oFAAoF;YACpFF,QAAQ,CAACE,IAAI,GAAGrE,KAAK,CAACqE,IAAI;QAC5B;IACF;IAEA,OAAOF;AACT;AAEA,OAAO,SAASM,wBACdC,gBAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,GAAG,CAAC;IAEjCH,iBAAiBI,iBAAiB,GAAG5E;IAErC,0CAA0C;IAC1C,mFAAmF;IACnFwE,iBAAiBJ,SAAS,CAACK,cAAc,GAAG,SAC1CI,KAAa,EACb1D,cAAmC,EACnC2D,OAA4B;QAE5B,gGAAgG;QAChG,OAAOrG,qBAAqBsG,IAAI,CAAC;YAC/B,MAAMd,WAAWD,eAAe,IAAI,EAAE7C;YAEtC,MAAM6D,wBAAwB,AAACf,QAAgB,CAACQ,cAAc;YAC9D,8BAA8B;YAC9B,gFAAgF;YAChF5B,OAAOC,cAAc,CAACmB,UAAUQ,eAAe;gBAC7CQ,OAAOrG;gBACPsG,YAAY;gBACZC,UAAU;YACZ;YACA,IAAI;gBACF,OAAOL,QAAQb,UAAU;oBACvB,GAAG9C,cAAc;oBACjB0D,OACE,AAAC1D,CAAAA,eAAe0D,KAAK,IACnB,qBAAqB;oBACrB,CAAA,IAAKA;gBACX;YACF,SAAU;;gBACNZ,QAAgB,CAACQ,cAAc,GAAGO;YACtC;QACF;IACF;AACF;AAEA,OAAO,SAASI,0BACdZ,gBAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,GAAG,CAAC;IAEjCH,iBAAiBI,iBAAiB,GAAG5E;IAErC,0CAA0C;IAC1C,mFAAmF;IACnFwE,iBAAiBJ,SAAS,CAACK,cAAc,GAAG,SAAU,EACpDY,MAAM,EAGP;QACC,gGAAgG;QAChG,OAAO5G,qBAAqBsG,IAAI,CAAC;YAC/B,MAAMd,WAAWD,eAAe,IAAI,EAAE,CAAC;YAEvC,MAAMgB,wBAAwB,AAACf,QAAgB,CAACQ,cAAc;YAC9D,8BAA8B;YAC9B5B,OAAOC,cAAc,CAACmB,UAAUQ,eAAe;gBAC7CQ,OAAOrG;gBACPsG,YAAY;gBACZC,UAAU;YACZ;YACA,IAAI;gBACF,OAAOE,OAAOpB;YAChB,SAAU;;gBACNA,QAAgB,CAACQ,cAAc,GAAGO;YACtC;QACF;IACF;AACF", "ignoreList": [0]}