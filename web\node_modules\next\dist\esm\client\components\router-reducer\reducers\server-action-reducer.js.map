{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "sourcesContent": ["import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_ACTION_NOT_FOUND_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\nimport { UnrecognizedActionError } from '../../unrecognized-action-error'\n\n// TODO: Explicitly import from client.browser\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  createFromFetch as createFromFetchBrowser,\n  createTemporaryReferenceSet,\n  encodeReply,\n} from 'react-server-dom-webpack/client'\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nconst createFromFetch =\n  createFromFetchBrowser as (typeof import('react-server-dom-webpack/client.browser'))['createFromFetch']\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult: ActionResult | undefined\n  actionFlightData: NormalizedFlightData[] | string | undefined\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch(state.canonicalUrl, {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n        state.tree\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  // Handle server actions that the server didn't recognize.\n  const unrecognizedActionHeader = res.headers.get(NEXT_ACTION_NOT_FOUND_HEADER)\n  if (unrecognizedActionHeader === '1') {\n    throw new UnrecognizedActionError(\n      `Server Action \"${actionId}\" was not found on the server. \\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = NO_REVALIDATED_PARTS\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n  const isRscResponse = !!(\n    contentType && contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n  )\n\n  // Handle invalid server action responses.\n  // A valid response must have `content-type: text/x-component`, unless it's an external redirect.\n  // (external redirects have an 'x-action-redirect' header, but the body is an empty 'text/plain')\n  if (!isRscResponse && !redirectLocation) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const message =\n      res.status >= 400 && contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(message)\n  }\n\n  let actionResult: FetchServerActionResult['actionResult']\n  let actionFlightData: FetchServerActionResult['actionFlightData']\n  if (isRscResponse) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    // An internal redirect can send an RSC response, but does not have a useful `actionResult`.\n    actionResult = redirectLocation ? undefined : response.a\n    actionFlightData = normalizeFlightData(response.f)\n  } else {\n    // An external redirect doesn't contain RSC data.\n    actionResult = undefined\n    actionFlightData = undefined\n  }\n\n  return {\n    actionResult,\n    actionFlightData,\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\nconst NO_REVALIDATED_PARTS = {\n  paths: [],\n  tag: false,\n  cookie: false,\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  const navigatedAt = Date.now()\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              navigatedAt,\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n"], "names": ["callServer", "findSourceMapURL", "ACTION_HEADER", "NEXT_ACTION_NOT_FOUND_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "UnrecognizedActionError", "createFromFetch", "createFromFetchBrowser", "createTemporaryReferenceSet", "encodeReply", "PrefetchKind", "assignLocation", "createHrefFromUrl", "handleExternalUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleMutable", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "hasInterceptionRouteInCurrentTree", "handleSegmentMismatch", "refreshInactiveParallelSegments", "normalizeFlightData", "prepareFlightRouterStateForRequest", "getRedirectError", "RedirectType", "createSeededPrefetchCacheEntry", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "extractInfoFromServerReferenceId", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revalidateEntireCache", "fetchServerAction", "state", "nextUrl", "actionId", "actionArgs", "temporaryReferences", "info", "usedArgs", "type", "body", "res", "fetch", "canonicalUrl", "method", "headers", "Accept", "tree", "process", "env", "NEXT_DEPLOYMENT_ID", "unrecognizedActionHeader", "get", "redirectHeader", "location", "_redirectType", "split", "redirectType", "push", "replace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "revalidatedParts", "revalidatedHeader", "JSON", "parse", "paths", "tag", "cookie", "e", "NO_REVALIDATED_PARTS", "redirectLocation", "URL", "window", "href", "contentType", "isRscResponse", "startsWith", "message", "status", "text", "Error", "actionResult", "actionFlightData", "response", "Promise", "resolve", "a", "f", "serverActionReducer", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "navigatedAt", "Date", "now", "then", "flightData", "redirectHref", "pushRef", "pendingPush", "actionRevalidated", "length", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "rsc", "cache", "prefetchRsc", "loading", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchCache", "Map", "updatedTree", "updatedCache", "includeNextUrl", "Boolean", "patchedTree", "url", "data", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "kind", "FULL", "AUTO"], "mappings": "AAIA,SAASA,UAAU,QAAQ,2BAA0B;AACrD,SAASC,gBAAgB,QAAQ,mCAAkC;AACnE,SACEC,aAAa,EACbC,4BAA4B,EAC5BC,wBAAwB,EACxBC,6BAA6B,EAC7BC,QAAQ,EACRC,uBAAuB,QAClB,2BAA0B;AACjC,SAASC,uBAAuB,QAAQ,kCAAiC;AAEzE,8CAA8C;AAC9C,6DAA6D;AAC7D,SACEC,mBAAmBC,sBAAsB,EACzCC,2BAA2B,EAC3BC,WAAW,QACN,kCAAiC;AAExC,SACEC,YAAY,QAKP,0BAAyB;AAChC,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAEjF,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SACEC,mBAAmB,EACnBC,kCAAkC,QAE7B,+BAA8B;AACrC,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,YAAY,QAAQ,uBAAsB;AACnD,SAASC,8BAA8B,QAAQ,0BAAyB;AACxE,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SACEC,gCAAgC,EAChCC,cAAc,QACT,+CAA8C;AACrD,SAASC,qBAAqB,QAAQ,sBAAqB;AAE3D,MAAMzB,kBACJC;AAeF,eAAeyB,kBACbC,KAA2B,EAC3BC,OAAwC,EACxC,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,sBAAsB7B;IAC5B,MAAM8B,OAAOT,iCAAiCM;IAE9C,6EAA6E;IAC7E,8EAA8E;IAC9E,+CAA+C;IAC/C,MAAMI,WACJD,KAAKE,IAAI,KAAK,cAAcV,eAAeM,YAAYE,QAAQF;IAEjE,MAAMK,OAAO,MAAMhC,YAAY8B,UAAU;QAAEF;IAAoB;IAE/D,MAAMK,MAAM,MAAMC,MAAMV,MAAMW,YAAY,EAAE;QAC1CC,QAAQ;QACRC,SAAS;YACPC,QAAQ3C;YACR,CAACL,cAAc,EAAEoC;YACjB,CAACjC,8BAA8B,EAAEqB,mCAC/BU,MAAMe,IAAI;YAEZ,GAAIC,QAAQC,GAAG,CAACC,kBAAkB,GAC9B;gBACE,mBAAmBF,QAAQC,GAAG,CAACC,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIjB,UACA;gBACE,CAAC/B,SAAS,EAAE+B;YACd,IACA,CAAC,CAAC;QACR;QACAO;IACF;IAEA,0DAA0D;IAC1D,MAAMW,2BAA2BV,IAAII,OAAO,CAACO,GAAG,CAACrD;IACjD,IAAIoD,6BAA6B,KAAK;QACpC,MAAM,qBAEL,CAFK,IAAI/C,wBACR,AAAC,oBAAiB8B,WAAS,8GADvB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMmB,iBAAiBZ,IAAII,OAAO,CAACO,GAAG,CAAC;IACvC,MAAM,CAACE,UAAUC,cAAc,GAAGF,CAAAA,kCAAAA,eAAgBG,KAAK,CAAC,SAAQ,EAAE;IAClE,IAAIC;IACJ,OAAQF;QACN,KAAK;YACHE,eAAejC,aAAakC,IAAI;YAChC;QACF,KAAK;YACHD,eAAejC,aAAamC,OAAO;YACnC;QACF;YACEF,eAAeG;IACnB;IAEA,MAAMC,cAAc,CAAC,CAACpB,IAAII,OAAO,CAACO,GAAG,CAACpD;IACtC,IAAI8D;IACJ,IAAI;QACF,MAAMC,oBAAoBC,KAAKC,KAAK,CAClCxB,IAAII,OAAO,CAACO,GAAG,CAAC,2BAA2B;QAE7CU,mBAAmB;YACjBI,OAAOH,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCI,KAAK,CAAC,CAACJ,iBAAiB,CAAC,EAAE;YAC3BK,QAAQL,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOM,GAAG;QACVP,mBAAmBQ;IACrB;IAEA,MAAMC,mBAAmBjB,WACrB5C,eACE4C,UACA,IAAIkB,IAAIxC,MAAMW,YAAY,EAAE8B,OAAOnB,QAAQ,CAACoB,IAAI,KAElDd;IAEJ,MAAMe,cAAclC,IAAII,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMwB,gBAAgB,CAAC,CACrBD,CAAAA,eAAeA,YAAYE,UAAU,CAAC1E,wBAAuB;IAG/D,0CAA0C;IAC1C,iGAAiG;IACjG,iGAAiG;IACjG,IAAI,CAACyE,iBAAiB,CAACL,kBAAkB;QACvC,kGAAkG;QAClG,sBAAsB;QACtB,MAAMO,UACJrC,IAAIsC,MAAM,IAAI,OAAOJ,gBAAgB,eACjC,MAAMlC,IAAIuC,IAAI,KACd;QAEN,MAAM,qBAAkB,CAAlB,IAAIC,MAAMH,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAII;IACJ,IAAIC;IACJ,IAAIP,eAAe;QACjB,MAAMQ,WAAiC,MAAM/E,gBAC3CgF,QAAQC,OAAO,CAAC7C,MAChB;YAAE7C;YAAYC;YAAkBuC;QAAoB;QAGtD,4FAA4F;QAC5F8C,eAAeX,mBAAmBX,YAAYwB,SAASG,CAAC;QACxDJ,mBAAmB9D,oBAAoB+D,SAASI,CAAC;IACnD,OAAO;QACL,iDAAiD;QACjDN,eAAetB;QACfuB,mBAAmBvB;IACrB;IAEA,OAAO;QACLsB;QACAC;QACAZ;QACAd;QACAK;QACAD;IACF;AACF;AAEA,MAAMS,uBAAuB;IAC3BJ,OAAO,EAAE;IACTC,KAAK;IACLC,QAAQ;AACV;AAEA;;;CAGC,GACD,OAAO,SAASqB,oBACdzD,KAA2B,EAC3B0D,MAA0B;IAE1B,MAAM,EAAEJ,OAAO,EAAEK,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IAEtC,IAAIC,cAAc7D,MAAMe,IAAI;IAE5B6C,QAAQE,0BAA0B,GAAG;IAErC,2GAA2G;IAC3G,mEAAmE;IACnE,4EAA4E;IAC5E,wDAAwD;IACxD,MAAM7D,UACJD,MAAMC,OAAO,IAAIf,kCAAkCc,MAAMe,IAAI,IACzDf,MAAMC,OAAO,GACb;IAEN,MAAM8D,cAAcC,KAAKC,GAAG;IAE5B,OAAOlE,kBAAkBC,OAAOC,SAASyD,QAAQQ,IAAI,CACnD;YAAO,EACLhB,YAAY,EACZC,kBAAkBgB,UAAU,EAC5B5B,gBAAgB,EAChBd,YAAY,EACZI,WAAW,EACXC,gBAAgB,EACjB;QACC,IAAIsC;QAEJ,mFAAmF;QACnF,IAAI7B,kBAAkB;YACpB,IAAId,iBAAiBjC,aAAamC,OAAO,EAAE;gBACzC3B,MAAMqE,OAAO,CAACC,WAAW,GAAG;gBAC5BV,QAAQU,WAAW,GAAG;YACxB,OAAO;gBACLtE,MAAMqE,OAAO,CAACC,WAAW,GAAG;gBAC5BV,QAAQU,WAAW,GAAG;YACxB;YAEAF,eAAezF,kBAAkB4D,kBAAkB;YACnDqB,QAAQjD,YAAY,GAAGyD;QACzB;QAEA,IAAI,CAACD,YAAY;YACfb,QAAQJ;YAER,2EAA2E;YAC3E,IAAIX,kBAAkB;gBACpB,OAAO3D,kBACLoB,OACA4D,SACArB,iBAAiBG,IAAI,EACrB1C,MAAMqE,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOtE;QACT;QAEA,IAAI,OAAOmE,eAAe,UAAU;YAClC,4DAA4D;YAC5Db,QAAQJ;YAER,OAAOtE,kBACLoB,OACA4D,SACAO,YACAnE,MAAMqE,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMC,oBACJzC,iBAAiBI,KAAK,CAACsC,MAAM,GAAG,KAChC1C,iBAAiBK,GAAG,IACpBL,iBAAiBM,MAAM;QAEzB,KAAK,MAAMqC,wBAAwBN,WAAY;YAC7C,MAAM,EACJpD,MAAM2D,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ1B,QAAQJ;gBAER,OAAOlD;YACT;YAEA,mGAAmG;YACnG,MAAMiF,UAAUpG,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJgF,aACAa,WACAN,eAAeA,eAAepE,MAAMW,YAAY;YAGlD,IAAIsE,YAAY,MAAM;gBACpB3B,QAAQJ;gBAER,OAAO/D,sBAAsBa,OAAO0D,QAAQgB;YAC9C;YAEA,IAAI5F,4BAA4B+E,aAAaoB,UAAU;gBACrD3B,QAAQJ;gBAER,OAAOtE,kBACLoB,OACA4D,SACAQ,gBAAgBpE,MAAMW,YAAY,EAClCX,MAAMqE,OAAO,CAACC,WAAW;YAE7B;YAEA,4FAA4F;YAC5F,IAAIM,sBAAsB,MAAM;gBAC9B,MAAMM,MAAMN,iBAAiB,CAAC,EAAE;gBAChC,MAAMO,QAAmBlG;gBACzBkG,MAAMD,GAAG,GAAGA;gBACZC,MAAMC,WAAW,GAAG;gBACpBD,MAAME,OAAO,GAAGT,iBAAiB,CAAC,EAAE;gBACpC5F,8BACE+E,aACAoB,OACA,yFAAyF;gBACzFvD,WACA8C,WACAE,mBACAC,MACAjD;gBAGFgC,QAAQuB,KAAK,GAAGA;gBAChB,IAAInE,QAAQC,GAAG,CAACqE,2BAA2B,EAAE;oBAC3CxF,sBAAsBE,MAAMC,OAAO,EAAEgF;gBACvC,OAAO;oBACLrB,QAAQ2B,aAAa,GAAG,IAAIC;gBAC9B;gBACA,IAAIjB,mBAAmB;oBACrB,MAAMnF,gCAAgC;wBACpC2E;wBACA/D;wBACAyF,aAAaR;wBACbS,cAAcP;wBACdQ,gBAAgBC,QAAQ3F;wBACxBU,cAAciD,QAAQjD,YAAY,IAAIX,MAAMW,YAAY;oBAC1D;gBACF;YACF;YAEAiD,QAAQiC,WAAW,GAAGZ;YACtBpB,cAAcoB;QAChB;QAEA,IAAI1C,oBAAoB6B,cAAc;YACpC,IAAI,CAACpD,QAAQC,GAAG,CAACqE,2BAA2B,IAAI,CAACf,mBAAmB;gBAClE,6FAA6F;gBAC7F,2FAA2F;gBAC3F,6EAA6E;gBAC7E,uFAAuF;gBACvF,uEAAuE;gBACvE,8DAA8D;gBAC9D,kEAAkE;gBAClE,mEAAmE;gBACnE,kEAAkE;gBAClE,eAAe;gBACf9E,+BAA+B;oBAC7BqG,KAAKvD;oBACLwD,MAAM;wBACJ5B;wBACAxD,cAAciB;wBACdoE,oBAAoB;wBACpBC,aAAa;wBACbC,WAAW;wBACX,2DAA2D;wBAC3D,oCAAoC;wBACpCC,WAAW,CAAC;oBACd;oBACApF,MAAMf,MAAMe,IAAI;oBAChBwE,eAAevF,MAAMuF,aAAa;oBAClCtF,SAASD,MAAMC,OAAO;oBACtBmG,MAAMvE,cAAcpD,aAAa4H,IAAI,GAAG5H,aAAa6H,IAAI;gBAC3D;gBACA1C,QAAQ2B,aAAa,GAAGvF,MAAMuF,aAAa;YAC7C;YAEA,+EAA+E;YAC/E,+EAA+E;YAC/E,sFAAsF;YACtF,oFAAoF;YACpF,mFAAmF;YACnF,2CAA2C;YAC3C5B,OACEpE,iBACEI,YAAYyE,gBACR1E,eAAe0E,gBACfA,cACJ3C,gBAAgBjC,aAAakC,IAAI;QAGvC,OAAO;YACL4B,QAAQJ;QACV;QAEA,OAAOnE,cAAciB,OAAO4D;IAC9B,GACA,CAACvB;QACC,mHAAmH;QACnHsB,OAAOtB;QAEP,OAAOrC;IACT;AAEJ", "ignoreList": [0]}