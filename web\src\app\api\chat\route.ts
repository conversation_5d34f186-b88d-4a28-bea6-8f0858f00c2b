// web/src/app/api/chat/route.ts
import { NextRequest, NextResponse } from 'next/server';
export async function POST(req: NextRequest) {
  const { query } = await req.json();
  const r = await fetch(process.env.NEXT_PUBLIC_BACKEND_URL! + '/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query })
  });
  const data = await r.json();
  return NextResponse.json(data);
}
