{"version": 3, "sources": ["../../../src/server/route-matcher-managers/dev-route-matcher-manager.ts"], "sourcesContent": ["import { RouteKind } from '../route-kind'\nimport type { RouteMatch } from '../route-matches/route-match'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport { DefaultRouteMatcherManager } from './default-route-matcher-manager'\nimport type { MatchOptions, RouteMatcherManager } from './route-matcher-manager'\nimport path from '../../shared/lib/isomorphic/path'\nimport * as Log from '../../build/output/log'\nimport { cyan } from '../../lib/picocolors'\nimport type { RouteMatcher } from '../route-matchers/route-matcher'\n\nexport interface RouteEnsurer {\n  ensure(match: RouteMatch, pathname: string): Promise<void>\n}\n\nexport class DevRouteMatcherManager extends DefaultRouteMatcherManager {\n  constructor(\n    private readonly production: RouteMatcherManager,\n    private readonly ensurer: RouteEnsurer,\n    private readonly dir: string\n  ) {\n    super()\n  }\n\n  public async test(pathname: string, options: MatchOptions): Promise<boolean> {\n    // Try to find a match within the developer routes.\n    const match = await super.match(pathname, options)\n\n    // Return if the match wasn't null. Unlike the implementation of `match`\n    // which uses `matchAll` here, this does not call `ensure` on the match\n    // found via the development matches.\n    return match !== null\n  }\n\n  protected validate(\n    pathname: string,\n    matcher: RouteMatcher,\n    options: MatchOptions\n  ): RouteMatch | null {\n    const match = super.validate(pathname, matcher, options)\n\n    // If a match was found, check to see if there were any conflicting app or\n    // pages files.\n    // TODO: maybe expand this to _any_ duplicated routes instead?\n    if (\n      match &&\n      matcher.duplicated &&\n      matcher.duplicated.some(\n        (duplicate) =>\n          duplicate.definition.kind === RouteKind.APP_PAGE ||\n          duplicate.definition.kind === RouteKind.APP_ROUTE\n      ) &&\n      matcher.duplicated.some(\n        (duplicate) =>\n          duplicate.definition.kind === RouteKind.PAGES ||\n          duplicate.definition.kind === RouteKind.PAGES_API\n      )\n    ) {\n      return null\n    }\n\n    return match\n  }\n\n  public async *matchAll(\n    pathname: string,\n    options: MatchOptions\n  ): AsyncGenerator<RouteMatch<RouteDefinition<RouteKind>>, null, undefined> {\n    // Compile the development routes.\n    // TODO: we may want to only run this during testing, users won't be fast enough to require this many dir scans\n    await super.reload()\n\n    // Iterate over the development matches to see if one of them match the\n    // request path.\n    for await (const developmentMatch of super.matchAll(pathname, options)) {\n      // We're here, which means that we haven't seen this match yet, so we\n      // should try to ensure it and recompile the production matcher.\n      await this.ensurer.ensure(developmentMatch, pathname)\n      await this.production.reload()\n\n      // Iterate over the production matches again, this time we should be able\n      // to match it against the production matcher unless there's an error.\n      for await (const productionMatch of this.production.matchAll(\n        pathname,\n        options\n      )) {\n        yield productionMatch\n      }\n    }\n\n    // We tried direct matching against the pathname and against all the dynamic\n    // paths, so there was no match.\n    return null\n  }\n\n  public async reload(): Promise<void> {\n    // Compile the production routes again.\n    await this.production.reload()\n\n    // Compile the development routes.\n    await super.reload()\n\n    // Check for and warn of any duplicates.\n    for (const [pathname, matchers] of Object.entries(\n      this.matchers.duplicates\n    )) {\n      // We only want to warn about matchers resolving to the same path if their\n      // identities are different.\n      const identity = matchers[0].identity\n      if (matchers.slice(1).some((matcher) => matcher.identity !== identity)) {\n        continue\n      }\n\n      Log.warn(\n        `Duplicate page detected. ${matchers\n          .map((matcher) =>\n            cyan(path.relative(this.dir, matcher.definition.filename))\n          )\n          .join(' and ')} resolve to ${cyan(pathname)}`\n      )\n    }\n  }\n}\n"], "names": ["RouteKind", "DefaultRouteMatcherManager", "path", "Log", "cyan", "DevRouteMatcherManager", "constructor", "production", "ensurer", "dir", "test", "pathname", "options", "match", "validate", "matcher", "duplicated", "some", "duplicate", "definition", "kind", "APP_PAGE", "APP_ROUTE", "PAGES", "PAGES_API", "matchAll", "reload", "developmentMatch", "ensure", "productionMatch", "matchers", "Object", "entries", "duplicates", "identity", "slice", "warn", "map", "relative", "filename", "join"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAe;AAGzC,SAASC,0BAA0B,QAAQ,kCAAiC;AAE5E,OAAOC,UAAU,mCAAkC;AACnD,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,uBAAsB;AAO3C,OAAO,MAAMC,+BAA+BJ;IAC1CK,YACE,AAAiBC,UAA+B,EAChD,AAAiBC,OAAqB,EACtC,AAAiBC,GAAW,CAC5B;QACA,KAAK,SAJYF,aAAAA,iBACAC,UAAAA,cACAC,MAAAA;IAGnB;IAEA,MAAaC,KAAKC,QAAgB,EAAEC,OAAqB,EAAoB;QAC3E,mDAAmD;QACnD,MAAMC,QAAQ,MAAM,KAAK,CAACA,MAAMF,UAAUC;QAE1C,wEAAwE;QACxE,uEAAuE;QACvE,qCAAqC;QACrC,OAAOC,UAAU;IACnB;IAEUC,SACRH,QAAgB,EAChBI,OAAqB,EACrBH,OAAqB,EACF;QACnB,MAAMC,QAAQ,KAAK,CAACC,SAASH,UAAUI,SAASH;QAEhD,0EAA0E;QAC1E,eAAe;QACf,8DAA8D;QAC9D,IACEC,SACAE,QAAQC,UAAU,IAClBD,QAAQC,UAAU,CAACC,IAAI,CACrB,CAACC,YACCA,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUqB,QAAQ,IAChDH,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUsB,SAAS,KAErDP,QAAQC,UAAU,CAACC,IAAI,CACrB,CAACC,YACCA,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUuB,KAAK,IAC7CL,UAAUC,UAAU,CAACC,IAAI,KAAKpB,UAAUwB,SAAS,GAErD;YACA,OAAO;QACT;QAEA,OAAOX;IACT;IAEA,OAAcY,SACZd,QAAgB,EAChBC,OAAqB,EACoD;QACzE,kCAAkC;QAClC,+GAA+G;QAC/G,MAAM,KAAK,CAACc;QAEZ,uEAAuE;QACvE,gBAAgB;QAChB,WAAW,MAAMC,oBAAoB,KAAK,CAACF,SAASd,UAAUC,SAAU;YACtE,qEAAqE;YACrE,gEAAgE;YAChE,MAAM,IAAI,CAACJ,OAAO,CAACoB,MAAM,CAACD,kBAAkBhB;YAC5C,MAAM,IAAI,CAACJ,UAAU,CAACmB,MAAM;YAE5B,yEAAyE;YACzE,sEAAsE;YACtE,WAAW,MAAMG,mBAAmB,IAAI,CAACtB,UAAU,CAACkB,QAAQ,CAC1Dd,UACAC,SACC;gBACD,MAAMiB;YACR;QACF;QAEA,4EAA4E;QAC5E,gCAAgC;QAChC,OAAO;IACT;IAEA,MAAaH,SAAwB;QACnC,uCAAuC;QACvC,MAAM,IAAI,CAACnB,UAAU,CAACmB,MAAM;QAE5B,kCAAkC;QAClC,MAAM,KAAK,CAACA;QAEZ,wCAAwC;QACxC,KAAK,MAAM,CAACf,UAAUmB,SAAS,IAAIC,OAAOC,OAAO,CAC/C,IAAI,CAACF,QAAQ,CAACG,UAAU,EACvB;YACD,0EAA0E;YAC1E,4BAA4B;YAC5B,MAAMC,WAAWJ,QAAQ,CAAC,EAAE,CAACI,QAAQ;YACrC,IAAIJ,SAASK,KAAK,CAAC,GAAGlB,IAAI,CAAC,CAACF,UAAYA,QAAQmB,QAAQ,KAAKA,WAAW;gBACtE;YACF;YAEA/B,IAAIiC,IAAI,CACN,CAAC,yBAAyB,EAAEN,SACzBO,GAAG,CAAC,CAACtB,UACJX,KAAKF,KAAKoC,QAAQ,CAAC,IAAI,CAAC7B,GAAG,EAAEM,QAAQI,UAAU,CAACoB,QAAQ,IAEzDC,IAAI,CAAC,SAAS,YAAY,EAAEpC,KAAKO,WAAW;QAEnD;IACF;AACF", "ignoreList": [0]}