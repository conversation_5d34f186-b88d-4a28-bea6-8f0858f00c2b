{"version": 3, "sources": ["../../../../src/build/babel/loader/get-config.ts"], "sourcesContent": ["import { readFileSync } from 'fs'\nimport JSON5 from 'next/dist/compiled/json5'\n\nimport { createConfigItem, loadOptions } from 'next/dist/compiled/babel/core'\nimport loadFullConfig from 'next/dist/compiled/babel/core-lib-config'\n\nimport type { NextBabelLoaderOptions, NextJsLoaderContext } from './types'\nimport {\n  consumeIterator,\n  type SourceMap,\n  type BabelLoaderTransformOptions,\n} from './util'\nimport * as Log from '../../output/log'\nimport jsx from 'next/dist/compiled/babel/plugin-syntax-jsx'\nimport { isReactCompilerRequired } from '../../swc'\n\n/**\n * An internal (non-exported) type used by babel.\n */\nexport type ResolvedBabelConfig = {\n  options: BabelLoaderTransformOptions\n  passes: BabelPluginPasses\n  externalDependencies: ReadonlyArray<string>\n}\n\nexport type BabelPlugin = unknown\nexport type BabelPluginPassList = ReadonlyArray<BabelPlugin>\nexport type BabelPluginPasses = ReadonlyArray<BabelPluginPassList>\n\nconst nextDistPath =\n  /(next[\\\\/]dist[\\\\/]shared[\\\\/]lib)|(next[\\\\/]dist[\\\\/]client)|(next[\\\\/]dist[\\\\/]pages)/\n\n/**\n * The properties defined here are the conditions with which subsets of inputs\n * can be identified that are able to share a common Babel config.  For example,\n * in dev mode, different transforms must be applied to a source file depending\n * on whether you're compiling for the client or for the server - thus `isServer`\n * is germane.\n *\n * However, these characteristics need not protect against circumstances that\n * will not be encountered in Next.js.  For example, a source file may be\n * transformed differently depending on whether we're doing a production compile\n * or for HMR in dev mode.  However, those two circumstances will never be\n * encountered within the context of a single V8 context (and, thus, shared\n * cache).  Therefore, hasReactRefresh is _not_ germane to caching.\n *\n * NOTE: This approach does not support multiple `.babelrc` files in a\n * single project.  A per-cache-key config will be generated once and,\n * if `.babelrc` is present, that config will be used for any subsequent\n * transformations.\n */\ninterface CharacteristicsGermaneToCaching {\n  isServer: boolean\n  isPageFile: boolean\n  isNextDist: boolean\n  hasModuleExports: boolean\n  fileNameOrExt: string\n}\n\nconst fileExtensionRegex = /\\.([a-z]+)$/\nfunction getCacheCharacteristics(\n  loaderOptions: NextBabelLoaderOptions,\n  source: string,\n  filename: string,\n  transformMode: 'default' | 'standalone'\n): CharacteristicsGermaneToCaching {\n  const { isServer, pagesDir } = loaderOptions\n  const isPageFile = filename.startsWith(pagesDir)\n  const isNextDist = nextDistPath.test(filename)\n  const hasModuleExports = source.indexOf('module.exports') !== -1\n  const fileNameOrExt =\n    transformMode === 'default'\n      ? fileExtensionRegex.exec(filename)?.[1] || 'unknown'\n      : filename\n\n  return {\n    isServer,\n    isPageFile,\n    isNextDist,\n    hasModuleExports,\n    fileNameOrExt,\n  }\n}\n\n/**\n * Return an array of Babel plugins, conditioned upon loader options and\n * source file characteristics.\n */\nfunction getPlugins(\n  loaderOptions: NextBabelLoaderOptions,\n  cacheCharacteristics: CharacteristicsGermaneToCaching\n) {\n  const { isServer, isPageFile, isNextDist, hasModuleExports } =\n    cacheCharacteristics\n\n  const { development } = loaderOptions\n  const hasReactRefresh =\n    loaderOptions.transformMode !== 'standalone'\n      ? loaderOptions.hasReactRefresh\n      : false\n\n  const applyCommonJsItem = hasModuleExports\n    ? createConfigItem(\n        require('../plugins/commonjs') as typeof import('../plugins/commonjs'),\n        { type: 'plugin' }\n      )\n    : null\n  const reactRefreshItem = hasReactRefresh\n    ? createConfigItem(\n        [\n          require('next/dist/compiled/react-refresh/babel') as typeof import('next/dist/compiled/react-refresh/babel'),\n          { skipEnvCheck: true },\n        ],\n        { type: 'plugin' }\n      )\n    : null\n  const pageConfigItem =\n    !isServer && isPageFile\n      ? createConfigItem(\n          [\n            require('../plugins/next-page-config') as typeof import('../plugins/next-page-config'),\n          ],\n          {\n            type: 'plugin',\n          }\n        )\n      : null\n  const disallowExportAllItem =\n    !isServer && isPageFile\n      ? createConfigItem(\n          [\n            require('../plugins/next-page-disallow-re-export-all-exports') as typeof import('../plugins/next-page-disallow-re-export-all-exports'),\n          ],\n          { type: 'plugin' }\n        )\n      : null\n  const transformDefineItem = createConfigItem(\n    [\n      require.resolve('next/dist/compiled/babel/plugin-transform-define'),\n      {\n        'process.env.NODE_ENV': development ? 'development' : 'production',\n        'typeof window': isServer ? 'undefined' : 'object',\n        'process.browser': isServer ? false : true,\n      },\n      'next-js-transform-define-instance',\n    ],\n    { type: 'plugin' }\n  )\n  const nextSsgItem =\n    !isServer && isPageFile\n      ? createConfigItem([require.resolve('../plugins/next-ssg-transform')], {\n          type: 'plugin',\n        })\n      : null\n  const commonJsItem = isNextDist\n    ? createConfigItem(\n        require('next/dist/compiled/babel/plugin-transform-modules-commonjs') as typeof import('next/dist/compiled/babel/plugin-transform-modules-commonjs'),\n        { type: 'plugin' }\n      )\n    : null\n  const nextFontUnsupported = createConfigItem(\n    [\n      require('../plugins/next-font-unsupported') as typeof import('../plugins/next-font-unsupported'),\n    ],\n    { type: 'plugin' }\n  )\n\n  return [\n    reactRefreshItem,\n    pageConfigItem,\n    disallowExportAllItem,\n    applyCommonJsItem,\n    transformDefineItem,\n    nextSsgItem,\n    commonJsItem,\n    nextFontUnsupported,\n  ].filter(Boolean)\n}\n\nconst isJsonFile = /\\.(json|babelrc)$/\nconst isJsFile = /\\.js$/\n\n/**\n * While this function does block execution while reading from disk, it\n * should not introduce any issues.  The function is only invoked when\n * generating a fresh config, and only a small handful of configs should\n * be generated during compilation.\n */\nfunction getCustomBabelConfig(configFilePath: string) {\n  if (isJsonFile.exec(configFilePath)) {\n    const babelConfigRaw = readFileSync(configFilePath, 'utf8')\n    return JSON5.parse(babelConfigRaw)\n  } else if (isJsFile.exec(configFilePath)) {\n    return require(configFilePath)\n  }\n  throw new Error(\n    'The Next.js Babel loader does not support .mjs or .cjs config files.'\n  )\n}\n\nlet babelConfigWarned = false\n/**\n * Check if custom babel configuration from user only contains options that\n * can be migrated into latest Next.js features supported by SWC.\n *\n * This raises soft warning messages only, not making any errors yet.\n */\nfunction checkCustomBabelConfigDeprecation(\n  config: Record<string, any> | undefined\n) {\n  if (!config || Object.keys(config).length === 0) {\n    return\n  }\n\n  const { plugins, presets, ...otherOptions } = config\n  if (Object.keys(otherOptions ?? {}).length > 0) {\n    return\n  }\n\n  if (babelConfigWarned) {\n    return\n  }\n\n  babelConfigWarned = true\n\n  const isPresetReadyToDeprecate =\n    !presets ||\n    presets.length === 0 ||\n    (presets.length === 1 && presets[0] === 'next/babel')\n  const pluginReasons = []\n  const unsupportedPlugins = []\n\n  if (Array.isArray(plugins)) {\n    for (const plugin of plugins) {\n      const pluginName = Array.isArray(plugin) ? plugin[0] : plugin\n\n      // [NOTE]: We cannot detect if the user uses babel-plugin-macro based transform plugins,\n      // such as `styled-components/macro` in here.\n      switch (pluginName) {\n        case 'styled-components':\n        case 'babel-plugin-styled-components':\n          pluginReasons.push(\n            `\\t- 'styled-components' can be enabled via 'compiler.styledComponents' in 'next.config.js'`\n          )\n          break\n        case '@emotion/babel-plugin':\n          pluginReasons.push(\n            `\\t- '@emotion/babel-plugin' can be enabled via 'compiler.emotion' in 'next.config.js'`\n          )\n          break\n        case 'babel-plugin-relay':\n          pluginReasons.push(\n            `\\t- 'babel-plugin-relay' can be enabled via 'compiler.relay' in 'next.config.js'`\n          )\n          break\n        case 'react-remove-properties':\n          pluginReasons.push(\n            `\\t- 'react-remove-properties' can be enabled via 'compiler.reactRemoveProperties' in 'next.config.js'`\n          )\n          break\n        case 'transform-remove-console':\n          pluginReasons.push(\n            `\\t- 'transform-remove-console' can be enabled via 'compiler.removeConsole' in 'next.config.js'`\n          )\n          break\n        default:\n          unsupportedPlugins.push(pluginName)\n          break\n      }\n    }\n  }\n\n  if (isPresetReadyToDeprecate && unsupportedPlugins.length === 0) {\n    Log.warn(\n      `It looks like there is a custom Babel configuration that can be removed${\n        pluginReasons.length > 0 ? ':' : '.'\n      }`\n    )\n\n    if (pluginReasons.length > 0) {\n      Log.warn(`Next.js supports the following features natively: `)\n      Log.warn(pluginReasons.join(''))\n      Log.warn(\n        `For more details configuration options, please refer https://nextjs.org/docs/architecture/nextjs-compiler#supported-features`\n      )\n    }\n  }\n}\n\n/**\n * Generate a new, flat Babel config, ready to be handed to Babel-traverse.\n * This config should have no unresolved overrides, presets, etc.\n */\nasync function getFreshConfig(\n  ctx: NextJsLoaderContext,\n  cacheCharacteristics: CharacteristicsGermaneToCaching,\n  loaderOptions: NextBabelLoaderOptions,\n  target: string,\n  filename: string,\n  inputSourceMap?: SourceMap\n): Promise<ResolvedBabelConfig | null> {\n  const hasReactCompiler = await (async () => {\n    if (\n      loaderOptions.reactCompilerPlugins &&\n      loaderOptions.reactCompilerPlugins.length === 0\n    ) {\n      return false\n    }\n\n    if (/[/\\\\]node_modules[/\\\\]/.test(filename)) {\n      return false\n    }\n\n    if (\n      loaderOptions.reactCompilerExclude &&\n      loaderOptions.reactCompilerExclude(filename)\n    ) {\n      return false\n    }\n\n    if (!(await isReactCompilerRequired(filename))) {\n      return false\n    }\n\n    return true\n  })()\n\n  const reactCompilerPluginsIfEnabled = hasReactCompiler\n    ? loaderOptions.reactCompilerPlugins ?? []\n    : []\n\n  let { isServer, pagesDir, srcDir, development } = loaderOptions\n\n  let options: BabelLoaderTransformOptions = {\n    babelrc: false,\n    cloneInputAst: false,\n    filename,\n    inputSourceMap,\n\n    // Ensure that Webpack will get a full absolute path in the sourcemap\n    // so that it can properly map the module back to its internal cached\n    // modules.\n    sourceFileName: filename,\n    sourceMaps: ctx.sourceMap,\n  }\n\n  const baseCaller = {\n    name: 'next-babel-turbo-loader',\n    supportsStaticESM: true,\n    supportsDynamicImport: true,\n\n    // Provide plugins with insight into webpack target.\n    // https://github.com/babel/babel-loader/issues/787\n    target,\n\n    // Webpack 5 supports TLA behind a flag. We enable it by default\n    // for Babel, and then webpack will throw an error if the experimental\n    // flag isn't enabled.\n    supportsTopLevelAwait: true,\n\n    isServer,\n    srcDir,\n    pagesDir,\n    isDev: development,\n\n    ...loaderOptions.caller,\n  }\n\n  if (loaderOptions.transformMode === 'standalone') {\n    if (!reactCompilerPluginsIfEnabled.length) {\n      return null\n    }\n\n    options.plugins = [jsx, ...reactCompilerPluginsIfEnabled]\n    options.presets = [\n      [\n        require('next/dist/compiled/babel/preset-typescript') as typeof import('next/dist/compiled/babel/preset-typescript'),\n        { allowNamespaces: true },\n      ],\n    ]\n    options.caller = baseCaller\n  } else {\n    let { configFile, hasJsxRuntime } = loaderOptions\n    let customConfig: any = configFile\n      ? getCustomBabelConfig(configFile)\n      : undefined\n\n    checkCustomBabelConfigDeprecation(customConfig)\n\n    // Set the default sourcemap behavior based on Webpack's mapping flag,\n    // but allow users to override if they want.\n    options.sourceMaps =\n      loaderOptions.sourceMaps === undefined\n        ? ctx.sourceMap\n        : loaderOptions.sourceMaps\n\n    options.plugins = [\n      ...getPlugins(loaderOptions, cacheCharacteristics),\n      ...reactCompilerPluginsIfEnabled,\n      ...(customConfig?.plugins || []),\n    ]\n\n    // target can be provided in babelrc\n    options.target = isServer ? undefined : customConfig?.target\n\n    // env can be provided in babelrc\n    options.env = customConfig?.env\n\n    options.presets = (() => {\n      // If presets is defined the user will have next/babel in their babelrc\n      if (customConfig?.presets) {\n        return customConfig.presets\n      }\n\n      // If presets is not defined the user will likely have \"env\" in their babelrc\n      if (customConfig) {\n        return undefined\n      }\n\n      // If no custom config is provided the default is to use next/babel\n      return ['next/babel']\n    })()\n\n    options.overrides = loaderOptions.overrides\n\n    options.caller = {\n      ...baseCaller,\n      hasJsxRuntime,\n    }\n  }\n\n  // Babel does strict checks on the config so undefined is not allowed\n  if (typeof options.target === 'undefined') {\n    delete options.target\n  }\n\n  Object.defineProperty(options.caller, 'onWarning', {\n    enumerable: false,\n    writable: false,\n    value: (reason: any) => {\n      if (!(reason instanceof Error)) {\n        reason = new Error(reason)\n      }\n      ctx.emitWarning(reason)\n    },\n  })\n\n  const loadedOptions = loadOptions(options)\n  const config = consumeIterator(loadFullConfig(loadedOptions))\n\n  return config\n}\n\n/**\n * Each key returned here corresponds with a Babel config that can be shared.\n * The conditions of permissible sharing between files is dependent on specific\n * file attributes and Next.js compiler states: `CharacteristicsGermaneToCaching`.\n */\nfunction getCacheKey(cacheCharacteristics: CharacteristicsGermaneToCaching) {\n  const { isServer, isPageFile, isNextDist, hasModuleExports, fileNameOrExt } =\n    cacheCharacteristics\n\n  const flags =\n    0 |\n    (isServer ? 0b0001 : 0) |\n    (isPageFile ? 0b0010 : 0) |\n    (isNextDist ? 0b0100 : 0) |\n    (hasModuleExports ? 0b1000 : 0)\n\n  return fileNameOrExt + flags\n}\n\nconst configCache: Map<any, ResolvedBabelConfig | null> = new Map()\nconst configFiles: Set<string> = new Set()\n\nexport default async function getConfig(\n  ctx: NextJsLoaderContext,\n  {\n    source,\n    target,\n    loaderOptions,\n    filename,\n    inputSourceMap,\n  }: {\n    source: string\n    loaderOptions: NextBabelLoaderOptions\n    target: string\n    filename: string\n    inputSourceMap?: SourceMap | undefined\n  }\n): Promise<ResolvedBabelConfig | null> {\n  const cacheCharacteristics = getCacheCharacteristics(\n    loaderOptions,\n    source,\n    filename,\n    loaderOptions.transformMode\n  )\n\n  if (loaderOptions.transformMode === 'default' && loaderOptions.configFile) {\n    // Ensures webpack invalidates the cache for this loader when the config file changes\n    ctx.addDependency(loaderOptions.configFile)\n  }\n\n  const cacheKey = getCacheKey(cacheCharacteristics)\n  if (configCache.has(cacheKey)) {\n    const cachedConfig = configCache.get(cacheKey)\n    if (!cachedConfig) {\n      return null\n    }\n\n    return {\n      ...cachedConfig,\n      options: {\n        ...cachedConfig.options,\n        cwd: loaderOptions.cwd,\n        root: loaderOptions.cwd,\n        filename,\n        sourceFileName: filename,\n      },\n    }\n  }\n\n  if (\n    loaderOptions.transformMode === 'default' &&\n    loaderOptions.configFile &&\n    !configFiles.has(loaderOptions.configFile)\n  ) {\n    configFiles.add(loaderOptions.configFile)\n    Log.info(\n      `Using external babel configuration from ${loaderOptions.configFile}`\n    )\n  }\n\n  const freshConfig = await getFreshConfig(\n    ctx,\n    cacheCharacteristics,\n    loaderOptions,\n    target,\n    filename,\n    inputSourceMap\n  )\n\n  configCache.set(cacheKey, freshConfig)\n\n  return freshConfig\n}\n"], "names": ["readFileSync", "JSON5", "createConfigItem", "loadOptions", "loadFullConfig", "consumeIterator", "Log", "jsx", "isReactCompilerRequired", "nextDistPath", "fileExtensionRegex", "getCacheCharacteristics", "loaderOptions", "source", "filename", "transformMode", "isServer", "pagesDir", "isPageFile", "startsWith", "isNextDist", "test", "hasModuleExports", "indexOf", "fileNameOrExt", "exec", "getPlugins", "cacheCharacteristics", "development", "hasReactRefresh", "applyCommonJsItem", "require", "type", "reactRefreshItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageConfigItem", "disallowExportAllItem", "transformDefineItem", "resolve", "nextSsgItem", "commonJsItem", "nextFontUnsupported", "filter", "Boolean", "isJsonFile", "isJsFile", "getCustomBabelConfig", "config<PERSON><PERSON><PERSON><PERSON>", "babelConfigRaw", "parse", "Error", "babelConfigWarned", "checkCustomBabelConfigDeprecation", "config", "Object", "keys", "length", "plugins", "presets", "otherOptions", "isPresetReadyToDeprecate", "pluginReasons", "unsupportedPlugins", "Array", "isArray", "plugin", "pluginName", "push", "warn", "join", "getFreshConfig", "ctx", "target", "inputSourceMap", "hasReactCompiler", "reactCompilerPlugins", "reactCompilerExclude", "reactCompilerPluginsIfEnabled", "srcDir", "options", "babelrc", "cloneInputAst", "sourceFileName", "sourceMaps", "sourceMap", "baseCaller", "name", "supportsStaticESM", "supportsDynamicImport", "supportsTopLevelAwait", "isDev", "caller", "allowNamespaces", "configFile", "hasJsxRuntime", "customConfig", "undefined", "env", "overrides", "defineProperty", "enumerable", "writable", "value", "reason", "emitWarning", "loadedOptions", "get<PERSON><PERSON><PERSON><PERSON>", "flags", "config<PERSON><PERSON>", "Map", "configFiles", "Set", "getConfig", "addDependency", "cache<PERSON>ey", "has", "cachedConfig", "get", "cwd", "root", "add", "info", "freshConfig", "set"], "mappings": "AAAA,SAASA,YAAY,QAAQ,KAAI;AACjC,OAAOC,WAAW,2BAA0B;AAE5C,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,gCAA+B;AAC7E,OAAOC,oBAAoB,2CAA0C;AAGrE,SACEC,eAAe,QAGV,SAAQ;AACf,YAAYC,SAAS,mBAAkB;AACvC,OAAOC,SAAS,6CAA4C;AAC5D,SAASC,uBAAuB,QAAQ,YAAW;AAenD,MAAMC,eACJ;AA6BF,MAAMC,qBAAqB;AAC3B,SAASC,wBACPC,aAAqC,EACrCC,MAAc,EACdC,QAAgB,EAChBC,aAAuC;QAQjCL;IANN,MAAM,EAAEM,QAAQ,EAAEC,QAAQ,EAAE,GAAGL;IAC/B,MAAMM,aAAaJ,SAASK,UAAU,CAACF;IACvC,MAAMG,aAAaX,aAAaY,IAAI,CAACP;IACrC,MAAMQ,mBAAmBT,OAAOU,OAAO,CAAC,sBAAsB,CAAC;IAC/D,MAAMC,gBACJT,kBAAkB,YACdL,EAAAA,2BAAAA,mBAAmBe,IAAI,CAACX,8BAAxBJ,wBAAmC,CAAC,EAAE,KAAI,YAC1CI;IAEN,OAAO;QACLE;QACAE;QACAE;QACAE;QACAE;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,WACPd,aAAqC,EACrCe,oBAAqD;IAErD,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAE,GAC1DK;IAEF,MAAM,EAAEC,WAAW,EAAE,GAAGhB;IACxB,MAAMiB,kBACJjB,cAAcG,aAAa,KAAK,eAC5BH,cAAciB,eAAe,GAC7B;IAEN,MAAMC,oBAAoBR,mBACtBpB,iBACE6B,QAAQ,wBACR;QAAEC,MAAM;IAAS,KAEnB;IACJ,MAAMC,mBAAmBJ,kBACrB3B,iBACE;QACE6B,QAAQ;QACR;YAAEG,cAAc;QAAK;KACtB,EACD;QAAEF,MAAM;IAAS,KAEnB;IACJ,MAAMG,iBACJ,CAACnB,YAAYE,aACThB,iBACE;QACE6B,QAAQ;KACT,EACD;QACEC,MAAM;IACR,KAEF;IACN,MAAMI,wBACJ,CAACpB,YAAYE,aACThB,iBACE;QACE6B,QAAQ;KACT,EACD;QAAEC,MAAM;IAAS,KAEnB;IACN,MAAMK,sBAAsBnC,iBAC1B;QACE6B,QAAQO,OAAO,CAAC;QAChB;YACE,wBAAwBV,cAAc,gBAAgB;YACtD,iBAAiBZ,WAAW,cAAc;YAC1C,mBAAmBA,WAAW,QAAQ;QACxC;QACA;KACD,EACD;QAAEgB,MAAM;IAAS;IAEnB,MAAMO,cACJ,CAACvB,YAAYE,aACThB,iBAAiB;QAAC6B,QAAQO,OAAO,CAAC;KAAiC,EAAE;QACnEN,MAAM;IACR,KACA;IACN,MAAMQ,eAAepB,aACjBlB,iBACE6B,QAAQ,+DACR;QAAEC,MAAM;IAAS,KAEnB;IACJ,MAAMS,sBAAsBvC,iBAC1B;QACE6B,QAAQ;KACT,EACD;QAAEC,MAAM;IAAS;IAGnB,OAAO;QACLC;QACAE;QACAC;QACAN;QACAO;QACAE;QACAC;QACAC;KACD,CAACC,MAAM,CAACC;AACX;AAEA,MAAMC,aAAa;AACnB,MAAMC,WAAW;AAEjB;;;;;CAKC,GACD,SAASC,qBAAqBC,cAAsB;IAClD,IAAIH,WAAWnB,IAAI,CAACsB,iBAAiB;QACnC,MAAMC,iBAAiBhD,aAAa+C,gBAAgB;QACpD,OAAO9C,MAAMgD,KAAK,CAACD;IACrB,OAAO,IAAIH,SAASpB,IAAI,CAACsB,iBAAiB;QACxC,OAAOhB,QAAQgB;IACjB;IACA,MAAM,qBAEL,CAFK,IAAIG,MACR,yEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,IAAIC,oBAAoB;AACxB;;;;;CAKC,GACD,SAASC,kCACPC,MAAuC;IAEvC,IAAI,CAACA,UAAUC,OAAOC,IAAI,CAACF,QAAQG,MAAM,KAAK,GAAG;QAC/C;IACF;IAEA,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAGC,cAAc,GAAGN;IAC9C,IAAIC,OAAOC,IAAI,CAACI,gBAAgB,CAAC,GAAGH,MAAM,GAAG,GAAG;QAC9C;IACF;IAEA,IAAIL,mBAAmB;QACrB;IACF;IAEAA,oBAAoB;IAEpB,MAAMS,2BACJ,CAACF,WACDA,QAAQF,MAAM,KAAK,KAClBE,QAAQF,MAAM,KAAK,KAAKE,OAAO,CAAC,EAAE,KAAK;IAC1C,MAAMG,gBAAgB,EAAE;IACxB,MAAMC,qBAAqB,EAAE;IAE7B,IAAIC,MAAMC,OAAO,CAACP,UAAU;QAC1B,KAAK,MAAMQ,UAAUR,QAAS;YAC5B,MAAMS,aAAaH,MAAMC,OAAO,CAACC,UAAUA,MAAM,CAAC,EAAE,GAAGA;YAEvD,wFAAwF;YACxF,6CAA6C;YAC7C,OAAQC;gBACN,KAAK;gBACL,KAAK;oBACHL,cAAcM,IAAI,CAChB,CAAC,0FAA0F,CAAC;oBAE9F;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qFAAqF,CAAC;oBAEzF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,gFAAgF,CAAC;oBAEpF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qGAAqG,CAAC;oBAEzG;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,8FAA8F,CAAC;oBAElG;gBACF;oBACEL,mBAAmBK,IAAI,CAACD;oBACxB;YACJ;QACF;IACF;IAEA,IAAIN,4BAA4BE,mBAAmBN,MAAM,KAAK,GAAG;QAC/DlD,IAAI8D,IAAI,CACN,CAAC,uEAAuE,EACtEP,cAAcL,MAAM,GAAG,IAAI,MAAM,KACjC;QAGJ,IAAIK,cAAcL,MAAM,GAAG,GAAG;YAC5BlD,IAAI8D,IAAI,CAAC,CAAC,kDAAkD,CAAC;YAC7D9D,IAAI8D,IAAI,CAACP,cAAcQ,IAAI,CAAC;YAC5B/D,IAAI8D,IAAI,CACN,CAAC,4HAA4H,CAAC;QAElI;IACF;AACF;AAEA;;;CAGC,GACD,eAAeE,eACbC,GAAwB,EACxB5C,oBAAqD,EACrDf,aAAqC,EACrC4D,MAAc,EACd1D,QAAgB,EAChB2D,cAA0B;IAE1B,MAAMC,mBAAmB,MAAM,AAAC,CAAA;QAC9B,IACE9D,cAAc+D,oBAAoB,IAClC/D,cAAc+D,oBAAoB,CAACnB,MAAM,KAAK,GAC9C;YACA,OAAO;QACT;QAEA,IAAI,yBAAyBnC,IAAI,CAACP,WAAW;YAC3C,OAAO;QACT;QAEA,IACEF,cAAcgE,oBAAoB,IAClChE,cAAcgE,oBAAoB,CAAC9D,WACnC;YACA,OAAO;QACT;QAEA,IAAI,CAAE,MAAMN,wBAAwBM,WAAY;YAC9C,OAAO;QACT;QAEA,OAAO;IACT,CAAA;IAEA,MAAM+D,gCAAgCH,mBAClC9D,cAAc+D,oBAAoB,IAAI,EAAE,GACxC,EAAE;IAEN,IAAI,EAAE3D,QAAQ,EAAEC,QAAQ,EAAE6D,MAAM,EAAElD,WAAW,EAAE,GAAGhB;IAElD,IAAImE,UAAuC;QACzCC,SAAS;QACTC,eAAe;QACfnE;QACA2D;QAEA,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXS,gBAAgBpE;QAChBqE,YAAYZ,IAAIa,SAAS;IAC3B;IAEA,MAAMC,aAAa;QACjBC,MAAM;QACNC,mBAAmB;QACnBC,uBAAuB;QAEvB,oDAAoD;QACpD,mDAAmD;QACnDhB;QAEA,gEAAgE;QAChE,sEAAsE;QACtE,sBAAsB;QACtBiB,uBAAuB;QAEvBzE;QACA8D;QACA7D;QACAyE,OAAO9D;QAEP,GAAGhB,cAAc+E,MAAM;IACzB;IAEA,IAAI/E,cAAcG,aAAa,KAAK,cAAc;QAChD,IAAI,CAAC8D,8BAA8BrB,MAAM,EAAE;YACzC,OAAO;QACT;QAEAuB,QAAQtB,OAAO,GAAG;YAAClD;eAAQsE;SAA8B;QACzDE,QAAQrB,OAAO,GAAG;YAChB;gBACE3B,QAAQ;gBACR;oBAAE6D,iBAAiB;gBAAK;aACzB;SACF;QACDb,QAAQY,MAAM,GAAGN;IACnB,OAAO;QACL,IAAI,EAAEQ,UAAU,EAAEC,aAAa,EAAE,GAAGlF;QACpC,IAAImF,eAAoBF,aACpB/C,qBAAqB+C,cACrBG;QAEJ5C,kCAAkC2C;QAElC,sEAAsE;QACtE,4CAA4C;QAC5ChB,QAAQI,UAAU,GAChBvE,cAAcuE,UAAU,KAAKa,YACzBzB,IAAIa,SAAS,GACbxE,cAAcuE,UAAU;QAE9BJ,QAAQtB,OAAO,GAAG;eACb/B,WAAWd,eAAee;eAC1BkD;eACCkB,CAAAA,gCAAAA,aAActC,OAAO,KAAI,EAAE;SAChC;QAED,oCAAoC;QACpCsB,QAAQP,MAAM,GAAGxD,WAAWgF,YAAYD,gCAAAA,aAAcvB,MAAM;QAE5D,iCAAiC;QACjCO,QAAQkB,GAAG,GAAGF,gCAAAA,aAAcE,GAAG;QAE/BlB,QAAQrB,OAAO,GAAG,AAAC,CAAA;YACjB,uEAAuE;YACvE,IAAIqC,gCAAAA,aAAcrC,OAAO,EAAE;gBACzB,OAAOqC,aAAarC,OAAO;YAC7B;YAEA,6EAA6E;YAC7E,IAAIqC,cAAc;gBAChB,OAAOC;YACT;YAEA,mEAAmE;YACnE,OAAO;gBAAC;aAAa;QACvB,CAAA;QAEAjB,QAAQmB,SAAS,GAAGtF,cAAcsF,SAAS;QAE3CnB,QAAQY,MAAM,GAAG;YACf,GAAGN,UAAU;YACbS;QACF;IACF;IAEA,qEAAqE;IACrE,IAAI,OAAOf,QAAQP,MAAM,KAAK,aAAa;QACzC,OAAOO,QAAQP,MAAM;IACvB;IAEAlB,OAAO6C,cAAc,CAACpB,QAAQY,MAAM,EAAE,aAAa;QACjDS,YAAY;QACZC,UAAU;QACVC,OAAO,CAACC;YACN,IAAI,CAAEA,CAAAA,kBAAkBrD,KAAI,GAAI;gBAC9BqD,SAAS,qBAAiB,CAAjB,IAAIrD,MAAMqD,SAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAgB;YAC3B;YACAhC,IAAIiC,WAAW,CAACD;QAClB;IACF;IAEA,MAAME,gBAAgBtG,YAAY4E;IAClC,MAAM1B,SAAShD,gBAAgBD,eAAeqG;IAE9C,OAAOpD;AACT;AAEA;;;;CAIC,GACD,SAASqD,YAAY/E,oBAAqD;IACxE,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAEE,aAAa,EAAE,GACzEG;IAEF,MAAMgF,QACJ,IACC3F,CAAAA,WAAW,IAAS,CAAA,IACpBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,mBAAmB,IAAS,CAAA;IAE/B,OAAOE,gBAAgBmF;AACzB;AAEA,MAAMC,cAAoD,IAAIC;AAC9D,MAAMC,cAA2B,IAAIC;AAErC,eAAe,eAAeC,UAC5BzC,GAAwB,EACxB,EACE1D,MAAM,EACN2D,MAAM,EACN5D,aAAa,EACbE,QAAQ,EACR2D,cAAc,EAOf;IAED,MAAM9C,uBAAuBhB,wBAC3BC,eACAC,QACAC,UACAF,cAAcG,aAAa;IAG7B,IAAIH,cAAcG,aAAa,KAAK,aAAaH,cAAciF,UAAU,EAAE;QACzE,qFAAqF;QACrFtB,IAAI0C,aAAa,CAACrG,cAAciF,UAAU;IAC5C;IAEA,MAAMqB,WAAWR,YAAY/E;IAC7B,IAAIiF,YAAYO,GAAG,CAACD,WAAW;QAC7B,MAAME,eAAeR,YAAYS,GAAG,CAACH;QACrC,IAAI,CAACE,cAAc;YACjB,OAAO;QACT;QAEA,OAAO;YACL,GAAGA,YAAY;YACfrC,SAAS;gBACP,GAAGqC,aAAarC,OAAO;gBACvBuC,KAAK1G,cAAc0G,GAAG;gBACtBC,MAAM3G,cAAc0G,GAAG;gBACvBxG;gBACAoE,gBAAgBpE;YAClB;QACF;IACF;IAEA,IACEF,cAAcG,aAAa,KAAK,aAChCH,cAAciF,UAAU,IACxB,CAACiB,YAAYK,GAAG,CAACvG,cAAciF,UAAU,GACzC;QACAiB,YAAYU,GAAG,CAAC5G,cAAciF,UAAU;QACxCvF,IAAImH,IAAI,CACN,CAAC,wCAAwC,EAAE7G,cAAciF,UAAU,EAAE;IAEzE;IAEA,MAAM6B,cAAc,MAAMpD,eACxBC,KACA5C,sBACAf,eACA4D,QACA1D,UACA2D;IAGFmC,YAAYe,GAAG,CAACT,UAAUQ;IAE1B,OAAOA;AACT", "ignoreList": [0]}