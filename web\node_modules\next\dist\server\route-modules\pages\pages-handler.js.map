{"version": 3, "sources": ["../../../../src/server/route-modules/pages/pages-handler.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'node:http'\nimport type { ParsedUrlQuery } from 'node:querystring'\nimport { RouteKind } from '../../route-kind'\nimport { BaseServerSpan } from '../../lib/trace/constants'\nimport { getTracer, SpanKind, type Span } from '../../lib/trace/tracer'\nimport { formatUrl } from '../../../shared/lib/router/utils/format-url'\nimport { addRequestMeta, getRequestMeta } from '../../request-meta'\nimport { interopDefault } from '../../app-render/interop-default'\nimport { getRevalidateReason } from '../../instrumentation/utils'\nimport { normalizeDataPath } from '../../../shared/lib/page-path/normalize-data-path'\nimport {\n  CachedRouteKind,\n  type CachedPageValue,\n  type CachedRedirectValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../response-cache'\n\nimport {\n  getCacheControlHeader,\n  type CacheControl,\n} from '../../lib/cache-control'\nimport { normalizeRepeatedSlashes } from '../../../shared/lib/utils'\nimport { getRedirectStatus } from '../../../lib/redirect-status'\nimport {\n  CACHE_ONE_YEAR,\n  HTML_CONTENT_TYPE_HEADER,\n  JSON_CONTENT_TYPE_HEADER,\n} from '../../../lib/constants'\nimport path from 'path'\nimport { sendRenderResult } from '../../send-payload'\nimport RenderResult from '../../render-result'\nimport { toResponseCacheEntry } from '../../response-cache/utils'\nimport { NoFallbackError } from '../../../shared/lib/no-fallback-error.external'\nimport { RedirectStatusCode } from '../../../client/components/redirect-status-code'\nimport { isBot } from '../../../shared/lib/router/utils/is-bot'\nimport { addPathPrefix } from '../../../shared/lib/router/utils/add-path-prefix'\nimport { removeTrailingSlash } from '../../../shared/lib/router/utils/remove-trailing-slash'\nimport type { PagesRouteModule } from './module.compiled'\nimport type {\n  GetServerSideProps,\n  GetStaticPaths,\n  GetStaticProps,\n} from '../../../types'\n\nexport const getHandler = ({\n  srcPage: originalSrcPage,\n  config,\n  userland,\n  routeModule,\n  isFallbackError,\n  getStaticPaths,\n  getStaticProps,\n  getServerSideProps,\n}: {\n  srcPage: string\n  config: Record<string, any> | undefined\n  userland: any\n  isFallbackError?: boolean\n  routeModule: PagesRouteModule\n  getStaticProps?: GetStaticProps\n  getStaticPaths?: GetStaticPaths\n  getServerSideProps?: GetServerSideProps\n}) => {\n  return async function handler(\n    req: IncomingMessage,\n    res: ServerResponse,\n    ctx: {\n      waitUntil: (prom: Promise<void>) => void\n    }\n  ): Promise<void> {\n    let srcPage = originalSrcPage\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n      srcPage = srcPage.replace(/\\/index$/, '') || '/'\n    } else if (srcPage === '/index') {\n      // we always normalize /index specifically\n      srcPage = '/'\n    }\n    const multiZoneDraftMode = process.env\n      .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n    const prepareResult = await routeModule.prepare(req, res, {\n      srcPage,\n      multiZoneDraftMode,\n    })\n\n    if (!prepareResult) {\n      res.statusCode = 400\n      res.end('Bad Request')\n      ctx.waitUntil?.(Promise.resolve())\n      return\n    }\n\n    const {\n      buildId,\n      query,\n      params,\n      parsedUrl,\n      originalQuery,\n      originalPathname,\n      buildManifest,\n      fallbackBuildManifest,\n      nextFontManifest,\n      serverFilesManifest,\n      reactLoadableManifest,\n      prerenderManifest,\n      isDraftMode,\n      isOnDemandRevalidate,\n      revalidateOnlyGenerated,\n      locale,\n      locales,\n      defaultLocale,\n      routerServerContext,\n      nextConfig,\n      resolvedPathname,\n    } = prepareResult\n\n    const isExperimentalCompile =\n      serverFilesManifest?.config?.experimental?.isExperimentalCompile\n\n    const hasServerProps = Boolean(getServerSideProps)\n    const hasStaticProps = Boolean(getStaticProps)\n    const hasStaticPaths = Boolean(getStaticPaths)\n    const hasGetInitialProps = Boolean(\n      (userland.default || userland).getInitialProps\n    )\n    const isAmp = query.amp && config?.amp\n    let cacheKey: null | string = null\n    let isIsrFallback = false\n    let isNextDataRequest =\n      prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps)\n\n    const is404Page = srcPage === '/404'\n    const is500Page = srcPage === '/500'\n    const isErrorPage = srcPage === '/_error'\n\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n      cacheKey = `${locale ? `/${locale}` : ''}${\n        (srcPage === '/' || resolvedPathname === '/') && locale\n          ? ''\n          : resolvedPathname\n      }${isAmp ? '.amp' : ''}`\n\n      if (is404Page || is500Page || isErrorPage) {\n        cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`\n      }\n\n      // ensure /index and / is normalized to one key\n      cacheKey = cacheKey === '/index' ? '/' : cacheKey\n    }\n\n    if (hasStaticPaths && !isDraftMode) {\n      const decodedPathname = removeTrailingSlash(\n        locale\n          ? addPathPrefix(resolvedPathname, `/${locale}`)\n          : resolvedPathname\n      )\n      const isPrerendered =\n        Boolean(prerenderManifest.routes[decodedPathname]) ||\n        prerenderManifest.notFoundRoutes.includes(decodedPathname)\n\n      const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage]\n\n      if (prerenderInfo) {\n        if (prerenderInfo.fallback === false && !isPrerendered) {\n          throw new NoFallbackError()\n        }\n\n        if (\n          typeof prerenderInfo.fallback === 'string' &&\n          !isPrerendered &&\n          !isNextDataRequest\n        ) {\n          isIsrFallback = true\n        }\n      }\n    }\n\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (\n      (isIsrFallback && isBot(req.headers['user-agent'] || '')) ||\n      getRequestMeta(req, 'minimalMode')\n    ) {\n      isIsrFallback = false\n    }\n\n    const tracer = getTracer()\n    const activeSpan = tracer.getActiveScopeSpan()\n\n    try {\n      const method = req.method || 'GET'\n\n      const resolvedUrl = formatUrl({\n        pathname: nextConfig.trailingSlash\n          ? parsedUrl.pathname\n          : removeTrailingSlash(parsedUrl.pathname || '/'),\n        // make sure to only add query values from original URL\n        query: hasStaticProps ? {} : originalQuery,\n      })\n\n      const publicRuntimeConfig: Record<string, string> =\n        routerServerContext?.publicRuntimeConfig ||\n        nextConfig.publicRuntimeConfig\n\n      const handleResponse = async (span?: Span) => {\n        const responseGenerator: ResponseGenerator = async ({\n          previousCacheEntry,\n        }) => {\n          const doRender = async () => {\n            try {\n              return await routeModule\n                .render(req, res, {\n                  query:\n                    hasStaticProps && !isExperimentalCompile\n                      ? ({\n                          ...params,\n                          ...(isAmp\n                            ? {\n                                amp: query.amp,\n                              }\n                            : {}),\n                        } as ParsedUrlQuery)\n                      : {\n                          ...query,\n                          ...params,\n                        },\n                  params,\n                  page: srcPage,\n                  renderContext: {\n                    isDraftMode,\n                    isFallback: isIsrFallback,\n                    developmentNotFoundSourcePage: getRequestMeta(\n                      req,\n                      'developmentNotFoundSourcePage'\n                    ),\n                  },\n                  sharedContext: {\n                    buildId,\n                    customServer:\n                      Boolean(routerServerContext?.isCustomServer) || undefined,\n                    deploymentId: process.env.NEXT_DEPLOYMENT_ID,\n                  },\n                  renderOpts: {\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    pageConfig: config || {},\n                    Component: interopDefault(userland),\n                    ComponentMod: userland,\n                    getStaticProps,\n                    getStaticPaths,\n                    getServerSideProps,\n                    supportsDynamicResponse: !hasStaticProps,\n                    buildManifest: isFallbackError\n                      ? fallbackBuildManifest\n                      : buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n\n                    assetPrefix: nextConfig.assetPrefix,\n                    previewProps: prerenderManifest.preview,\n                    images: nextConfig.images as any,\n                    nextConfigOutput: nextConfig.output,\n                    optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                    nextScriptWorkers: Boolean(\n                      nextConfig.experimental.nextScriptWorkers\n                    ),\n                    domainLocales: nextConfig.i18n?.domains,\n                    crossOrigin: nextConfig.crossOrigin,\n\n                    multiZoneDraftMode,\n                    basePath: nextConfig.basePath,\n                    canonicalBase: nextConfig.amp.canonicalBase || '',\n                    ampOptimizerConfig: nextConfig.experimental.amp?.optimizer,\n                    disableOptimizedLoading:\n                      nextConfig.experimental.disableOptimizedLoading,\n                    largePageDataBytes:\n                      nextConfig.experimental.largePageDataBytes,\n                    // Only the `publicRuntimeConfig` key is exposed to the client side\n                    // It'll be rendered as part of __NEXT_DATA__ on the client side\n                    runtimeConfig:\n                      Object.keys(publicRuntimeConfig).length > 0\n                        ? publicRuntimeConfig\n                        : undefined,\n\n                    isExperimentalCompile,\n\n                    experimental: {\n                      clientTraceMetadata:\n                        nextConfig.experimental.clientTraceMetadata ||\n                        ([] as any),\n                    },\n\n                    locale,\n                    locales,\n                    defaultLocale,\n                    setIsrStatus: routerServerContext?.setIsrStatus,\n\n                    isNextDataRequest:\n                      isNextDataRequest && (hasServerProps || hasStaticProps),\n\n                    resolvedUrl,\n                    // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                    // and not the resolved URL to prevent a hydration mismatch on\n                    // asPath\n                    resolvedAsPath:\n                      hasServerProps || hasGetInitialProps\n                        ? formatUrl({\n                            // we use the original URL pathname less the _next/data prefix if\n                            // present\n                            pathname: isNextDataRequest\n                              ? normalizeDataPath(originalPathname)\n                              : originalPathname,\n                            query: originalQuery,\n                          })\n                        : resolvedUrl,\n\n                    isOnDemandRevalidate,\n\n                    ErrorDebug: getRequestMeta(req, 'PagesErrorDebug'),\n                    err: getRequestMeta(req, 'invokeError'),\n                    dev: routeModule.isDev,\n\n                    // needed for experimental.optimizeCss feature\n                    distDir: path.join(\n                      /* turbopackIgnore: true */\n                      process.cwd(),\n                      routeModule.relativeProjectDir,\n                      routeModule.distDir\n                    ),\n\n                    ampSkipValidation:\n                      nextConfig.experimental.amp?.skipValidation,\n                    ampValidator: getRequestMeta(req, 'ampValidator'),\n                  },\n                })\n                .then((renderResult): ResponseCacheEntry => {\n                  const { metadata } = renderResult\n\n                  let cacheControl: CacheControl | undefined =\n                    metadata.cacheControl\n\n                  if ('isNotFound' in metadata && metadata.isNotFound) {\n                    return {\n                      value: null,\n                      cacheControl,\n                    } satisfies ResponseCacheEntry\n                  }\n\n                  // Handle `isRedirect`.\n                  if (metadata.isRedirect) {\n                    return {\n                      value: {\n                        kind: CachedRouteKind.REDIRECT,\n                        props: metadata.pageData ?? metadata.flightData,\n                      } satisfies CachedRedirectValue,\n                      cacheControl,\n                    } satisfies ResponseCacheEntry\n                  }\n\n                  return {\n                    value: {\n                      kind: CachedRouteKind.PAGES,\n                      html: renderResult,\n                      pageData: renderResult.metadata.pageData,\n                      headers: renderResult.metadata.headers,\n                      status: renderResult.metadata.statusCode,\n                    },\n                    cacheControl,\n                  }\n                })\n                .finally(() => {\n                  if (!span) return\n\n                  span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false,\n                  })\n\n                  const rootSpanAttributes = tracer.getRootSpanAttributes()\n                  // We were unable to get attributes, probably OTEL is not enabled\n                  if (!rootSpanAttributes) {\n                    return\n                  }\n\n                  if (\n                    rootSpanAttributes.get('next.span_type') !==\n                    BaseServerSpan.handleRequest\n                  ) {\n                    console.warn(\n                      `Unexpected root span type '${rootSpanAttributes.get(\n                        'next.span_type'\n                      )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n                    )\n                    return\n                  }\n\n                  const route = rootSpanAttributes.get('next.route')\n                  if (route) {\n                    const name = `${method} ${route}`\n\n                    span.setAttributes({\n                      'next.route': route,\n                      'http.route': route,\n                      'next.span_name': name,\n                    })\n                    span.updateName(name)\n                  } else {\n                    span.updateName(`${method} ${req.url}`)\n                  }\n                })\n            } catch (err: unknown) {\n              // if this is a background revalidate we need to report\n              // the request error here as it won't be bubbled\n              if (previousCacheEntry?.isStale) {\n                await routeModule.onRequestError(\n                  req,\n                  err,\n                  {\n                    routerKind: 'Pages Router',\n                    routePath: srcPage,\n                    routeType: 'render',\n                    revalidateReason: getRevalidateReason({\n                      isRevalidate: hasStaticProps,\n                      isOnDemandRevalidate,\n                    }),\n                  },\n                  routerServerContext\n                )\n              }\n              throw err\n            }\n          }\n\n          // if we've already generated this page we no longer\n          // serve the fallback\n          if (previousCacheEntry) {\n            isIsrFallback = false\n          }\n\n          if (isIsrFallback) {\n            const fallbackResponse = await routeModule\n              .getResponseCache(req)\n              .get(\n                routeModule.isDev\n                  ? null\n                  : locale\n                    ? `/${locale}${srcPage}`\n                    : srcPage,\n                async ({\n                  previousCacheEntry: previousFallbackCacheEntry = null,\n                }) => {\n                  if (!routeModule.isDev) {\n                    return toResponseCacheEntry(previousFallbackCacheEntry)\n                  }\n                  return doRender()\n                },\n                {\n                  routeKind: RouteKind.PAGES,\n                  isFallback: true,\n                  isRoutePPREnabled: false,\n                  isOnDemandRevalidate: false,\n                  incrementalCache: await routeModule.getIncrementalCache(\n                    req,\n                    nextConfig,\n                    prerenderManifest\n                  ),\n                  waitUntil: ctx.waitUntil,\n                }\n              )\n            if (fallbackResponse) {\n              // Remove the cache control from the response to prevent it from being\n              // used in the surrounding cache.\n              delete fallbackResponse.cacheControl\n              fallbackResponse.isMiss = true\n              return fallbackResponse\n            }\n          }\n\n          if (\n            !getRequestMeta(req, 'minimalMode') &&\n            isOnDemandRevalidate &&\n            revalidateOnlyGenerated &&\n            !previousCacheEntry\n          ) {\n            res.statusCode = 404\n            // on-demand revalidate always sets this header\n            res.setHeader('x-nextjs-cache', 'REVALIDATED')\n            res.end('This page could not be found')\n            return null\n          }\n\n          if (\n            isIsrFallback &&\n            previousCacheEntry?.value?.kind === CachedRouteKind.PAGES\n          ) {\n            return {\n              value: {\n                kind: CachedRouteKind.PAGES,\n                html: new RenderResult(\n                  Buffer.from(previousCacheEntry.value.html),\n                  {\n                    contentType: HTML_CONTENT_TYPE_HEADER,\n                    metadata: {\n                      statusCode: previousCacheEntry.value.status,\n                      headers: previousCacheEntry.value.headers,\n                    },\n                  }\n                ),\n                pageData: {},\n                status: previousCacheEntry.value.status,\n                headers: previousCacheEntry.value.headers,\n              } satisfies CachedPageValue,\n              cacheControl: { revalidate: 0, expire: undefined },\n            } satisfies ResponseCacheEntry\n          }\n          return doRender()\n        }\n\n        const result = await routeModule.handleResponse({\n          cacheKey,\n          req,\n          nextConfig,\n          routeKind: RouteKind.PAGES,\n          isOnDemandRevalidate,\n          revalidateOnlyGenerated,\n          waitUntil: ctx.waitUntil,\n          responseGenerator: responseGenerator,\n          prerenderManifest,\n        })\n\n        // if we got a cache hit this wasn't an ISR fallback\n        // but it wasn't generated during build so isn't in the\n        // prerender-manifest\n        if (isIsrFallback && !result?.isMiss) {\n          isIsrFallback = false\n        }\n\n        // response is finished is no cache entry\n        if (!result) {\n          return\n        }\n\n        if (hasStaticProps && !getRequestMeta(req, 'minimalMode')) {\n          res.setHeader(\n            'x-nextjs-cache',\n            isOnDemandRevalidate\n              ? 'REVALIDATED'\n              : result.isMiss\n                ? 'MISS'\n                : result.isStale\n                  ? 'STALE'\n                  : 'HIT'\n          )\n        }\n\n        let cacheControl: CacheControl | undefined\n\n        if (!hasStaticProps || isIsrFallback) {\n          if (!res.getHeader('Cache-Control')) {\n            cacheControl = { revalidate: 0, expire: undefined }\n          }\n        } else if (is404Page) {\n          const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n          cacheControl = {\n            revalidate:\n              typeof notFoundRevalidate === 'undefined'\n                ? 0\n                : notFoundRevalidate,\n            expire: undefined,\n          }\n        } else if (is500Page) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        } else if (result.cacheControl) {\n          // If the cache entry has a cache control with a revalidate value that's\n          // a number, use it.\n          if (typeof result.cacheControl.revalidate === 'number') {\n            if (result.cacheControl.revalidate < 1) {\n              throw new Error(\n                `Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`\n              )\n            }\n            cacheControl = {\n              revalidate: result.cacheControl.revalidate,\n              expire: result.cacheControl?.expire ?? nextConfig.expireTime,\n            }\n          } else {\n            // revalidate: false\n            cacheControl = {\n              revalidate: CACHE_ONE_YEAR,\n              expire: undefined,\n            }\n          }\n        }\n\n        // If cache control is already set on the response we don't\n        // override it to allow users to customize it via next.config\n        if (cacheControl && !res.getHeader('Cache-Control')) {\n          res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n        }\n\n        // notFound: true case\n        if (!result.value) {\n          // add revalidate metadata before rendering 404 page\n          // so that we can use this as source of truth for the\n          // cache-control header instead of what the 404 page returns\n          // for the revalidate value\n          addRequestMeta(\n            req,\n            'notFoundRevalidate',\n            result.cacheControl?.revalidate\n          )\n\n          res.statusCode = 404\n\n          if (isNextDataRequest) {\n            res.end('{\"notFound\":true}')\n            return\n          }\n          // TODO: should route-module itself handle rendering the 404\n          if (routerServerContext?.render404) {\n            await routerServerContext.render404(req, res, parsedUrl, false)\n          } else {\n            res.end('This page could not be found')\n          }\n          return\n        }\n\n        if (result.value.kind === CachedRouteKind.REDIRECT) {\n          if (isNextDataRequest) {\n            res.setHeader('content-type', JSON_CONTENT_TYPE_HEADER)\n            res.end(JSON.stringify(result.value.props))\n            return\n          } else {\n            const handleRedirect = (pageData: any) => {\n              const redirect = {\n                destination: pageData.pageProps.__N_REDIRECT,\n                statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n              }\n              const statusCode = getRedirectStatus(redirect)\n              const { basePath } = nextConfig\n\n              if (\n                basePath &&\n                redirect.basePath !== false &&\n                redirect.destination.startsWith('/')\n              ) {\n                redirect.destination = `${basePath}${redirect.destination}`\n              }\n\n              if (redirect.destination.startsWith('/')) {\n                redirect.destination = normalizeRepeatedSlashes(\n                  redirect.destination\n                )\n              }\n\n              res.statusCode = statusCode\n              res.setHeader('Location', redirect.destination)\n              if (statusCode === RedirectStatusCode.PermanentRedirect) {\n                res.setHeader('Refresh', `0;url=${redirect.destination}`)\n              }\n              res.end(redirect.destination)\n            }\n            await handleRedirect(result.value.props)\n            return null\n          }\n        }\n\n        if (result.value.kind !== CachedRouteKind.PAGES) {\n          throw new Error(\n            `Invariant: received non-pages cache entry in pages handler`\n          )\n        }\n\n        // In dev, we should not cache pages for any reason.\n        if (routeModule.isDev) {\n          res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        }\n\n        // Draft mode should never be cached\n        if (isDraftMode) {\n          res.setHeader(\n            'Cache-Control',\n            'private, no-cache, no-store, max-age=0, must-revalidate'\n          )\n        }\n\n        // when invoking _error before pages/500 we don't actually\n        // send the _error response\n        if (\n          getRequestMeta(req, 'customErrorRender') ||\n          (isErrorPage &&\n            getRequestMeta(req, 'minimalMode') &&\n            res.statusCode === 500)\n        ) {\n          return null\n        }\n\n        await sendRenderResult({\n          req,\n          res,\n          // If we are rendering the error page it's not a data request\n          // anymore\n          result:\n            isNextDataRequest && !isErrorPage && !is500Page\n              ? new RenderResult(\n                  Buffer.from(JSON.stringify(result.value.pageData)),\n                  {\n                    contentType: JSON_CONTENT_TYPE_HEADER,\n                    metadata: result.value.html.metadata,\n                  }\n                )\n              : result.value.html,\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          cacheControl: routeModule.isDev ? undefined : cacheControl,\n        })\n      }\n\n      // TODO: activeSpan code path is for when wrapped by\n      // next-server can be removed when this is no longer used\n      if (activeSpan) {\n        await handleResponse()\n      } else {\n        await tracer.withPropagatedContext(req.headers, () =>\n          tracer.trace(\n            BaseServerSpan.handleRequest,\n            {\n              spanName: `${method} ${req.url}`,\n              kind: SpanKind.SERVER,\n              attributes: {\n                'http.method': method,\n                'http.target': req.url,\n              },\n            },\n            handleResponse\n          )\n        )\n      }\n    } catch (err) {\n      if (!(err instanceof NoFallbackError)) {\n        await routeModule.onRequestError(\n          req,\n          err,\n          {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: getRevalidateReason({\n              isRevalidate: hasStaticProps,\n              isOnDemandRevalidate,\n            }),\n          },\n          routerServerContext\n        )\n      }\n\n      // rethrow so that we can handle serving error page\n      throw err\n    }\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "srcPage", "originalSrcPage", "config", "userland", "routeModule", "isFallbackError", "getStaticPaths", "getStaticProps", "getServerSideProps", "handler", "req", "res", "ctx", "serverFilesManifest", "process", "env", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "originalQuery", "originalPathname", "buildManifest", "fallbackBuildManifest", "nextFontManifest", "reactLoadableManifest", "prerenderManifest", "isDraftMode", "isOnDemandRevalidate", "revalidateOnlyGenerated", "locale", "locales", "defaultLocale", "routerServerContext", "nextConfig", "resolvedPathname", "isExperimentalCompile", "experimental", "hasServerProps", "Boolean", "hasStaticProps", "hasStaticPaths", "hasGetInitialProps", "default", "getInitialProps", "isAmp", "amp", "cache<PERSON>ey", "isIsrFallback", "isNextDataRequest", "is404Page", "is500Page", "isErrorPage", "isDev", "decodedPathname", "removeTrailingSlash", "addPathPrefix", "isP<PERSON>endered", "routes", "notFoundRoutes", "includes", "prerenderInfo", "dynamicRoutes", "fallback", "NoFallbackError", "isBot", "headers", "getRequestMeta", "tracer", "getTracer", "activeSpan", "getActiveScopeSpan", "method", "resolvedUrl", "formatUrl", "pathname", "trailingSlash", "publicRuntimeConfig", "handleResponse", "span", "responseGenerator", "previousCacheEntry", "doR<PERSON>", "render", "page", "renderContext", "<PERSON><PERSON><PERSON><PERSON>", "developmentNotFoundSourcePage", "sharedContext", "customServer", "isCustomServer", "undefined", "deploymentId", "NEXT_DEPLOYMENT_ID", "renderOpts", "pageConfig", "Component", "interopDefault", "ComponentMod", "supportsDynamicResponse", "assetPrefix", "previewProps", "preview", "images", "nextConfigOutput", "output", "optimizeCss", "nextScriptWorkers", "domainLocales", "i18n", "domains", "crossOrigin", "basePath", "canonicalBase", "ampOptimizerConfig", "optimizer", "disableOptimizedLoading", "largePageDataBytes", "runtimeConfig", "Object", "keys", "length", "clientTraceMetadata", "setIsrStatus", "resolvedAsPath", "normalizeDataPath", "ErrorDebug", "err", "dev", "distDir", "path", "join", "cwd", "relativeProjectDir", "ampSkipValidation", "skipValidation", "ampValidator", "then", "renderResult", "metadata", "cacheControl", "isNotFound", "value", "isRedirect", "kind", "CachedRouteKind", "REDIRECT", "props", "pageData", "flightData", "PAGES", "html", "status", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "BaseServerSpan", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "isStale", "onRequestError", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "isRevalidate", "fallbackResponse", "getResponseCache", "previousFallbackCacheEntry", "toResponseCacheEntry", "routeKind", "RouteKind", "isRoutePPREnabled", "incrementalCache", "getIncrementalCache", "isMiss", "<PERSON><PERSON><PERSON><PERSON>", "RenderResult", "<PERSON><PERSON><PERSON>", "from", "contentType", "HTML_CONTENT_TYPE_HEADER", "revalidate", "expire", "result", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "Error", "expireTime", "CACHE_ONE_YEAR", "getCacheControlHeader", "addRequestMeta", "render404", "JSON_CONTENT_TYPE_HEADER", "JSON", "stringify", "handleRedirect", "redirect", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "getRedirectStatus", "startsWith", "normalizeRepeatedSlashes", "RedirectStatusCode", "PermanentRedirect", "sendRenderResult", "generateEtags", "poweredByHeader", "withPropagatedContext", "trace", "spanName", "SpanKind", "SERVER", "attributes"], "mappings": ";;;;+BA6CaA;;;eAAAA;;;2BA3Ca;2BACK;wBACgB;2BACrB;6BACqB;gCAChB;uBACK;mCACF;+BAO3B;8BAKA;wBACkC;gCACP;4BAK3B;6DACU;6BACgB;qEACR;wBACY;yCACL;oCACG;uBACb;+BACQ;qCACM;;;;;;AAQ7B,MAAMA,aAAa,CAAC,EACzBC,SAASC,eAAe,EACxBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAUnB;IACC,OAAO,eAAeC,QACpBC,GAAoB,EACpBC,GAAmB,EACnBC,GAEC;YAoDCC,0CAAAA;QAlDF,IAAIb,UAAUC;QACd,wDAAwD;QACxD,mDAAmD;QACnD,6DAA6D;QAC7D,IAAIa,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzBhB,UAAUA,QAAQiB,OAAO,CAAC,YAAY,OAAO;QAC/C,OAAO,IAAIjB,YAAY,UAAU;YAC/B,0CAA0C;YAC1CA,UAAU;QACZ;QACA,MAAMkB,qBAAqBJ,QAAQC,GAAG,CACnCI,4BAA4B;QAE/B,MAAMC,gBAAgB,MAAMhB,YAAYiB,OAAO,CAACX,KAAKC,KAAK;YACxDX;YACAkB;QACF;QAEA,IAAI,CAACE,eAAe;YAClBT,IAAIW,UAAU,GAAG;YACjBX,IAAIY,GAAG,CAAC;YACRX,IAAIY,SAAS,oBAAbZ,IAAIY,SAAS,MAAbZ,KAAgBa,QAAQC,OAAO;YAC/B;QACF;QAEA,MAAM,EACJC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,qBAAqB,EACrBC,gBAAgB,EAChBtB,mBAAmB,EACnBuB,qBAAqB,EACrBC,iBAAiB,EACjBC,WAAW,EACXC,oBAAoB,EACpBC,uBAAuB,EACvBC,MAAM,EACNC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,UAAU,EACVC,gBAAgB,EACjB,GAAG1B;QAEJ,MAAM2B,wBACJlC,wCAAAA,8BAAAA,oBAAqBX,MAAM,sBAA3BW,2CAAAA,4BAA6BmC,YAAY,qBAAzCnC,yCAA2CkC,qBAAqB;QAElE,MAAME,iBAAiBC,QAAQ1C;QAC/B,MAAM2C,iBAAiBD,QAAQ3C;QAC/B,MAAM6C,iBAAiBF,QAAQ5C;QAC/B,MAAM+C,qBAAqBH,QACzB,AAAC/C,CAAAA,SAASmD,OAAO,IAAInD,QAAO,EAAGoD,eAAe;QAEhD,MAAMC,QAAQ5B,MAAM6B,GAAG,KAAIvD,0BAAAA,OAAQuD,GAAG;QACtC,IAAIC,WAA0B;QAC9B,IAAIC,gBAAgB;QACpB,IAAIC,oBACFxC,cAAcwC,iBAAiB,IAAKT,CAAAA,kBAAkBF,cAAa;QAErE,MAAMY,YAAY7D,YAAY;QAC9B,MAAM8D,YAAY9D,YAAY;QAC9B,MAAM+D,cAAc/D,YAAY;QAEhC,IAAI,CAACI,YAAY4D,KAAK,IAAI,CAAC1B,eAAea,gBAAgB;YACxDO,WAAW,GAAGjB,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACpC,AAACzC,CAAAA,YAAY,OAAO8C,qBAAqB,GAAE,KAAML,SAC7C,KACAK,mBACHU,QAAQ,SAAS,IAAI;YAExB,IAAIK,aAAaC,aAAaC,aAAa;gBACzCL,WAAW,GAAGjB,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKzC,UAAUwD,QAAQ,SAAS,IAAI;YAC5E;YAEA,+CAA+C;YAC/CE,WAAWA,aAAa,WAAW,MAAMA;QAC3C;QAEA,IAAIN,kBAAkB,CAACd,aAAa;YAClC,MAAM2B,kBAAkBC,IAAAA,wCAAmB,EACzCzB,SACI0B,IAAAA,4BAAa,EAACrB,kBAAkB,CAAC,CAAC,EAAEL,QAAQ,IAC5CK;YAEN,MAAMsB,gBACJlB,QAAQb,kBAAkBgC,MAAM,CAACJ,gBAAgB,KACjD5B,kBAAkBiC,cAAc,CAACC,QAAQ,CAACN;YAE5C,MAAMO,gBAAgBnC,kBAAkBoC,aAAa,CAACzE,QAAQ;YAE9D,IAAIwE,eAAe;gBACjB,IAAIA,cAAcE,QAAQ,KAAK,SAAS,CAACN,eAAe;oBACtD,MAAM,IAAIO,wCAAe;gBAC3B;gBAEA,IACE,OAAOH,cAAcE,QAAQ,KAAK,YAClC,CAACN,iBACD,CAACR,mBACD;oBACAD,gBAAgB;gBAClB;YACF;QACF;QAEA,yEAAyE;QACzE,wEAAwE;QACxE,0BAA0B;QAC1B,IACE,AAACA,iBAAiBiB,IAAAA,YAAK,EAAClE,IAAImE,OAAO,CAAC,aAAa,IAAI,OACrDC,IAAAA,2BAAc,EAACpE,KAAK,gBACpB;YACAiD,gBAAgB;QAClB;QAEA,MAAMoB,SAASC,IAAAA,iBAAS;QACxB,MAAMC,aAAaF,OAAOG,kBAAkB;QAE5C,IAAI;YACF,MAAMC,SAASzE,IAAIyE,MAAM,IAAI;YAE7B,MAAMC,cAAcC,IAAAA,oBAAS,EAAC;gBAC5BC,UAAUzC,WAAW0C,aAAa,GAC9BzD,UAAUwD,QAAQ,GAClBpB,IAAAA,wCAAmB,EAACpC,UAAUwD,QAAQ,IAAI;gBAC9C,uDAAuD;gBACvD1D,OAAOuB,iBAAiB,CAAC,IAAIpB;YAC/B;YAEA,MAAMyD,sBACJ5C,CAAAA,uCAAAA,oBAAqB4C,mBAAmB,KACxC3C,WAAW2C,mBAAmB;YAEhC,MAAMC,iBAAiB,OAAOC;gBAC5B,MAAMC,oBAAuC,OAAO,EAClDC,kBAAkB,EACnB;wBA+RGA;oBA9RF,MAAMC,WAAW;wBACf,IAAI;gCA0DmBhD,kBAMKA,8BA2DlBA;4BA1HR,OAAO,MAAMzC,YACV0F,MAAM,CAACpF,KAAKC,KAAK;gCAChBiB,OACEuB,kBAAkB,CAACJ,wBACd;oCACC,GAAGlB,MAAM;oCACT,GAAI2B,QACA;wCACEC,KAAK7B,MAAM6B,GAAG;oCAChB,IACA,CAAC,CAAC;gCACR,IACA;oCACE,GAAG7B,KAAK;oCACR,GAAGC,MAAM;gCACX;gCACNA;gCACAkE,MAAM/F;gCACNgG,eAAe;oCACb1D;oCACA2D,YAAYtC;oCACZuC,+BAA+BpB,IAAAA,2BAAc,EAC3CpE,KACA;gCAEJ;gCACAyF,eAAe;oCACbxE;oCACAyE,cACElD,QAAQN,uCAAAA,oBAAqByD,cAAc,KAAKC;oCAClDC,cAAczF,QAAQC,GAAG,CAACyF,kBAAkB;gCAC9C;gCACAC,YAAY;oCACV5E;oCACAzB;oCACA2F,MAAM/F;oCACN0G,YAAYxG,UAAU,CAAC;oCACvByG,WAAWC,IAAAA,8BAAc,EAACzG;oCAC1B0G,cAAc1G;oCACdI;oCACAD;oCACAE;oCACAsG,yBAAyB,CAAC3D;oCAC1BlB,eAAe5B,kBACX6B,wBACAD;oCACJE;oCACAC;oCAEA2E,aAAalE,WAAWkE,WAAW;oCACnCC,cAAc3E,kBAAkB4E,OAAO;oCACvCC,QAAQrE,WAAWqE,MAAM;oCACzBC,kBAAkBtE,WAAWuE,MAAM;oCACnCC,aAAanE,QAAQL,WAAWG,YAAY,CAACqE,WAAW;oCACxDC,mBAAmBpE,QACjBL,WAAWG,YAAY,CAACsE,iBAAiB;oCAE3CC,aAAa,GAAE1E,mBAAAA,WAAW2E,IAAI,qBAAf3E,iBAAiB4E,OAAO;oCACvCC,aAAa7E,WAAW6E,WAAW;oCAEnCxG;oCACAyG,UAAU9E,WAAW8E,QAAQ;oCAC7BC,eAAe/E,WAAWY,GAAG,CAACmE,aAAa,IAAI;oCAC/CC,kBAAkB,GAAEhF,+BAAAA,WAAWG,YAAY,CAACS,GAAG,qBAA3BZ,6BAA6BiF,SAAS;oCAC1DC,yBACElF,WAAWG,YAAY,CAAC+E,uBAAuB;oCACjDC,oBACEnF,WAAWG,YAAY,CAACgF,kBAAkB;oCAC5C,mEAAmE;oCACnE,gEAAgE;oCAChEC,eACEC,OAAOC,IAAI,CAAC3C,qBAAqB4C,MAAM,GAAG,IACtC5C,sBACAc;oCAENvD;oCAEAC,cAAc;wCACZqF,qBACExF,WAAWG,YAAY,CAACqF,mBAAmB,IAC1C,EAAE;oCACP;oCAEA5F;oCACAC;oCACAC;oCACA2F,YAAY,EAAE1F,uCAAAA,oBAAqB0F,YAAY;oCAE/C1E,mBACEA,qBAAsBX,CAAAA,kBAAkBE,cAAa;oCAEvDiC;oCACA,uFAAuF;oCACvF,8DAA8D;oCAC9D,SAAS;oCACTmD,gBACEtF,kBAAkBI,qBACdgC,IAAAA,oBAAS,EAAC;wCACR,iEAAiE;wCACjE,UAAU;wCACVC,UAAU1B,oBACN4E,IAAAA,oCAAiB,EAACxG,oBAClBA;wCACJJ,OAAOG;oCACT,KACAqD;oCAEN7C;oCAEAkG,YAAY3D,IAAAA,2BAAc,EAACpE,KAAK;oCAChCgI,KAAK5D,IAAAA,2BAAc,EAACpE,KAAK;oCACzBiI,KAAKvI,YAAY4D,KAAK;oCAEtB,8CAA8C;oCAC9C4E,SAASC,aAAI,CAACC,IAAI,CAChB,yBAAyB,GACzBhI,QAAQiI,GAAG,IACX3I,YAAY4I,kBAAkB,EAC9B5I,YAAYwI,OAAO;oCAGrBK,iBAAiB,GACfpG,gCAAAA,WAAWG,YAAY,CAACS,GAAG,qBAA3BZ,8BAA6BqG,cAAc;oCAC7CC,cAAcrE,IAAAA,2BAAc,EAACpE,KAAK;gCACpC;4BACF,GACC0I,IAAI,CAAC,CAACC;gCACL,MAAM,EAAEC,QAAQ,EAAE,GAAGD;gCAErB,IAAIE,eACFD,SAASC,YAAY;gCAEvB,IAAI,gBAAgBD,YAAYA,SAASE,UAAU,EAAE;oCACnD,OAAO;wCACLC,OAAO;wCACPF;oCACF;gCACF;gCAEA,uBAAuB;gCACvB,IAAID,SAASI,UAAU,EAAE;oCACvB,OAAO;wCACLD,OAAO;4CACLE,MAAMC,8BAAe,CAACC,QAAQ;4CAC9BC,OAAOR,SAASS,QAAQ,IAAIT,SAASU,UAAU;wCACjD;wCACAT;oCACF;gCACF;gCAEA,OAAO;oCACLE,OAAO;wCACLE,MAAMC,8BAAe,CAACK,KAAK;wCAC3BC,MAAMb;wCACNU,UAAUV,aAAaC,QAAQ,CAACS,QAAQ;wCACxClF,SAASwE,aAAaC,QAAQ,CAACzE,OAAO;wCACtCsF,QAAQd,aAAaC,QAAQ,CAAChI,UAAU;oCAC1C;oCACAiI;gCACF;4BACF,GACCa,OAAO,CAAC;gCACP,IAAI,CAAC1E,MAAM;gCAEXA,KAAK2E,aAAa,CAAC;oCACjB,oBAAoB1J,IAAIW,UAAU;oCAClC,YAAY;gCACd;gCAEA,MAAMgJ,qBAAqBvF,OAAOwF,qBAAqB;gCACvD,iEAAiE;gCACjE,IAAI,CAACD,oBAAoB;oCACvB;gCACF;gCAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBC,yBAAc,CAACC,aAAa,EAC5B;oCACAC,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEN,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;oCAE1E;gCACF;gCAEA,MAAMK,QAAQP,mBAAmBE,GAAG,CAAC;gCACrC,IAAIK,OAAO;oCACT,MAAMC,OAAO,GAAG3F,OAAO,CAAC,EAAE0F,OAAO;oCAEjCnF,KAAK2E,aAAa,CAAC;wCACjB,cAAcQ;wCACd,cAAcA;wCACd,kBAAkBC;oCACpB;oCACApF,KAAKqF,UAAU,CAACD;gCAClB,OAAO;oCACLpF,KAAKqF,UAAU,CAAC,GAAG5F,OAAO,CAAC,EAAEzE,IAAIsK,GAAG,EAAE;gCACxC;4BACF;wBACJ,EAAE,OAAOtC,KAAc;4BACrB,uDAAuD;4BACvD,gDAAgD;4BAChD,IAAI9C,sCAAAA,mBAAoBqF,OAAO,EAAE;gCAC/B,MAAM7K,YAAY8K,cAAc,CAC9BxK,KACAgI,KACA;oCACEyC,YAAY;oCACZC,WAAWpL;oCACXqL,WAAW;oCACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;wCACpCC,cAAcrI;wCACdZ;oCACF;gCACF,GACAK;4BAEJ;4BACA,MAAM8F;wBACR;oBACF;oBAEA,oDAAoD;oBACpD,qBAAqB;oBACrB,IAAI9C,oBAAoB;wBACtBjC,gBAAgB;oBAClB;oBAEA,IAAIA,eAAe;wBACjB,MAAM8H,mBAAmB,MAAMrL,YAC5BsL,gBAAgB,CAAChL,KACjB8J,GAAG,CACFpK,YAAY4D,KAAK,GACb,OACAvB,SACE,CAAC,CAAC,EAAEA,SAASzC,SAAS,GACtBA,SACN,OAAO,EACL4F,oBAAoB+F,6BAA6B,IAAI,EACtD;4BACC,IAAI,CAACvL,YAAY4D,KAAK,EAAE;gCACtB,OAAO4H,IAAAA,4BAAoB,EAACD;4BAC9B;4BACA,OAAO9F;wBACT,GACA;4BACEgG,WAAWC,oBAAS,CAAC7B,KAAK;4BAC1BhE,YAAY;4BACZ8F,mBAAmB;4BACnBxJ,sBAAsB;4BACtByJ,kBAAkB,MAAM5L,YAAY6L,mBAAmB,CACrDvL,KACAmC,YACAR;4BAEFb,WAAWZ,IAAIY,SAAS;wBAC1B;wBAEJ,IAAIiK,kBAAkB;4BACpB,sEAAsE;4BACtE,iCAAiC;4BACjC,OAAOA,iBAAiBlC,YAAY;4BACpCkC,iBAAiBS,MAAM,GAAG;4BAC1B,OAAOT;wBACT;oBACF;oBAEA,IACE,CAAC3G,IAAAA,2BAAc,EAACpE,KAAK,kBACrB6B,wBACAC,2BACA,CAACoD,oBACD;wBACAjF,IAAIW,UAAU,GAAG;wBACjB,+CAA+C;wBAC/CX,IAAIwL,SAAS,CAAC,kBAAkB;wBAChCxL,IAAIY,GAAG,CAAC;wBACR,OAAO;oBACT;oBAEA,IACEoC,iBACAiC,CAAAA,uCAAAA,4BAAAA,mBAAoB6D,KAAK,qBAAzB7D,0BAA2B+D,IAAI,MAAKC,8BAAe,CAACK,KAAK,EACzD;wBACA,OAAO;4BACLR,OAAO;gCACLE,MAAMC,8BAAe,CAACK,KAAK;gCAC3BC,MAAM,IAAIkC,qBAAY,CACpBC,OAAOC,IAAI,CAAC1G,mBAAmB6D,KAAK,CAACS,IAAI,GACzC;oCACEqC,aAAaC,oCAAwB;oCACrClD,UAAU;wCACRhI,YAAYsE,mBAAmB6D,KAAK,CAACU,MAAM;wCAC3CtF,SAASe,mBAAmB6D,KAAK,CAAC5E,OAAO;oCAC3C;gCACF;gCAEFkF,UAAU,CAAC;gCACXI,QAAQvE,mBAAmB6D,KAAK,CAACU,MAAM;gCACvCtF,SAASe,mBAAmB6D,KAAK,CAAC5E,OAAO;4BAC3C;4BACA0E,cAAc;gCAAEkD,YAAY;gCAAGC,QAAQpG;4BAAU;wBACnD;oBACF;oBACA,OAAOT;gBACT;gBAEA,MAAM8G,SAAS,MAAMvM,YAAYqF,cAAc,CAAC;oBAC9C/B;oBACAhD;oBACAmC;oBACAgJ,WAAWC,oBAAS,CAAC7B,KAAK;oBAC1B1H;oBACAC;oBACAhB,WAAWZ,IAAIY,SAAS;oBACxBmE,mBAAmBA;oBACnBtD;gBACF;gBAEA,oDAAoD;gBACpD,uDAAuD;gBACvD,qBAAqB;gBACrB,IAAIsB,iBAAiB,EAACgJ,0BAAAA,OAAQT,MAAM,GAAE;oBACpCvI,gBAAgB;gBAClB;gBAEA,yCAAyC;gBACzC,IAAI,CAACgJ,QAAQ;oBACX;gBACF;gBAEA,IAAIxJ,kBAAkB,CAAC2B,IAAAA,2BAAc,EAACpE,KAAK,gBAAgB;oBACzDC,IAAIwL,SAAS,CACX,kBACA5J,uBACI,gBACAoK,OAAOT,MAAM,GACX,SACAS,OAAO1B,OAAO,GACZ,UACA;gBAEZ;gBAEA,IAAI1B;gBAEJ,IAAI,CAACpG,kBAAkBQ,eAAe;oBACpC,IAAI,CAAChD,IAAIiM,SAAS,CAAC,kBAAkB;wBACnCrD,eAAe;4BAAEkD,YAAY;4BAAGC,QAAQpG;wBAAU;oBACpD;gBACF,OAAO,IAAIzC,WAAW;oBACpB,MAAMgJ,qBAAqB/H,IAAAA,2BAAc,EAACpE,KAAK;oBAE/C6I,eAAe;wBACbkD,YACE,OAAOI,uBAAuB,cAC1B,IACAA;wBACNH,QAAQpG;oBACV;gBACF,OAAO,IAAIxC,WAAW;oBACpByF,eAAe;wBAAEkD,YAAY;wBAAGC,QAAQpG;oBAAU;gBACpD,OAAO,IAAIqG,OAAOpD,YAAY,EAAE;oBAC9B,wEAAwE;oBACxE,oBAAoB;oBACpB,IAAI,OAAOoD,OAAOpD,YAAY,CAACkD,UAAU,KAAK,UAAU;4BAQ5CE;wBAPV,IAAIA,OAAOpD,YAAY,CAACkD,UAAU,GAAG,GAAG;4BACtC,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,2CAA2C,EAAEH,OAAOpD,YAAY,CAACkD,UAAU,CAAC,IAAI,CAAC,GAD9E,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACAlD,eAAe;4BACbkD,YAAYE,OAAOpD,YAAY,CAACkD,UAAU;4BAC1CC,QAAQC,EAAAA,uBAAAA,OAAOpD,YAAY,qBAAnBoD,qBAAqBD,MAAM,KAAI7J,WAAWkK,UAAU;wBAC9D;oBACF,OAAO;wBACL,oBAAoB;wBACpBxD,eAAe;4BACbkD,YAAYO,0BAAc;4BAC1BN,QAAQpG;wBACV;oBACF;gBACF;gBAEA,2DAA2D;gBAC3D,6DAA6D;gBAC7D,IAAIiD,gBAAgB,CAAC5I,IAAIiM,SAAS,CAAC,kBAAkB;oBACnDjM,IAAIwL,SAAS,CAAC,iBAAiBc,IAAAA,mCAAqB,EAAC1D;gBACvD;gBAEA,sBAAsB;gBACtB,IAAI,CAACoD,OAAOlD,KAAK,EAAE;wBAQfkD;oBAPF,oDAAoD;oBACpD,qDAAqD;oBACrD,4DAA4D;oBAC5D,2BAA2B;oBAC3BO,IAAAA,2BAAc,EACZxM,KACA,uBACAiM,wBAAAA,OAAOpD,YAAY,qBAAnBoD,sBAAqBF,UAAU;oBAGjC9L,IAAIW,UAAU,GAAG;oBAEjB,IAAIsC,mBAAmB;wBACrBjD,IAAIY,GAAG,CAAC;wBACR;oBACF;oBACA,4DAA4D;oBAC5D,IAAIqB,uCAAAA,oBAAqBuK,SAAS,EAAE;wBAClC,MAAMvK,oBAAoBuK,SAAS,CAACzM,KAAKC,KAAKmB,WAAW;oBAC3D,OAAO;wBACLnB,IAAIY,GAAG,CAAC;oBACV;oBACA;gBACF;gBAEA,IAAIoL,OAAOlD,KAAK,CAACE,IAAI,KAAKC,8BAAe,CAACC,QAAQ,EAAE;oBAClD,IAAIjG,mBAAmB;wBACrBjD,IAAIwL,SAAS,CAAC,gBAAgBiB,oCAAwB;wBACtDzM,IAAIY,GAAG,CAAC8L,KAAKC,SAAS,CAACX,OAAOlD,KAAK,CAACK,KAAK;wBACzC;oBACF,OAAO;wBACL,MAAMyD,iBAAiB,CAACxD;4BACtB,MAAMyD,WAAW;gCACfC,aAAa1D,SAAS2D,SAAS,CAACC,YAAY;gCAC5CrM,YAAYyI,SAAS2D,SAAS,CAACE,mBAAmB;gCAClDjG,UAAUoC,SAAS2D,SAAS,CAACG,sBAAsB;4BACrD;4BACA,MAAMvM,aAAawM,IAAAA,iCAAiB,EAACN;4BACrC,MAAM,EAAE7F,QAAQ,EAAE,GAAG9E;4BAErB,IACE8E,YACA6F,SAAS7F,QAAQ,KAAK,SACtB6F,SAASC,WAAW,CAACM,UAAU,CAAC,MAChC;gCACAP,SAASC,WAAW,GAAG,GAAG9F,WAAW6F,SAASC,WAAW,EAAE;4BAC7D;4BAEA,IAAID,SAASC,WAAW,CAACM,UAAU,CAAC,MAAM;gCACxCP,SAASC,WAAW,GAAGO,IAAAA,gCAAwB,EAC7CR,SAASC,WAAW;4BAExB;4BAEA9M,IAAIW,UAAU,GAAGA;4BACjBX,IAAIwL,SAAS,CAAC,YAAYqB,SAASC,WAAW;4BAC9C,IAAInM,eAAe2M,sCAAkB,CAACC,iBAAiB,EAAE;gCACvDvN,IAAIwL,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEqB,SAASC,WAAW,EAAE;4BAC1D;4BACA9M,IAAIY,GAAG,CAACiM,SAASC,WAAW;wBAC9B;wBACA,MAAMF,eAAeZ,OAAOlD,KAAK,CAACK,KAAK;wBACvC,OAAO;oBACT;gBACF;gBAEA,IAAI6C,OAAOlD,KAAK,CAACE,IAAI,KAAKC,8BAAe,CAACK,KAAK,EAAE;oBAC/C,MAAM,qBAEL,CAFK,IAAI6C,MACR,CAAC,0DAA0D,CAAC,GADxD,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,oDAAoD;gBACpD,IAAI1M,YAAY4D,KAAK,EAAE;oBACrBrD,IAAIwL,SAAS,CAAC,iBAAiB;gBACjC;gBAEA,oCAAoC;gBACpC,IAAI7J,aAAa;oBACf3B,IAAIwL,SAAS,CACX,iBACA;gBAEJ;gBAEA,0DAA0D;gBAC1D,2BAA2B;gBAC3B,IACErH,IAAAA,2BAAc,EAACpE,KAAK,wBACnBqD,eACCe,IAAAA,2BAAc,EAACpE,KAAK,kBACpBC,IAAIW,UAAU,KAAK,KACrB;oBACA,OAAO;gBACT;gBAEA,MAAM6M,IAAAA,6BAAgB,EAAC;oBACrBzN;oBACAC;oBACA,6DAA6D;oBAC7D,UAAU;oBACVgM,QACE/I,qBAAqB,CAACG,eAAe,CAACD,YAClC,IAAIsI,qBAAY,CACdC,OAAOC,IAAI,CAACe,KAAKC,SAAS,CAACX,OAAOlD,KAAK,CAACM,QAAQ,IAChD;wBACEwC,aAAaa,oCAAwB;wBACrC9D,UAAUqD,OAAOlD,KAAK,CAACS,IAAI,CAACZ,QAAQ;oBACtC,KAEFqD,OAAOlD,KAAK,CAACS,IAAI;oBACvBkE,eAAevL,WAAWuL,aAAa;oBACvCC,iBAAiBxL,WAAWwL,eAAe;oBAC3C9E,cAAcnJ,YAAY4D,KAAK,GAAGsC,YAAYiD;gBAChD;YACF;YAEA,oDAAoD;YACpD,yDAAyD;YACzD,IAAItE,YAAY;gBACd,MAAMQ;YACR,OAAO;gBACL,MAAMV,OAAOuJ,qBAAqB,CAAC5N,IAAImE,OAAO,EAAE,IAC9CE,OAAOwJ,KAAK,CACV9D,yBAAc,CAACC,aAAa,EAC5B;wBACE8D,UAAU,GAAGrJ,OAAO,CAAC,EAAEzE,IAAIsK,GAAG,EAAE;wBAChCrB,MAAM8E,gBAAQ,CAACC,MAAM;wBACrBC,YAAY;4BACV,eAAexJ;4BACf,eAAezE,IAAIsK,GAAG;wBACxB;oBACF,GACAvF;YAGN;QACF,EAAE,OAAOiD,KAAK;YACZ,IAAI,CAAEA,CAAAA,eAAe/D,wCAAe,AAAD,GAAI;gBACrC,MAAMvE,YAAY8K,cAAc,CAC9BxK,KACAgI,KACA;oBACEyC,YAAY;oBACZC,WAAWpL;oBACXqL,WAAW;oBACXC,kBAAkBC,IAAAA,0BAAmB,EAAC;wBACpCC,cAAcrI;wBACdZ;oBACF;gBACF,GACAK;YAEJ;YAEA,mDAAmD;YACnD,MAAM8F;QACR;IACF;AACF", "ignoreList": [0]}