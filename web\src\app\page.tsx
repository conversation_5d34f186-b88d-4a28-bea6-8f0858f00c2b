import { Box, Container, VStack, Heading, Text } from '@chakra-ui/react';
import ChatBox from '../components/ChatBox';

export default function HomePage() {
    return (
        <Box minH="100vh" bg="gray.50">
            {/* Header */}
            <Box bg="white" shadow="sm" borderBottom="1px" borderColor="gray.200">
                <Container maxW="6xl" py={4}>
                    <VStack gap={2} align="start">
                        <Heading
                            size="lg"
                            color="brand.700"
                            fontWeight="bold"
                        >
                            🤖 AI Chatbot
                        </Heading>
                        <Text
                            fontSize="sm"
                            color="gray.600"
                            maxW="2xl"
                        >
                            <PERSON><PERSON> thống chatbot thông minh sử dụng RAG (Retrieval-Augmented Generation)
                            để trả lời câu hỏi dựa trên dữ liệu được huấn luyện
                        </Text>
                    </VStack>
                </Container>
            </Box>

            {/* Main Content */}
            <Container maxW="4xl" py={8}>
                <VStack gap={6} align="stretch">
                    <Box
                        bg="white"
                        rounded="2xl"
                        shadow="lg"
                        p={6}
                        border="1px"
                        borderColor="gray.200"
                    >
                        <ChatBox />
                    </Box>

                    {/* Footer Info */}
                    <Box textAlign="center" py={4}>
                        <Text fontSize="xs" color="gray.500">
                            Được xây dựng với ❤️ sử dụng Next.js, Chakra UI và FastAPI
                        </Text>
                    </Box>
                </VStack>
            </Container>
        </Box>
    );
}
