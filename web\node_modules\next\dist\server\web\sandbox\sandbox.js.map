{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "sourcesContent": ["import type { NodejsRequestData, FetchEventResult, RequestData } from '../types'\nimport type { EdgeFunctionDefinition } from '../../../build/webpack/plugins/middleware-plugin'\nimport type { EdgeRuntime } from 'next/dist/compiled/edge-runtime'\nimport {\n  getModuleContext,\n  requestStore,\n  edgeSandboxNextRequestContext,\n} from './context'\nimport { requestToBodyStream } from '../../body-streams'\nimport type { ServerComponentsHmrCache } from '../../response-cache'\nimport {\n  getBuiltinRequestContext,\n  type BuiltinRequestContextValue,\n} from '../../after/builtin-request-context'\nimport {\n  RouterServerContextSymbol,\n  routerServerGlobal,\n} from '../../lib/router-utils/router-server-context'\n\nexport const ErrorSource = Symbol('SandboxError')\n\nconst FORBIDDEN_HEADERS = [\n  'content-length',\n  'content-encoding',\n  'transfer-encoding',\n]\n\ninterface RunnerFnParams {\n  name: string\n  onError?: (err: unknown) => void\n  onWarning?: (warn: Error) => void\n  paths: string[]\n  request: NodejsRequestData\n  useCache: boolean\n  edgeFunctionEntry: Pick<EdgeFunctionDefinition, 'assets' | 'wasm' | 'env'>\n  distDir: string\n  incrementalCache?: any\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n}\n\ntype RunnerFn = (params: RunnerFnParams) => Promise<FetchEventResult>\n\n/**\n * Decorates the runner function making sure all errors it can produce are\n * tagged with `edge-server` so they can properly be rendered in dev.\n */\nfunction withTaggedErrors(fn: RunnerFn): RunnerFn {\n  if (process.env.NODE_ENV === 'development') {\n    const { getServerError } =\n      require('../../dev/node-stack-frames') as typeof import('../../dev/node-stack-frames')\n\n    return (params) =>\n      fn(params)\n        .then((result) => ({\n          ...result,\n          waitUntil: result?.waitUntil?.catch((error) => {\n            // TODO: used COMPILER_NAMES.edgeServer instead. Verify that it does not increase the runtime size.\n            throw getServerError(error, 'edge-server')\n          }),\n        }))\n        .catch((error) => {\n          // TODO: used COMPILER_NAMES.edgeServer instead\n          throw getServerError(error, 'edge-server')\n        })\n  }\n\n  return fn\n}\n\nexport async function getRuntimeContext(\n  params: Omit<RunnerFnParams, 'request'>\n): Promise<EdgeRuntime<any>> {\n  const { runtime, evaluateInContext } = await getModuleContext({\n    moduleName: params.name,\n    onWarning: params.onWarning ?? (() => {}),\n    onError: params.onError ?? (() => {}),\n    useCache: params.useCache !== false,\n    edgeFunctionEntry: params.edgeFunctionEntry,\n    distDir: params.distDir,\n  })\n\n  if (params.incrementalCache) {\n    runtime.context.globalThis.__incrementalCacheShared = true\n    runtime.context.globalThis.__incrementalCache = params.incrementalCache\n  }\n\n  // expose router server context for access to dev handlers like\n  // logErrorWithOriginalStack\n  ;(runtime.context.globalThis as any as typeof routerServerGlobal)[\n    RouterServerContextSymbol\n  ] = routerServerGlobal[RouterServerContextSymbol]\n\n  if (params.serverComponentsHmrCache) {\n    runtime.context.globalThis.__serverComponentsHmrCache =\n      params.serverComponentsHmrCache\n  }\n\n  for (const paramPath of params.paths) {\n    evaluateInContext(paramPath)\n  }\n  return runtime\n}\n\nexport const run = withTaggedErrors(async function runWithTaggedErrors(params) {\n  const runtime = await getRuntimeContext(params)\n\n  const edgeFunction: (args: {\n    request: RequestData\n  }) => Promise<FetchEventResult> = (\n    await runtime.context._ENTRIES[`middleware_${params.name}`]\n  ).default\n\n  const cloned = !['HEAD', 'GET'].includes(params.request.method)\n    ? params.request.body?.cloneBodyStream()\n    : undefined\n\n  const KUint8Array = runtime.evaluate('Uint8Array')\n  const urlInstance = new URL(params.request.url)\n\n  params.request.url = urlInstance.toString()\n\n  const headers = new Headers()\n  for (const [key, value] of Object.entries(params.request.headers)) {\n    headers.set(key, value?.toString() ?? '')\n  }\n\n  try {\n    let result: FetchEventResult | undefined = undefined\n    const builtinRequestCtx: BuiltinRequestContextValue = {\n      ...getBuiltinRequestContext(),\n      // FIXME(after):\n      // arguably, this is an abuse of \"@next/request-context\" --\n      // it'd make more sense to simply forward its existing value into the sandbox (in `createModuleContext`)\n      // but here we're using it to just pass in `waitUntil` regardless if we were running in this context or not.\n      waitUntil: params.request.waitUntil,\n    }\n    await edgeSandboxNextRequestContext.run(builtinRequestCtx, () =>\n      requestStore.run({ headers }, async () => {\n        result = await edgeFunction({\n          request: {\n            ...params.request,\n            body:\n              cloned &&\n              requestToBodyStream(runtime.context, KUint8Array, cloned),\n          },\n        })\n        for (const headerName of FORBIDDEN_HEADERS) {\n          result.response.headers.delete(headerName)\n        }\n      })\n    )\n\n    if (!result) throw new Error('Edge function did not return a response')\n    return result\n  } finally {\n    await params.request.body?.finalize()\n  }\n})\n"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCacheShared", "__incrementalCache", "RouterServerContextSymbol", "routerServerGlobal", "serverComponentsHmrCache", "__serverComponentsHmrCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "toString", "headers", "Headers", "key", "value", "Object", "entries", "set", "builtinRequestCtx", "getBuiltinRequestContext", "edgeSandboxNextRequestContext", "requestStore", "requestToBodyStream", "headerName", "response", "delete", "Error", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAmBaA,WAAW;eAAXA;;IAkDSC,iBAAiB;eAAjBA;;IAkCTC,GAAG;eAAHA;;;yBAhGN;6BAC6B;uCAK7B;qCAIA;AAEA,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAiBD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEO,eAAeL,kBACpBW,MAAuC;IAEvC,MAAM,EAAEM,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BV,QAAQW,OAAO,CAACC,UAAU,CAACC,wBAAwB,GAAG;QACtDb,QAAQW,OAAO,CAACC,UAAU,CAACE,kBAAkB,GAAGpB,OAAOgB,gBAAgB;IACzE;IAEA,+DAA+D;IAC/D,4BAA4B;;IAC1BV,QAAQW,OAAO,CAACC,UAAU,AAAqC,CAC/DG,8CAAyB,CAC1B,GAAGC,uCAAkB,CAACD,8CAAyB,CAAC;IAEjD,IAAIrB,OAAOuB,wBAAwB,EAAE;QACnCjB,QAAQW,OAAO,CAACC,UAAU,CAACM,0BAA0B,GACnDxB,OAAOuB,wBAAwB;IACnC;IAEA,KAAK,MAAME,aAAazB,OAAO0B,KAAK,CAAE;QACpCnB,kBAAkBkB;IACpB;IACA,OAAOnB;AACT;AAEO,MAAMhB,MAAMG,iBAAiB,eAAekC,oBAAoB3B,MAAM;QAUvEA;IATJ,MAAMM,UAAU,MAAMjB,kBAAkBW;IAExC,MAAM4B,eAE4B,AAChC,CAAA,MAAMtB,QAAQW,OAAO,CAACY,QAAQ,CAAC,CAAC,WAAW,EAAE7B,OAAOU,IAAI,EAAE,CAAC,AAAD,EAC1DoB,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAChC,OAAOiC,OAAO,CAACC,MAAM,KAC1DlC,uBAAAA,OAAOiC,OAAO,CAACE,IAAI,qBAAnBnC,qBAAqBoC,eAAe,KACpCC;IAEJ,MAAMC,cAAchC,QAAQiC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIzC,OAAOiC,OAAO,CAACS,GAAG;IAE9C1C,OAAOiC,OAAO,CAACS,GAAG,GAAGF,YAAYG,QAAQ;IAEzC,MAAMC,UAAU,IAAIC;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACjD,OAAOiC,OAAO,CAACW,OAAO,EAAG;QACjEA,QAAQM,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOJ,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIzC,SAAuCmC;QAC3C,MAAMc,oBAAgD;YACpD,GAAGC,IAAAA,+CAAwB,GAAE;YAC7B,gBAAgB;YAChB,2DAA2D;YAC3D,wGAAwG;YACxG,4GAA4G;YAC5GjD,WAAWH,OAAOiC,OAAO,CAAC9B,SAAS;QACrC;QACA,MAAMkD,sCAA6B,CAAC/D,GAAG,CAAC6D,mBAAmB,IACzDG,qBAAY,CAAChE,GAAG,CAAC;gBAAEsD;YAAQ,GAAG;gBAC5B1C,SAAS,MAAM0B,aAAa;oBAC1BK,SAAS;wBACP,GAAGjC,OAAOiC,OAAO;wBACjBE,MACEJ,UACAwB,IAAAA,gCAAmB,EAACjD,QAAQW,OAAO,EAAEqB,aAAaP;oBACtD;gBACF;gBACA,KAAK,MAAMyB,cAAchE,kBAAmB;oBAC1CU,OAAOuD,QAAQ,CAACb,OAAO,CAACc,MAAM,CAACF;gBACjC;YACF;QAGF,IAAI,CAACtD,QAAQ,MAAM,qBAAoD,CAApD,IAAIyD,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;QACtE,OAAOzD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOiC,OAAO,CAACE,IAAI,qBAAnBnC,sBAAqB4D,QAAQ;IACrC;AACF", "ignoreList": [0]}