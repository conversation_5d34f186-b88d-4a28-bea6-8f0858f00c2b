{"version": 3, "sources": ["../../../../src/server/lib/router-utils/instrumentation-globals.external.ts"], "sourcesContent": ["import path from 'node:path'\nimport isError from '../../../lib/is-error'\nimport { INSTRUMENTATION_HOOK_FILENAME } from '../../../lib/constants'\nimport type {\n  InstrumentationModule,\n  InstrumentationOnRequestError,\n} from '../../instrumentation/types'\nimport { interopDefault } from '../../../lib/interop-default'\nimport { afterRegistration as extendInstrumentationAfterRegistration } from './instrumentation-node-extensions'\n\nlet cachedInstrumentationModule: InstrumentationModule\n\nexport async function getInstrumentationModule(\n  projectDir: string,\n  distDir: string\n): Promise<InstrumentationModule | undefined> {\n  if (cachedInstrumentationModule) {\n    return cachedInstrumentationModule\n  }\n\n  try {\n    cachedInstrumentationModule = interopDefault(\n      await require(\n        path.join(\n          projectDir,\n          distDir,\n          'server',\n          `${INSTRUMENTATION_HOOK_FILENAME}.js`\n        )\n      )\n    )\n    return cachedInstrumentationModule\n  } catch (err: unknown) {\n    if (\n      isError(err) &&\n      err.code !== 'ENOENT' &&\n      err.code !== 'MODULE_NOT_FOUND' &&\n      err.code !== 'ERR_MODULE_NOT_FOUND'\n    ) {\n      throw err\n    }\n  }\n}\n\nlet instrumentationModulePromise: Promise<any> | null = null\n\nasync function registerInstrumentation(projectDir: string, distDir: string) {\n  // Ensure registerInstrumentation is not called in production build\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return\n  }\n  if (!instrumentationModulePromise) {\n    instrumentationModulePromise = getInstrumentationModule(projectDir, distDir)\n  }\n  const instrumentation = await instrumentationModulePromise\n  if (instrumentation?.register) {\n    try {\n      await instrumentation.register()\n      extendInstrumentationAfterRegistration()\n    } catch (err: any) {\n      err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n      throw err\n    }\n  }\n}\n\nexport async function instrumentationOnRequestError(\n  projectDir: string,\n  distDir: string,\n  ...args: Parameters<InstrumentationOnRequestError>\n) {\n  const instrumentation = await getInstrumentationModule(projectDir, distDir)\n  try {\n    await instrumentation?.onRequestError?.(...args)\n  } catch (err) {\n    // Log the soft error and continue, since the original error has already been thrown\n    console.error('Error in instrumentation.onRequestError:', err)\n  }\n}\n\nlet registerInstrumentationPromise: Promise<void> | null = null\nexport function ensureInstrumentationRegistered(\n  projectDir: string,\n  distDir: string\n) {\n  if (!registerInstrumentationPromise) {\n    registerInstrumentationPromise = registerInstrumentation(\n      projectDir,\n      distDir\n    )\n  }\n  return registerInstrumentationPromise\n}\n"], "names": ["path", "isError", "INSTRUMENTATION_HOOK_FILENAME", "interopDefault", "afterRegistration", "extendInstrumentationAfterRegistration", "cachedInstrumentationModule", "getInstrumentationModule", "projectDir", "distDir", "require", "join", "err", "code", "instrumentationModulePromise", "registerInstrumentation", "process", "env", "NEXT_PHASE", "instrumentation", "register", "message", "instrumentationOnRequestError", "args", "onRequestError", "console", "error", "registerInstrumentationPromise", "ensureInstrumentationRegistered"], "mappings": "AAAA,OAAOA,UAAU,YAAW;AAC5B,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,6BAA6B,QAAQ,yBAAwB;AAKtE,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,qBAAqBC,sCAAsC,QAAQ,oCAAmC;AAE/G,IAAIC;AAEJ,OAAO,eAAeC,yBACpBC,UAAkB,EAClBC,OAAe;IAEf,IAAIH,6BAA6B;QAC/B,OAAOA;IACT;IAEA,IAAI;QACFA,8BAA8BH,eAC5B,MAAMO,QACJV,KAAKW,IAAI,CACPH,YACAC,SACA,UACA,GAAGP,8BAA8B,GAAG,CAAC;QAI3C,OAAOI;IACT,EAAE,OAAOM,KAAc;QACrB,IACEX,QAAQW,QACRA,IAAIC,IAAI,KAAK,YACbD,IAAIC,IAAI,KAAK,sBACbD,IAAIC,IAAI,KAAK,wBACb;YACA,MAAMD;QACR;IACF;AACF;AAEA,IAAIE,+BAAoD;AAExD,eAAeC,wBAAwBP,UAAkB,EAAEC,OAAe;IACxE,mEAAmE;IACnE,IAAIO,QAAQC,GAAG,CAACC,UAAU,KAAK,0BAA0B;QACvD;IACF;IACA,IAAI,CAACJ,8BAA8B;QACjCA,+BAA+BP,yBAAyBC,YAAYC;IACtE;IACA,MAAMU,kBAAkB,MAAML;IAC9B,IAAIK,mCAAAA,gBAAiBC,QAAQ,EAAE;QAC7B,IAAI;YACF,MAAMD,gBAAgBC,QAAQ;YAC9Bf;QACF,EAAE,OAAOO,KAAU;YACjBA,IAAIS,OAAO,GAAG,CAAC,sDAAsD,EAAET,IAAIS,OAAO,EAAE;YACpF,MAAMT;QACR;IACF;AACF;AAEA,OAAO,eAAeU,8BACpBd,UAAkB,EAClBC,OAAe,EACf,GAAGc,IAA+C;IAElD,MAAMJ,kBAAkB,MAAMZ,yBAAyBC,YAAYC;IACnE,IAAI;YACIU;QAAN,OAAMA,oCAAAA,kCAAAA,gBAAiBK,cAAc,qBAA/BL,qCAAAA,oBAAqCI;IAC7C,EAAE,OAAOX,KAAK;QACZ,oFAAoF;QACpFa,QAAQC,KAAK,CAAC,4CAA4Cd;IAC5D;AACF;AAEA,IAAIe,iCAAuD;AAC3D,OAAO,SAASC,gCACdpB,UAAkB,EAClBC,OAAe;IAEf,IAAI,CAACkB,gCAAgC;QACnCA,iCAAiCZ,wBAC/BP,YACAC;IAEJ;IACA,OAAOkB;AACT", "ignoreList": [0]}