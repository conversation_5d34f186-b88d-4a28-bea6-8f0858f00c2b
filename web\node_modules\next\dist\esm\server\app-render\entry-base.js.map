{"version": 3, "sources": ["../../../src/server/app-render/entry-base.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nexport {\n  createTemporaryReferenceSet,\n  renderToReadableStream,\n  decodeReply,\n  decodeAction,\n  decodeFormState,\n} from 'react-server-dom-webpack/server'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { unstable_prerender as prerender } from 'react-server-dom-webpack/static'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { captureOwnerStack } from 'react'\n\nexport { default as LayoutRouter } from '../../client/components/layout-router'\nexport { default as RenderFromTemplateContext } from '../../client/components/render-from-template-context'\nexport { workAsyncStorage } from '../app-render/work-async-storage.external'\nexport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nexport { actionAsyncStorage } from '../app-render/action-async-storage.external'\n\nexport { ClientPageRoot } from '../../client/components/client-page'\nexport { ClientSegmentRoot } from '../../client/components/client-segment'\nexport {\n  createServerSearchParamsForServerPage,\n  createPrerenderSearchParamsForClientPage,\n} from '../request/search-params'\nexport {\n  createServerParamsForServerSegment,\n  createPrerenderParamsForClientSegment,\n} from '../request/params'\nexport * as serverHooks from '../../client/components/hooks-server-context'\nexport { HTTPAccessFallbackBoundary } from '../../client/components/http-access-fallback/error-boundary'\nexport { createMetadataComponents } from '../../lib/metadata/metadata'\nexport {\n  MetadataBoundary,\n  ViewportBoundary,\n  OutletBoundary,\n  RootLayoutBoundary,\n} from '../../lib/framework/boundary-components'\n\nexport { preloadStyle, preloadFont, preconnect } from './rsc/preloads'\nexport { Postpone } from './rsc/postpone'\nexport { taintObjectReference } from './rsc/taint'\nexport { collectSegmentData } from './collect-segment-data'\n\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { patchFetch as _patchFetch } from '../lib/patch-fetch'\n\nlet SegmentViewNode: typeof import('../../next-devtools/userspace/app/segment-explorer-node').SegmentViewNode =\n  () => null\nlet SegmentViewStateNode: typeof import('../../next-devtools/userspace/app/segment-explorer-node').SegmentViewStateNode =\n  () => null\nif (process.env.NODE_ENV === 'development') {\n  const mod =\n    require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n  SegmentViewNode = mod.SegmentViewNode\n  SegmentViewStateNode = mod.SegmentViewStateNode\n}\n\n// For hot-reloader\ndeclare global {\n  var __next__clear_chunk_cache__: (() => void) | null | undefined\n  var __turbopack_clear_chunk_cache__: () => void | null | undefined\n}\n// hot-reloader modules are not bundled so we need to inject `__next__clear_chunk_cache__`\n// into globalThis from this file which is bundled.\nif (process.env.TURBOPACK) {\n  globalThis.__next__clear_chunk_cache__ = __turbopack_clear_chunk_cache__\n} else {\n  // Webpack does not have chunks on the server\n  globalThis.__next__clear_chunk_cache__ = null\n}\n\n// patchFetch makes use of APIs such as `React.unstable_postpone` which are only available\n// in the experimental channel of React, so export it from here so that it comes from the bundled runtime\nexport function patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\n// Development only\nexport { SegmentViewNode, SegmentViewStateNode }\n"], "names": ["createTemporaryReferenceSet", "renderToReadableStream", "decodeReply", "decodeAction", "decodeFormState", "unstable_prerender", "prerender", "captureOwnerStack", "default", "LayoutRouter", "RenderFromTemplateContext", "workAsyncStorage", "workUnitAsyncStorage", "actionAsyncStorage", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createPrerenderParamsForClientSegment", "serverHooks", "HTTPAccessFallbackBoundary", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "OutletBoundary", "RootLayoutBoundary", "preloadStyle", "preloadFont", "preconnect", "Postpone", "taintObjectReference", "collectSegmentData", "patchFetch", "_patchFetch", "SegmentViewNode", "SegmentViewStateNode", "process", "env", "NODE_ENV", "mod", "require", "TURBOPACK", "globalThis", "__next__clear_chunk_cache__", "__turbopack_clear_chunk_cache__"], "mappings": "AAAA,6DAA6D;AAC7D,SACEA,2BAA2B,EAC3BC,sBAAsB,EACtBC,WAAW,EACXC,YAAY,EACZC,eAAe,QACV,kCAAiC;AAExC,6DAA6D;AAC7D,SAASC,sBAAsBC,SAAS,QAAQ,kCAAiC;AAEjF,6DAA6D;AAC7D,SAASC,iBAAiB,QAAQ,QAAO;AAEzC,SAASC,WAAWC,YAAY,QAAQ,wCAAuC;AAC/E,SAASD,WAAWE,yBAAyB,QAAQ,uDAAsD;AAC3G,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,kBAAkB,QAAQ,8CAA6C;AAEhF,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,iBAAiB,QAAQ,yCAAwC;AAC1E,SACEC,qCAAqC,EACrCC,wCAAwC,QACnC,2BAA0B;AACjC,SACEC,kCAAkC,EAClCC,qCAAqC,QAChC,oBAAmB;AAC1B,OAAO,KAAKC,WAAW,MAAM,+CAA8C;AAC3E,SAASC,0BAA0B,QAAQ,8DAA6D;AACxG,SAASC,wBAAwB,QAAQ,8BAA6B;AACtE,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,kBAAkB,QACb,0CAAyC;AAEhD,SAASC,YAAY,EAAEC,WAAW,EAAEC,UAAU,QAAQ,iBAAgB;AACtE,SAASC,QAAQ,QAAQ,iBAAgB;AACzC,SAASC,oBAAoB,QAAQ,cAAa;AAClD,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,SAASrB,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASqB,cAAcC,WAAW,QAAQ,qBAAoB;AAE9D,IAAIC,kBACF,IAAM;AACR,IAAIC,uBACF,IAAM;AACR,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,MACJC,QAAQ;IACVN,kBAAkBK,IAAIL,eAAe;IACrCC,uBAAuBI,IAAIJ,oBAAoB;AACjD;AAOA,0FAA0F;AAC1F,mDAAmD;AACnD,IAAIC,QAAQC,GAAG,CAACI,SAAS,EAAE;IACzBC,WAAWC,2BAA2B,GAAGC;AAC3C,OAAO;IACL,6CAA6C;IAC7CF,WAAWC,2BAA2B,GAAG;AAC3C;AAEA,0FAA0F;AAC1F,yGAAyG;AACzG,OAAO,SAASX;IACd,OAAOC,YAAY;QACjBvB;QACAC;IACF;AACF;AAEA,mBAAmB;AACnB,SAASuB,eAAe,EAAEC,oBAAoB,GAAE", "ignoreList": [0]}