{"version": 3, "sources": ["../../../src/server/request/root-params.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n} from '../app-render/dynamic-rendering'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type StaticPrerenderStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FallbackRouteParams } from './fallback-params'\nimport type { Params, ParamValue } from './params'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { actionAsyncStorage } from '../app-render/action-async-storage.external'\nimport { warnOnce } from '../../build/output/log'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\n/**\n * @deprecated import specific root params from `next/root-params` instead.\n */\nexport async function unstable_rootParams(): Promise<Params> {\n  warnOnce(\n    '`unstable_rootParams()` is deprecated and will be removed in an upcoming major release. Import specific root params from `next/root-params` instead.'\n  )\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError('Missing workStore in unstable_rootParams')\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workUnitStore) {\n    throw new Error(\n      `Route ${workStore.route} used \\`unstable_rootParams()\\` in Pages Router. This API is only available within App Router.`\n    )\n  }\n\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache': {\n      throw new Error(\n        `Route ${workStore.route} used \\`unstable_rootParams()\\` inside \\`\"use cache\"\\` or \\`unstable_cache\\`. Support for this API inside cache scopes is planned for a future version of Next.js.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      return createPrerenderRootParams(\n        workUnitStore.rootParams,\n        workStore,\n        workUnitStore\n      )\n    case 'private-cache':\n    case 'prerender-runtime':\n    case 'request':\n      return Promise.resolve(workUnitStore.rootParams)\n    default:\n      return workUnitStore satisfies never\n  }\n}\n\nfunction createPrerenderRootParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender-client': {\n      const exportName = '`unstable_rootParams`'\n      throw new InvariantError(\n        `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n      )\n    }\n    case 'prerender': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            const cachedParams = CachedParams.get(underlyingParams)\n            if (cachedParams) {\n              return cachedParams\n            }\n\n            const promise = makeHangingPromise<Params>(\n              prerenderStore.renderSignal,\n              workStore.route,\n              '`unstable_rootParams`'\n            )\n            CachedParams.set(underlyingParams, promise)\n\n            return promise\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // We have fallback params at this level so we need to make an erroring\n            // params object which will postpone if you access the fallback params\n            return makeErroringRootParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingParams)\n}\n\nfunction makeErroringRootParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess(\n              'unstable_rootParams',\n              prop\n            )\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\n/**\n * Used for the compiler-generated `next/root-params` module.\n * @internal\n */\nexport function getRootParam(paramName: string): Promise<ParamValue> {\n  const apiName = `\\`import('next/root-params').${paramName}()\\``\n\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(`Missing workStore in ${apiName}`)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore) {\n    throw new Error(\n      `Route ${workStore.route} used ${apiName} outside of a Server Component. This is not allowed.`\n    )\n  }\n\n  const actionStore = actionAsyncStorage.getStore()\n  if (actionStore) {\n    if (actionStore.isAppRoute) {\n      // TODO(root-params): add support for route handlers\n      throw new Error(\n        `Route ${workStore.route} used ${apiName} inside a Route Handler. Support for this API in Route Handlers is planned for a future version of Next.js.`\n      )\n    }\n    if (actionStore.isAction && workUnitStore.phase === 'action') {\n      // Actions are not fundamentally tied to a route (even if they're always submitted from some page),\n      // so root params would be inconsistent if an action is called from multiple roots.\n      // Make sure we check if the phase is \"action\" - we should not error in the rerender\n      // after an action revalidates or updates cookies (which will still have `actionStore.isAction === true`)\n      throw new Error(\n        `${apiName} was used inside a Server Action. This is not supported. Functions from 'next/root-params' can only be called in the context of a route.`\n      )\n    }\n  }\n\n  switch (workUnitStore.type) {\n    case 'unstable-cache':\n    case 'cache': {\n      throw new Error(\n        `Route ${workStore.route} used ${apiName} inside \\`\"use cache\"\\` or \\`unstable_cache\\`. Support for this API inside cache scopes is planned for a future version of Next.js.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy': {\n      return createPrerenderRootParamPromise(\n        paramName,\n        workStore,\n        workUnitStore,\n        apiName\n      )\n    }\n    case 'private-cache':\n    case 'prerender-runtime':\n    case 'request': {\n      break\n    }\n    default: {\n      workUnitStore satisfies never\n    }\n  }\n  return Promise.resolve(workUnitStore.rootParams[paramName])\n}\n\nfunction createPrerenderRootParamPromise(\n  paramName: string,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore,\n  apiName: string\n): Promise<ParamValue> {\n  switch (prerenderStore.type) {\n    case 'prerender-client': {\n      throw new InvariantError(\n        `${apiName} must not be used within a client component. Next.js should be preventing ${apiName} from being included in client components statically, but did not in this case.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    default:\n  }\n\n  const underlyingParams = prerenderStore.rootParams\n\n  switch (prerenderStore.type) {\n    case 'prerender': {\n      // We are in a dynamicIO prerender.\n      // The param is a fallback, so it should be treated as dynamic.\n      if (\n        prerenderStore.fallbackRouteParams &&\n        prerenderStore.fallbackRouteParams.has(paramName)\n      ) {\n        return makeHangingPromise<ParamValue>(\n          prerenderStore.renderSignal,\n          workStore.route,\n          apiName\n        )\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      // We aren't in a dynamicIO prerender, but the param is a fallback,\n      // so we need to make an erroring params object which will postpone/error if you access it\n      if (\n        prerenderStore.fallbackRouteParams &&\n        prerenderStore.fallbackRouteParams.has(paramName)\n      ) {\n        return makeErroringRootParamPromise(\n          paramName,\n          workStore,\n          prerenderStore,\n          apiName\n        )\n      }\n      break\n    }\n    case 'prerender-legacy': {\n      // legacy prerenders can't have fallback params\n      break\n    }\n    default: {\n      prerenderStore satisfies never\n    }\n  }\n\n  // If the param is not a fallback param, we just return the statically available value.\n  return Promise.resolve(underlyingParams[paramName])\n}\n\n/** Deliberately async -- we want to create a rejected promise, not error synchronously. */\nasync function makeErroringRootParamPromise(\n  paramName: string,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy,\n  apiName: string\n): Promise<ParamValue> {\n  const expression = describeStringPropertyAccess(apiName, paramName)\n  // In most dynamic APIs, we also throw if `dynamic = \"error\"`.\n  // However, root params are only dynamic when we're generating a fallback shell,\n  // and even with `dynamic = \"error\"` we still support generating dynamic fallback shells.\n  // TODO: remove this comment when dynamicIO is the default since there will be no `dynamic = \"error\"`\n  switch (prerenderStore.type) {\n    case 'prerender-ppr': {\n      return postponeWithTracking(\n        workStore.route,\n        expression,\n        prerenderStore.dynamicTracking\n      )\n    }\n    case 'prerender-legacy': {\n      return throwToInterruptStaticGeneration(\n        expression,\n        workStore,\n        prerenderStore\n      )\n    }\n    default: {\n      prerenderStore satisfies never\n    }\n  }\n}\n"], "names": ["InvariantError", "postponeWithTracking", "throwToInterruptStaticGeneration", "workAsyncStorage", "workUnitAsyncStorage", "makeHangingPromise", "describeStringPropertyAccess", "wellKnownProperties", "actionAsyncStorage", "warnOnce", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "unstable_rootParams", "workStore", "getStore", "workUnitStore", "Error", "route", "type", "createPrerenderRootParams", "rootParams", "Promise", "resolve", "underlyingParams", "prerenderStore", "exportName", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "cachedParams", "get", "promise", "renderSignal", "set", "makeErroringRootParams", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "prop", "defineProperty", "expression", "dynamicTracking", "enumerable", "getRootParam", "paramName", "apiName", "actionStore", "isAppRoute", "isAction", "phase", "createPrerenderRootParamPromise", "makeErroringRootParamPromise"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAkC;AACjE,SACEC,oBAAoB,EACpBC,gCAAgC,QAC3B,kCAAiC;AACxC,SACEC,gBAAgB,QAEX,4CAA2C;AAClD,SACEC,oBAAoB,QAIf,iDAAgD;AACvD,SAASC,kBAAkB,QAAQ,6BAA4B;AAG/D,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;AAC7C,SAASC,kBAAkB,QAAQ,8CAA6C;AAChF,SAASC,QAAQ,QAAQ,yBAAwB;AAGjD,MAAMC,eAAe,IAAIC;AAEzB;;CAEC,GACD,OAAO,eAAeC;IACpBH,SACE;IAEF,MAAMI,YAAYV,iBAAiBW,QAAQ;IAC3C,IAAI,CAACD,WAAW;QACd,MAAM,qBAA8D,CAA9D,IAAIb,eAAe,6CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,MAAMe,gBAAgBX,qBAAqBU,QAAQ;IAEnD,IAAI,CAACC,eAAe;QAClB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,8FAA8F,CAAC,GADpH,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAQF,cAAcG,IAAI;QACxB,KAAK;QACL,KAAK;YAAkB;gBACrB,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,kKAAkK,CAAC,GADxL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOE,0BACLJ,cAAcK,UAAU,EACxBP,WACAE;QAEJ,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOM,QAAQC,OAAO,CAACP,cAAcK,UAAU;QACjD;YACE,OAAOL;IACX;AACF;AAEA,SAASI,0BACPI,gBAAwB,EACxBV,SAAoB,EACpBW,cAAoC;IAEpC,OAAQA,eAAeN,IAAI;QACzB,KAAK;YAAoB;gBACvB,MAAMO,aAAa;gBACnB,MAAM,qBAEL,CAFK,IAAIzB,eACR,GAAGyB,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;YAAa;gBAChB,MAAMC,iBAAiBF,eAAeG,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOL,iBAAkB;wBAClC,IAAIG,eAAeG,GAAG,CAACD,MAAM;4BAC3B,MAAME,eAAepB,aAAaqB,GAAG,CAACR;4BACtC,IAAIO,cAAc;gCAChB,OAAOA;4BACT;4BAEA,MAAME,UAAU3B,mBACdmB,eAAeS,YAAY,EAC3BpB,UAAUI,KAAK,EACf;4BAEFP,aAAawB,GAAG,CAACX,kBAAkBS;4BAEnC,OAAOA;wBACT;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMN,iBAAiBF,eAAeG,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOL,iBAAkB;wBAClC,IAAIG,eAAeG,GAAG,CAACD,MAAM;4BAC3B,uEAAuE;4BACvE,sEAAsE;4BACtE,OAAOO,uBACLZ,kBACAG,gBACAb,WACAW;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEA;IACJ;IAEA,qFAAqF;IACrF,OAAOH,QAAQC,OAAO,CAACC;AACzB;AAEA,SAASY,uBACPZ,gBAAwB,EACxBG,cAAmC,EACnCb,SAAoB,EACpBW,cAAwD;IAExD,MAAMM,eAAepB,aAAaqB,GAAG,CAACR;IACtC,IAAIO,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMM,sBAAsB;QAAE,GAAGb,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMS,UAAUX,QAAQC,OAAO,CAACc;IAChC1B,aAAawB,GAAG,CAACX,kBAAkBS;IAEnCK,OAAOC,IAAI,CAACf,kBAAkBgB,OAAO,CAAC,CAACC;QACrC,IAAIjC,oBAAoBsB,GAAG,CAACW,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAId,eAAeG,GAAG,CAACW,OAAO;gBAC5BH,OAAOI,cAAc,CAACL,qBAAqBI,MAAM;oBAC/CT;wBACE,MAAMW,aAAapC,6BACjB,uBACAkC;wBAEF,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAIhB,eAAeN,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;4BACrCjB,qBACEY,UAAUI,KAAK,EACfyB,YACAlB,eAAemB,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBzC,iCACEwC,YACA7B,WACAW;wBAEJ;oBACF;oBACAoB,YAAY;gBACd;YACF,OAAO;;gBACHZ,OAAe,CAACQ,KAAK,GAAGjB,gBAAgB,CAACiB,KAAK;YAClD;QACF;IACF;IAEA,OAAOR;AACT;AAEA;;;CAGC,GACD,OAAO,SAASa,aAAaC,SAAiB;IAC5C,MAAMC,UAAU,CAAC,6BAA6B,EAAED,UAAU,IAAI,CAAC;IAE/D,MAAMjC,YAAYV,iBAAiBW,QAAQ;IAC3C,IAAI,CAACD,WAAW;QACd,MAAM,qBAAqD,CAArD,IAAIb,eAAe,CAAC,qBAAqB,EAAE+C,SAAS,GAApD,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,MAAMhC,gBAAgBX,qBAAqBU,QAAQ;IACnD,IAAI,CAACC,eAAe;QAClB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,MAAM,EAAE8B,QAAQ,oDAAoD,CAAC,GAD1F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,cAAcxC,mBAAmBM,QAAQ;IAC/C,IAAIkC,aAAa;QACf,IAAIA,YAAYC,UAAU,EAAE;YAC1B,oDAAoD;YACpD,MAAM,qBAEL,CAFK,IAAIjC,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,MAAM,EAAE8B,QAAQ,2GAA2G,CAAC,GADjJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIC,YAAYE,QAAQ,IAAInC,cAAcoC,KAAK,KAAK,UAAU;YAC5D,mGAAmG;YACnG,mFAAmF;YACnF,oFAAoF;YACpF,yGAAyG;YACzG,MAAM,qBAEL,CAFK,IAAInC,MACR,GAAG+B,QAAQ,wIAAwI,CAAC,GADhJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,OAAQhC,cAAcG,IAAI;QACxB,KAAK;QACL,KAAK;YAAS;gBACZ,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,MAAM,EAAE8B,QAAQ,mIAAmI,CAAC,GADzK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAoB;gBACvB,OAAOK,gCACLN,WACAjC,WACAE,eACAgC;YAEJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACd;YACF;QACA;YAAS;gBACPhC;YACF;IACF;IACA,OAAOM,QAAQC,OAAO,CAACP,cAAcK,UAAU,CAAC0B,UAAU;AAC5D;AAEA,SAASM,gCACPN,SAAiB,EACjBjC,SAAoB,EACpBW,cAAoC,EACpCuB,OAAe;IAEf,OAAQvB,eAAeN,IAAI;QACzB,KAAK;YAAoB;gBACvB,MAAM,qBAEL,CAFK,IAAIlB,eACR,GAAG+C,QAAQ,0EAA0E,EAAEA,QAAQ,+EAA+E,CAAC,GAD3K,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL;IACF;IAEA,MAAMxB,mBAAmBC,eAAeJ,UAAU;IAElD,OAAQI,eAAeN,IAAI;QACzB,KAAK;YAAa;gBAChB,mCAAmC;gBACnC,+DAA+D;gBAC/D,IACEM,eAAeG,mBAAmB,IAClCH,eAAeG,mBAAmB,CAACE,GAAG,CAACiB,YACvC;oBACA,OAAOzC,mBACLmB,eAAeS,YAAY,EAC3BpB,UAAUI,KAAK,EACf8B;gBAEJ;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,mEAAmE;gBACnE,0FAA0F;gBAC1F,IACEvB,eAAeG,mBAAmB,IAClCH,eAAeG,mBAAmB,CAACE,GAAG,CAACiB,YACvC;oBACA,OAAOO,6BACLP,WACAjC,WACAW,gBACAuB;gBAEJ;gBACA;YACF;QACA,KAAK;YAAoB;gBAEvB;YACF;QACA;YAAS;gBACPvB;YACF;IACF;IAEA,uFAAuF;IACvF,OAAOH,QAAQC,OAAO,CAACC,gBAAgB,CAACuB,UAAU;AACpD;AAEA,yFAAyF,GACzF,eAAeO,6BACbP,SAAiB,EACjBjC,SAAoB,EACpBW,cAAwD,EACxDuB,OAAe;IAEf,MAAML,aAAapC,6BAA6ByC,SAASD;IACzD,8DAA8D;IAC9D,gFAAgF;IAChF,yFAAyF;IACzF,qGAAqG;IACrG,OAAQtB,eAAeN,IAAI;QACzB,KAAK;YAAiB;gBACpB,OAAOjB,qBACLY,UAAUI,KAAK,EACfyB,YACAlB,eAAemB,eAAe;YAElC;QACA,KAAK;YAAoB;gBACvB,OAAOzC,iCACLwC,YACA7B,WACAW;YAEJ;QACA;YAAS;gBACPA;YACF;IACF;AACF", "ignoreList": [0]}