{"version": 3, "sources": ["../../../src/client/components/segment-cache.ts"], "sourcesContent": ["/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\nexport type { NormalizedSearch } from './segment-cache-impl/cache-key'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/prefetch') as typeof import('./segment-cache-impl/prefetch')\n        ).prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/navigation') as typeof import('./segment-cache-impl/navigation')\n        ).navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/cache') as typeof import('./segment-cache-impl/cache')\n        ).revalidateEntireCache(...args)\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/cache') as typeof import('./segment-cache-impl/cache')\n        ).getCurrentCacheVersion(...args)\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/scheduler') as typeof import('./segment-cache-impl/scheduler')\n        ).schedulePrefetchTask(...args)\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/scheduler') as typeof import('./segment-cache-impl/scheduler')\n        ).cancelPrefetchTask(...args)\n      }\n    : notEnabled\n\nexport const reschedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').reschedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/scheduler') as typeof import('./segment-cache-impl/scheduler')\n        ).reschedulePrefetchTask(...args)\n      }\n    : notEnabled\n\nexport const isPrefetchTaskDirty: typeof import('./segment-cache-impl/scheduler').isPrefetchTaskDirty =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/scheduler') as typeof import('./segment-cache-impl/scheduler')\n        ).isPrefetchTaskDirty(...args)\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return (\n          require('./segment-cache-impl/cache-key') as typeof import('./segment-cache-impl/cache-key')\n        ).createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to the most recently hovered/touched link. Special network\n   * bandwidth is reserved for this task only. There's only ever one Intent-\n   * priority task at a time; when a new Intent task is scheduled, the previous\n   * one is bumped down to Default.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n\nexport const enum FetchStrategy {\n  // Deliberately ordered so we can easily compare two segments\n  // and determine if one segment is \"more specific\" than another\n  // (i.e. if it's likely that it contains more data)\n  LoadingBoundary = 0,\n  PPR = 1,\n  PPRRuntime = 2,\n  Full = 3,\n}\n\n/**\n * A subset of fetch strategies used for prefetch tasks.\n * A prefetch task can't know if it should use `PPR` or `LoadingBoundary`\n * until we complete the initial tree prefetch request, so we use `PPR` to signal both cases\n * and adjust it based on the route when actually fetching.\n * */\nexport type PrefetchTaskFetchStrategy =\n  | FetchStrategy.PPR\n  | FetchStrategy.PPRRuntime\n  | FetchStrategy.Full\n"], "names": ["notEnabled", "Error", "prefetch", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "args", "require", "navigate", "revalidateEntireCache", "getCurrentCacheVersion", "schedulePrefetchTask", "cancelPrefetchTask", "reschedulePrefetchTask", "isPrefetchTaskDirty", "createCacheKey", "NavigationResultTag", "PrefetchPriority", "FetchStrategy"], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC,GAMD,MAAMA,aAAkB;IACtB,MAAM,qBAEL,CAFK,IAAIC,MACR,uEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,OAAO,MAAMC,WACXC,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,iCACRL,QAAQ,IAAII;AAChB,IACAN,WAAU;AAEhB,OAAO,MAAMQ,WACXL,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,mCACRC,QAAQ,IAAIF;AAChB,IACAN,WAAU;AAEhB,OAAO,MAAMS,wBACXN,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,8BACRE,qBAAqB,IAAIH;AAC7B,IACAN,WAAU;AAEhB,OAAO,MAAMU,yBACXP,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,8BACRG,sBAAsB,IAAIJ;AAC9B,IACAN,WAAU;AAEhB,OAAO,MAAMW,uBACXR,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,kCACRI,oBAAoB,IAAIL;AAC5B,IACAN,WAAU;AAEhB,OAAO,MAAMY,qBACXT,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,kCACRK,kBAAkB,IAAIN;AAC1B,IACAN,WAAU;AAEhB,OAAO,MAAMa,yBACXV,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,kCACRM,sBAAsB,IAAIP;AAC9B,IACAN,WAAU;AAEhB,OAAO,MAAMc,sBACXX,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,kCACRO,mBAAmB,IAAIR;AAC3B,IACAN,WAAU;AAEhB,OAAO,MAAMe,iBACXZ,QAAQC,GAAG,CAACC,2BAA2B,GACnC;IAAU,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAO;;IACf,OAAO,AACLC,QAAQ,kCACRQ,cAAc,IAAIT;AACtB,IACAN,WAAU;AAEhB;;;CAGC,GAED,OAAO,IAAA,AAAWgB,6CAAAA;;;;;WAAAA;MAKjB;AAED;;CAEC,GACD,OAAO,IAAA,AAAWC,0CAAAA;IAChB;;;;;GAKC;IAED;;GAEC;IAED;;;GAGC;WAfeA;MAiBjB;AAED,OAAO,IAAA,AAAWC,uCAAAA;IAChB,6DAA6D;IAC7D,+DAA+D;IAC/D,mDAAmD;;;;;WAHnCA;MAQjB", "ignoreList": [0]}