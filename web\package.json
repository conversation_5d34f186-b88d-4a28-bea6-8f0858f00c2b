{"name": "web", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@chakra-ui/react": "^3.25.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "framer-motion": "^12.23.12", "next": "^15.5.2", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@types/node": "24.5.2", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "typescript": "^5.9.2"}}