'use client';

import { <PERSON><PERSON><PERSON>rovider, createSystem, defaultConfig } from '@chakra-ui/react';

// Tạo custom theme system với màu sắc và font đẹp
const customSystem = createSystem(defaultConfig, {
    theme: {
        tokens: {
            colors: {
                brand: {
                    50: { value: '#e8f4fd' },
                    100: { value: '#bee3f8' },
                    200: { value: '#90cdf4' },
                    300: { value: '#63b3ed' },
                    400: { value: '#4299e1' },
                    500: { value: '#3182ce' },
                    600: { value: '#2b77cb' },
                    700: { value: '#2c5aa0' },
                    800: { value: '#2a4365' },
                    900: { value: '#1a365d' },
                },
                gray: {
                    50: { value: '#f9fafb' },
                    100: { value: '#f3f4f6' },
                    200: { value: '#e5e7eb' },
                    300: { value: '#d1d5db' },
                    400: { value: '#9ca3af' },
                    500: { value: '#6b7280' },
                    600: { value: '#4b5563' },
                    700: { value: '#374151' },
                    800: { value: '#1f2937' },
                    900: { value: '#111827' },
                },
            },
            fonts: {
                body: {
                    value: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
                },
                heading: {
                    value: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
                },
                mono: {
                    value: 'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
                },
            },
            shadows: {
                sm: { value: '0 1px 2px 0 rgba(0, 0, 0, 0.05)' },
                md: { value: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' },
                lg: { value: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' },
                xl: { value: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' },
            },
        },
    },
});

interface ProvidersProps {
    children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
    return (
        <ChakraProvider value={customSystem}>
            {children}
        </ChakraProvider>
    );
}