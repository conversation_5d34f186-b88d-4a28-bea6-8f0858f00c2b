{"version": 3, "sources": ["../../../src/lib/framework/boundary-components.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from './boundary-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [ROOT_LAYOUT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n\nexport const RootLayoutBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[\n    ROOT_LAYOUT_BOUNDARY_NAME.slice(0) as typeof ROOT_LAYOUT_BOUNDARY_NAME\n  ]\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "NameSpace", "children", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary", "RootLayoutBoundary"], "mappings": "AAAA;AAGA,SACEA,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,QACpB,uBAAsB;AAE7B,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACJ,uBAAuB,EAAE,SAAU,EAAEK,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACJ,uBAAuB,EAAE,SAAU,EAAEI,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACH,qBAAqB,EAAE,SAAU,EAAEG,QAAQ,EAA2B;QACrE,OAAOA;IACT;IACA,CAACF,0BAA0B,EAAE,SAAU,EACrCE,QAAQ,EAGT;QACC,OAAOA;IACT;AACF;AAEA,OAAO,MAAMC,mBACX,gFAAgF;AAChF,4DAA4D;AAC5DF,SAAS,CAACJ,uBAAuBO,KAAK,CAAC,GAAoC,CAAA;AAE7E,OAAO,MAAMC,mBACX,gFAAgF;AAChF,4DAA4D;AAC5DJ,SAAS,CAACH,uBAAuBM,KAAK,CAAC,GAAoC,CAAA;AAE7E,OAAO,MAAME,iBACX,gFAAgF;AAChF,4DAA4D;AAC5DL,SAAS,CAACF,qBAAqBK,KAAK,CAAC,GAAkC,CAAA;AAEzE,OAAO,MAAMG,qBACX,gFAAgF;AAChF,4DAA4D;AAC5DN,SAAS,CACPD,0BAA0BI,KAAK,CAAC,GACjC,CAAA", "ignoreList": [0]}