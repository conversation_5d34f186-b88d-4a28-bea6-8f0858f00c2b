{"version": 3, "sources": ["../../../src/next-devtools/server/devtools-config-middleware.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { DevToolsConfig } from '../dev-overlay/shared'\n\nimport { existsSync } from 'fs'\nimport { readFile, writeFile, mkdir } from 'fs/promises'\nimport { dirname, join } from 'path'\n\nimport { middlewareResponse } from './middleware-response'\nimport { devToolsConfigSchema } from '../shared/devtools-config-schema'\nimport { deepMerge } from '../shared/deepmerge'\n\nconst DEVTOOLS_CONFIG_FILENAME = 'next-devtools-config.json'\nconst DEVTOOLS_CONFIG_MIDDLEWARE_ENDPOINT = '/__nextjs_devtools_config'\n\nexport function devToolsConfigMiddleware({\n  distDir,\n  sendUpdateSignal,\n}: {\n  distDir: string\n  sendUpdateSignal: (data: DevToolsConfig) => void\n}) {\n  const configPath = join(distDir, 'cache', DEVTOOLS_CONFIG_FILENAME)\n\n  return async function devToolsConfigMiddlewareHandler(\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname } = new URL(`http://n${req.url}`)\n\n    if (pathname !== DEVTOOLS_CONFIG_MIDDLEWARE_ENDPOINT) {\n      return next()\n    }\n\n    if (req.method !== 'POST') {\n      return middlewareResponse.methodNotAllowed(res)\n    }\n\n    const currentConfig = await getDevToolsConfig(distDir)\n\n    const chunks: Buffer[] = []\n    for await (const chunk of req) {\n      chunks.push(Buffer.from(chunk))\n    }\n\n    let body = Buffer.concat(chunks).toString('utf8')\n    try {\n      body = JSON.parse(body)\n    } catch (error) {\n      console.error('[Next.js DevTools] Invalid config body passed:', error)\n      return middlewareResponse.badRequest(res)\n    }\n\n    const validation = devToolsConfigSchema.safeParse(body)\n    if (!validation.success) {\n      console.error(\n        '[Next.js DevTools] Invalid config passed:',\n        validation.error.message\n      )\n      return middlewareResponse.badRequest(res)\n    }\n\n    const newConfig = deepMerge(currentConfig, validation.data)\n    await writeFile(configPath, JSON.stringify(newConfig, null, 2))\n\n    sendUpdateSignal(newConfig)\n\n    return middlewareResponse.noContent(res)\n  }\n}\n\nexport async function getDevToolsConfig(\n  distDir: string\n): Promise<DevToolsConfig> {\n  const configPath = join(distDir, 'cache', DEVTOOLS_CONFIG_FILENAME)\n\n  if (!existsSync(configPath)) {\n    await mkdir(dirname(configPath), { recursive: true })\n    await writeFile(configPath, JSON.stringify({}))\n    return {}\n  }\n\n  return JSON.parse(await readFile(configPath, 'utf8'))\n}\n"], "names": ["existsSync", "readFile", "writeFile", "mkdir", "dirname", "join", "middlewareResponse", "devToolsConfigSchema", "deepMerge", "DEVTOOLS_CONFIG_FILENAME", "DEVTOOLS_CONFIG_MIDDLEWARE_ENDPOINT", "devToolsConfigMiddleware", "distDir", "sendUpdateSignal", "config<PERSON><PERSON>", "devToolsConfigMiddlewareHandler", "req", "res", "next", "pathname", "URL", "url", "method", "methodNotAllowed", "currentConfig", "getDevToolsConfig", "chunks", "chunk", "push", "<PERSON><PERSON><PERSON>", "from", "body", "concat", "toString", "JSON", "parse", "error", "console", "badRequest", "validation", "safeParse", "success", "message", "newConfig", "data", "stringify", "noContent", "recursive"], "mappings": "AAGA,SAASA,UAAU,QAAQ,KAAI;AAC/B,SAASC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,QAAQ,cAAa;AACxD,SAASC,OAAO,EAAEC,IAAI,QAAQ,OAAM;AAEpC,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,oBAAoB,QAAQ,mCAAkC;AACvE,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,MAAMC,2BAA2B;AACjC,MAAMC,sCAAsC;AAE5C,OAAO,SAASC,yBAAyB,KAMxC;IANwC,IAAA,EACvCC,OAAO,EACPC,gBAAgB,EAIjB,GANwC;IAOvC,MAAMC,aAAaT,KAAKO,SAAS,SAASH;IAE1C,OAAO,eAAeM,gCACpBC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUJ,IAAIK,GAAG;QAE/C,IAAIF,aAAaT,qCAAqC;YACpD,OAAOQ;QACT;QAEA,IAAIF,IAAIM,MAAM,KAAK,QAAQ;YACzB,OAAOhB,mBAAmBiB,gBAAgB,CAACN;QAC7C;QAEA,MAAMO,gBAAgB,MAAMC,kBAAkBb;QAE9C,MAAMc,SAAmB,EAAE;QAC3B,WAAW,MAAMC,SAASX,IAAK;YAC7BU,OAAOE,IAAI,CAACC,OAAOC,IAAI,CAACH;QAC1B;QAEA,IAAII,OAAOF,OAAOG,MAAM,CAACN,QAAQO,QAAQ,CAAC;QAC1C,IAAI;YACFF,OAAOG,KAAKC,KAAK,CAACJ;QACpB,EAAE,OAAOK,OAAO;YACdC,QAAQD,KAAK,CAAC,kDAAkDA;YAChE,OAAO9B,mBAAmBgC,UAAU,CAACrB;QACvC;QAEA,MAAMsB,aAAahC,qBAAqBiC,SAAS,CAACT;QAClD,IAAI,CAACQ,WAAWE,OAAO,EAAE;YACvBJ,QAAQD,KAAK,CACX,6CACAG,WAAWH,KAAK,CAACM,OAAO;YAE1B,OAAOpC,mBAAmBgC,UAAU,CAACrB;QACvC;QAEA,MAAM0B,YAAYnC,UAAUgB,eAAee,WAAWK,IAAI;QAC1D,MAAM1C,UAAUY,YAAYoB,KAAKW,SAAS,CAACF,WAAW,MAAM;QAE5D9B,iBAAiB8B;QAEjB,OAAOrC,mBAAmBwC,SAAS,CAAC7B;IACtC;AACF;AAEA,OAAO,eAAeQ,kBACpBb,OAAe;IAEf,MAAME,aAAaT,KAAKO,SAAS,SAASH;IAE1C,IAAI,CAACT,WAAWc,aAAa;QAC3B,MAAMX,MAAMC,QAAQU,aAAa;YAAEiC,WAAW;QAAK;QACnD,MAAM7C,UAAUY,YAAYoB,KAAKW,SAAS,CAAC,CAAC;QAC5C,OAAO,CAAC;IACV;IAEA,OAAOX,KAAKC,KAAK,CAAC,MAAMlC,SAASa,YAAY;AAC/C", "ignoreList": [0]}