{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport type {\n  ServerComponentsHmrCache,\n  ResponseCacheBase,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { getBotType, isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getPreviouslyRevalidatedTags, getServerUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport {\n  getTracer,\n  isBubbledError,\n  SpanKind,\n  SpanStatusCode,\n} from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport { normalizeNextQueryParam } from './web/utils'\nimport {\n  HTML_CONTENT_TYPE_HEADER,\n  JSON_CONTENT_TYPE_HEADER,\n  MATCHED_PATH_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsPossibleServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { NextRequestHint } from './web/adapter'\nimport type { RouteModule } from './route-modules/route-module'\nimport { type FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc'\nimport { shouldServeStreamingMetadata } from './lib/streaming-metadata'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\nimport { getCacheHandlers } from './use-cache/handlers'\nimport { fixMojibake } from './lib/fix-mojibake'\nimport { computeCacheBustingSearchParam } from '../shared/lib/router/utils/cache-busting-search-param'\nimport { setCacheBustingSearchParamWithHash } from '../client/components/router-reducer/set-cache-busting-search-param'\nimport type { CacheControl } from './lib/cache-control'\nimport type { PrerenderedRoute } from '../build/static-paths/types'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  body: RenderResult\n  cacheControl?: CacheControl\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly segmentPrefetchRSC: SegmentPrefixRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n  private readonly isAppSegmentPrefetchEnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for cache components\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir = (require('path') as typeof import('path')).resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir = (require('path') as typeof import('path')).join(\n      this.dir,\n      this.nextConfig.distDir\n    )\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.isAppSegmentPrefetchEnabled =\n      this.enabledDirectories.app &&\n      this.nextConfig.experimental.clientSegmentCache === true\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      segmentPrefetchRSC:\n        this.isAppSegmentPrefetchEnabled && this.minimalMode\n          ? new SegmentPrefixRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n    process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n\n    this.renderOpts = {\n      dir: this.dir,\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      htmlLimitedBots: this.nextConfig.htmlLimitedBots,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        staleTimes: this.nextConfig.experimental.staleTimes,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        cacheComponents: this.nextConfig.experimental.cacheComponents ?? false,\n        clientSegmentCache:\n          this.nextConfig.experimental.clientSegmentCache === 'client-only'\n            ? 'client-only'\n            : Boolean(this.nextConfig.experimental.clientSegmentCache),\n        clientParamParsing:\n          this.nextConfig.experimental.clientParamParsing ?? false,\n        dynamicOnHover: this.nextConfig.experimental.dynamicOnHover ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n      devtoolSegmentExplorer:\n        this.nextConfig.experimental.devtoolSegmentExplorer,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.segmentPrefetchRSC?.match(parsedUrl.pathname)) {\n      const result = this.normalizers.segmentPrefetchRSC.extract(\n        parsedUrl.pathname\n      )\n      if (!result) return false\n\n      const { originalPathname, segmentPath } = result\n      parsedUrl.pathname = originalPathname\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] = segmentPath\n\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath)\n    } else if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n\n        const segmentPrefetchRSCRequest =\n          req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]\n        if (typeof segmentPrefetchRSCRequest === 'string') {\n          addRequestMeta(\n            req,\n            'segmentPrefetchRSCRequest',\n            segmentPrefetchRSCRequest\n          )\n        }\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = await this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (getRequestMeta(req, 'middlewareInvoke')) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        addRequestMeta(req, 'locale', localePathResult.detectedLocale)\n        addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          removeRequestMeta(req, 'localeInferredFromDefault')\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      addRequestMeta(req, 'isNextDataReq', true)\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            if (res.statusCode && res.statusCode >= 500) {\n              // For 5xx status codes: SHOULD be set to 'Error' span status.\n              // x-ref: https://opentelemetry.io/docs/specs/semconv/http/http-spans/#status\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n              })\n              // For span status 'Error', SHOULD set 'error.type' attribute.\n              span.setAttribute('error.type', res.statusCode.toString())\n            }\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            fixMojibake(req.headers[MATCHED_PATH_HEADER] as string),\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          matchedPath = denormalizePagePath(matchedPath)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale)\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              addRequestMeta(req, 'localeInferredFromDefault', true)\n            } else {\n              removeRequestMeta(req, 'localeInferredFromDefault')\n            }\n          }\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n          let paramsResult: {\n            params: ParsedUrlQuery | false\n            hasValidParams: boolean\n          } = {\n            params: false,\n            hasValidParams: false,\n          }\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n\n              // The page is dynamic if the params are defined. We know at this\n              // stage that the matched path is not a static page if the params\n              // were parsed from the matched path header.\n              if (typeof match.params !== 'undefined') {\n                pageIsDynamic = true\n                paramsResult.params = match.params\n                paramsResult.hasValidParams = true\n              }\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getServerUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          // Store a copy of `parsedUrl.query` before calling handleRewrites.\n          // Since `handleRewrites` might add new queries to `parsedUrl.query`.\n          const originQueryParams = { ...parsedUrl.query }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParamKeys = Object.keys(\n            utils.handleRewrites(req, parsedUrl)\n          )\n\n          // Create a copy of the query params to avoid mutating the original\n          // object. This prevents any overlapping query params that have the\n          // same normalized key from causing issues.\n          const queryParams = { ...parsedUrl.query }\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n\n          const routeParamKeys = new Set<string>()\n          for (const [key, value] of Object.entries(parsedUrl.query)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            // Remove the prefixed key from the query params because we want\n            // to consume it for the dynamic route matcher.\n            delete parsedUrl.query[key]\n            routeParamKeys.add(normalizedKey)\n\n            if (typeof value === 'undefined') continue\n\n            queryParams[normalizedKey] = Array.isArray(value)\n              ? value.map((v) => decodeQueryPathParameter(v))\n              : decodeQueryPathParameter(value)\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            // If we don't already have valid params, try to parse them from\n            // the query params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                false\n              )\n            }\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams, false)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult = utils.normalizeDynamicRouteParams(\n                  matcherParams,\n                  false\n                )\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            const routeMatchesHeader = req.headers['x-now-route-matches']\n            if (\n              typeof routeMatchesHeader === 'string' &&\n              routeMatchesHeader &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const routeMatches =\n                utils.getParamsFromRouteMatches(routeMatchesHeader)\n\n              if (routeMatches) {\n                paramsResult = utils.normalizeDynamicRouteParams(\n                  routeMatches,\n                  true\n                )\n\n                if (paramsResult.hasValidParams) {\n                  params = paramsResult.params\n                }\n              }\n            }\n\n            // Try to parse the params from the query if we couldn't parse them\n            // from the route matches but ignore missing optional params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // If the pathname being requested is the same as the source\n            // pathname, and we don't have valid params, we want to use the\n            // default route matches.\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // If the route matches header is an empty string, we want to\n              // render a fallback shell. This is because we know this came from\n              // a prerender (it has the header) but it's values were filtered\n              // out (because the allowQuery was empty). If it was undefined\n              // then we know that the request is hitting the lambda directly.\n              if (routeMatchesHeader === '') {\n                addRequestMeta(req, 'renderFallbackShell', true)\n              }\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n\n              // If the request is for a segment prefetch, we need to update the\n              // segment prefetch request path to include the interpolated\n              // params.\n              let segmentPrefetchRSCRequest = getRequestMeta(\n                req,\n                'segmentPrefetchRSCRequest'\n              )\n              if (\n                segmentPrefetchRSCRequest &&\n                isDynamicRoute(segmentPrefetchRSCRequest, false)\n              ) {\n                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(\n                  segmentPrefetchRSCRequest,\n                  params\n                )\n\n                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] =\n                  segmentPrefetchRSCRequest\n                addRequestMeta(\n                  req,\n                  'segmentPrefetchRSCRequest',\n                  segmentPrefetchRSCRequest\n                )\n              }\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeCdnUrl(req, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          // Remove the route `params` keys from `parsedUrl.query` if they are\n          // not in the original query params.\n          // If it's used in both route `params` and query `searchParams`, it should be kept.\n          for (const key of routeParamKeys) {\n            if (!(key in originQueryParams)) {\n              delete parsedUrl.query[key]\n            }\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !getRequestMeta(req, 'locale')) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          addRequestMeta(req, 'locale', pathnameInfo.locale)\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          addRequestMeta(req, 'localeInferredFromDefault', true)\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n        })\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        // This is needed for pages router to leverage unstable_cache\n        // TODO: re-work this handling to not use global and use a AsyncStore\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      const cacheHandlers = getCacheHandlers()\n\n      if (cacheHandlers) {\n        await Promise.all(\n          [...cacheHandlers].map(async (cacheHandler) => {\n            if ('refreshTags' in cacheHandler) {\n              // Note: cacheHandler.refreshTags() is called lazily before the\n              // first cache entry is retrieved. It allows us to skip the\n              // refresh request if no caches are read at all.\n            } else {\n              const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n                req.headers,\n                this.getPrerenderManifest().preview.previewModeId\n              )\n\n              await cacheHandler.receiveExpiredTags(\n                ...previouslyRevalidatedTags\n              )\n            }\n          })\n        )\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath = !useMatchedPathHeader && invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          addRequestMeta(req, 'locale', invokePathnameInfo.locale)\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales\n        )\n\n        if (normalizeResult.detectedLocale) {\n          addRequestMeta(req, 'locale', normalizeResult.detectedLocale)\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          delete parsedUrl.query[key]\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (getRequestMeta(req, 'middlewareInvoke')) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the segment prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.segmentPrefetchRSC) {\n      normalizers.push(this.normalizers.segmentPrefetchRSC)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.nextConfig.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    // Get instrumentation module\n    if (!this.instrumentation) {\n      this.instrumentation = await this.loadInstrumentationModule()\n    }\n    if (this.preparedPromise === null) {\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const ua = partialContext.req.headers['user-agent'] || ''\n\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        // `renderOpts.botType` is accumulated in `this.renderImpl()`\n        supportsDynamicResponse: !this.renderOpts.botType,\n        serveStreamingMetadata: shouldServeStreamingMetadata(\n          ua,\n          this.nextConfig.htmlLimitedBots\n        ),\n      },\n    }\n\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body } = payload\n    let { cacheControl } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        cacheControl = undefined\n      }\n\n      if (cacheControl && cacheControl.expire === undefined) {\n        cacheControl.expire = this.nextConfig.expireTime\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        generateEtags,\n        poweredByHeader,\n        cacheControl,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.serverOptions.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    const ua = req.headers['user-agent'] || ''\n    this.renderOpts.botType = getBotType(ua)\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !getRequestMeta(req, 'isNextDataReq') &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    urlPathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    prerenderedRoutes?: PrerenderedRoute[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.appendHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    const isPossibleServerAction = getIsPossibleServerAction(req)\n    let isSSG = !!components.getStaticProps\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // Not all CDNs respect the Vary header when caching. We must assume that\n    // only the URL is used to vary the responses. The Next client computes a\n    // hash of the header values and sends it as a search param. Before\n    // responding to a request, we must verify that the hash matches the\n    // expected value. Neglecting to do this properly can lead to cache\n    // poisoning attacks on certain CDNs.\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders &&\n      isRSCRequest\n    ) {\n      const headers = req.headers\n\n      const prefetchHeaderValue = headers[NEXT_ROUTER_PREFETCH_HEADER]\n      const routerPrefetch =\n        prefetchHeaderValue !== undefined\n          ? // We only recognize '1' and '2'. Strip all other values here.\n            prefetchHeaderValue === '1' || prefetchHeaderValue === '2'\n            ? prefetchHeaderValue\n            : undefined\n          : // For runtime prefetches, we always perform a dynamic request,\n            // so we don't expect the header to be stripped by an intermediate layer.\n            // This should only happen for static prefetches, so we only handle those here.\n            getRequestMeta(req, 'isPrefetchRSCRequest')\n            ? '1'\n            : undefined\n\n      const segmentPrefetchRSCRequest =\n        headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] ||\n        getRequestMeta(req, 'segmentPrefetchRSCRequest')\n\n      const expectedHash = computeCacheBustingSearchParam(\n        routerPrefetch,\n        segmentPrefetchRSCRequest,\n        headers[NEXT_ROUTER_STATE_TREE_HEADER],\n        headers[NEXT_URL]\n      )\n      const actualHash =\n        getRequestMeta(req, 'cacheBustingSearchParam') ??\n        new URL(req.url || '', 'http://localhost').searchParams.get(\n          NEXT_RSC_UNION_QUERY\n        )\n\n      if (expectedHash !== actualHash) {\n        // The hash sent by the client does not match the expected value.\n        // Redirect to the URL with the correct cache-busting search param.\n        // This prevents cache poisoning attacks on CDNs that don't respect Vary headers.\n        // Note: When no headers are present, expectedHash is empty string and client\n        // must send `_rsc` param, otherwise actualHash is null and hash check fails.\n        const url = new URL(req.url || '', 'http://localhost')\n        setCacheBustingSearchParamWithHash(url, expectedHash)\n        res.statusCode = 307\n        res.setHeader('location', `${url.pathname}${url.search}`)\n        res.body('').send()\n        return null\n      }\n    }\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let hasFallback = false\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        getRequestMeta(req, 'isNextDataReq') ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    const locale = getRequestMeta(req, 'locale')\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isPossibleServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      res.body('Method Not Allowed').send()\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        body: RenderResult.fromStatic(\n          components.Component,\n          HTML_CONTENT_TYPE_HEADER\n        ),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const ua = req.headers['user-agent'] || ''\n      const isBotRequest = isBot(ua)\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      await this.getIncrementalCache({\n        requestHeaders: Object.assign({}, req.headers),\n      })\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    if (\n      routeModule?.isDev &&\n      isDynamicRoute(pathname) &&\n      (components.getStaticPaths || isAppPath)\n    ) {\n      const pathsResults = await this.getStaticPaths({\n        pathname,\n        urlPathname,\n        requestHeaders: req.headers,\n        page: components.page,\n        isAppPath,\n      })\n      if (isAppPath && this.nextConfig.experimental.cacheComponents) {\n        if (pathsResults.prerenderedRoutes?.length) {\n          let smallestFallbackRouteParams = null\n          for (const route of pathsResults.prerenderedRoutes) {\n            const fallbackRouteParams = route.fallbackRouteParams\n            if (!fallbackRouteParams || fallbackRouteParams.length === 0) {\n              // There are no fallback route params so we don't need to continue\n              smallestFallbackRouteParams = null\n              break\n            }\n            if (\n              smallestFallbackRouteParams === null ||\n              fallbackRouteParams.length < smallestFallbackRouteParams.length\n            ) {\n              smallestFallbackRouteParams = fallbackRouteParams\n            }\n          }\n          if (smallestFallbackRouteParams) {\n            const devValidatingFallbackParams = new Map<string, string>(\n              smallestFallbackRouteParams.map((v) => [v, ''])\n            )\n            addRequestMeta(\n              req,\n              'devValidatingFallbackParams',\n              devValidatingFallbackParams\n            )\n          }\n        }\n      }\n    }\n\n    // An OPTIONS request to a page handler is invalid.\n    if (\n      req.method === 'OPTIONS' &&\n      !is404Page &&\n      (!routeModule || !isAppRouteRouteModule(routeModule))\n    ) {\n      await sendResponse(req, res, new Response(null, { status: 400 }))\n      return null\n    }\n\n    const request = isNodeNextRequest(req) ? req.originalRequest : req\n    const response = isNodeNextResponse(res) ? res.originalResponse : res\n\n    const parsedInitUrl = parseUrl(getRequestMeta(req, 'initURL') || req.url)\n    let initPathname = parsedInitUrl.pathname || '/'\n\n    for (const normalizer of [\n      this.normalizers.segmentPrefetchRSC,\n      this.normalizers.prefetchRSC,\n      this.normalizers.rsc,\n    ]) {\n      if (normalizer?.match(initPathname)) {\n        initPathname = normalizer.normalize(initPathname)\n      }\n    }\n\n    // On minimal mode, the request url of dynamic route can be a\n    // literal dynamic route ('/[slug]') instead of actual URL, so overwriting to initPathname\n    // will transform back the resolved url to the dynamic route pathname.\n    if (!(this.minimalMode && isErrorPathname)) {\n      request.url = `${initPathname}${parsedInitUrl.search || ''}`\n    }\n\n    // propagate the request context for dev\n    setRequestMeta(request, getRequestMeta(req))\n    addRequestMeta(request, 'distDir', this.distDir)\n    addRequestMeta(request, 'query', query)\n    addRequestMeta(request, 'params', opts.params)\n    addRequestMeta(request, 'ampValidator', this.renderOpts.ampValidator)\n    addRequestMeta(request, 'minimalMode', this.minimalMode)\n\n    if (opts.err) {\n      addRequestMeta(request, 'invokeError', opts.err)\n    }\n\n    const handler: (\n      req: ServerRequest | IncomingMessage,\n      res: ServerResponse | HTTPServerResponse,\n      ctx: {\n        waitUntil: ReturnType<Server['getWaitUntil']>\n      }\n    ) => Promise<void> = components.ComponentMod.handler\n\n    const maybeDevRequest =\n      // we need to capture fetch metrics when they are set\n      // and can't wait for handler to resolve as the fetch\n      // metrics are logged on response close which happens\n      // before handler resolves\n      process.env.NODE_ENV === 'development'\n        ? new Proxy(request, {\n            get(target: any, prop) {\n              if (typeof target[prop] === 'function') {\n                return target[prop].bind(target)\n              }\n              return target[prop]\n            },\n            set(target: any, prop, value) {\n              if (prop === 'fetchMetrics') {\n                ;(req as any).fetchMetrics = value\n              }\n              target[prop] = value\n              return true\n            },\n          })\n        : request\n\n    await handler(maybeDevRequest, response, {\n      waitUntil: this.getWaitUntil(),\n    })\n\n    // response is handled fully in handler\n    return null\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      locale: getRequestMeta(ctx.req, 'locale'),\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): Promise<MiddlewareRoutingItem | undefined>\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { req, res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback =\n      getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false\n\n    if (\n      !this.minimalMode &&\n      this.nextConfig.experimental.validateRSCRequestHeaders\n    ) {\n      addRequestMeta(\n        ctx.req,\n        'cacheBustingSearchParam',\n        query[NEXT_RSC_UNION_QUERY]\n      )\n    }\n    delete query[NEXT_RSC_UNION_QUERY]\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromRequest(req, pathname),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        addRequestMeta(ctx.req, 'customErrorRender', true)\n        await this.renderErrorToResponse(ctx, err)\n        removeRequestMeta(ctx.req, 'customErrorRender')\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (this.minimalMode || this.renderOpts.dev) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    const middleware = await this.getMiddleware()\n    if (\n      middleware &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      const locale = getRequestMeta(req, 'locale')\n\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('Content-Type', JSON_CONTENT_TYPE_HEADER)\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        body: RenderResult.EMPTY,\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !getRequestMeta(ctx.req, 'customErrorRender') &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          locale: getRequestMeta(ctx.req, 'locale'),\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`,\n              HTML_CONTENT_TYPE_HEADER\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        body: RenderResult.fromStatic('Internal Server Error', 'text/plain'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    // Ensure the locales are provided on the request meta.\n    if (this.nextConfig.i18n) {\n      if (!getRequestMeta(req, 'locale')) {\n        addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale)\n      }\n      addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale)\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["WrappedBuildError", "Server", "Error", "constructor", "innerError", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "segmentPrefetchRSC", "match", "result", "extract", "originalPathname", "segmentPath", "headers", "RSC_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "addRequestMeta", "prefetchRSC", "normalize", "rsc", "stripFlightHeaders", "segmentPrefetchRSCRequest", "url", "parsed", "parseUrl", "formatUrl", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "matchNextDataPathname", "path", "buildId", "getRequestMeta", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "getRouteFromAssetPath", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "toLowerCase", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "removeRequestMeta", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "execOnce", "Log", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "formatHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "I18NProvider", "localeNormalizer", "LocaleRouteNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "process", "env", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "checkIsAppPPREnabled", "ppr", "isAppSegmentPrefetchEnabled", "clientSegmentCache", "RSCPathnameNormalizer", "PrefetchRSCPathnameNormalizer", "SegmentPrefixRSCPathnameNormalizer", "NextDataPathnameNormalizer", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "htmlLimitedBots", "expireTime", "staleTimes", "clientTraceMetadata", "cacheComponents", "Boolean", "clientParamParsing", "dynamicOnHover", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "devtoolSegmentExplorer", "setConfig", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "manifest<PERSON><PERSON>der", "ServerManifestLoader", "name", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "DefaultRouteMatcherManager", "PagesRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "NextRequestHint", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "getTracer", "withPropagatedContext", "trace", "BaseServerSpan", "spanName", "kind", "SpanKind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "setStatus", "code", "SpanStatusCode", "ERROR", "setAttribute", "toString", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "patchSetHeaderWithCookieSupport", "isNodeNextResponse", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "normalizeRepeatedSlashes", "redirect", "body", "send", "query", "URLSearchParams", "isNodeNextRequest", "xForwardedProto", "isHttps", "socket", "encrypted", "remoteAddress", "attachRequestMeta", "getHostname", "parseUrlUtil", "replace", "pathnameInfo", "getNextPathnameInfo", "removePathPrefix", "useMatchedPathHeader", "MATCHED_PATH_HEADER", "<PERSON><PERSON><PERSON>", "URL", "fixMojibake", "urlPathname", "NEXT_RESUME_HEADER", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "denormalizePagePath", "localeAnalysisResult", "inferredFromDefault", "srcPathname", "pageIsDynamic", "isDynamicRoute", "paramsResult", "hasValidParams", "definition", "utils", "getServerUtils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "originQueryParams", "pathnameBeforeRewrite", "rewriteParamKeys", "handleRewrites", "queryParams", "didRewrite", "routeParamKeys", "Set", "key", "value", "normalizedKey", "normalizeNextQueryParam", "add", "Array", "isArray", "map", "v", "decodeQueryPathParameter", "normalizeDynamicRouteParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "routeMatchesHeader", "routeMatches", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeCdnUrl", "defaultRouteRegex", "groups", "DecodeError", "NormalizeError", "renderError", "webServerConfig", "incrementalCache", "getIncrementalCache", "requestHeaders", "resetRequestCache", "__incrementalCache", "cacheHandlers", "getCacheHandlers", "Promise", "all", "cache<PERSON><PERSON><PERSON>", "previouslyRevalidatedTags", "getPreviouslyRevalidatedTags", "previewModeId", "receiveExpiredTags", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "normalizeLocalePath", "response", "Response", "bubble", "run", "NoFallbackError", "isBubbledError", "getProperError", "getRequestHandlerWithMetadata", "meta", "handler", "getRequestHandler", "setRequestMeta", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "normalizeAppPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "ua", "botType", "serveStreamingMetadata", "shouldServeStreamingMetadata", "payload", "originalStatus", "cacheControl", "sent", "<PERSON><PERSON><PERSON><PERSON>", "expire", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "getBuiltinRequestContext", "waitUntil", "getInternalWaitUntil", "startsWith", "customServer", "hasPage", "getBotType", "isBlockedPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "parseFallbackField", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "isInterceptionRouteAppPath", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "NEXT_ROUTER_STATE_TREE_HEADER", "addedNextUrlToVary", "append<PERSON><PERSON>er", "NEXT_URL", "opts", "components", "prerenderManifest", "UNDERSCORE_NOT_FOUND_ROUTE", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "isPossibleServerAction", "getIsPossibleServerAction", "isSSG", "getStaticProps", "validateRSCRequestHeaders", "prefetchHeaderValue", "routerPrefetch", "expectedHash", "computeCacheBustingSearchParam", "actualHash", "searchParams", "NEXT_RSC_UNION_QUERY", "setCacheBustingSearchParamWithHash", "search", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "includes", "routes", "toRoute", "isNextDataRequest", "routeModule", "couldSupportPPR", "isAppPageRouteModule", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "isRoutePPREnabled", "renderingMode", "minimalPostponed", "STATIC_STATUS_PAGES", "parseInt", "slice", "Component", "RenderResult", "fromStatic", "HTML_CONTENT_TYPE_HEADER", "isBotRequest", "isBot", "isSupportedDocument", "Document", "getInitialProps", "NEXT_BUILTIN_DOCUMENT", "removeTrailingSlash", "isDev", "pathsResults", "prerenderedRoutes", "smallestFallbackRouteParams", "fallbackRouteParams", "devValidatingFallbackParams", "Map", "isAppRouteRouteModule", "sendResponse", "status", "request", "parsedInitUrl", "initPathname", "ampValidator", "ComponentMod", "maybeDevRequest", "NODE_ENV", "Proxy", "target", "prop", "set", "fetchMetrics", "stripLocale", "splitPath", "indexOf", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "fromRequest", "matchAll", "invokeOutput", "MissingStaticPage", "JSON", "stringify", "initUrl", "rewroteUrl", "renderErrorToResponse", "isWrappedError", "isError", "JSON_CONTENT_TYPE_HEADER", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "EMPTY", "is404", "using404Page", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "statusPage", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": ";;;;;;;;;;;;;;;IAyRaA,iBAAiB;eAAjBA;;IAmBb,OA2mFC;eA3mF6BC;;;uBAlRvB;qBAqBgD;gCACxB;2BAQxB;wBACwB;uCACL;wBAEI;uBACI;qEACT;qCACW;qCACA;6DACf;6BACwC;iEACrB;6BAMjC;kCAC0B;0BACA;6BACL;0BACa;qCACL;kCAQ7B;uCAK+B;4CACK;6CACC;8CACC;8CACA;2CACH;sCACL;wBAM9B;4BACwB;8BACF;8BACA;wBACW;4BAMjC;qCAC6B;uCACE;8EACJ;qBACI;oCACH;wBAI5B;6BACuC;0BACH;yCACD;oCACC;yBACnB;yBAE8B;gCACN;qBACX;uCAI9B;yBACyB;0BAEsB;kCACH;mCACN;0CACJ;yCACT;0BACC;6BACL;yCACmB;4CACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuI5C,MAAMD,0BAA0BE;IAGrCC,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAYe,MAAeH;IAgGlBI,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAsBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YAoCrB,uBAoEE,mCAQL;aAgEXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,sCAiBO,+BAWA;YA9BX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,uCAAA,IAAI,CAACC,WAAW,CAACC,kBAAkB,qBAAnC,qCAAqCC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClE,MAAMI,SAAS,IAAI,CAACH,WAAW,CAACC,kBAAkB,CAACG,OAAO,CACxDN,UAAUC,QAAQ;gBAEpB,IAAI,CAACI,QAAQ,OAAO;gBAEpB,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGH;gBAC1CL,UAAUC,QAAQ,GAAGM;gBAErB,iDAAiD;gBACjDT,IAAIW,OAAO,CAACC,4BAAU,CAAC,GAAG;gBAC1BZ,IAAIW,OAAO,CAACE,6CAA2B,CAAC,GAAG;gBAC3Cb,IAAIW,OAAO,CAACG,qDAAmC,CAAC,GAAGJ;gBAEnDK,IAAAA,2BAAc,EAACf,KAAK,gBAAgB;gBACpCe,IAAAA,2BAAc,EAACf,KAAK,wBAAwB;gBAC5Ce,IAAAA,2BAAc,EAACf,KAAK,6BAA6BU;YACnD,OAAO,KAAI,gCAAA,IAAI,CAACN,WAAW,CAACY,WAAW,qBAA5B,8BAA8BV,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClED,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACY,WAAW,CAACC,SAAS,CACzDf,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIW,OAAO,CAACC,4BAAU,CAAC,GAAG;gBAC1BZ,IAAIW,OAAO,CAACE,6CAA2B,CAAC,GAAG;gBAC3CE,IAAAA,2BAAc,EAACf,KAAK,gBAAgB;gBACpCe,IAAAA,2BAAc,EAACf,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACc,GAAG,qBAApB,sBAAsBZ,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACc,GAAG,CAACD,SAAS,CACjDf,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIW,OAAO,CAACC,4BAAU,CAAC,GAAG;gBAC1BG,IAAAA,2BAAc,EAACf,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIW,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvCQ,IAAAA,sCAAkB,EAACnB,IAAIW,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIX,IAAIW,OAAO,CAACC,4BAAU,CAAC,KAAK,KAAK;gBAC1CG,IAAAA,2BAAc,EAACf,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIW,OAAO,CAACE,6CAA2B,CAAC,KAAK,KAAK;oBACpDE,IAAAA,2BAAc,EAACf,KAAK,wBAAwB;oBAE5C,MAAMoB,4BACJpB,IAAIW,OAAO,CAACG,qDAAmC,CAAC;oBAClD,IAAI,OAAOM,8BAA8B,UAAU;wBACjDL,IAAAA,2BAAc,EACZf,KACA,6BACAoB;oBAEJ;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIpB,IAAIqB,GAAG,EAAE;gBACX,MAAMC,SAASC,IAAAA,UAAQ,EAACvB,IAAIqB,GAAG;gBAC/BC,OAAOnB,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIqB,GAAG,GAAGG,IAAAA,WAAS,EAACF;YACtB;YAEA,OAAO;QACT;aAEQG,wBACN,OAAOzB,KAAK0B,KAAKxB;YACf,MAAMyB,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,MAAMC,SAASC,IAAAA,4CAAqB,EAAC5B,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAAC0B,UAAU,CAACA,OAAOE,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAIF,OAAOE,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IAAIC,IAAAA,2BAAc,EAACjC,KAAK,qBAAqB;oBAC3C,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAACkC,SAAS,CAAClC,KAAK0B,KAAKxB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1B2B,OAAOE,IAAI,CAACI,KAAK;YAEjB,MAAMC,YAAYP,OAAOE,IAAI,CAACF,OAAOE,IAAI,CAACM,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAAClC,KAAK0B,KAAKxB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAE0B,OAAOE,IAAI,CAACQ,IAAI,CAAC,MAAM;YAC1CpC,WAAWqC,IAAAA,8BAAqB,EAACrC,UAAU;YAE3C,iDAAiD;YACjD,IAAIwB,YAAY;gBACd,IAAI,IAAI,CAACnC,UAAU,CAACiD,aAAa,IAAI,CAACtC,SAASmC,QAAQ,CAAC,MAAM;oBAC5DnC,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAACiD,aAAa,IAC9BtC,SAASkC,MAAM,GAAG,KAClBlC,SAASmC,QAAQ,CAAC,MAClB;oBACAnC,WAAWA,SAASuC,SAAS,CAAC,GAAGvC,SAASkC,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACM,YAAY,EAAE;oBAEJ3C;gBADjB,gDAAgD;gBAChD,MAAM4C,WAAW5C,wBAAAA,oBAAAA,IAAKW,OAAO,CAACkC,IAAI,qBAAjB7C,kBAAmB8C,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACC,WAAW;gBAEhE,MAAMC,eAAe,IAAI,CAACL,YAAY,CAACM,kBAAkB,CAACL;gBAC1D,MAAMM,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACP,YAAY,CAACQ,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACT,YAAY,CAACU,OAAO,CAAClD;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAIiD,iBAAiBE,cAAc,EAAE;oBACnCnD,WAAWiD,iBAAiBjD,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChEY,IAAAA,2BAAc,EAACf,KAAK,UAAUoD,iBAAiBE,cAAc;gBAC7DvC,IAAAA,2BAAc,EAACf,KAAK,iBAAiBkD;gBAErC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpCC,IAAAA,8BAAiB,EAACvD,KAAK;gBACzB;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAACoD,iBAAiBE,cAAc,IAAI,CAAC3B,YAAY;oBACnDZ,IAAAA,2BAAc,EAACf,KAAK,UAAUkD;oBAC9B,MAAM,IAAI,CAAChB,SAAS,CAAClC,KAAK0B,KAAKxB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrBY,IAAAA,2BAAc,EAACf,KAAK,iBAAiB;YAErC,OAAO;QACT;aAEQwD,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QA4xBV;;;;;;GAMC,QACOzC,YAAY,CAACd;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAACuD,IAAI,EAAE;gBACzBvD,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACuD,IAAI;YACxC;YAEA,2EAA2E;YAC3E,qEAAqE;YACrE,IAAI,IAAI,CAACvD,WAAW,CAACC,kBAAkB,EAAE;gBACvCD,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACC,kBAAkB;YACtD;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACD,WAAW,CAACY,WAAW,EAAE;gBAChCZ,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACY,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACZ,WAAW,CAACc,GAAG,EAAE;gBACxBd,YAAYwD,IAAI,CAAC,IAAI,CAACxD,WAAW,CAACc,GAAG;YACvC;YAEA,KAAK,MAAM2C,cAAczD,YAAa;gBACpC,IAAI,CAACyD,WAAWvD,KAAK,CAACH,WAAW;gBAEjC,OAAO0D,WAAW5C,SAAS,CAACd,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQ2D,6BAGJ,OAAO9D,KAAK0B,KAAKL;YACnB,IAAI0C,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAACxD,KAAK0B,KAAKL;YAC3D,IAAI0C,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAACtC,qBAAqB,CAACzB,KAAK0B,KAAKL;gBACtD,IAAI0C,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAgCUG,WAAoB;aACpBC,kBAAwC;aAohC1CC,uBAAuBC,IAAAA,eAAQ,EAAC;YACtCC,KAAIC,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QAnvEE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnBhC,QAAQ,EACRiC,IAAI,EACJC,qBAAqB,EACtB,GAAGhF;QAEJ,IAAI,CAACgF,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGjF;QAErB,IAAI,CAAC0E,GAAG,GAAG,AAACQ,QAAQ,QAAkCC,OAAO,CAACT;QAE9D,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACS,aAAa,CAAC;YAAEP;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACnF,UAAU,GAAGkF;QAClB,IAAI,CAAC9B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACuC,aAAa,GAAGC,IAAAA,8BAAc,EAAC,IAAI,CAACxC,QAAQ;QACnD;QACA,IAAI,CAACiC,IAAI,GAAGA;QACZ,IAAI,CAACQ,OAAO,GAAG,AAACL,QAAQ,QAAkCzC,IAAI,CAC5D,IAAI,CAACiC,GAAG,EACR,IAAI,CAAChF,UAAU,CAAC6F,OAAO;QAEzB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACZ,eAAe,IAAI,CAACa,eAAe;QAExD,IAAI,CAAC9C,YAAY,GAAG,EAAA,wBAAA,IAAI,CAACnD,UAAU,CAACkG,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAIC,0BAAY,CAAC,IAAI,CAACpG,UAAU,CAACkG,IAAI,IACrC7F;QAEJ,yEAAyE;QACzE,IAAI,CAACgG,gBAAgB,GAAG,IAAI,CAAClD,YAAY,GACrC,IAAImD,4CAAqB,CAAC,IAAI,CAACnD,YAAY,IAC3C9C;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJkG,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC1G,UAAU;QAEnB,IAAI,CAACwC,OAAO,GAAG,IAAI,CAACmE,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBxB,eAAe,CAAC,CAACyB,QAAQC,GAAG,CAACC,yBAAyB;QAExD,IAAI,CAACvC,kBAAkB,GAAG,IAAI,CAACwC,qBAAqB,CAAC7B;QAErD,IAAI,CAAC8B,eAAe,GAClB,IAAI,CAACzC,kBAAkB,CAAC0C,GAAG,IAC3BC,IAAAA,yBAAoB,EAAC,IAAI,CAACnH,UAAU,CAACC,YAAY,CAACmH,GAAG;QAEvD,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAAC7C,kBAAkB,CAAC0C,GAAG,IAC3B,IAAI,CAAClH,UAAU,CAACC,YAAY,CAACqH,kBAAkB,KAAK;QAEtD,IAAI,CAAC1G,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCc,KACE,IAAI,CAAC8C,kBAAkB,CAAC0C,GAAG,IAAI,IAAI,CAAC9B,WAAW,GAC3C,IAAImC,0BAAqB,KACzBlH;YACNmB,aACE,IAAI,CAACyF,eAAe,IAAI,IAAI,CAAC7B,WAAW,GACpC,IAAIoC,0CAA6B,KACjCnH;YACNQ,oBACE,IAAI,CAACwG,2BAA2B,IAAI,IAAI,CAACjC,WAAW,GAChD,IAAIqC,oDAAkC,KACtCpH;YACN8D,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAIiD,oCAA0B,CAAC,IAAI,CAAClF,OAAO,IAC3CnC;QACN;QAEA,IAAI,CAACsH,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChDf,QAAQC,GAAG,CAACe,kBAAkB,GAAG,IAAI,CAAC7H,UAAU,CAAC8H,YAAY,IAAI;QAEjE,IAAI,CAACC,UAAU,GAAG;YAChB/C,KAAK,IAAI,CAACA,GAAG;YACbgD,yBAAyB;YACzB/E,eAAe,IAAI,CAACjD,UAAU,CAACiD,aAAa;YAC5C6E,cAAc,IAAI,CAAC9H,UAAU,CAAC8H,YAAY;YAC1CG,iBAAiB,IAAI,CAACjI,UAAU,CAACiI,eAAe;YAChDC,eAAe,IAAI,CAAClI,UAAU,CAACmI,GAAG,CAACD,aAAa,IAAI;YACpDxB;YACA0B,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDC,kBAAkB,GAAE,oCAAA,IAAI,CAACvI,UAAU,CAACC,YAAY,CAACkI,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAACzI,UAAU,CAACyI,QAAQ;YAClCC,QAAQ,IAAI,CAAC1I,UAAU,CAAC0I,MAAM;YAC9BC,aAAa,IAAI,CAAC3I,UAAU,CAACC,YAAY,CAAC0I,WAAW;YACrDC,kBAAkB,IAAI,CAAC5I,UAAU,CAAC6I,MAAM;YACxCC,mBAAmB,IAAI,CAAC9I,UAAU,CAACC,YAAY,CAAC6I,iBAAiB;YACjEC,yBACE,IAAI,CAAC/I,UAAU,CAACC,YAAY,CAAC8I,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAChJ,UAAU,CAACkG,IAAI,qBAApB,uBAAsB+C,OAAO;YAC5CpD,SAAS,IAAI,CAACA,OAAO;YACrBqD,kBAAkB,IAAI,CAAC1E,kBAAkB,CAAC0C,GAAG;YAC7CiC,mBAAmB,IAAI,CAACnJ,UAAU,CAACC,YAAY,CAACmJ,SAAS;YACzDC,gBAAgB,IAAI,CAACrJ,UAAU,CAACC,YAAY,CAACqJ,KAAK;YAClDC,aAAa,IAAI,CAACvJ,UAAU,CAACuJ,WAAW,GACpC,IAAI,CAACvJ,UAAU,CAACuJ,WAAW,GAC3BlJ;YACJmJ,oBAAoB,IAAI,CAACxJ,UAAU,CAACC,YAAY,CAACuJ,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAACnD,qBAAqB3D,MAAM,GAAG,IACtC2D,sBACAnG;YAENuJ,uBAAuB,IAAI,CAAC5J,UAAU,CAACC,YAAY,CAAC2J,qBAAqB;YACzE,8EAA8E;YAC9EC,iBAAiB,IAAI,CAAC7J,UAAU,CAAC6J,eAAe;YAChD5J,cAAc;gBACZ6J,YAAY,IAAI,CAAC9J,UAAU,CAAC8J,UAAU;gBACtCC,YAAY,IAAI,CAAC/J,UAAU,CAACC,YAAY,CAAC8J,UAAU;gBACnDC,qBAAqB,IAAI,CAAChK,UAAU,CAACC,YAAY,CAAC+J,mBAAmB;gBACrEC,iBAAiB,IAAI,CAACjK,UAAU,CAACC,YAAY,CAACgK,eAAe,IAAI;gBACjE3C,oBACE,IAAI,CAACtH,UAAU,CAACC,YAAY,CAACqH,kBAAkB,KAAK,gBAChD,gBACA4C,QAAQ,IAAI,CAAClK,UAAU,CAACC,YAAY,CAACqH,kBAAkB;gBAC7D6C,oBACE,IAAI,CAACnK,UAAU,CAACC,YAAY,CAACkK,kBAAkB,IAAI;gBACrDC,gBAAgB,IAAI,CAACpK,UAAU,CAACC,YAAY,CAACmK,cAAc,IAAI;gBAC/DC,WAAW,IAAI,CAACrK,UAAU,CAACC,YAAY,CAACoK,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAACtK,UAAU,CAACC,YAAY,CAACqK,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAAC1K,UAAU,CAAC0K,qBAAqB;YAC5DC,wBACE,IAAI,CAAC3K,UAAU,CAACC,YAAY,CAAC0K,sBAAsB;QACvD;QAEA,4DAA4D;QAC5DC,IAAAA,gCAAS,EAAC;YACRrE;YACAC;QACF;QAEA,IAAI,CAACqE,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAAC/E;QACpB,IAAI,CAACgF,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAEvG;QAAI;IACnD;IA+LUmG,mBAAwC;QAChD,yEAAyE;QACzE,MAAMK,iBAAiB,IAAIC,0CAAoB,CAAC,CAACC;YAC/C,OAAQA;gBACN,KAAKC,yBAAc;oBACjB,OAAO,IAAI,CAAChB,gBAAgB,MAAM;gBACpC,KAAKiB,6BAAkB;oBACrB,OAAO,IAAI,CAACf,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAIW,sDAA0B;QAEpE,8BAA8B;QAC9BX,SAASjH,IAAI,CACX,IAAI6H,oDAAyB,CAC3B,IAAI,CAACpG,OAAO,EACZ8F,gBACA,IAAI,CAACxI,YAAY;QAIrB,uCAAuC;QACvCkI,SAASjH,IAAI,CACX,IAAI8H,0DAA4B,CAC9B,IAAI,CAACrG,OAAO,EACZ8F,gBACA,IAAI,CAACxI,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACqB,kBAAkB,CAAC0C,GAAG,EAAE;YAC/B,gCAAgC;YAChCmE,SAASjH,IAAI,CACX,IAAI+H,wDAA2B,CAAC,IAAI,CAACtG,OAAO,EAAE8F;YAEhDN,SAASjH,IAAI,CACX,IAAIgI,0DAA4B,CAAC,IAAI,CAACvG,OAAO,EAAE8F;QAEnD;QAEA,OAAON;IACT;IAEA,MAAgBb,8BACd,GAAG6B,IAAqD,EACxD;QACA,MAAM,CAACC,KAAK9L,KAAK+L,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACE/J,MAAM/B,IAAIqB,GAAG,IAAI;oBACjB6K,QAAQlM,IAAIkM,MAAM,IAAI;oBACtB,gEAAgE;oBAChEvL,SACEX,eAAemM,wBAAe,GAC1BjD,OAAOkD,WAAW,CAACpM,IAAIW,OAAO,CAAC0L,OAAO,MACtCrM,IAAIW,OAAO;gBACnB,GACAoL;YAEJ,EAAE,OAAOO,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASX,GAAU,EAAQ;QAChC,IAAI,IAAI,CAACrH,KAAK,EAAE;QAChBH,KAAIkI,KAAK,CAACV;IACZ;IAEA,MAAaY,cACX1M,GAAkB,EAClB0B,GAAmB,EACnBxB,SAAkC,EACnB;QACf,MAAM,IAAI,CAACyM,OAAO;QAClB,MAAMT,SAASlM,IAAIkM,MAAM,CAACU,WAAW;QAErC,MAAMC,SAASC,IAAAA,iBAAS;QACxB,OAAOD,OAAOE,qBAAqB,CAAC/M,IAAIW,OAAO,EAAE;YAC/C,OAAOkM,OAAOG,KAAK,CACjBC,0BAAc,CAACP,aAAa,EAC5B;gBACEQ,UAAU,GAAGhB,OAAO,CAAC,EAAElM,IAAIqB,GAAG,EAAE;gBAChC8L,MAAMC,gBAAQ,CAACC,MAAM;gBACrBC,YAAY;oBACV,eAAepB;oBACf,eAAelM,IAAIqB,GAAG;gBACxB;YACF,GACA,OAAOkM,OACL,IAAI,CAACC,iBAAiB,CAACxN,KAAK0B,KAAKxB,WAAWuN,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAezL,IAAAA,2BAAc,EAACjC,KAAK,mBAAmB;oBAC5DuN,KAAKI,aAAa,CAAC;wBACjB,oBAAoBjM,IAAIkM,UAAU;wBAClC,YAAYF;oBACd;oBAEA,IAAIhM,IAAIkM,UAAU,IAAIlM,IAAIkM,UAAU,IAAI,KAAK;wBAC3C,8DAA8D;wBAC9D,6EAA6E;wBAC7EL,KAAKM,SAAS,CAAC;4BACbC,MAAMC,sBAAc,CAACC,KAAK;wBAC5B;wBACA,8DAA8D;wBAC9DT,KAAKU,YAAY,CAAC,cAAcvM,IAAIkM,UAAU,CAACM,QAAQ;oBACzD;oBAEA,MAAMC,qBAAqBtB,OAAOuB,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvBpB,0BAAc,CAACP,aAAa,EAC5B;wBACAH,QAAQhI,IAAI,CACV,CAAC,2BAA2B,EAAE4J,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAMjD,OAAOqC,eACT,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAEoC,OAAO,GACxB,GAAGpC,OAAO,CAAC,EAAEoC,OAAO;wBAExBf,KAAKI,aAAa,CAAC;4BACjB,cAAcW;4BACd,cAAcA;4BACd,kBAAkBjD;wBACpB;wBACAkC,KAAKgB,UAAU,CAAClD;oBAClB,OAAO;wBACLkC,KAAKgB,UAAU,CACbb,eACI,CAAC,IAAI,EAAExB,OAAO,CAAC,EAAElM,IAAIqB,GAAG,EAAE,GAC1B,GAAG6K,OAAO,CAAC,EAAElM,IAAIqB,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcmM,kBACZxN,GAAkB,EAClB0B,GAAmB,EACnBxB,SAAkC,EACnB;QACf,IAAI;gBAiDKsO,yBAS4BA,0BASd,oBAKY;YAvEjC,qCAAqC;YACrC,MAAM,IAAI,CAAC3D,QAAQ,CAAC4D,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDC,IAAAA,+CAA+B,EAC7B1O,KACA2O,IAAAA,2BAAkB,EAACjN,OAAOA,IAAIkN,gBAAgB,GAAGlN;YAGnD,MAAMmN,WAAW,AAAC7O,CAAAA,IAAIqB,GAAG,IAAI,EAAC,EAAGyB,KAAK,CAAC,KAAK;YAC5C,MAAMgM,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAYxO,KAAK,CAAC,cAAc;gBAClC,MAAMyO,WAAWC,IAAAA,+BAAwB,EAAChP,IAAIqB,GAAG;gBACjDK,IAAIuN,QAAQ,CAACF,UAAU,KAAKG,IAAI,CAACH,UAAUI,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACjP,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIqB,GAAG,EAAE;oBACZ,MAAM,qBAAgD,CAAhD,IAAIjC,MAAM,wCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+C;gBACvD;gBAEAc,YAAYqB,IAAAA,UAAQ,EAACvB,IAAIqB,GAAG,EAAG;YACjC;YAEA,IAAI,CAACnB,UAAUC,QAAQ,EAAE;gBACvB,MAAM,qBAA+C,CAA/C,IAAIf,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEA,iFAAiF;YACjF,IAAI,OAAOc,UAAUkP,KAAK,KAAK,UAAU;gBACvClP,UAAUkP,KAAK,GAAGlG,OAAOkD,WAAW,CAClC,IAAIiD,gBAAgBnP,UAAUkP,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAEZ,kBAAkB,IAAI,EAAE,GAAGc,IAAAA,0BAAiB,EAACtP,OAAOA,MAAM,CAAC;YACnE,MAAMuP,kBAAkBf,mCAAAA,gBAAiB7N,OAAO,CAAC,oBAAoB;YACrE,MAAM6O,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEf,oCAAAA,0BAAAA,gBAAiBiB,MAAM,qBAAxB,AAACjB,wBAAuCkB,SAAS;YAEvD1P,IAAIW,OAAO,CAAC,mBAAmB,KAAKX,IAAIW,OAAO,CAAC,OAAO,IAAI,IAAI,CAACiC,QAAQ;YACxE5C,IAAIW,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACkE,IAAI,GACzC,IAAI,CAACA,IAAI,CAACqJ,QAAQ,KAClBsB,UACE,QACA;YACNxP,IAAIW,OAAO,CAAC,oBAAoB,KAAK6O,UAAU,UAAU;YACzDxP,IAAIW,OAAO,CAAC,kBAAkB,KAAK6N,oCAAAA,2BAAAA,gBAAiBiB,MAAM,qBAAvBjB,yBAAyBmB,aAAa;YAEzE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACC,iBAAiB,CAAC5P,KAAKE;YAE5B,IAAI6D,WAAW,MAAM,IAAI,CAAChE,gBAAgB,CAACC,KAAK0B,KAAKxB;YACrD,IAAI6D,UAAU;YAEd,MAAMf,gBAAe,qBAAA,IAAI,CAACL,YAAY,qBAAjB,mBAAmBM,kBAAkB,CACxD4M,IAAAA,wBAAW,EAAC3P,WAAWF,IAAIW,OAAO;YAGpC,MAAMuC,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAAC1D,UAAU,CAACkG,IAAI,qBAApB,sBAAsBxC,aAAa;YACpEnC,IAAAA,2BAAc,EAACf,KAAK,iBAAiBkD;YAErC,MAAM7B,MAAMyO,IAAAA,kBAAY,EAAC9P,IAAIqB,GAAG,CAAC0O,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAeC,IAAAA,wCAAmB,EAAC5O,IAAIlB,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3BmD,cAAc,IAAI,CAACA,YAAY;YACjC;YACAtB,IAAIlB,QAAQ,GAAG6P,aAAa7P,QAAQ;YAEpC,IAAI6P,aAAa/H,QAAQ,EAAE;gBACzBjI,IAAIqB,GAAG,GAAG6O,IAAAA,kCAAgB,EAAClQ,IAAIqB,GAAG,EAAG,IAAI,CAAC7B,UAAU,CAACyI,QAAQ;YAC/D;YAEA,MAAMkI,uBACJ,IAAI,CAACvL,WAAW,IAAI,OAAO5E,IAAIW,OAAO,CAACyP,+BAAmB,CAAC,KAAK;YAElE,uCAAuC;YACvC,IAAID,sBAAsB;gBACxB,IAAI;wBAuBE,wBA6ByB,qBA6DjB;oBAhHZ,IAAI,IAAI,CAACnM,kBAAkB,CAAC0C,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI1G,IAAIqB,GAAG,CAACf,KAAK,CAAC,mBAAmB;4BACnCN,IAAIqB,GAAG,GAAGrB,IAAIqB,GAAG,CAAC0O,OAAO,CAAC,YAAY;wBACxC;wBACA7P,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAUkQ,WAAW,EAAE,GAAG,IAAIC,IAClCC,IAAAA,wBAAW,EAACvQ,IAAIW,OAAO,CAACyP,+BAAmB,CAAC,GAC5C;oBAGF,IAAI,EAAEjQ,UAAUqQ,WAAW,EAAE,GAAG,IAAIF,IAAItQ,IAAIqB,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAACjB,WAAW,CAACuD,IAAI,qBAArB,uBAAuBrD,KAAK,CAACkQ,cAAc;wBAC7CzP,IAAAA,2BAAc,EAACf,KAAK,iBAAiB;oBACvC,OAGK,IACH,IAAI,CAACyG,eAAe,IACpB,IAAI,CAAC7B,WAAW,IAChB5E,IAAIW,OAAO,CAAC8P,8BAAkB,CAAC,KAAK,OACpCzQ,IAAIkM,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAMgD,OAAsB,EAAE;wBAC9B,WAAW,MAAMwB,SAAS1Q,IAAIkP,IAAI,CAAE;4BAClCA,KAAKtL,IAAI,CAAC8M;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAAC3B,MAAMhB,QAAQ,CAAC;wBAE/CnN,IAAAA,2BAAc,EAACf,KAAK,aAAa2Q;oBACnC;oBAEAN,cAAc,IAAI,CAACpP,SAAS,CAACoP;oBAC7B,MAAMS,oBAAoB,IAAI,CAACC,iBAAiB,CAACP;oBAEjDH,cAAcW,IAAAA,wCAAmB,EAACX;oBAElC,8CAA8C;oBAC9C,MAAMY,wBAAuB,sBAAA,IAAI,CAACtO,YAAY,qBAAjB,oBAAmBU,OAAO,CAACgN,aAAa;wBACnEnN;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAI+N,sBAAsB;wBACxBlQ,IAAAA,2BAAc,EAACf,KAAK,UAAUiR,qBAAqB3N,cAAc;wBAEjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAI2N,qBAAqBC,mBAAmB,EAAE;4BAC5CnQ,IAAAA,2BAAc,EAACf,KAAK,6BAA6B;wBACnD,OAAO;4BACLuD,IAAAA,8BAAiB,EAACvD,KAAK;wBACzB;oBACF;oBAEA,IAAImR,cAAcd;oBAClB,IAAIe,gBAAgBC,IAAAA,sBAAc,EAACF;oBACnC,IAAIG,eAGA;wBACFzP,QAAQ;wBACR0P,gBAAgB;oBAClB;oBAEA,IAAI,CAACH,eAAe;wBAClB,MAAM9Q,QAAQ,MAAM,IAAI,CAACuK,QAAQ,CAACvK,KAAK,CAAC6Q,aAAa;4BACnDzL,MAAMuL;wBACR;wBAEA,6DAA6D;wBAC7D,IAAI3Q,OAAO;4BACT6Q,cAAc7Q,MAAMkR,UAAU,CAACrR,QAAQ;4BAEvC,iEAAiE;4BACjE,iEAAiE;4BACjE,4CAA4C;4BAC5C,IAAI,OAAOG,MAAMuB,MAAM,KAAK,aAAa;gCACvCuP,gBAAgB;gCAChBE,aAAazP,MAAM,GAAGvB,MAAMuB,MAAM;gCAClCyP,aAAaC,cAAc,GAAG;4BAChC;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAIN,sBAAsB;wBACxBZ,cAAcY,qBAAqB9Q,QAAQ;oBAC7C;oBAEA,MAAMsR,QAAQC,IAAAA,2BAAc,EAAC;wBAC3BN;wBACAO,MAAMR;wBACNzL,MAAM,IAAI,CAAClG,UAAU,CAACkG,IAAI;wBAC1BuC,UAAU,IAAI,CAACzI,UAAU,CAACyI,QAAQ;wBAClC2J,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAACzS,UAAU,CAACC,YAAY,CAACyS,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIhP,iBAAiB,CAAC8M,aAAamC,MAAM,EAAE;wBACzCjS,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAE+C,gBAAgBhD,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,mEAAmE;oBACnE,qEAAqE;oBACrE,MAAMiS,oBAAoB;wBAAE,GAAGlS,UAAUkP,KAAK;oBAAC;oBAE/C,MAAMiD,wBAAwBnS,UAAUC,QAAQ;oBAChD,MAAMmS,mBAAmBpJ,OAAOC,IAAI,CAClCsI,MAAMc,cAAc,CAACvS,KAAKE;oBAG5B,mEAAmE;oBACnE,mEAAmE;oBACnE,2CAA2C;oBAC3C,MAAMsS,cAAc;wBAAE,GAAGtS,UAAUkP,KAAK;oBAAC;oBACzC,MAAMqD,aAAaJ,0BAA0BnS,UAAUC,QAAQ;oBAE/D,IAAIsS,cAAcvS,UAAUC,QAAQ,EAAE;wBACpCY,IAAAA,2BAAc,EAACf,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBAEA,MAAMuS,iBAAiB,IAAIC;oBAC3B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAI3J,OAAOmD,OAAO,CAACnM,UAAUkP,KAAK,EAAG;wBAC1D,MAAM0D,gBAAgBC,IAAAA,+BAAuB,EAACH;wBAC9C,IAAI,CAACE,eAAe;wBAEpB,gEAAgE;wBAChE,+CAA+C;wBAC/C,OAAO5S,UAAUkP,KAAK,CAACwD,IAAI;wBAC3BF,eAAeM,GAAG,CAACF;wBAEnB,IAAI,OAAOD,UAAU,aAAa;wBAElCL,WAAW,CAACM,cAAc,GAAGG,MAAMC,OAAO,CAACL,SACvCA,MAAMM,GAAG,CAAC,CAACC,IAAMC,IAAAA,kDAAwB,EAACD,MAC1CC,IAAAA,kDAAwB,EAACR;oBAC/B;oBAEA,yDAAyD;oBACzD,IAAIzB,eAAe;wBACjB,IAAIvP,SAAiC,CAAC;wBAEtC,gEAAgE;wBAChE,oBAAoB;wBACpB,IAAI,CAACyP,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM6B,2BAA2B,CAC9Cd,aACA;wBAEJ;wBAEA,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAAClB,aAAaC,cAAc,IAC5B,CAACF,IAAAA,sBAAc,EAACP,oBAChB;4BACA,IAAIyC,gBAAgB9B,MAAM+B,mBAAmB,oBAAzB/B,MAAM+B,mBAAmB,MAAzB/B,OAA4BX;4BAEhD,IAAIyC,eAAe;gCACjB9B,MAAM6B,2BAA2B,CAACC,eAAe;gCACjDrK,OAAOuK,MAAM,CAACnC,aAAazP,MAAM,EAAE0R;gCACnCjC,aAAaC,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9DlB,gBAAgB,YAChB,CAACiB,aAAaC,cAAc,IAC5B,CAACF,IAAAA,sBAAc,EAAChB,cAChB;4BACA,IAAIkD,gBAAgB9B,MAAM+B,mBAAmB,oBAAzB/B,MAAM+B,mBAAmB,MAAzB/B,OAA4BpB;4BAEhD,IAAIkD,eAAe;gCACjB,MAAMG,kBAAkBjC,MAAM6B,2BAA2B,CACvDC,eACA;gCAGF,IAAIG,gBAAgBnC,cAAc,EAAE;oCAClCrI,OAAOuK,MAAM,CAAC5R,QAAQ0R;oCACtBjC,eAAeoC;gCACjB;4BACF;wBACF;wBAEA,IAAIpC,aAAaC,cAAc,EAAE;4BAC/B1P,SAASyP,aAAazP,MAAM;wBAC9B;wBAEA,MAAM8R,qBAAqB3T,IAAIW,OAAO,CAAC,sBAAsB;wBAC7D,IACE,OAAOgT,uBAAuB,YAC9BA,sBACAtC,IAAAA,sBAAc,EAAChB,gBACf,CAACiB,aAAaC,cAAc,EAC5B;4BACA,MAAMqC,eACJnC,MAAMoC,yBAAyB,CAACF;4BAElC,IAAIC,cAAc;gCAChBtC,eAAeG,MAAM6B,2BAA2B,CAC9CM,cACA;gCAGF,IAAItC,aAAaC,cAAc,EAAE;oCAC/B1P,SAASyP,aAAazP,MAAM;gCAC9B;4BACF;wBACF;wBAEA,mEAAmE;wBACnE,6DAA6D;wBAC7D,IAAI,CAACyP,aAAaC,cAAc,EAAE;4BAChCD,eAAeG,MAAM6B,2BAA2B,CAC9Cd,aACA;4BAGF,IAAIlB,aAAaC,cAAc,EAAE;gCAC/B1P,SAASyP,aAAazP,MAAM;4BAC9B;wBACF;wBAEA,4DAA4D;wBAC5D,+DAA+D;wBAC/D,yBAAyB;wBACzB,IACE4P,MAAMqC,mBAAmB,IACzBhD,sBAAsBK,eACtB,CAACG,aAAaC,cAAc,EAC5B;4BACA1P,SAAS4P,MAAMqC,mBAAmB;4BAElC,6DAA6D;4BAC7D,kEAAkE;4BAClE,gEAAgE;4BAChE,8DAA8D;4BAC9D,gEAAgE;4BAChE,IAAIH,uBAAuB,IAAI;gCAC7B5S,IAAAA,2BAAc,EAACf,KAAK,uBAAuB;4BAC7C;wBACF;wBAEA,IAAI6B,QAAQ;4BACVwO,cAAcoB,MAAMsC,sBAAsB,CAAC5C,aAAatP;4BACxD7B,IAAIqB,GAAG,GAAGoQ,MAAMsC,sBAAsB,CAAC/T,IAAIqB,GAAG,EAAGQ;4BAEjD,kEAAkE;4BAClE,4DAA4D;4BAC5D,UAAU;4BACV,IAAIT,4BAA4Ba,IAAAA,2BAAc,EAC5CjC,KACA;4BAEF,IACEoB,6BACAiQ,IAAAA,sBAAc,EAACjQ,2BAA2B,QAC1C;gCACAA,4BAA4BqQ,MAAMsC,sBAAsB,CACtD3S,2BACAS;gCAGF7B,IAAIW,OAAO,CAACG,qDAAmC,CAAC,GAC9CM;gCACFL,IAAAA,2BAAc,EACZf,KACA,6BACAoB;4BAEJ;wBACF;oBACF;oBAEA,IAAIgQ,iBAAiBqB,YAAY;4BAGdhB;wBAFjBA,MAAMuC,eAAe,CAAChU,KAAK;+BACtBsS;+BACApJ,OAAOC,IAAI,CAACsI,EAAAA,2BAAAA,MAAMwC,iBAAiB,qBAAvBxC,yBAAyByC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACA,oEAAoE;oBACpE,oCAAoC;oBACpC,mFAAmF;oBACnF,KAAK,MAAMtB,OAAOF,eAAgB;wBAChC,IAAI,CAAEE,CAAAA,OAAOR,iBAAgB,GAAI;4BAC/B,OAAOlS,UAAUkP,KAAK,CAACwD,IAAI;wBAC7B;oBACF;oBACA1S,UAAUC,QAAQ,GAAGkQ;oBACrBhP,IAAIlB,QAAQ,GAAGD,UAAUC,QAAQ;oBACjC4D,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAAC9D,KAAK0B,KAAKxB;oBAC3D,IAAI6D,UAAU;gBAChB,EAAE,OAAO+H,KAAK;oBACZ,IAAIA,eAAeqI,kBAAW,IAAIrI,eAAesI,qBAAc,EAAE;wBAC/D1S,IAAIkM,UAAU,GAAG;wBACjB,OAAO,IAAI,CAACyG,WAAW,CAAC,MAAMrU,KAAK0B,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAMoK;gBACR;YACF;YAEA/K,IAAAA,2BAAc,EAACf,KAAK,kBAAkB0J,QAAQ1G;YAE9C,IAAIgN,aAAamC,MAAM,EAAE;gBACvBnS,IAAIqB,GAAG,GAAGG,IAAAA,WAAS,EAACH;gBACpBN,IAAAA,2BAAc,EAACf,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC4E,WAAW,IAAI,CAAC3C,IAAAA,2BAAc,EAACjC,KAAK,WAAW;gBACvD,gEAAgE;gBAChE,IAAIgQ,aAAamC,MAAM,EAAE;oBACvBpR,IAAAA,2BAAc,EAACf,KAAK,UAAUgQ,aAAamC,MAAM;gBACnD,OAGK,IAAIjP,eAAe;oBACtBnC,IAAAA,2BAAc,EAACf,KAAK,UAAUkD;oBAC9BnC,IAAAA,2BAAc,EAACf,KAAK,6BAA6B;gBACnD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAAC+E,aAAa,CAASuP,eAAe,IAC5C,CAACrS,IAAAA,2BAAc,EAACjC,KAAK,qBACrB;gBACA,MAAMuU,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBvL,OAAOuK,MAAM,CAAC,CAAC,GAAGzT,IAAIW,OAAO;gBAC/C;gBAEA4T,iBAAiBG,iBAAiB;gBAClC3T,IAAAA,2BAAc,EAACf,KAAK,oBAAoBuU;gBAGtC5U,WAAmBgV,kBAAkB,GAAGJ;YAC5C;YAEA,MAAMK,gBAAgBC,IAAAA,0BAAgB;YAEtC,IAAID,eAAe;gBACjB,MAAME,QAAQC,GAAG,CACf;uBAAIH;iBAAc,CAACzB,GAAG,CAAC,OAAO6B;oBAC5B,IAAI,iBAAiBA,cAAc;oBACjC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,gDAAgD;oBAClD,OAAO;wBACL,MAAMC,4BAA4BC,IAAAA,yCAA4B,EAC5DlV,IAAIW,OAAO,EACX,IAAI,CAACkH,oBAAoB,GAAGC,OAAO,CAACqN,aAAa;wBAGnD,MAAMH,aAAaI,kBAAkB,IAChCH;oBAEP;gBACF;YAEJ;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAAChT,IAAAA,2BAAc,EAACjC,KAAK,6BAA6B;gBACpDe,IAAAA,2BAAc,EACZf,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAM8V,aAAapT,IAAAA,2BAAc,EAACjC,KAAK;YACvC,MAAMsV,gBAAgB,CAACnF,wBAAwBkF;YAE/C,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAetT,IAAAA,2BAAc,EAACjC,KAAK;gBACzC,IAAIuV,cAAc;oBAChB,MAAMC,cAAcvT,IAAAA,2BAAc,EAACjC,KAAK;oBAExC,IAAIwV,aAAa;wBACftM,OAAOuK,MAAM,CAACvT,UAAUkP,KAAK,EAAEoG;oBACjC;oBAEA9T,IAAIkM,UAAU,GAAG2H;oBACjB,IAAIzJ,MAAoB7J,IAAAA,2BAAc,EAACjC,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAACqU,WAAW,CAACvI,KAAK9L,KAAK0B,KAAK,WAAWxB,UAAUkP,KAAK;gBACnE;gBAEA,MAAMqG,oBAAoB,IAAInF,IAAI+E,cAAc,KAAK;gBACrD,MAAMK,qBAAqBzF,IAAAA,wCAAmB,EAC5CwF,kBAAkBtV,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3BmW,WAAW;gBACb;gBAGF,IAAID,mBAAmBvD,MAAM,EAAE;oBAC7BpR,IAAAA,2BAAc,EAACf,KAAK,UAAU0V,mBAAmBvD,MAAM;gBACzD;gBAEA,IAAIjS,UAAUC,QAAQ,KAAKsV,kBAAkBtV,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAGsV,kBAAkBtV,QAAQ;oBAC/CY,IAAAA,2BAAc,EAACf,KAAK,cAAc0V,mBAAmBvV,QAAQ;gBAC/D;gBACA,MAAMyV,kBAAkBC,IAAAA,wCAAmB,EACzC3F,IAAAA,kCAAgB,EAAChQ,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAACyI,QAAQ,IAAI,MACjE,yBAAA,IAAI,CAACzI,UAAU,CAACkG,IAAI,qBAApB,uBAAsBC,OAAO;gBAG/B,IAAIiQ,gBAAgBtS,cAAc,EAAE;oBAClCvC,IAAAA,2BAAc,EAACf,KAAK,UAAU4V,gBAAgBtS,cAAc;gBAC9D;gBACApD,UAAUC,QAAQ,GAAGyV,gBAAgBzV,QAAQ;gBAE7C,KAAK,MAAMyS,OAAO1J,OAAOC,IAAI,CAACjJ,UAAUkP,KAAK,EAAG;oBAC9C,OAAOlP,UAAUkP,KAAK,CAACwD,IAAI;gBAC7B;gBACA,MAAM4C,cAAcvT,IAAAA,2BAAc,EAACjC,KAAK;gBAExC,IAAIwV,aAAa;oBACftM,OAAOuK,MAAM,CAACvT,UAAUkP,KAAK,EAAEoG;gBACjC;gBAEAzR,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAAC9D,KAAK0B,KAAKxB;gBAC3D,IAAI6D,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAACzD,KAAK0B,KAAKxB;gBACjD;YACF;YAEA,IAAI+B,IAAAA,2BAAc,EAACjC,KAAK,qBAAqB;gBAC3C+D,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAAC9D,KAAK0B,KAAKxB;gBAC3D,IAAI6D,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnD1D,KACA0B,KACAxB;gBAEF,IAAI6D,UAAU;gBAEd,MAAM+H,MAAM,IAAI1M;gBACd0M,IAAYvL,MAAM,GAAG;oBACrBuV,UAAU,IAAIC,SAAS,MAAM;wBAC3BpV,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACEmL,IAAYkK,MAAM,GAAG;gBACvB,MAAMlK;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACqE,wBAAwBH,aAAa/H,QAAQ,EAAE;gBAClD/H,UAAUC,QAAQ,GAAG+P,IAAAA,kCAAgB,EACnChQ,UAAUC,QAAQ,EAClB6P,aAAa/H,QAAQ;YAEzB;YAEAvG,IAAIkM,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAACqI,GAAG,CAACjW,KAAK0B,KAAKxB;QAClC,EAAE,OAAO4L,KAAU;YACjB,IAAIA,eAAeoK,wCAAe,EAAE;gBAClC,MAAMpK;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAIgC,IAAI,KAAK,qBAChDhC,eAAeqI,kBAAW,IAC1BrI,eAAesI,qBAAc,EAC7B;gBACA1S,IAAIkM,UAAU,GAAG;gBACjB,OAAO,IAAI,CAACyG,WAAW,CAAC,MAAMrU,KAAK0B,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAACkD,WAAW,IAChB,IAAI,CAAC2C,UAAU,CAAC5C,GAAG,IAClBwR,IAAAA,sBAAc,EAACrK,QAAQA,IAAIkK,MAAM,EAClC;gBACA,MAAMlK;YACR;YACA,IAAI,CAACW,QAAQ,CAAC2J,IAAAA,uBAAc,EAACtK;YAC7BpK,IAAIkM,UAAU,GAAG;YACjBlM,IAAIwN,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAwDA;;GAEC,GACD,AAAOkH,8BACLC,IAAiB,EACkC;QACnD,MAAMC,UAAU,IAAI,CAACC,iBAAiB;QACtC,OAAO,CAACxW,KAAK0B,KAAKxB;YAChBuW,IAAAA,2BAAc,EAACzW,KAAKsW;YACpB,OAAOC,QAAQvW,KAAK0B,KAAKxB;QAC3B;IACF;IAEOsW,oBAGL;QACA,OAAO,IAAI,CAAC9J,aAAa,CAACzC,IAAI,CAAC,IAAI;IACrC;IAQOe,eAAe0L,MAAe,EAAQ;QAC3C,IAAI,CAAClX,UAAU,CAACyG,WAAW,GAAGyQ,SAASA,OAAO3G,OAAO,CAAC,OAAO,MAAM;IACrE;IAIA;;;GAGC,GACD,MAAapD,UAAyB;QACpC,IAAI,IAAI,CAACzI,QAAQ,EAAE;QAEnB,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC8H,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAG,MAAM,IAAI,CAAC2K,yBAAyB;QAC7D;QACA,IAAI,IAAI,CAACxS,eAAe,KAAK,MAAM;YACjC,IAAI,CAACA,eAAe,GAAG,IAAI,CAACyS,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAAC3S,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgByS,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3BpM,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDvB,OAAOC,IAAI,CAAC,IAAI,CAACoB,gBAAgB,IAAI,CAAC,GAAGwM,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiBC,IAAAA,0BAAgB,EAACF;YACxC,IAAI,CAACvM,aAAa,CAACwM,eAAe,EAAE;gBAClCxM,aAAa,CAACwM,eAAe,GAAG,EAAE;YACpC;YACAxM,aAAa,CAACwM,eAAe,CAACrT,IAAI,CAACoT;QACrC;QACA,OAAOvM;IACT;IAEA,MAAgBwL,IACdjW,GAAkB,EAClB0B,GAAmB,EACnBxB,SAA6B,EACd;QACf,OAAO4M,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACgJ,GAAG,EAAE,UAC3C,IAAI,CAACkB,OAAO,CAACnX,KAAK0B,KAAKxB;IAE3B;IAEA,MAAciX,QACZnX,GAAkB,EAClB0B,GAAmB,EACnBxB,SAA6B,EACd;QACf,MAAM,IAAI,CAACuD,2BAA2B,CAACzD,KAAK0B,KAAKxB;IACnD;IAEA,MAAckX,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAOxK,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACmK,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,KAAKF,eAAetX,GAAG,CAACW,OAAO,CAAC,aAAa,IAAI;QAEvD,MAAMoL,MAAqD;YACzD,GAAGuL,cAAc;YACjB/P,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClB,6DAA6D;gBAC7DC,yBAAyB,CAAC,IAAI,CAACD,UAAU,CAACkQ,OAAO;gBACjDC,wBAAwBC,IAAAA,+CAA4B,EAClDH,IACA,IAAI,CAAChY,UAAU,CAAC6J,eAAe;YAEnC;QACF;QAEA,MAAMuO,UAAU,MAAMP,GAAGtL;QACzB,IAAI6L,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAE5X,GAAG,EAAE0B,GAAG,EAAE,GAAGqK;QACrB,MAAM8L,iBAAiBnW,IAAIkM,UAAU;QACrC,MAAM,EAAEsB,IAAI,EAAE,GAAG0I;QACjB,IAAI,EAAEE,YAAY,EAAE,GAAGF;QACvB,IAAI,CAAClW,IAAIqW,IAAI,EAAE;YACb,MAAM,EAAE7R,aAAa,EAAEuB,eAAe,EAAE9C,GAAG,EAAE,GAAG,IAAI,CAAC4C,UAAU;YAE/D,oDAAoD;YACpD,IAAI5C,KAAK;gBACPjD,IAAIsW,SAAS,CAAC,iBAAiB;gBAC/BF,eAAejY;YACjB;YAEA,IAAIiY,gBAAgBA,aAAaG,MAAM,KAAKpY,WAAW;gBACrDiY,aAAaG,MAAM,GAAG,IAAI,CAACzY,UAAU,CAAC8J,UAAU;YAClD;YAEA,MAAM,IAAI,CAAC4O,gBAAgB,CAAClY,KAAK0B,KAAK;gBACpCnB,QAAQ2O;gBACRhJ;gBACAuB;gBACAqQ;YACF;YACApW,IAAIkM,UAAU,GAAGiK;QACnB;IACF;IAEA,MAAcM,cACZd,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAMvL,MAAqD;YACzD,GAAGuL,cAAc;YACjB/P,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMoQ,UAAU,MAAMP,GAAGtL;QACzB,IAAI6L,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQ1I,IAAI,CAACkJ,iBAAiB;IACvC;IAEA,MAAaC,OACXrY,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAA4B,CAAC,CAAC,EAC9BlP,SAAkC,EAClCoY,iBAAiB,KAAK,EACP;QACf,OAAOxL,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACoL,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAACvY,KAAK0B,KAAKvB,UAAUiP,OAAOlP,WAAWoY;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwBC,IAAAA,+CAAwB;QACtD,IAAID,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBE,SAAS;QACxC;QAEA,IAAI,IAAI,CAAC/T,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAO/E;QACT;QAEA,OAAO,IAAI,CAAC+Y,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAO/Y;IACT;IAEA,MAAc0Y,WACZvY,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAA4B,CAAC,CAAC,EAC9BlP,SAAkC,EAClCoY,iBAAiB,KAAK,EACP;YA4BZtY;QA3BH,IAAI,CAACG,SAAS0Y,UAAU,CAAC,MAAM;YAC7BtM,QAAQhI,IAAI,CACV,CAAC,8BAA8B,EAAEpE,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAAC4E,aAAa,CAAC+T,YAAY,IAC/B3Y,aAAa,YACb,CAAE,MAAM,IAAI,CAAC4Y,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxC5Y,WAAW;QACb;QAEA,MAAMqX,KAAKxX,IAAIW,OAAO,CAAC,aAAa,IAAI;QACxC,IAAI,CAAC4G,UAAU,CAACkQ,OAAO,GAAGuB,IAAAA,iBAAU,EAACxB;QAErC,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAACc,kBACD,CAAC,IAAI,CAAC1T,WAAW,IACjB,CAAC3C,IAAAA,2BAAc,EAACjC,KAAK,oBACpBA,CAAAA,EAAAA,WAAAA,IAAIqB,GAAG,qBAAPrB,SAASM,KAAK,CAAC,kBACb,IAAI,CAACkF,YAAY,IAAIxF,IAAIqB,GAAG,CAAEf,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACoM,aAAa,CAAC1M,KAAK0B,KAAKxB;QACtC;QAEA,IAAI+Y,IAAAA,qBAAa,EAAC9Y,WAAW;YAC3B,OAAO,IAAI,CAAC+B,SAAS,CAAClC,KAAK0B,KAAKxB;QAClC;QAEA,OAAO,IAAI,CAACkX,IAAI,CAAC,CAACrL,MAAQ,IAAI,CAACmN,gBAAgB,CAACnN,MAAM;YACpD/L;YACA0B;YACAvB;YACAiP;QACF;IACF;IAEA,MAAgB+J,eAAe,EAC7BhZ,QAAQ,EAOT,EAIE;YAGC;QAFF,+DAA+D;QAC/D,MAAMiZ,iBACJ,oDAAA,IAAI,CAACvR,oBAAoB,GAAGwR,aAAa,CAAClZ,SAAS,qBAAnD,kDAAqD6R,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvCsH,aAAazZ;YACb0Z,cAAcC,IAAAA,4BAAkB,EAACJ;QACnC;IACF;IAEA,MAAcK,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAO7M,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAACwM,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACEC,IAAAA,8CAA0B,EAACD,qBAC3B,IAAI,CAACnP,yBAAyB,CAACqP,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACJ;QACrB;IAEJ;IAEUK,cACRna,GAAkB,EAClB0B,GAAmB,EACnB0Y,SAAkB,EAClBN,gBAAwB,EAClB;QACN,MAAMO,iBAAiB,GAAGzZ,4BAAU,CAAC,EAAE,EAAE0Z,+CAA6B,CAAC,EAAE,EAAEzZ,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;QAChJ,MAAM4M,eAAezL,IAAAA,2BAAc,EAACjC,KAAK,mBAAmB;QAE5D,IAAIua,qBAAqB;QAEzB,IAAIH,aAAa,IAAI,CAACP,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FpY,IAAI8Y,YAAY,CAAC,QAAQ,GAAGH,eAAe,EAAE,EAAEI,0BAAQ,EAAE;YACzDF,qBAAqB;QACvB,OAAO,IAAIH,aAAa1M,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnGhM,IAAI8Y,YAAY,CAAC,QAAQH;QAC3B;QAEA,IAAI,CAACE,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOva,IAAIW,OAAO,CAAC8Z,0BAAQ,CAAC;QAC9B;IACF;IAEA,MAAcb,mCACZ,EACE5Z,GAAG,EACH0B,GAAG,EACHvB,QAAQ,EACRoH,YAAYmT,IAAI,EAC8B,EAChD,EAAEC,UAAU,EAAEvL,KAAK,EAAwB,EACV;YAiL7BwL;QAhLJ,IAAIza,aAAa0a,qCAA0B,EAAE;YAC3C1a,WAAW;QACb;QACA,MAAM2a,kBAAkB3a,aAAa;QACrC,MAAM4a,YACJ5a,aAAa,UAAW2a,mBAAmBpZ,IAAIkM,UAAU,KAAK;QAChE,MAAMoN,YACJ7a,aAAa,UAAW2a,mBAAmBpZ,IAAIkM,UAAU,KAAK;QAChE,MAAMwM,YAAYO,WAAWP,SAAS,KAAK;QAE3C,MAAMa,iBAAiB,CAAC,CAACN,WAAWO,kBAAkB;QACtD,MAAMC,yBAAyBC,IAAAA,kDAAyB,EAACpb;QACzD,IAAIqb,QAAQ,CAAC,CAACV,WAAWW,cAAc;QACvC,uFAAuF;QACvF,MAAM5N,eAAezL,IAAAA,2BAAc,EAACjC,KAAK,mBAAmB;QAE5D,yEAAyE;QACzE,yEAAyE;QACzE,mEAAmE;QACnE,oEAAoE;QACpE,mEAAmE;QACnE,qCAAqC;QACrC,IACE,CAAC,IAAI,CAAC4E,WAAW,IACjB,IAAI,CAACpF,UAAU,CAACC,YAAY,CAAC8b,yBAAyB,IACtD7N,cACA;YACA,MAAM/M,UAAUX,IAAIW,OAAO;YAE3B,MAAM6a,sBAAsB7a,OAAO,CAACE,6CAA2B,CAAC;YAChE,MAAM4a,iBACJD,wBAAwB3b,YAEpB2b,wBAAwB,OAAOA,wBAAwB,MACrDA,sBACA3b,YAEF,yEAAyE;YACzE,+EAA+E;YAC/EoC,IAAAA,2BAAc,EAACjC,KAAK,0BAClB,MACAH;YAER,MAAMuB,4BACJT,OAAO,CAACG,qDAAmC,CAAC,IAC5CmB,IAAAA,2BAAc,EAACjC,KAAK;YAEtB,MAAM0b,eAAeC,IAAAA,uDAA8B,EACjDF,gBACAra,2BACAT,OAAO,CAAC2Z,+CAA6B,CAAC,EACtC3Z,OAAO,CAAC8Z,0BAAQ,CAAC;YAEnB,MAAMmB,aACJ3Z,IAAAA,2BAAc,EAACjC,KAAK,8BACpB,IAAIsQ,IAAItQ,IAAIqB,GAAG,IAAI,IAAI,oBAAoBwa,YAAY,CAACxN,GAAG,CACzDyN,sCAAoB;YAGxB,IAAIJ,iBAAiBE,YAAY;gBAC/B,iEAAiE;gBACjE,mEAAmE;gBACnE,iFAAiF;gBACjF,6EAA6E;gBAC7E,6EAA6E;gBAC7E,MAAMva,MAAM,IAAIiP,IAAItQ,IAAIqB,GAAG,IAAI,IAAI;gBACnC0a,IAAAA,8DAAkC,EAAC1a,KAAKqa;gBACxCha,IAAIkM,UAAU,GAAG;gBACjBlM,IAAIsW,SAAS,CAAC,YAAY,GAAG3W,IAAIlB,QAAQ,GAAGkB,IAAI2a,MAAM,EAAE;gBACxDta,IAAIwN,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;QACF;QAEA,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIqB,cAAcjP,IAAAA,UAAQ,EAACvB,IAAIqB,GAAG,IAAI,IAAIlB,QAAQ,IAAI;QAEtD,IAAI8b,sBAAsBha,IAAAA,2BAAc,EAACjC,KAAK,iBAAiBwQ;QAE/D,IAAI,CAAC2J,aAAa,CAACna,KAAK0B,KAAK0Y,WAAW6B;QAExC,IAAI3C;QACJ,IAAI4C,cAAc;QAElB,MAAMtB,oBAAoB,IAAI,CAAC/S,oBAAoB;QAEnD,IACEqU,gBACA5C,+BAAAA,YAAa6C,QAAQ,CAACF,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/Bjc,IAAIW,OAAO,CAAC,sBAAsB,EAClC;YACA0a,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAAC9T,UAAU,CAAC5C,GAAG,EAAE;YAC/B0W,UAAU,CAAC,CAACT,kBAAkBwB,MAAM,CAACC,IAAAA,gBAAO,EAAClc,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAMmc,oBACJ,CAAC,CACCra,CAAAA,IAAAA,2BAAc,EAACjC,KAAK,oBACnBA,IAAIW,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAACoE,aAAa,CAASuP,eAAe,KAE9C+G,CAAAA,SAASJ,cAAa;QAEzB,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACI,SACDrb,IAAIW,OAAO,CAAC,wBAAwB,IACpC,CAAEoa,CAAAA,aAAa5a,aAAa,SAAQ,GACpC;YACAuB,IAAIsW,SAAS,CAAC5H,+BAAmB,EAAEjQ;YACnCuB,IAAIsW,SAAS,CAAC,qBAAqB;YACnCtW,IAAIsW,SAAS,CACX,iBACA;YAEFtW,IAAIwN,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,iEAAiE;QACjE,IACEkM,SACA,IAAI,CAACzW,WAAW,IAChB5E,IAAIW,OAAO,CAACyP,+BAAmB,CAAC,IAChCpQ,IAAIqB,GAAG,CAACwX,UAAU,CAAC,gBACnB;YACA7Y,IAAIqB,GAAG,GAAG,IAAI,CAAC0P,iBAAiB,CAAC/Q,IAAIqB,GAAG;QAC1C;QAEA,MAAM8Q,SAASlQ,IAAAA,2BAAc,EAACjC,KAAK;QAEnC,IACE,CAAC,CAACA,IAAIW,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACe,IAAIkM,UAAU,IAAIlM,IAAIkM,UAAU,KAAK,GAAE,GACzC;YACAlM,IAAIsW,SAAS,CACX,yBACA,GAAG7F,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKhS,UAAU;QAE9C;QAEA,IAAIoc;QACJ,IAAI5B,WAAW4B,WAAW,EAAE;YAC1BA,cAAc5B,WAAW4B,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAAC/V,eAAe,IACpB,OAAO8V,gBAAgB,eACvBE,IAAAA,4BAAoB,EAACF;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAMG,2BACJrW,QAAQC,GAAG,CAACqW,0CAA0C,KAAK,OAC3D,OAAOvN,MAAMwN,aAAa,KAAK,eAC/BJ;QAEF,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAMK,oBACJL,mBACC,CAAA,EACC5B,QAAAA,kBAAkBwB,MAAM,CAACjc,SAAS,IAClCya,kBAAkBvB,aAAa,CAAClZ,SAAS,qBAF1C,AACCya,MAECkC,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BJ,4BACE,CAAA,IAAI,CAACnV,UAAU,CAAC5C,GAAG,KAAK,QACvB,IAAI,CAACG,qBAAqB,KAAK,IAAG,CAAE;QAE5C,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMiY,mBAAmBF,oBACrB5a,IAAAA,2BAAc,EAACjC,KAAK,eACpBH;QAEJ,gEAAgE;QAChE,IAAIkb,aAAa,CAACuB,qBAAqB,CAAC5O,cAAc;YACpDhM,IAAIkM,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAIoP,8BAAmB,CAACb,QAAQ,CAAChc,WAAW;YAC1CuB,IAAIkM,UAAU,GAAGqP,SAAS9c,SAAS+c,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAAC/B,0BACD,uCAAuC;QACvC,CAAC4B,oBACD,CAAChC,aACD,CAACC,aACD7a,aAAa,aACbH,IAAIkM,MAAM,KAAK,UACflM,IAAIkM,MAAM,KAAK,SACd,CAAA,OAAOyO,WAAWwC,SAAS,KAAK,YAAY9B,KAAI,GACjD;YACA3Z,IAAIkM,UAAU,GAAG;YACjBlM,IAAIsW,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtCtW,IAAIwN,IAAI,CAAC,sBAAsBC,IAAI;YACnC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAOwL,WAAWwC,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACLjO,MAAMkO,qBAAY,CAACC,UAAU,CAC3B1C,WAAWwC,SAAS,EACpBG,oCAAwB;YAE5B;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAASlO,SAAS,CAACA,MAAMzH,GAAG,EAAE,OAAOyH,MAAMzH,GAAG;QAElD,IAAI+S,KAAKlT,uBAAuB,KAAK,MAAM;gBAIhCmT;YAHT,MAAMnD,KAAKxX,IAAIW,OAAO,CAAC,aAAa,IAAI;YACxC,MAAM4c,eAAeC,IAAAA,YAAK,EAAChG;YAC3B,MAAMiG,sBACJ,SAAO9C,uBAAAA,WAAW+C,QAAQ,qBAAnB/C,qBAAqBgD,eAAe,MAAK,cAChD,oFAAoF;YACpFC,gCAAqB,IAAIjD,WAAW+C,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClDhD,KAAKlT,uBAAuB,GAC1B,CAAC6T,SAAS,CAACkC,gBAAgB,CAACnO,MAAMzH,GAAG,IAAI8V;QAC7C;QAEA,2DAA2D;QAC3D,IAAI,CAACnB,qBAAqBlC,aAAaM,KAAK/V,GAAG,EAAE;YAC/C+V,KAAKlT,uBAAuB,GAAG;QACjC;QAEA,IAAI6T,SAAS,IAAI,CAACzW,WAAW,IAAI5E,IAAIW,OAAO,CAACyP,+BAAmB,CAAC,EAAE;YACjE,uEAAuE;YACvE6L,sBAAsBzL;QACxB;QAEAA,cAAcqN,IAAAA,wCAAmB,EAACrN;QAClCyL,sBAAsB4B,IAAAA,wCAAmB,EAAC5B;QAC1C,IAAI,IAAI,CAACpW,gBAAgB,EAAE;YACzBoW,sBAAsB,IAAI,CAACpW,gBAAgB,CAAC5E,SAAS,CAACgb;QACxD;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAIK,mBAAmB;YACrBL,sBAAsB,IAAI,CAAClL,iBAAiB,CAACkL;YAC7CzL,cAAc,IAAI,CAACO,iBAAiB,CAACP;QACvC;QAEA,sDAAsD;QACtD,MAAM+D,mBACJ,MAAM,IAAI,CAACC,mBAAmB,CAAC;YAC7BC,gBAAgBvL,OAAOuK,MAAM,CAAC,CAAC,GAAGzT,IAAIW,OAAO;QAC/C;QAEF,0EAA0E;QAC1E4T,iBAAiBG,iBAAiB;QAElC,IACE6H,CAAAA,+BAAAA,YAAauB,KAAK,KAClBzM,IAAAA,sBAAc,EAAClR,aACdwa,CAAAA,WAAWxB,cAAc,IAAIiB,SAAQ,GACtC;YACA,MAAM2D,eAAe,MAAM,IAAI,CAAC5E,cAAc,CAAC;gBAC7ChZ;gBACAqQ;gBACAiE,gBAAgBzU,IAAIW,OAAO;gBAC3BgR,MAAMgJ,WAAWhJ,IAAI;gBACrByI;YACF;YACA,IAAIA,aAAa,IAAI,CAAC5a,UAAU,CAACC,YAAY,CAACgK,eAAe,EAAE;oBACzDsU;gBAAJ,KAAIA,kCAAAA,aAAaC,iBAAiB,qBAA9BD,gCAAgC1b,MAAM,EAAE;oBAC1C,IAAI4b,8BAA8B;oBAClC,KAAK,MAAM3P,SAASyP,aAAaC,iBAAiB,CAAE;wBAClD,MAAME,sBAAsB5P,MAAM4P,mBAAmB;wBACrD,IAAI,CAACA,uBAAuBA,oBAAoB7b,MAAM,KAAK,GAAG;4BAC5D,kEAAkE;4BAClE4b,8BAA8B;4BAC9B;wBACF;wBACA,IACEA,gCAAgC,QAChCC,oBAAoB7b,MAAM,GAAG4b,4BAA4B5b,MAAM,EAC/D;4BACA4b,8BAA8BC;wBAChC;oBACF;oBACA,IAAID,6BAA6B;wBAC/B,MAAME,8BAA8B,IAAIC,IACtCH,4BAA4B9K,GAAG,CAAC,CAACC,IAAM;gCAACA;gCAAG;6BAAG;wBAEhDrS,IAAAA,2BAAc,EACZf,KACA,+BACAme;oBAEJ;gBACF;YACF;QACF;QAEA,mDAAmD;QACnD,IACEne,IAAIkM,MAAM,KAAK,aACf,CAAC6O,aACA,CAAA,CAACwB,eAAe,CAAC8B,IAAAA,6BAAqB,EAAC9B,YAAW,GACnD;YACA,MAAM+B,IAAAA,0BAAY,EAACte,KAAK0B,KAAK,IAAIqU,SAAS,MAAM;gBAAEwI,QAAQ;YAAI;YAC9D,OAAO;QACT;QAEA,MAAMC,UAAUlP,IAAAA,0BAAiB,EAACtP,OAAOA,IAAIwO,eAAe,GAAGxO;QAC/D,MAAM8V,WAAWnH,IAAAA,2BAAkB,EAACjN,OAAOA,IAAIkN,gBAAgB,GAAGlN;QAElE,MAAM+c,gBAAgBld,IAAAA,UAAQ,EAACU,IAAAA,2BAAc,EAACjC,KAAK,cAAcA,IAAIqB,GAAG;QACxE,IAAIqd,eAAeD,cAActe,QAAQ,IAAI;QAE7C,KAAK,MAAM0D,cAAc;YACvB,IAAI,CAACzD,WAAW,CAACC,kBAAkB;YACnC,IAAI,CAACD,WAAW,CAACY,WAAW;YAC5B,IAAI,CAACZ,WAAW,CAACc,GAAG;SACrB,CAAE;YACD,IAAI2C,8BAAAA,WAAYvD,KAAK,CAACoe,eAAe;gBACnCA,eAAe7a,WAAW5C,SAAS,CAACyd;YACtC;QACF;QAEA,6DAA6D;QAC7D,0FAA0F;QAC1F,sEAAsE;QACtE,IAAI,CAAE,CAAA,IAAI,CAAC9Z,WAAW,IAAIkW,eAAc,GAAI;YAC1C0D,QAAQnd,GAAG,GAAG,GAAGqd,eAAeD,cAAczC,MAAM,IAAI,IAAI;QAC9D;QAEA,wCAAwC;QACxCvF,IAAAA,2BAAc,EAAC+H,SAASvc,IAAAA,2BAAc,EAACjC;QACvCe,IAAAA,2BAAc,EAACyd,SAAS,WAAW,IAAI,CAACnZ,OAAO;QAC/CtE,IAAAA,2BAAc,EAACyd,SAAS,SAASpP;QACjCrO,IAAAA,2BAAc,EAACyd,SAAS,UAAU9D,KAAK7Y,MAAM;QAC7Cd,IAAAA,2BAAc,EAACyd,SAAS,gBAAgB,IAAI,CAACjX,UAAU,CAACoX,YAAY;QACpE5d,IAAAA,2BAAc,EAACyd,SAAS,eAAe,IAAI,CAAC5Z,WAAW;QAEvD,IAAI8V,KAAK5O,GAAG,EAAE;YACZ/K,IAAAA,2BAAc,EAACyd,SAAS,eAAe9D,KAAK5O,GAAG;QACjD;QAEA,MAAMyK,UAMeoE,WAAWiE,YAAY,CAACrI,OAAO;QAEpD,MAAMsI,kBACJ,qDAAqD;QACrD,qDAAqD;QACrD,qDAAqD;QACrD,0BAA0B;QAC1BxY,QAAQC,GAAG,CAACwY,QAAQ,KAAK,gBACrB,IAAIC,MAAMP,SAAS;YACjBnQ,KAAI2Q,MAAW,EAAEC,IAAI;gBACnB,IAAI,OAAOD,MAAM,CAACC,KAAK,KAAK,YAAY;oBACtC,OAAOD,MAAM,CAACC,KAAK,CAAChV,IAAI,CAAC+U;gBAC3B;gBACA,OAAOA,MAAM,CAACC,KAAK;YACrB;YACAC,KAAIF,MAAW,EAAEC,IAAI,EAAEpM,KAAK;gBAC1B,IAAIoM,SAAS,gBAAgB;;oBACzBjf,IAAYmf,YAAY,GAAGtM;gBAC/B;gBACAmM,MAAM,CAACC,KAAK,GAAGpM;gBACf,OAAO;YACT;QACF,KACA2L;QAEN,MAAMjI,QAAQsI,iBAAiB/I,UAAU;YACvC6C,WAAW,IAAI,CAACH,YAAY;QAC9B;QAEA,uCAAuC;QACvC,OAAO;IACT;IAEQzH,kBAAkBhP,IAAY,EAAEqd,cAAc,IAAI,EAAE;QAC1D,IAAIrd,KAAKoa,QAAQ,CAAC,IAAI,CAACna,OAAO,GAAG;YAC/B,MAAMqd,YAAYtd,KAAKW,SAAS,CAC9BX,KAAKud,OAAO,CAAC,IAAI,CAACtd,OAAO,IAAI,IAAI,CAACA,OAAO,CAACK,MAAM;YAGlDN,OAAOiP,IAAAA,wCAAmB,EAACqO,UAAUtP,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAAClK,gBAAgB,IAAIuZ,aAAa;YACxC,OAAO,IAAI,CAACvZ,gBAAgB,CAAC5E,SAAS,CAACc;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChCwd,oBAAoBjR,KAAa,EAAE;QAC3C,IAAI,IAAI,CAACtK,kBAAkB,CAAC0C,GAAG,EAAE;gBACP;YAAxB,MAAM8Y,mBAAkB,sBAAA,IAAI,CAAC/U,aAAa,qBAAlB,mBAAoB,CAAC6D,MAAM;YAEnD,IAAI,CAACkR,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACd1T,GAAkD,EAClD2T,gBAAyB,EACzB;YAkBgB;QAjBhB,MAAM,EAAEtQ,KAAK,EAAEjP,QAAQ,EAAE,GAAG4L;QAE5B,MAAM4T,WAAW,IAAI,CAACJ,mBAAmB,CAACpf;QAC1C,MAAMia,YAAYnH,MAAMC,OAAO,CAACyM;QAEhC,IAAIhO,OAAOxR;QACX,IAAIia,WAAW;YACb,4EAA4E;YAC5EzI,OAAOgO,QAAQ,CAACA,SAAStd,MAAM,GAAG,EAAE;QACtC;QAEA,MAAM9B,SAAS,MAAM,IAAI,CAACqf,kBAAkB,CAAC;YAC3CzN,QAAQlQ,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;YAChC2R;YACAvC;YACAvN,QAAQkK,IAAIxE,UAAU,CAAC1F,MAAM,IAAI,CAAC;YAClCuY;YACAyF,YAAY,CAAC,GAAC,oCAAA,IAAI,CAACrgB,UAAU,CAACC,YAAY,CAACqgB,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAIzf,QAAQ;YACVuM,IAAAA,iBAAS,IAAGmT,oBAAoB,CAAC,cAAc9f;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACsZ,8BAA8B,CAAC1N,KAAKxL;YACxD,EAAE,OAAOuL,KAAK;gBACZ,MAAMoU,oBAAoBpU,eAAeoK,wCAAe;gBAExD,IAAI,CAACgK,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAM5T;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAcoN,iBACZnN,GAAkD,EACjB;QACjC,OAAOe,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,0BAAc,CAACiM,gBAAgB,EAC/B;YACEhM,UAAU,CAAC,cAAc,CAAC;YAC1BI,YAAY;gBACV,cAAcvB,IAAI5L,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAACggB,oBAAoB,CAACpU;QACnC;IAEJ;IAQA,MAAcoU,qBACZpU,GAAkD,EACjB;YAmBzB;QAlBR,MAAM,EAAE/L,GAAG,EAAE0B,GAAG,EAAE0N,KAAK,EAAEjP,QAAQ,EAAE,GAAG4L;QACtC,IAAI4F,OAAOxR;QACX,MAAMuf,mBACJzd,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE,uBAAuB;QAEjD,IACE,CAAC,IAAI,CAAC4E,WAAW,IACjB,IAAI,CAACpF,UAAU,CAACC,YAAY,CAAC8b,yBAAyB,EACtD;YACAxa,IAAAA,2BAAc,EACZgL,IAAI/L,GAAG,EACP,2BACAoP,KAAK,CAAC0M,sCAAoB,CAAC;QAE/B;QACA,OAAO1M,KAAK,CAAC0M,sCAAoB,CAAC;QAElC,MAAMhc,UAAwB;YAC5B4F,IAAI,GAAE,qBAAA,IAAI,CAAC/C,YAAY,qBAAjB,mBAAmByd,WAAW,CAACpgB,KAAKG;QAC5C;QAEA,IAAI;YACF,WAAW,MAAMG,SAAS,IAAI,CAACuK,QAAQ,CAACwV,QAAQ,CAAClgB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAMwgB,eAAere,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAAC4E,WAAW,IACjB,OAAO0b,iBAAiB,YACxBjP,IAAAA,sBAAc,EAACiP,gBAAgB,OAC/BA,iBAAiBhgB,MAAMkR,UAAU,CAACrR,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMI,SAAS,MAAM,IAAI,CAACkf,mBAAmB,CAC3C;oBACE,GAAG1T,GAAG;oBACN5L,UAAUG,MAAMkR,UAAU,CAACrR,QAAQ;oBACnCoH,YAAY;wBACV,GAAGwE,IAAIxE,UAAU;wBACjB1F,QAAQvB,MAAMuB,MAAM;oBACtB;gBACF,GACA6d;gBAEF,IAAInf,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAACwE,aAAa,CAACuP,eAAe,EAAE;gBACtC,sDAAsD;gBACtDvI,IAAI5L,QAAQ,GAAG,IAAI,CAAC4E,aAAa,CAACuP,eAAe,CAAC3C,IAAI;gBACtD,MAAMpR,SAAS,MAAM,IAAI,CAACkf,mBAAmB,CAAC1T,KAAK2T;gBACnD,IAAInf,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAOiM,OAAO;YACd,MAAMV,MAAMsK,IAAAA,uBAAc,EAAC5J;YAE3B,IAAIA,iBAAiB+T,wBAAiB,EAAE;gBACtChU,QAAQC,KAAK,CACX,yCACAgU,KAAKC,SAAS,CACZ;oBACE9O;oBACAtQ,KAAK0K,IAAI/L,GAAG,CAACqB,GAAG;oBAChBgP,aAAatE,IAAI/L,GAAG,CAACW,OAAO,CAACyP,+BAAmB,CAAC;oBACjDsQ,SAASze,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;oBACjCyS,YAAY,CAAC,CAACxQ,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;oBACtC2gB,YAAY1e,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAM8L;YACR;YAEA,IAAIA,eAAeoK,wCAAe,IAAIwJ,kBAAkB;gBACtD,MAAM5T;YACR;YACA,IAAIA,eAAeqI,kBAAW,IAAIrI,eAAesI,qBAAc,EAAE;gBAC/D1S,IAAIkM,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAACgT,qBAAqB,CAAC7U,KAAKD;YAC/C;YAEApK,IAAIkM,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAACmL,OAAO,CAAC,SAAS;gBAC9BhY,IAAAA,2BAAc,EAACgL,IAAI/L,GAAG,EAAE,qBAAqB;gBAC7C,MAAM,IAAI,CAAC4gB,qBAAqB,CAAC7U,KAAKD;gBACtCvI,IAAAA,8BAAiB,EAACwI,IAAI/L,GAAG,EAAE;YAC7B;YAEA,MAAM6gB,iBAAiB/U,eAAe5M;YAEtC,IAAI,CAAC2hB,gBAAgB;gBACnB,IAAI,IAAI,CAACjc,WAAW,IAAI,IAAI,CAAC2C,UAAU,CAAC5C,GAAG,EAAE;oBAC3C,IAAImc,IAAAA,gBAAO,EAAChV,MAAMA,IAAI6F,IAAI,GAAGA;oBAC7B,MAAM7F;gBACR;gBACA,IAAI,CAACW,QAAQ,CAAC2J,IAAAA,uBAAc,EAACtK;YAC/B;YACA,MAAMgK,WAAW,MAAM,IAAI,CAAC8K,qBAAqB,CAC/C7U,KACA8U,iBAAiB,AAAC/U,IAA0BxM,UAAU,GAAGwM;YAE3D,OAAOgK;QACT;QAEA,MAAMnU,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IACED,cACA,CAAC,CAACoK,IAAI/L,GAAG,CAACW,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACe,IAAIkM,UAAU,IAAIlM,IAAIkM,UAAU,KAAK,OAAOlM,IAAIkM,UAAU,KAAK,GAAE,GACnE;YACA,MAAMuE,SAASlQ,IAAAA,2BAAc,EAACjC,KAAK;YAEnC0B,IAAIsW,SAAS,CACX,yBACA,GAAG7F,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKhS,UAAU;YAE5CuB,IAAIkM,UAAU,GAAG;YACjBlM,IAAIsW,SAAS,CAAC,gBAAgB+I,oCAAwB;YACtDrf,IAAIwN,IAAI,CAAC;YACTxN,IAAIyN,IAAI;YACR,OAAO;QACT;QAEAzN,IAAIkM,UAAU,GAAG;QACjB,OAAO,IAAI,CAACgT,qBAAqB,CAAC7U,KAAK;IACzC;IAEA,MAAaiV,aACXhhB,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAAwB,CAAC,CAAC,EACF;QACxB,OAAOtC,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC+T,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAACjhB,KAAK0B,KAAKvB,UAAUiP;QACnD;IACF;IAEA,MAAc6R,iBACZjhB,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC+I,aAAa,CAAC,CAACpM,MAAQ,IAAI,CAACmN,gBAAgB,CAACnN,MAAM;YAC7D/L;YACA0B;YACAvB;YACAiP;QACF;IACF;IAEA,MAAaiF,YACXvI,GAAiB,EACjB9L,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAA4B,CAAC,CAAC,EAC9B8R,aAAa,IAAI,EACF;QACf,OAAOpU,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAACoH,WAAW,EAAE;YACnD,OAAO,IAAI,CAAC8M,eAAe,CAACrV,KAAK9L,KAAK0B,KAAKvB,UAAUiP,OAAO8R;QAC9D;IACF;IAEA,MAAcC,gBACZrV,GAAiB,EACjB9L,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAA4B,CAAC,CAAC,EAC9B8R,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdxf,IAAIsW,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACZ,IAAI,CACd,OAAOrL;YACL,MAAM+J,WAAW,MAAM,IAAI,CAAC8K,qBAAqB,CAAC7U,KAAKD;YACvD,IAAI,IAAI,CAAClH,WAAW,IAAIlD,IAAIkM,UAAU,KAAK,KAAK;gBAC9C,MAAM9B;YACR;YACA,OAAOgK;QACT,GACA;YAAE9V;YAAK0B;YAAKvB;YAAUiP;QAAM;IAEhC;IAQA,MAAcwR,sBACZ7U,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOgB,IAAAA,iBAAS,IAAGE,KAAK,CAACC,0BAAc,CAAC2T,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACQ,yBAAyB,CAACrV,KAAKD;QAC7C;IACF;IAEA,MAAgBsV,0BACdrV,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAACvE,UAAU,CAAC5C,GAAG,IAAIoH,IAAI5L,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACL+O,MAAMkO,qBAAY,CAACiE,KAAK;YAC1B;QACF;QACA,MAAM,EAAE3f,GAAG,EAAE0N,KAAK,EAAE,GAAGrD;QAEvB,IAAI;YACF,IAAIxL,SAAsC;YAE1C,MAAM+gB,QAAQ5f,IAAIkM,UAAU,KAAK;YACjC,IAAI2T,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAACtd,kBAAkB,CAAC0C,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CnG,SAAS,MAAM,IAAI,CAACqf,kBAAkB,CAAC;wBACrCzN,QAAQlQ,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;wBAChC2R,MAAM6P,2CAAgC;wBACtCpS;wBACAvN,QAAQ,CAAC;wBACTuY,WAAW;wBACX4F,cAAc;wBACd3e,KAAK0K,IAAI/L,GAAG,CAACqB,GAAG;oBAClB;oBACAkgB,eAAehhB,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAACwY,OAAO,CAAC,SAAU;oBAC3CxY,SAAS,MAAM,IAAI,CAACqf,kBAAkB,CAAC;wBACrCzN,QAAQlQ,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;wBAChC2R,MAAM;wBACNvC;wBACAvN,QAAQ,CAAC;wBACTuY,WAAW;wBACX,qEAAqE;wBACrE4F,cAAc;wBACd3e,KAAK0K,IAAI/L,GAAG,CAACqB,GAAG;oBAClB;oBACAkgB,eAAehhB,WAAW;gBAC5B;YACF;YACA,IAAIkhB,aAAa,CAAC,CAAC,EAAE/f,IAAIkM,UAAU,EAAE;YAErC,IACE,CAAC3L,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE,wBACzB,CAACO,UACDyc,8BAAmB,CAACb,QAAQ,CAACsF,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAACla,UAAU,CAAC5C,GAAG,EAAE;oBACjDpE,SAAS,MAAM,IAAI,CAACqf,kBAAkB,CAAC;wBACrCzN,QAAQlQ,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;wBAChC2R,MAAM8P;wBACNrS;wBACAvN,QAAQ,CAAC;wBACTuY,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACT4F,cAAc;wBACd3e,KAAK0K,IAAI/L,GAAG,CAACqB,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACd,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAACqf,kBAAkB,CAAC;oBACrCzN,QAAQlQ,IAAAA,2BAAc,EAAC8J,IAAI/L,GAAG,EAAE;oBAChC2R,MAAM;oBACNvC;oBACAvN,QAAQ,CAAC;oBACTuY,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACT4F,cAAc;oBACd3e,KAAK0K,IAAI/L,GAAG,CAACqB,GAAG;gBAClB;gBACAogB,aAAa;YACf;YAEA,IACEpb,QAAQC,GAAG,CAACwY,QAAQ,KAAK,gBACzB,CAACyC,gBACA,MAAM,IAAI,CAACxI,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAAC3U,oBAAoB;YAC3B;YAEA,IAAI,CAAC7D,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAACgH,UAAU,CAAC5C,GAAG,EAAE;oBACvB,OAAO;wBACL,mDAAmD;wBACnDuK,MAAMkO,qBAAY,CAACC,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC,EACVC,oCAAwB;oBAE5B;gBACF;gBAEA,MAAM,IAAIpe,kBACR,qBAA8C,CAA9C,IAAIE,MAAM,sCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YAEjD;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAImB,OAAOoa,UAAU,CAAC4B,WAAW,EAAE;gBACjCxb,IAAAA,2BAAc,EAACgL,IAAI/L,GAAG,EAAE,SAAS;oBAC/BwR,YAAYjR,OAAOoa,UAAU,CAAC4B,WAAW,CAAC/K,UAAU;oBACpD3P,QAAQhC;gBACV;YACF,OAAO;gBACL0D,IAAAA,8BAAiB,EAACwI,IAAI/L,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACyZ,8BAA8B,CAC9C;oBACE,GAAG1N,GAAG;oBACN5L,UAAUshB;oBACVla,YAAY;wBACV,GAAGwE,IAAIxE,UAAU;wBACjBuE;oBACF;gBACF,GACAvL;YAEJ,EAAE,OAAOmhB,oBAAoB;gBAC3B,IAAIA,8BAA8BxL,wCAAe,EAAE;oBACjD,MAAM,qBAAmD,CAAnD,IAAI9W,MAAM,2CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkD;gBAC1D;gBACA,MAAMsiB;YACR;QACF,EAAE,OAAOlV,OAAO;YACd,MAAMmV,oBAAoBvL,IAAAA,uBAAc,EAAC5J;YACzC,MAAMqU,iBAAiBc,6BAA6BziB;YACpD,IAAI,CAAC2hB,gBAAgB;gBACnB,IAAI,CAACpU,QAAQ,CAACkV;YAChB;YACAjgB,IAAIkM,UAAU,GAAG;YACjB,MAAMgU,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9D9V,IAAI/L,GAAG,CAACqB,GAAG;YAGb,IAAIugB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnC7gB,IAAAA,2BAAc,EAACgL,IAAI/L,GAAG,EAAE,SAAS;oBAC/BwR,YAAYoQ,mBAAmBrF,WAAW,CAAE/K,UAAU;oBACtD3P,QAAQhC;gBACV;gBAEA,OAAO,IAAI,CAAC4Z,8BAA8B,CACxC;oBACE,GAAG1N,GAAG;oBACN5L,UAAU;oBACVoH,YAAY;wBACV,GAAGwE,IAAIxE,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtCuE,KAAK+U,iBACDc,kBAAkBriB,UAAU,GAC5BqiB;oBACN;gBACF,GACA;oBACEvS;oBACAuL,YAAYiH;gBACd;YAEJ;YACA,OAAO;gBACL1S,MAAMkO,qBAAY,CAACC,UAAU,CAAC,yBAAyB;YACzD;QACF;IACF;IAEA,MAAayE,kBACXhW,GAAiB,EACjB9L,GAAkB,EAClB0B,GAAmB,EACnBvB,QAAgB,EAChBiP,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC+I,aAAa,CAAC,CAACpM,MAAQ,IAAI,CAAC6U,qBAAqB,CAAC7U,KAAKD,MAAM;YACvE9L;YACA0B;YACAvB;YACAiP;QACF;IACF;IAEA,MAAalN,UACXlC,GAAkB,EAClB0B,GAAmB,EACnBxB,SAA8D,EAC9DghB,aAAa,IAAI,EACF;QACf,MAAM,EAAE/gB,QAAQ,EAAEiP,KAAK,EAAE,GAAGlP,YAAYA,YAAYqB,IAAAA,UAAQ,EAACvB,IAAIqB,GAAG,EAAG;QAEvE,uDAAuD;QACvD,IAAI,IAAI,CAAC7B,UAAU,CAACkG,IAAI,EAAE;YACxB,IAAI,CAACzD,IAAAA,2BAAc,EAACjC,KAAK,WAAW;gBAClCe,IAAAA,2BAAc,EAACf,KAAK,UAAU,IAAI,CAACR,UAAU,CAACkG,IAAI,CAACxC,aAAa;YAClE;YACAnC,IAAAA,2BAAc,EAACf,KAAK,iBAAiB,IAAI,CAACR,UAAU,CAACkG,IAAI,CAACxC,aAAa;QACzE;QAEAxB,IAAIkM,UAAU,GAAG;QACjB,OAAO,IAAI,CAACyG,WAAW,CAAC,MAAMrU,KAAK0B,KAAKvB,UAAWiP,OAAO8R;IAC5D;AACF", "ignoreList": [0]}