"use strict";
'use strict';

var tabs = require('./tabs.cjs');
var tabs$1 = require('@ark-ui/react/tabs');



exports.Content = tabs.TabsContent;
exports.ContentGroup = tabs.TabsContentGroup;
exports.Indicator = tabs.TabsIndicator;
exports.List = tabs.TabsList;
exports.PropsProvider = tabs.TabsPropsProvider;
exports.Root = tabs.TabsRoot;
exports.RootProvider = tabs.TabsRootProvider;
exports.Trigger = tabs.TabsTrigger;
Object.defineProperty(exports, "Context", {
  enumerable: true,
  get: function () { return tabs$1.TabsContext; }
});
