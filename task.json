{"version": "2.0.0", "tasks": [{"label": "Backend: Install", "type": "shell", "command": "pip install -r requirements.txt", "options": {"cwd": "${workspaceFolder}/backend"}}, {"label": "Frontend: Install", "type": "shell", "command": "npm install", "options": {"cwd": "${workspaceFolder}/web"}}, {"label": "Index: Build", "type": "shell", "command": "python build_index.py", "options": {"cwd": "${workspaceFolder}/backend"}, "dependsOn": ["Backend: Install"]}]}